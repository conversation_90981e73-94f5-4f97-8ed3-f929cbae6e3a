#!/usr/bin/env python3
"""
Cursor对话统计分析器
统计Cursor中多个对话的代码修改行数和采纳率
时间周期：上周一到今天
"""

import os
import json
import sqlite3
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import argparse


@dataclass
class CodeChange:
    """代码变更记录"""
    file_path: str
    lines_added: int
    lines_removed: int
    lines_modified: int
    timestamp: datetime
    conversation_id: str
    is_accepted: bool = False
    is_cursor_generated: bool = True  # 从Cursor对话中提取的都是Cursor生成的
    code_content: str = ""


@dataclass
class ConversationStats:
    """对话统计信息"""
    conversation_id: str
    total_changes: int
    total_lines_added: int
    total_lines_removed: int
    total_lines_modified: int
    accepted_changes: int
    acceptance_rate: float
    start_time: datetime
    end_time: datetime
    cursor_generated_lines: int = 0
    total_code_blocks: int = 0


class CursorStatsAnalyzer:
    """Cursor统计分析器"""
    
    def __init__(self, cursor_data_path: Optional[str] = None):
        """
        初始化分析器
        
        Args:
            cursor_data_path: Cursor数据目录路径，如果为None则自动检测
        """
        self.cursor_data_path = cursor_data_path or self._find_cursor_data_path()
        self.conversations: List[Dict] = []
        self.code_changes: List[CodeChange] = []
        
    def _find_cursor_data_path(self) -> str:
        """自动查找Cursor数据目录"""
        possible_paths = [
            # macOS
            os.path.expanduser("~/Library/Application Support/Cursor"),
            os.path.expanduser("~/Library/Application Support/Cursor/User/workspaceStorage"),
            # Windows
            os.path.expanduser("~/AppData/Roaming/Cursor"),
            os.path.expanduser("~/AppData/Local/Cursor"),
            # Linux
            os.path.expanduser("~/.config/Cursor"),
            os.path.expanduser("~/.cursor"),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                print(f"找到Cursor数据目录: {path}")
                return path
                
        raise FileNotFoundError("无法找到Cursor数据目录，请手动指定路径")
    
    def _get_time_range(self) -> Tuple[datetime, datetime]:
        """获取时间范围：上周一到今天"""
        today = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 计算上周一
        days_since_monday = today.weekday()  # 0=Monday, 6=Sunday
        days_to_last_monday = days_since_monday + 7
        last_monday = today - timedelta(days=days_to_last_monday)
        last_monday = last_monday.replace(hour=0, minute=0, second=0, microsecond=0)
        
        return last_monday, today
    
    def _parse_diff(self, diff_text: str) -> Tuple[int, int, int]:
        """
        解析diff文本，计算行数变更
        
        Returns:
            (lines_added, lines_removed, lines_modified)
        """
        if not diff_text:
            return 0, 0, 0
            
        lines_added = 0
        lines_removed = 0
        lines_modified = 0
        
        # 简单的diff解析
        for line in diff_text.split('\n'):
            line = line.strip()
            if line.startswith('+') and not line.startswith('+++'):
                lines_added += 1
            elif line.startswith('-') and not line.startswith('---'):
                lines_removed += 1
                
        # 修改的行数 = min(添加, 删除)
        lines_modified = min(lines_added, lines_removed)
        lines_added -= lines_modified
        lines_removed -= lines_modified
        
        return lines_added, lines_removed, lines_modified
    
    def load_conversations_from_sqlite(self) -> None:
        """从SQLite数据库加载对话数据"""
        db_files = []
        
        # 查找可能的数据库文件
        for root, dirs, files in os.walk(self.cursor_data_path):
            for file in files:
                if file.endswith('.db') or file.endswith('.sqlite'):
                    db_files.append(os.path.join(root, file))
        
        print(f"找到 {len(db_files)} 个数据库文件")
        
        start_time, end_time = self._get_time_range()
        print(f"时间范围: {start_time.strftime('%Y-%m-%d %H:%M')} 到 {end_time.strftime('%Y-%m-%d %H:%M')}")
        
        for db_file in db_files:
            try:
                self._load_from_db(db_file, start_time, end_time)
            except Exception as e:
                print(f"读取数据库 {db_file} 时出错: {e}")
                continue
    
    def _load_from_db(self, db_file: str, start_time: datetime, end_time: datetime) -> None:
        """从单个数据库文件加载数据"""
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 查找可能包含对话数据的表
            conversation_tables = [t for t in tables if any(keyword in t.lower() 
                                 for keyword in ['conversation', 'chat', 'message', 'history'])]
            
            for table in conversation_tables:
                try:
                    self._extract_conversations_from_table(cursor, table, start_time, end_time)
                except Exception as e:
                    print(f"从表 {table} 提取数据时出错: {e}")
                    continue
                    
            conn.close()
            
        except Exception as e:
            print(f"连接数据库 {db_file} 时出错: {e}")
    
    def _extract_conversations_from_table(self, cursor, table: str, start_time: datetime, end_time: datetime) -> None:
        """从表中提取对话数据"""
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table})")
        columns = [row[1] for row in cursor.fetchall()]
        
        # 查找时间相关的列
        time_columns = [col for col in columns if any(keyword in col.lower() 
                       for keyword in ['time', 'date', 'created', 'updated', 'timestamp'])]
        
        if not time_columns:
            return
            
        time_col = time_columns[0]
        
        # 查询数据
        query = f"SELECT * FROM {table} ORDER BY {time_col} DESC LIMIT 1000"
        cursor.execute(query)
        rows = cursor.fetchall()
        
        for row in rows:
            try:
                # 创建字典
                row_dict = dict(zip(columns, row))
                
                # 解析时间
                timestamp_str = str(row_dict.get(time_col, ''))
                timestamp = self._parse_timestamp(timestamp_str)
                
                if timestamp and start_time <= timestamp <= end_time:
                    # 查找可能包含代码变更的字段
                    content_fields = [col for col in columns if any(keyword in col.lower() 
                                    for keyword in ['content', 'message', 'text', 'diff', 'change'])]
                    
                    for field in content_fields:
                        content = row_dict.get(field, '')
                        if content and self._contains_code_change(str(content)):
                            self._extract_code_changes(row_dict, timestamp, str(content))
                            
            except Exception as e:
                continue
    
    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """解析时间戳"""
        if not timestamp_str:
            return None
            
        # 尝试不同的时间格式
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S.%fZ',
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str, fmt)
            except ValueError:
                continue
                
        # 尝试解析Unix时间戳
        try:
            if timestamp_str.isdigit():
                timestamp = int(timestamp_str)
                if timestamp > 1000000000000:  # 毫秒时间戳
                    timestamp = timestamp / 1000
                return datetime.fromtimestamp(timestamp)
        except (ValueError, OSError):
            pass
            
        return None
    
    def _contains_code_change(self, content: str) -> bool:
        """检查内容是否包含代码变更"""
        # 检查是否包含代码块标记
        if '```' in content:
            return True

        code_indicators = [
            'class ', 'function ', 'def ', 'import ',
            '{', '}', ';', '//', '/*', '*/',
            'public ', 'private ', 'protected ',
            '@Override', '@Component', '@Service',
            'package ', 'interface ', 'extends ',
            'implements ', 'return ', 'if (', 'for (',
            'while (', 'try {', 'catch (', 'finally {'
        ]

        content_lower = content.lower()
        return any(indicator in content_lower for indicator in code_indicators)

    def _extract_code_blocks(self, content: str) -> List[str]:
        """从内容中提取代码块"""
        code_blocks = []

        # 提取```包围的代码块
        import re
        pattern = r'```[\w]*\n(.*?)\n```'
        matches = re.findall(pattern, content, re.DOTALL)
        code_blocks.extend(matches)

        # 提取单行代码（`包围的）
        pattern = r'`([^`\n]+)`'
        matches = re.findall(pattern, content)
        code_blocks.extend(matches)

        return code_blocks

    def _count_code_lines(self, code_blocks: List[str]) -> int:
        """统计代码行数"""
        total_lines = 0
        for block in code_blocks:
            # 过滤空行和注释行
            lines = block.split('\n')
            code_lines = 0
            for line in lines:
                line = line.strip()
                if line and not line.startswith('//') and not line.startswith('/*') and not line.startswith('*'):
                    code_lines += 1
            total_lines += code_lines
        return total_lines
    
    def _extract_code_changes(self, row_dict: Dict, timestamp: datetime, content: str) -> None:
        """提取代码变更信息"""
        # 提取代码块
        code_blocks = self._extract_code_blocks(content)

        if code_blocks:
            # 统计代码行数
            cursor_lines = self._count_code_lines(code_blocks)

            # 简单的diff解析（如果有的话）
            lines_added, lines_removed, lines_modified = self._parse_diff(content)

            # 如果没有diff信息，使用代码块行数作为添加行数
            if lines_added == 0 and cursor_lines > 0:
                lines_added = cursor_lines

            change = CodeChange(
                file_path=self._extract_file_path(content),
                lines_added=lines_added,
                lines_removed=lines_removed,
                lines_modified=lines_modified,
                timestamp=timestamp,
                conversation_id=str(row_dict.get('id', 'unknown')),
                is_accepted=True,  # 从对话中提取的代码默认认为是被采纳的
                is_cursor_generated=True,
                code_content='\n'.join(code_blocks)
            )

            self.code_changes.append(change)

    def _extract_file_path(self, content: str) -> str:
        """从内容中提取文件路径"""
        import re

        # 查找常见的文件路径模式
        patterns = [
            r'文件[：:]\s*([^\s\n]+)',
            r'File[：:]\s*([^\s\n]+)',
            r'路径[：:]\s*([^\s\n]+)',
            r'Path[：:]\s*([^\s\n]+)',
            r'```[\w]*\s*//\s*([^\n]+\.(java|xml|js|py|ts|css|html))',
            r'```[\w]*\s*#\s*([^\n]+\.(java|xml|js|py|ts|css|html))',
        ]

        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)

        return "unknown"
    
    def load_conversations_from_json(self) -> None:
        """从JSON文件加载对话数据（备用方法）"""
        json_files = []
        
        for root, dirs, files in os.walk(self.cursor_data_path):
            for file in files:
                if file.endswith('.json'):
                    json_files.append(os.path.join(root, file))
        
        start_time, end_time = self._get_time_range()
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._process_json_data(data, start_time, end_time)
            except Exception as e:
                print(f"读取JSON文件 {json_file} 时出错: {e}")
                continue
    
    def _process_json_data(self, data: Dict, start_time: datetime, end_time: datetime) -> None:
        """处理JSON数据"""
        try:
            # 处理不同可能的JSON结构
            if isinstance(data, dict):
                # 查找对话相关的键
                conversation_keys = ['conversations', 'chats', 'messages', 'history', 'sessions']

                for key in conversation_keys:
                    if key in data:
                        self._process_conversations_array(data[key], start_time, end_time)
                        break
                else:
                    # 如果没有找到对话数组，尝试处理单个对话
                    if 'messages' in data or 'content' in data:
                        self._process_single_conversation(data, start_time, end_time)

            elif isinstance(data, list):
                # 如果数据本身就是数组
                for item in data:
                    self._process_single_conversation(item, start_time, end_time)

        except Exception as e:
            print(f"处理JSON数据时出错: {e}")

    def _process_conversations_array(self, conversations: List, start_time: datetime, end_time: datetime) -> None:
        """处理对话数组"""
        for conv in conversations:
            if isinstance(conv, dict):
                self._process_single_conversation(conv, start_time, end_time)

    def _process_single_conversation(self, conversation: Dict, start_time: datetime, end_time: datetime) -> None:
        """处理单个对话"""
        try:
            # 提取时间戳
            timestamp_fields = ['timestamp', 'created_at', 'date', 'time', 'createdAt']
            timestamp = None

            for field in timestamp_fields:
                if field in conversation:
                    timestamp = self._parse_timestamp(str(conversation[field]))
                    break

            if not timestamp or not (start_time <= timestamp <= end_time):
                return

            # 提取消息内容
            content_fields = ['content', 'message', 'text', 'body', 'response']
            content = ""

            for field in content_fields:
                if field in conversation:
                    content = str(conversation[field])
                    break

            # 如果有messages数组，处理所有消息
            if 'messages' in conversation and isinstance(conversation['messages'], list):
                for msg in conversation['messages']:
                    if isinstance(msg, dict):
                        for field in content_fields:
                            if field in msg:
                                content += "\n" + str(msg[field])

            # 检查是否包含代码
            if content and self._contains_code_change(content):
                conv_id = str(conversation.get('id', conversation.get('conversation_id', 'unknown')))
                self._extract_code_changes({'id': conv_id}, timestamp, content)

        except Exception as e:
            print(f"处理单个对话时出错: {e}")
    
    def calculate_statistics(self) -> List[ConversationStats]:
        """计算统计信息"""
        if not self.code_changes:
            print("没有找到代码变更记录")
            return []
        
        # 按对话ID分组
        conversations = {}
        for change in self.code_changes:
            conv_id = change.conversation_id
            if conv_id not in conversations:
                conversations[conv_id] = []
            conversations[conv_id].append(change)
        
        stats = []
        for conv_id, changes in conversations.items():
            total_changes = len(changes)
            total_lines_added = sum(c.lines_added for c in changes)
            total_lines_removed = sum(c.lines_removed for c in changes)
            total_lines_modified = sum(c.lines_modified for c in changes)
            accepted_changes = sum(1 for c in changes if c.is_accepted)
            acceptance_rate = (accepted_changes / total_changes * 100) if total_changes > 0 else 0
            
            start_time = min(c.timestamp for c in changes)
            end_time = max(c.timestamp for c in changes)
            
            stat = ConversationStats(
                conversation_id=conv_id,
                total_changes=total_changes,
                total_lines_added=total_lines_added,
                total_lines_removed=total_lines_removed,
                total_lines_modified=total_lines_modified,
                accepted_changes=accepted_changes,
                acceptance_rate=acceptance_rate,
                start_time=start_time,
                end_time=end_time
            )
            stats.append(stat)
        
        return sorted(stats, key=lambda x: x.start_time, reverse=True)
    
    def generate_report(self, stats: List[ConversationStats]) -> str:
        """生成统计报告"""
        if not stats:
            return "没有找到符合条件的对话数据"
        
        start_time, end_time = self._get_time_range()
        
        report = f"""
Cursor对话代码修改统计报告
================================

统计时间范围: {start_time.strftime('%Y-%m-%d')} 到 {end_time.strftime('%Y-%m-%d')}
总对话数: {len(stats)}

总体统计:
--------
总代码变更次数: {sum(s.total_changes for s in stats)}
总添加行数: {sum(s.total_lines_added for s in stats)}
总删除行数: {sum(s.total_lines_removed for s in stats)}
总修改行数: {sum(s.total_lines_modified for s in stats)}
总净增行数: {sum(s.total_lines_added - s.total_lines_removed for s in stats)}
平均采纳率: {sum(s.acceptance_rate for s in stats) / len(stats):.1f}%

详细对话统计:
-----------
"""
        
        for i, stat in enumerate(stats, 1):
            net_lines = stat.total_lines_added - stat.total_lines_removed
            report += f"""
对话 {i} (ID: {stat.conversation_id[:8]}...):
  时间: {stat.start_time.strftime('%Y-%m-%d %H:%M')} - {stat.end_time.strftime('%Y-%m-%d %H:%M')}
  变更次数: {stat.total_changes}
  添加行数: {stat.total_lines_added}
  删除行数: {stat.total_lines_removed}
  修改行数: {stat.total_lines_modified}
  净增行数: {net_lines:+d}
  采纳率: {stat.acceptance_rate:.1f}% ({stat.accepted_changes}/{stat.total_changes})
"""
        
        return report
    
    def run_analysis(self) -> str:
        """运行完整分析"""
        print("开始分析Cursor对话数据...")
        
        # 尝试从SQLite加载数据
        try:
            self.load_conversations_from_sqlite()
        except Exception as e:
            print(f"从SQLite加载数据失败: {e}")
        
        # 如果SQLite没有数据，尝试JSON
        if not self.code_changes:
            try:
                self.load_conversations_from_json()
            except Exception as e:
                print(f"从JSON加载数据失败: {e}")
        
        # 计算统计信息
        stats = self.calculate_statistics()
        
        # 生成报告
        return self.generate_report(stats)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Cursor对话代码修改统计分析器')
    parser.add_argument('--data-path', type=str, help='Cursor数据目录路径')
    parser.add_argument('--output', type=str, help='输出文件路径')
    
    args = parser.parse_args()
    
    try:
        analyzer = CursorStatsAnalyzer(args.data_path)
        report = analyzer.run_analysis()
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"报告已保存到: {args.output}")
        else:
            print(report)
            
    except Exception as e:
        print(f"分析过程中出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
