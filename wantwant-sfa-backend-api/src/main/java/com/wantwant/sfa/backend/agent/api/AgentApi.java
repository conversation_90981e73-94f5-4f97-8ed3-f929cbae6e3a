package com.wantwant.sfa.backend.agent.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.agent.vo.AgentVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/05/上午11:05
 */
@Api(value = "AgentApi",tags = "代办事项")
public interface AgentApi {

    @GetMapping("/agent/{person}")
    @ApiOperation(value="获取代办事项信息",notes = "获取代办事项信息",httpMethod="GET")
    Response<List<AgentVo>> getAgentList(@PathVariable String person, @RequestParam boolean cache,@RequestParam(required = false) Integer isApp);


}
