package com.wantwant.sfa.backend.leave.api;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.leave.request.LeaveAuditRequest;
import com.wantwant.sfa.backend.leave.request.LeaveCancelInfoRequest;
import com.wantwant.sfa.backend.leave.request.LeaveCommitInfoRequest;
import com.wantwant.sfa.backend.leave.request.LeaveListRequest;
import com.wantwant.sfa.backend.leave.vo.LeaveAuditRecordVo;
import com.wantwant.sfa.backend.leave.vo.LeaveDetailVo;
import com.wantwant.sfa.backend.leave.vo.LeaveListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Description: 请假API。
 * @Auther: 周明
 * @Date: 2022/06/22/下午7:34
 */
@Api(value = "LeaveApi", tags = "请假API")
@Deprecated
public interface LeaveApi {

    @ApiOperation(value = "请假信息提交", notes = "请假信息提交", httpMethod = "POST")
    @PostMapping("/leave/commit")
    Response commitLeaveInfo(@RequestBody @Validated LeaveCommitInfoRequest request);

    @ApiOperation(value = "销假信息提交", notes = "请假信息提交", httpMethod = "POST")
    @PostMapping("/leave/cancel")
    Response LeaveCancelInfo(@RequestBody @Validated List<LeaveCancelInfoRequest> list);

    @ApiOperation(value = "请假列表查询", notes = "请假列表查询", httpMethod = "GET")
    @GetMapping("/leave/list")
    Response<IPage<LeaveListVo>> leaveList(LeaveListRequest request);

    @ApiOperation(value = "请假详情的销假数据", notes = "请假详情的销假数据", httpMethod = "GET")
    @GetMapping("/leave/detail")
    Response<LeaveDetailVo> leaveDetail(@RequestParam("businessNum") String businessNum);

    @ApiOperation(value = "请假的签核记录", notes = "请假的签核记录", httpMethod = "GET")
    @GetMapping("/leave/auditRecord")
    Response<List<LeaveAuditRecordVo>> leaveAuditList(@RequestParam("businessNum") String businessNum);

    @ApiOperation(value = "请假审批", notes = "请假审批", httpMethod = "POST")
    @PostMapping("/leave/audit")
    Response leaveAudit(@RequestBody @Validated LeaveAuditRequest request);

}
