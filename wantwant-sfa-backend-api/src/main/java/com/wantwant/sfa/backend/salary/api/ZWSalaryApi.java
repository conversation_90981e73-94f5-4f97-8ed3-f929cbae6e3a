package com.wantwant.sfa.backend.salary.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.salary.request.*;
import com.wantwant.sfa.backend.salary.vo.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

public interface ZWSalaryApi {

    @ApiOperation(value = "获取薪资列表", notes = "获取薪资列表", httpMethod = "POST")
    @PostMapping("/zw/salary/list")
    Response<ZWSalaryVo> zwSalaryList(@RequestBody ZWSalaryRequest request);

    @ApiOperation(value = "合伙人薪资", notes = "合伙人薪资", httpMethod = "POST")
    @PostMapping("/zw/salary/branch")
    Response<List<SalaryExpenseQueryPartnerVo>> salaryBranch(@RequestBody @Validated SalaryExpenseQueryRequest request);

    @ApiOperation(value = "区域经理薪资", notes = "区域经理薪资", httpMethod = "POST")
    @PostMapping("/zw/salary/department")
    Response<List<SalaryExpenseQueryCityManagerVo>> salaryDepartment(@RequestBody @Validated SalaryExpenseQueryRequest request);

    @ApiOperation(value = "区域总监薪资", notes = "区域总监薪资", httpMethod = "POST")
    @PostMapping("/zw/salary/company")
    Response<List<SalaryExpenseQueryRegionalDirectorVo>> salaryCompany(@RequestBody @Validated SalaryExpenseQueryRequest request);

    @ApiOperation(value = "设置薪资管控额度", notes = "设置薪资管控额度", httpMethod = "POST")
    @PostMapping("/salary/control/config")
    Response setSalaryControl(@RequestBody @Valid SalaryControlConfigRequest salaryControlConfigRequest);

    @ApiOperation(value = "薪资查询", notes = "薪资查询", httpMethod = "POST")
    @PostMapping("/salary/search")
    Response<Page<SalaryVo>> searchSalary(@RequestBody @Valid SalarySearchRequest salarySearchRequest);

    @ApiOperation(value = "薪资查询-明细列表弹窗", notes = "薪资查询-明细列表弹窗", httpMethod = "POST")
    @PostMapping("/querySalaryDetailList")
    Response<List<SalaryDetailVo>> querySalaryDetailList(@RequestBody @Validated SalaryDetailSearchRequest request);

    @ApiOperation(value = "获取战区额度管控", notes = "获取战区额度管控", httpMethod = "GET")
    @GetMapping("/salary/area/control")
    Response<List<SalaryControlVo>> getAreaQuotaControl();

    @ApiOperation(value = "用人费用明细", notes = "用人费用明细", httpMethod = "POST")
    @PostMapping("/salary/config")
    Response<List<SalaryConfigVo>> getSalaryQuotaConfig(@RequestBody SalaryControlSearchRequest salaryControlSearchRequest);

    @ApiOperation(value = "获取管控额度分配", notes = "获取管控额度分配", httpMethod = "POST")
    @PostMapping("/salary/control")
    Response<List<SalaryControlConfigVo>> getQuotaControl(@RequestBody SalaryControlSearchRequest salaryControlSearchRequest);


    @ApiOperation(value = "导出管控额度分配", notes = "导出管控额度分配", httpMethod = "POST")
    @PostMapping("/salary/control/export")
    void uploadSalaryPackage(@RequestBody SalaryControlSearchRequest salaryControlSearchRequest);


    @ApiOperation(value = "获取分配方式类型", notes = "获取分配方式类型", httpMethod = "GET")
    @GetMapping("/salary/distributePattern/{organizationId}")
    Response<String> getConfigOrgType(@PathVariable String organizationId);


    @ApiOperation(value = "获取额度配置历史", notes = "获取额度配置历史", httpMethod = "GET")
    @GetMapping("/salary/control/history/{organizationId}")
    Response<List<SalaryConfigHistoryVo>> getConfigHistory(@PathVariable String organizationId);

    @ApiOperation(value = "获取当前组织管控额度", notes = "获取当前组织管控额度", httpMethod = "GET")
    @GetMapping("/salary/control/{organizationId}")
    Response<BigDecimal> currentOrgControl(@PathVariable String organizationId);

    @ApiOperation(value = "获取省区使用额度", notes = "获取省区使用额度", httpMethod = "GET")
    @GetMapping("/salary/provinceUsedControl/{organizationId}")
    Response<BigDecimal> provinceUsedControl(@PathVariable String organizationId);

    @ApiOperation(value = "薪资方案查询", notes = "薪资方案查询", httpMethod = "POST")
    @PostMapping("/salary/scheme")
    Response<Page<SalarySchemeVo>> getSalaryScheme(@RequestBody SalarySchemeRequest salarySchemeRequest);

    @ApiOperation(value = "获取用人费用管控权限", notes = "获取用人费用管控权限", httpMethod = "GET")
    @GetMapping("/salary/permission/{person}")
    Response<SalaryControlPermissionVo> getSalaryControlPermission(@PathVariable String person);

    @ApiOperation(value = "用人费用包设置", notes = "用人费用包设置", httpMethod = "POST")
    @PostMapping("/salary/package/config")
    Response salaryPackageConfig(@Valid @RequestBody SalaryPackageConfigRequest salaryPackageConfigRequest);

    @ApiOperation(value = "用人费用包信息", notes = "用人费用包信息", httpMethod = "GET")
    @GetMapping("/salary/package/{person}")
    Response<SalaryPackageVo> getSalaryPackageInfo(@PathVariable String person);

    @ApiOperation(value = "用人费用包已用额度", notes = "用人费用包已用额度", httpMethod = "POST")
    @PostMapping("/salary/getAllocatedHistory")
    Response<List<SalaryAllocatedVo>> getAllocatedHistory(@Valid @RequestBody SearchAllocatedHistoryRequest searchAllocatedHistoryRequest);

    @ApiOperation(value = "导出用人费用包已用额度", notes = "导出用人费用包已用额度", httpMethod = "POST")
    @PostMapping("/salary/getAllocatedHistory/export")
    void uploadAllocatedHistory(@RequestBody SearchAllocatedHistoryRequest searchAllocatedHistoryRequest);


    @ApiOperation(value = "业务BD编织配置", notes = "业务BD编织配置", httpMethod = "POST")
    @PostMapping("/businessBDConfig")
    Response businessBDConfig(@Valid@RequestBody BusinessBDConfigRequest businessBDConfigRequest);

    @ApiOperation(value = "业务BD编织管控查询", notes = "业务BD编织管控查询", httpMethod = "POST")
    @PostMapping("/searchBusinessBDControl")
    Response<IPage<BusinessBDControlVo>> searchBusinessBDControl(@Valid @RequestBody BusinessBDControlSearchRequest businessBDControlSearchRequest);

    @ApiOperation(value = "检查是否可新增业务BD", notes = "检查是否可新增业务BD", httpMethod = "GET")
    @GetMapping("/checkAddBusinessBD/{organizationId}")
    Response<Boolean> checkAddBusinessBD(@PathVariable String organizationId);


    @ApiOperation(value = "导入业务BD规则", notes = "导入业务BD规则", httpMethod = "PUT")
    @PutMapping("/businessBDRule")
    Response importBusinessBDRule(@RequestParam(value = "file", required = true) MultipartFile file,
                                  @RequestParam(value="person",required = true)String person);

    @ApiOperation(value = "查询业务BD自动化", notes = "查询业务BD自动化", httpMethod = "POST")
    @PostMapping("/businessBDRule/search")
    Response<IPage<BusinessBDRuleVo>> searchBusinessBDRule(@RequestBody BusinessBDRuleSearchRequest request);


    @ApiOperation(value = "查询业务BD自动化导出", notes = "查询业务BD自动化导出", httpMethod = "POST")
    @PostMapping( "/businessBDRule/export")
    void exportBusinessBDRule(@RequestBody BusinessBDRuleSearchRequest request);

    @ApiOperation(value = "获取业务BD合伙人配置详情", notes = "获取业务BD合伙人配置详情", httpMethod = "GET")
    @GetMapping("/businessBDRule/{theYearMonth}/{organizationId}")
    Response<List<BusinessBDDetailVO>> getBusinessBDDetail(@PathVariable String theYearMonth,@PathVariable String organizationId);

    @ApiOperation(value = "查询业务BD明细", notes = "查询业务BD明细", httpMethod = "POST")
    @PostMapping("/selectBusinessBDDetail")
    Response<IPage<BusinessBDDetailVO>> selectBusinessBDDetail(@RequestBody BusinessBDSearchRequest businessBDSearchRequest);

    @ApiOperation(value = "查询业务BD按组织明细", notes = "查询业务BD按组织明细", httpMethod = "POST")
    @PostMapping("/selectBusinessBDOrgDetail")
    Response<IPage<BusinessBDOrgDetailVO>> selectBusinessBDOrgDetail(@RequestBody BusinessBDSearchRequest businessBDSearchRequest);

    @ApiOperation(value = "导出业务BD按组织明细", notes = "导出业务BD按组织明细", httpMethod = "POST")
    @PostMapping("/exportBusinessBDOrgDetail")
    void exportBusinessBDOrgDetail(@RequestBody BusinessBDSearchRequest businessBDSearchRequest);

    @ApiOperation(value = "导出业务BD明细", notes = "导出业务BD明细", httpMethod = "POST")
    @PostMapping("/exportBusinessDetail")
    void exportBusinessDetail(@RequestBody BusinessBDSearchRequest businessBDSearchRequest);

    @ApiOperation(value = "查询费用包明细", notes = "查询费用包明细", httpMethod = "POST")
    @PostMapping("/selectSalaryPackageDetail")
    Response<IPage<SalaryPackageDetailVO>> selectSalaryPackageDetail(@Validated @RequestBody SalaryPackageSearchRequest salaryPackageSearchRequest);


    @ApiOperation(value = "获取业务BD费用包额度",notes = "获取业务BD费用包额度", httpMethod = "POST")
    @PostMapping("/getBusinessBDPackageSalary")
    Response<BigDecimal> getBusinessBDPackageSalary(@ApiParam("营业所ID") @RequestParam(value = "departmentId") String departmentId,
                                                    @ApiParam("面试申请ID")@RequestParam(value = "applyId",required = false)Integer applyId);

    @ApiOperation(value = "获取业务BD管控额度", notes = "获取业务BD管控额度", httpMethod = "POST")
    @PostMapping("/salary/businessBD/control")
    Response<IPage<BusinessBDQuotaConfigVo>> getBusinessBDQuotaControl(@RequestBody SalaryControlSearchRequest salaryControlSearchRequest);

    @ApiOperation(value = "导出业务BD管控额度", notes = "导出业务BD管控额度（不分页）", httpMethod = "POST")
    @PostMapping("/salary/businessBD/control/export")
    void exportBusinessBDQuotaControl(@RequestBody SalaryControlSearchRequest salaryControlSearchRequest);


    @ApiOperation(value = "用人费用包业务BD已用额度", notes = "用人费用包业务BD已用额度", httpMethod = "POST")
    @PostMapping("/salary/getAllocatedBusinessBDHistory")
    Response<IPage<BusinessBDSalaryVo>> getAllocatedBusinessBDHistory(@Valid @RequestBody BusinessBDAllocatedRequest businessBDAllocatedRequest);

    @ApiOperation(value = "导出用人费用包业务BD已用额度", notes = "导出用人费用包业务BD已用额度（不分页）", httpMethod = "POST")
    @PostMapping("/salary/getAllocatedBusinessBDHistory/export")
    void exportAllocatedBusinessBDHistory(@Valid @RequestBody BusinessBDAllocatedRequest businessBDAllocatedRequest);



    @ApiOperation(value = "业务BD额度管控设置查询",notes = "业务BD额度管控设置查询", httpMethod = "POST")
    @PostMapping("/selectBusinessBDControl")
    Response<IPage<BusinessBDControlDetailVO>> selectBusinessBDControl(@RequestBody BusinessBDControlSearch businessBDControlSearch);

    @ApiOperation(value = "业务BD额度管控设置导出",notes = "业务BD额度管控设置导出", httpMethod = "POST")
    @PostMapping("/exportBusinessBDControl")
    void exportBusinessBDControl(@RequestBody BusinessBDControlSearch businessBDControlSearch);

    @ApiOperation(value = "获取业务BD费用率修改记录",notes = "获取业务BD费用率修改记录", httpMethod = "POST")
    @PostMapping("/getSalaryPackageLog")
    Response<List<SalaryPackageRateLogVO>> getSalaryPackageLog(@ApiParam("营业所ID") @RequestParam("departmentId") String departmentId);

    @ApiOperation(value = "导入业务BD费用率",notes = "导入业务BD费用率", httpMethod = "POST")
    @PostMapping("/importSalaryPackageRatio")
    Response importSalaryPackageRatio( @RequestParam(value = "file", required = true) MultipartFile file,
            @RequestParam(value = "employeeId", required = true) String employeeId);

    @ApiOperation(value = "设置业务BD费用率",notes = "设置业务BD费用率", httpMethod = "POST")
    @PostMapping("/setBusinessBDSalaryPackageConfig")
    Response setBusinessBDSalaryPackageConfig(@RequestBody @Valid BusinessBDSalaryPackageConfig businessBDSalaryPackageConfig);

}
