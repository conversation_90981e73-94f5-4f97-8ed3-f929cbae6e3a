package com.wantwant.sfa.backend.interview.request;


import com.wantwant.sfa.backend.interview.dto.MemberExperienceDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 面试操作用Request。 @Auther: zhengxu @Date: 2021/11/06/下午12:25
 */
@Data
@ToString
public class InterviewOperateRequest {
    @NotNull
    @ApiModelProperty(value = "申请ID")
    private Integer applicationId;

    @NotNull
    @ApiModelProperty("过程ID")
    private Integer interviewProcessRecordId;

    @NotBlank
    @ApiModelProperty("操作人ID")
    private String processUserId;

    @ApiModelProperty("操作人姓名")
    private String processUserName;

    @ApiModelProperty(value = "应聘岗位：1:造旺合伙人,2:造旺区域总监,3:战区督导,4.区域经理人,5.大区总监,6.省区总监,7:业务BD")
    private Integer position;

    @ApiModelProperty(value = "主岗信息")
    private String wwPosition;

    @ApiModelProperty(value = "1:全职 2:兼职")
    private Integer jobsType;

    @ApiModelProperty(value = "报名来源")
    private Integer source;

    @ApiModelProperty(value = "员工姓名")
    private String userName;

    @ApiModelProperty(value = "员工性别", allowableValues = "0.未知 1.男 2.女")
    private Integer gender;

    @ApiModelProperty(value = "手机号")
    private String userMobile;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区")
    private String district;

    @ApiModelProperty(value = "推荐人工号")
    private String superiorEmployId;

    @ApiModelProperty(value = "推荐人名称")
    private String superiorName;

    @ApiModelProperty(value = "推荐人手机号")
    private String superiorMobile;

    @ApiModelProperty(value = "个人介绍")
    private String remark;

    @ApiModelProperty(value = "员工工号")
    private String employeeId;

    @ApiModelProperty(value = "是否旺旺员工(1.旺旺在职 0.第三方兼职)")
    private Integer ceoFlag;

    @ApiModelProperty(value = "主岗")
    private String department;

    @ApiModelProperty(value = "战区CODE")
    private String areaCode;

    @ApiModelProperty(value = "省区CODE")
    private String provinceCode;

    @ApiModelProperty(value = "大区CODE")
    private String vareaCode;

    @ApiModelProperty(value = "分公司CODE")
    private String companyCode;

    @ApiModelProperty(value = "营业所CODE")
    private String branchCode;

    @ApiModelProperty(value = "已选四级地CODE")
    private List<String> regionCodes;

    @ApiModelProperty(value = "面试结果记录(1.通过 2.不通过,3.待定(暂存),4.关闭)")
    private Integer processResult;

    @ApiModelProperty(value = "原因")
    private List<Integer> reasons;

    @ApiModelProperty(value = "面试记录(入职意见)(离职意见)")
    private String comment;

    @ApiModelProperty(value = "建议底薪")
    private Integer adviceSalary;

    @ApiModelProperty("建议入职时间")
    private String recommendOnboardTime;

    @ApiModelProperty("毕业时间")
    private String graduationDate;
    @ApiModelProperty("毕业学校")
    private String graduateSchool;
    @ApiModelProperty("所学专业")
    private String specializing;
    @ApiModelProperty("当前状态:0.未知 1.在职 2.离职")
    private Integer currentStatus;

    @ApiModelProperty(value = "工作经历")
    List<MemberExperienceDTO> experiences;

    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    @ApiModelProperty(value = "最高学历")
    private String highestEducation;

    @ApiModelProperty(value = "居住年限")
    private String residenceYears;

    @ApiModelProperty(value = "当前身份")
    private String identity;

    @ApiModelProperty(value = "销售经验年限")
    private String salesYears;

    @ApiModelProperty(value = "所在行业")
    private String industry;

    @ApiModelProperty(value = "食品饮料经验(1:有,2:无)")
    private String foodExperience;

    @ApiModelProperty(value = "客户资源")
    private String customerResources;

    @ApiModelProperty(value = "管理经验年限")
    private String managementExperienceYears;

    @ApiModelProperty(value = "特殊渠道资源")
    private String specialChannelResources;

    @ApiModelProperty(value = "期望薪资")
    private String salaryExpectation;

    @ApiModelProperty(value = "可选复试时间")
    private String optionalRetestTime;

    @ApiModelProperty(value = "态度意向评分")
    private String attitudeIntentionScore;

    @ApiModelProperty(value = "经验与能力评分")
    private String experienceAbilityScore;

    @ApiModelProperty(value = "客户资源评分")
    private String customerResourcesScore;

    @ApiModelProperty(value = "入职日期(yyyy-MM-dd)")
    private String onboardTime;

    @ApiModelProperty(value = "离职日期(yyyy-MM-dd)")
    private Date offTime;

    @ApiModelProperty(value = "入职公司")
    private String joiningCompany;

    @ApiModelProperty(value = "保存类型:1.面试办理；2.离职办理")
    private Integer cacheType;

    @ApiModelProperty(value = "身份证正面照片")
    private String idCardFrontUrl;

    @ApiModelProperty(value = "身份证反面照片")
    private String idCardBackUrl;

    @ApiModelProperty(value = "手持身份证照片")
    private String holdCardUrl;

    @ApiModelProperty("头像url")
    private String picUrl;

    @ApiModelProperty("生日")
    private String birthDate;

    @ApiModelProperty("关联对象")
    private List<Integer> contextEmpIdList;

    @ApiModelProperty("支付方式(1.现金 2.旺金币)")
    private Integer paymentType;

    /**
     * 市场调查报告地址
     */
    @ApiModelProperty(value = "市场调查报告地址")
    private List<MarketResearchReportModel> marketResearchReportUrl;



    @ApiModelProperty(value = "招聘需求ID")
    private Integer recruitmentId;

    @ApiModelProperty(value = "待定原因")
    private String pendingReason;

    @ApiModelProperty(value = "待定类型：0其它 1未联系上 2面试主管考虑中 3应聘者考虑中 4市场走访中")
    private Integer pendingReasonType;

    @ApiModelProperty(value = "企业所在合伙人的公司名称")
    private String partnerCompanyName;

    @ApiModelProperty(value = "已选的小标市场code")
    List<String> smallMarketCodes;
    @ApiModelProperty(value = "产品组信息")
    List<String> productionGroup;
    /**  企业合伙人相关信息  start */
    @ApiModelProperty(value = "合伙人类型:1.全职合伙人 2.企业 3.承揽 4.兼职合伙人 7.全职业务BD 8.兼职业务BD 9.承揽业务BD")
    private Integer ceoType;
    @ApiModelProperty(value = "营业执照编号")
    private String businessLicense;
    @ApiModelProperty(value = "营业执照照片")
    private String businessLicensePhoto;
    @ApiModelProperty(value = "团队人数")
    private String teamCount;
    @ApiModelProperty(value = "机动车数量")
    private String motorCount;
    @ApiModelProperty(value = "详细地址")
    private String street;
    @ApiModelProperty(value = "仓库地址")
    private String warehouse;
    @ApiModelProperty(value = "月流水 (万元)")
    private String bankStatements;
    @ApiModelProperty(value = "终端网点数")
    private String terminalCount;
    @ApiModelProperty(value = "进销存系统")
    private String invoicingSys;
    /**  企业合伙人相关信息  end️️ */

    @ApiModelProperty(value = "薪资方案ID")
    private Integer salaryStructureId;

    @ApiModelProperty(value = "评分")
    private BigDecimal score;
    @ApiModelProperty(value = "评分评价")
    private String scoreComment;
    @ApiModelProperty(value = "是否允许重复:0.否 1.是")
    private Integer allowDuplicate = 0;

    @ApiModelProperty(value = "经销渠道(1.流通 2.商超 3.特通）")
    private List<Integer> marketChannel;

    @ApiModelProperty(value = "可销sku")
    private List<String> sku;

    private String contractCompany;

    @ApiModelProperty(value = "工作经验")
    private String workExperience;
    @ApiModelProperty(value = "综合能力")
    private String comprehensiveAbility;
    @ApiModelProperty(value = "简历来源")
    private String resumeSource;

    @ApiModelProperty(value = "是否拥有营业执照:1.是 0.否")
    private Integer licenseOwner;
    @ApiModelProperty(value = "是否有亲属在集团内任职:1.是 0.否")
    private Integer relativeInCompany;
    @ApiModelProperty(value="集团内亲属关系")
    private List<GroupRelationShipRequest> groupRelationShipRequests;

    @ApiModelProperty(value = "工作地点")
    private String workPlace;

    @ApiModelProperty(value = "工作模式:1.访单 2.车销")
    private Integer workMode;
}
