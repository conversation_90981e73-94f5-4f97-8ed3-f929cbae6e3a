package com.wantwant.sfa.backend.map.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@ApiModel("地图打卡列表返回")
public class MapDetailListVo {

    @ApiModelProperty(value = "序号-用来计算标记最远距离", hidden = true)
    private Integer indexTag;

    @ApiModelProperty("序号")
    private Integer serialNumber;

    @ApiModelProperty("是否下班")
    private Boolean isOffWork;

    @ApiModelProperty("距上一点距离")
    private double prePointDistance;

    @ApiModelProperty("此点周围1km内总点数")
    private Integer totalPoint;

    @ApiModelProperty("打卡类型: map_attendance_type")
    private Integer attendanceType;

    @ApiModelProperty("打卡子类型: map_attendance_child_type")
    private Integer attendanceChildType;

    @ApiModelProperty("打卡日期")
    private LocalDate attendanceDate;

    @ApiModelProperty("打卡时间(HH:mm)")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private LocalTime time;

    @ApiModelProperty("打卡名称")
    private String attendanceName;

    @ApiModelProperty("打卡时间")
    private LocalDateTime attendanceTime;

    @ApiModelProperty(value = "排序用的时间", hidden = true)
    private LocalDateTime attendanceTimeSort;

    private String address;

    private String longitude;

    private String latitude;

    private String image;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("姓名")
    private String employeeName;

    @ApiModelProperty(value = "员工表id")
    private Integer employeeInfoId;

    @ApiModelProperty("考勤打卡状态名称")
    private String attendanceStatusName;
    @ApiModelProperty("考勤打卡异常类型名称")
    private String attendanceExecptionTypeName;
    @ApiModelProperty("考勤稽核状态名称")
    private String attendanceAuditStatusName;
    @ApiModelProperty("考勤稽核原因名称")
    private String attendanceAuditReasonName;
    @ApiModelProperty("是否第一次上班打卡点")
    private Boolean isFirstOnWork;
    @ApiModelProperty("距昨日第一次上班打卡点距离")
    private double preFirstOnWorkPointDistance;

    @ApiModelProperty("当日距上班点最远距离点")
    private String farthestDistanceName;

    @ApiModelProperty(value = "0：拜访打卡 1：上班打卡 2：下班打卡 3：通关打卡")
    private Integer originalAttendanceType;



    @ApiModelProperty("通关编号")
    private String completeNum;
    @ApiModelProperty("通关状态名称")
    private String completeStatusName;
    @ApiModelProperty("通关稽核状态名称")
    private String completeAuditStatusName;
    @ApiModelProperty("通关稽核异常原因")
    private String completeReason;
    @ApiModelProperty("通关时间段")
    private String completeTimePeriod;


    @ApiModelProperty("拜访客户名称")
    private String visitCustomerName;
    @ApiModelProperty("拜访客户类型名称")
    private String visitCustomerTypeName;
    @ApiModelProperty("拜访稽核状态名称")
    private String visitAuditStatusName;
    @ApiModelProperty("拜访稽核异常原因")
    private String visitReason;
    @ApiModelProperty("拜访时间段")
    private String visitTimePeriod;
    @ApiModelProperty("拜访时长")
    private String visitTimeDuration;
    @ApiModelProperty("拜访时长-分钟")
    private long visitTimeDurationMinutes;
    @ApiModelProperty(value = "客户类型：0-批发、1-终端、2-合伙人", hidden = true)
    private Integer customerType;
    @ApiModelProperty(value = "开户类型：0 - 意向客户、1 - 潜在客户、2-陈列客户", hidden = true)
    private Integer openType;


    @ApiModelProperty("会议状态（已召开/未召开/无需召开）")
    private String meetingNeedConveneTypeName;
    @ApiModelProperty("会议签到状态")
    private String meetingCheckStatusName;
    @ApiModelProperty("会议类型")
    private String meetingCategory;
    @ApiModelProperty("会议时间段")
    private String meetingTimePeriod;
    @ApiModelProperty("会议间隔分钟数")
    private long meetingIntervalMinutes;
    @ApiModelProperty("会议实际时间段")
    private String meetingActualTimePeriod;
    @ApiModelProperty("会议实际间隔分钟数")
    private long meetingActualIntervalMinutes;
    @ApiModelProperty("会议方式")
    private String meetingMode;
    @ApiModelProperty(value = "业务组id")
    private Integer meetingBusinessGroup;
    @ApiModelProperty(value = "业务组")
    private String meetingBusinessGroupName;
    @ApiModelProperty("组织id")
    private String meetingOrganizationId;
    @ApiModelProperty("组织")
    private String meetingOrganizationName;
    @ApiModelProperty("能否跳转")
    private Boolean meetingCanLink;
    @ApiModelProperty("参会率")
    private String meetingParticipationRate;

    @ApiModelProperty("是否最新动态定位点")
    private Boolean isNewestRealtimePositioning;

    @ApiModelProperty("是否当日距上班点最远距离点")
    private Boolean isFarthestDistance;

    @ApiModelProperty("当日距上班点最远距离")
    private double farthestDistance;
}
