package com.wantwant.sfa.backend.interview.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.interview.dto.MemberExperienceDTO;
import com.wantwant.sfa.backend.interview.request.GroupRelationShipRequest;
import com.wantwant.sfa.backend.interview.request.MarketResearchReportModel;
import com.wantwant.sfa.backend.market.vo.MarketTreeV2Vo;
import com.wantwant.sfa.backend.market.vo.SignupMarketVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @Description: 面试人员信息用VO。 @Auther: zhengxu @Date: 2021/11/05/下午7:40
 */
@Data
public class InterviewEmployeeInfoVo {
    @ApiModelProperty(value = "申请ID")
    private Integer applicationId;

    @ApiModelProperty("过程ID")
    private Integer interviewProcessRecordId;

    @ApiModelProperty(value = "应聘岗位:1.造旺合伙人 2.总监 3.大区 4.区域经理人 7.业务BD")
    private Integer position;

    @ApiModelProperty(value = "1:全职 2:兼职")
    private Integer jobsType;

    @ApiModelProperty(value = "报名来源")
    private Integer source;

    @ApiModelProperty(value = "员工姓名")
    private String userName;

    @ApiModelProperty(value = "员工性别", allowableValues = "0.未知 1.男 2.女")
    private Integer gender;

    @ApiModelProperty(value = "手机号")
    private String userMobile;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区")
    private String district;

    @ApiModelProperty(value = "推荐公司名称")
    private String superiorCompany;

    @ApiModelProperty(value = "推荐人工号")
    private String superiorEmployId;

    @ApiModelProperty(value = "推荐人名称")
    private String superiorName;

    @ApiModelProperty(value = "推荐人手机号")
    private String superiorMobile;

    @ApiModelProperty(value = "个人介绍")
    private String remark;

    @ApiModelProperty(value = "面试时间")
    private String applyTime;

    @ApiModelProperty(value = "员工工号")
    private String employeeId;

    @ApiModelProperty(value = "是否旺旺员工(1.旺旺在职 0.第三方兼职)")
    private Integer ceoFlag;

    @ApiModelProperty(value = "主岗")
    private String wwPosition;

    @ApiModelProperty(value = "战区CODE")
    private String areaCode;

    @ApiModelProperty(value = "大区名称")
    private String areaName;

    @ApiModelProperty(value = "大区CODE")
    private String vareaCode;

    @ApiModelProperty(value = "大区名称")
    private String vareaName;

    @ApiModelProperty(value = "省区CODE")
    private String provinceCode;

    @ApiModelProperty(value = "省区名称")
    private String provinceName;

    @ApiModelProperty(value = "分公司CODE")
    private String companyCode;

    @ApiModelProperty(value = "营业所CODE")
    private String branchCode;


    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    @ApiModelProperty(value = "营业所名称")
    private String branchName;

    //  @ApiModelProperty(value = "小标市场树")
//  List<SignupMarketVo> smallMarketTree;
//
    @ApiModelProperty(value = "已选的小标市场code")
    List<Integer> smallMarketCodes;
    @ApiModelProperty(value = "已选择的四级地CODE")
    private List<String> regionCodes;

    @ApiModelProperty(value = "已选小标市场名称")
    private String regionNames;


    @ApiModelProperty(value = "可选原因")
    List<InterviewReasonVo> reasonVOList;

    @ApiModelProperty(value = "以选原因")
    List<Integer> reasons;

    @ApiModelProperty(value = "面试结果记录(1.通过 2.不通过)")
    private Integer processResult;

    @ApiModelProperty(value = "原因")
    private Integer reason;

    @ApiModelProperty(value = "面试记录")
    private String comment;

    @ApiModelProperty(value = "建议入职日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime recommendOnboardTime;

    @ApiModelProperty(value = "入职公司")
    private String joiningCompany;

    @ApiModelProperty(value = "实际入职公司")
    private String actualJoiningCompany;

    @ApiModelProperty(value = "面试历史记录")
    List<InterviewProcessRecordVo> interviewProcessRecordVoList;

    @ApiModelProperty(value = "工作经历")
    List<MemberExperienceDTO> experiences;

    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    @ApiModelProperty(value = "简历")
    private String resumeUrl;

    @ApiModelProperty(value = "企业合伙人所在公司名称")
    private String partnerCompanyName;


    @ApiModelProperty(value = "出生日期")
    // @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private String birthDate;

    @ApiModelProperty(value = "最高学历")
    private String highestEducation;

    @ApiModelProperty(value = "居住年限")
    private String residenceYears;

    @ApiModelProperty(value = "当前身份")
    private String identity;

    @ApiModelProperty(value = "销售经验年限")
    private String salesYears;

    @ApiModelProperty(value = "所在行业")
    private String industry;

    @ApiModelProperty(value = "食品饮料经验(1:有,2:无)")
    private String foodExperience;

    @ApiModelProperty(value = "客户资源")
    private String customerResources;

    @ApiModelProperty(value = "管理经验年限")
    private String managementExperienceYears;

    @ApiModelProperty(value = "特殊渠道资源")
    private String specialChannelResources;

    @ApiModelProperty(value = "期望薪资")
    private String salaryExpectation;

    @ApiModelProperty(value = "复试时间")
    private String retestTime;

    @ApiModelProperty(value = "可选复试时间")
    private String optionalRetestTime;

    @ApiModelProperty(value = "入职日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd,timezone = "GMT+8")
    private Date onboardTime;

    @ApiModelProperty(value = "离职日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd,timezone = "GMT+8")
    private Date offTime;

    @ApiModelProperty("建议离职日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd,timezone = "GMT+8")
    private LocalDate adviceDepartureTime;

    @ApiModelProperty(value = "个人头像")
    private String picUrl;

    @ApiModelProperty("实名认证-身份证正面")
    private String idCardFrontUrl;

    @ApiModelProperty("实名认证-身份证反面")
    private String idCardBackUrl;

    @ApiModelProperty("实名认证-手持身份证")
    private String holdCardUrl;

    @ApiModelProperty(value = "建议(标准)底薪")
    private Integer adviceSalary;

    @ApiModelProperty(value = "底薪参考线(区域总监)")
    Integer salaryReferenceLine;

    @ApiModelProperty(value = "底薪默认值(区域总监)")
    Integer salaryDefaultValue;

    @ApiModelProperty(value = "态度意向评分")
    private String attitudeIntentionScore;

    @ApiModelProperty(value = "经验与能力评分")
    private String experienceAbilityScore;

    @ApiModelProperty(value = "客户资源评分")
    private String customerResourcesScore;

    /**
     * 市场调查报告地址
     */
    @ApiModelProperty(value = "市场调查报告地址")
    private List<MarketResearchReportModel> marketResearchReportUrl;

    @ApiModelProperty(value = "负责市场")
    private String ResponsibleMarket;

    @ApiModelProperty(value = "面试记录提示")
    private String msg;

    @ApiModelProperty(value = "是否显示薪资信息")
    private boolean showSalaryInfo;

    @ApiModelProperty(value = "招聘需求信息")
    private String recruitmentTitle;

    @ApiModelProperty(value = "招聘需求ID")
    private Integer recruitmentId;

    @ApiModelProperty(value = "待定原因，只有process_result为3的时候才有")
    private String pendingReason;

    @ApiModelProperty(value = "待定类型：0其它 1未联系上 2面试主管考虑中 3应聘者考虑中 4市场走访中")
    private Integer pendingReasonType;


    @ApiModelProperty(value = "是否来自于推荐系统,1.是 0.否")
    private int ehrSource;

    @ApiModelProperty(value = "劳动合同")
    private String laborContract;

    @ApiModelProperty("产品组信息")
    private List<Integer> productionGroup;
    @ApiModelProperty(value = "合伙人类型:1.全职合伙人 2.企业 3.承揽 4.兼职合伙人")
    private Integer ceoType;
    @ApiModelProperty(value = "营业执照编号")
    private String businessLicense;
    @ApiModelProperty(value = "营业执照照片")
    private String businessLicensePhoto;
    @ApiModelProperty(value = "团队人数")
    private String teamCount;
    @ApiModelProperty(value = "机动车数量")
    private String motorCount;
    @ApiModelProperty(value = "详细地址")
    private String street;
    @ApiModelProperty(value = "仓库地址")
    private String warehouse;
    @ApiModelProperty(value = "月流水 (万元)")
    private String bankStatements;
    @ApiModelProperty(value = "终端网点数")
    private String terminalCount;
    @ApiModelProperty(value = "进销存系统")
    private String InvoicingSys;


    @ApiModelProperty(value = "毕业日期")
    private String graduationDate;
    @ApiModelProperty(value = "毕业学校")
    private String graduateSchool;
    @ApiModelProperty(value = "专业")
    private String specializing;
    @ApiModelProperty(value = "当前状态(0.未知 1.在职 2.离职)")
    private Integer currentStatus;

    @ApiModelProperty(value = "返聘次数")
    private int rehireCount;

    @ApiModelProperty(value = "招聘渠道意见")
    private String externalComment;

    @ApiModelProperty(value = "薪资结构ID")
    private Integer salaryStructureId;

    @ApiModelProperty(value = "评分")
    private BigDecimal score;
    @ApiModelProperty(value = "评分评价")
    private String scoreComment;

    @ApiModelProperty(value = "是否是总部人资组")
    private boolean humanResourceFlag;

    @ApiModelProperty(value = "所属产品组")
    private List<String> businessGroupNames;

    @ApiModelProperty(value = "主岗描述")
    private String mainPositionDescription;

    @ApiModelProperty(value = "兼岗描述")
    private List<String> partTimePositionDescription;

    @ApiModelProperty(value = "经销渠道")
    private List<Integer>marketChannel;

    @ApiModelProperty("社保缴纳地-省")
    private String socialInsuranceProvince;
    @ApiModelProperty("社保缴纳地-市")
    private String socialInsuranceCity;
    @ApiModelProperty("社保缴纳地-区")
    private String socialInsuranceDistrict;

    @ApiModelProperty("关联对象")
    private List<Integer> contextEmpIdList;

    @ApiModelProperty("支付方式(1.现金 2.旺金币)")
    private Integer paymentType;

    @ApiModelProperty("是否拥有营业执照：1.是 0.否")
    private Integer licenseOwner;

    @ApiModelProperty("是否有亲属在集团内任职：1.是 0.否")
    private Integer relativeInCompany;

    @ApiModelProperty(value="集团内亲属关系")
    private List<GroupRelationShipRequest> groupRelationShipRequests;

    @ApiModelProperty(value="办公地点")
    private String workPlace;

    @ApiModelProperty(value="办公地点名称")
    private String workPlaceName;

    @ApiModelProperty(value="延迟入职信息")
    private String delayOnBoardMessage;

    @ApiModelProperty(value="居住地址")
    private String residentialAddress;

    @ApiModelProperty(value="工作模式")
    private Integer workMode;
}
