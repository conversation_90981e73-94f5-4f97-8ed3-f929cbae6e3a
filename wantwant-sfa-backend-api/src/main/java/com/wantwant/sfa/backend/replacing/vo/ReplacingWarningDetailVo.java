package com.wantwant.sfa.backend.replacing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel(value = "汰换预警明细返回参数")
@Data
public class ReplacingWarningDetailVo {

  private String empId;

  private String positionId;

  private String picUrl;

  private Integer positionType;

  private String memberKey;

  @ApiModelProperty(value = "组织信息")
  private String organizationDetails;

  @ApiModelProperty(value = "业务员名称")
  private String employeeName;

  @ApiModelProperty(value = "岗位名称")
  private String positionName;

  @ApiModelProperty("入职日期")
  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
  private LocalDate entryDate;

  @ApiModelProperty("入岗日期")
  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
  private LocalDate entryPositionDate;

  @ApiModelProperty("入职天数")
  private Integer entryNum;

  @ApiModelProperty("月份盘价业绩")
  private String monthBiddingPerformance;

  @ApiModelProperty("月份业绩目标")
  private String monthPerformanceTarget;

  @ApiModelProperty("月份业绩目标达成率")
  private String monthPerformanceTargetRate;

  @ApiModelProperty("月份全国目标达成率")
  private String monthNationwideTargetRate;

  @ApiModelProperty("季度盘价业绩")
  private String quarterBiddingPerformance;

  @ApiModelProperty("季度业绩目标")
  private String quarterPerformanceTarget;

  @ApiModelProperty("季度业绩目标达成率")
  private String quarterPerformanceTargetRate;

  @ApiModelProperty("季度全国目标达成率")
  private String quarterNationwideTargetRate;

  @ApiModelProperty("下半年盘价业绩")
  private String secondHalfYearPerformance;

  @ApiModelProperty("下半年业绩目标")
  private String secondHalfYearPerformanceTarget;

  @ApiModelProperty("下半年业绩目标达成率")
  private String secondHalfYearPerformanceTargetRate;

  @ApiModelProperty("下半年全国目标达成率")
  private String secondHalfYearNationwideTargetRate;

  @ApiModelProperty("本月客户拜访")
  private String thisMonthCustomerVisit;

  /**
   * 本月
   */

  @ApiModelProperty("本月拜访达标率")
  private String thisMonthVisitingRate;

  @ApiModelProperty("本月出勤率")
  private String thisMonthAttendanceRate;

  /**
   * 季度
   */

  @ApiModelProperty("季度拜访达标率")
  private String quarterVisitingRate;

  @ApiModelProperty("季度出勤率")
  private String quarterAttendanceRate;

  /**
   * 半年
   */

  @ApiModelProperty("半年拜访达标率")
  private String secondHalfYearVisitingRate;

  @ApiModelProperty("半年出勤率")
  private String secondHalfYearAttendanceRate;

  @ApiModelProperty("周报填写率")
  private String weeklyFillingRate;

  @ApiModelProperty("月报填写率")
  private String monthlyFillingRate;

  @ApiModelProperty("特批次数")
  private String giveSpecialApprovalFrequency;

  @ApiModelProperty("总考核次数")
  private String totalNumberChecks;

  @ApiModelProperty(value = "考核区域(保护区/正常区/汰换区)")
  private String areaBelongs;

  @ApiModelProperty("考核周期报表")
  private List<ReplacingStatementDetailVo> weeklyStatementList;

  @ApiModelProperty("考核月期报表")
  private List<ReplacingStatementDetailVo> montthStatementList;

  @ApiModelProperty("审核人")
  private String examinePersonnel;

  @ApiModelProperty("审核时间")
  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
  private LocalDateTime examineTime;

  @ApiModelProperty("审核意见")
  private String examineIdea;

  @ApiModelProperty("审核结果")
  private String examineType;

  @ApiModelProperty(value = "是否可以审核(1：是；0：否)")
  private int isExamine;

  @ApiModelProperty(value = "特批明细")
  private List<ReplacingWarningSpecialApprovalVo> specialApprovalList;
}
