package com.wantwant.sfa.backend.salary.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: BD Quota Config VO（含过期额度）
 * @Author: zhengxu
 * @Date: 2023/10/08/下午5:25
 */
@Data
@ApiModel("BD Quota Config VO")
public class BusinessBDQuotaConfigVo {
    @ApiModelProperty("战区")
    @Excel(name = "战区")
    private String areaName;

    @ApiModelProperty("是否可操作")
    private boolean canProcess;

    @ApiModelProperty("大区")
    @Excel(name = "大区")
    private String vareaName;

    @ApiModelProperty("省区")
    @Excel(name = "省区")
    private String provinceName;

    @ApiModelProperty("分公司")
    @Excel(name = "分公司")
    private String companyName;

    @ApiModelProperty("当月结余额度")
    @Excel(name = "当月结余额度", numFormat = "#.#")
    private BigDecimal balanceAmountCm;

    @ApiModelProperty("累计结余额度")
    @Excel(name = "累计结余额度", numFormat = "#.#")
    private BigDecimal cumulativeBalanceAmount;

    @ApiModelProperty("管控额度")
    @Excel(name = "管控额度", numFormat = "#.#")
    private BigDecimal quota;

    @ApiModelProperty("已用额度")
    @Excel(name = "已用额度", numFormat = "#.#")
    private BigDecimal usedQuota;

    @ApiModelProperty("过期额度")
    @Excel(name = "过期额度", numFormat = "#.#")
    private BigDecimal expiredQuota;

    @ApiModelProperty("剩余额度")
    @Excel(name = "剩余额度", numFormat = "#.#")
    private BigDecimal surplus;

    @ApiModelProperty("当前组织")
    private String currentOrgCode;

    @ApiModelProperty("当前组织类型")
    private String currentOrgType;


    @ApiModelProperty("组织全路径")
    private List<String> orgPath;


}


