package com.wantwant.sfa.backend.arch.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.request.MenuModuleRequest;
import com.wantwant.sfa.backend.arch.vo.ModuleVo;
import com.wantwant.sfa.backend.arch.vo.ResourcesListVo;
import com.wantwant.sfa.backend.arch.vo.ResourcesVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/20/下午2:05
 */
@Api(value = "ResourcesAPI",tags = "资源API")
@RequestMapping("/resources")
public interface ResourcesApi {

    @GetMapping(value = {"/{terminal}","/{terminal}/{roleId}"})
    @ApiOperation(value = "获取资源",notes = "获取资源",httpMethod = "GET")
    Response<List<ResourcesVo>> getResources(@PathVariable Integer terminal,@PathVariable(required = false) Integer roleId);

    @GetMapping(value = {"/list"})
    @ApiOperation(value = "ai小助手-获取资源-只包含app和pc，且只包含一层菜单",notes = "获取资源",httpMethod = "GET")
    Response<List<ResourcesListVo>> getResourcesList();

    @PostMapping(value = "/module")
    @ApiOperation(value = "获取菜单模块",notes = "获取菜单模块",httpMethod = "POST")
    Response<List<ModuleVo>>getMenuModule(@RequestBody @Valid MenuModuleRequest request);

    @GetMapping(value = "/promptCount/{person}")
    @ApiOperation(value = "获取更新条数",notes = "获取更新条数",httpMethod = "GET")
    Response<Integer> getPromptCount(@PathVariable String person);
}
