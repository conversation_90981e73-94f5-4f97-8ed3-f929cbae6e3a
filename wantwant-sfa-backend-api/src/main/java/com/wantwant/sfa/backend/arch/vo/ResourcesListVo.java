package com.wantwant.sfa.backend.arch.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/20/下午2:02
 */
@Data
public class ResourcesListVo {
    @ApiModelProperty("资源名称")
    private String resourceName;
    @ApiModelProperty("资源ID")
    private String resourcesId;
    @ApiModelProperty("终端")
    private Integer terminal;
    @ApiModelProperty("是否隐藏(0.否 1.是)")
    private Integer hidden;
    @ApiModelProperty("关联菜单")
    private List<Integer> contextMenu;
    @ApiModelProperty("是否选中")
    private boolean checked;
    @ApiModelProperty("子集")
    private List<ResourcesListVo> children;

    @ApiModelProperty(hidden = true)
    private Integer id;
    @ApiModelProperty(hidden = true)
    private Integer parentId;
}
