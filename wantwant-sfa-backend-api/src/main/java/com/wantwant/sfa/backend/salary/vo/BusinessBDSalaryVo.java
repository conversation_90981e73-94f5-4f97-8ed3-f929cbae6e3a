package com.wantwant.sfa.backend.salary.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 业务BD薪资信息 VO
 */
@Data
@ApiModel("业务BD薪资信息")
public class BusinessBDSalaryVo {
    @ApiModelProperty("员工ID")
    private Integer employeeInfoId;

    @ApiModelProperty("战区")
    @Excel(name = "战区")
    private String areaName;

    @ApiModelProperty("大区")
    @Excel(name = "大区")
    private String vareaName;

    @ApiModelProperty("省区")
    @Excel(name = "省区")
    private String provinceName;

    @Excel(name = "分公司")
    @ApiModelProperty("分公司")
    private String companyName;

    @ApiModelProperty("营业所")
    @Excel(name = "营业所")
    private String departmentName;

    @Excel(name = "员工名称")
    @ApiModelProperty("员工名称")
    private String employeeName;

    @ApiModelProperty("岗位")
    @Excel(name = "岗位")
    private String position;

    @ApiModelProperty("状态")
    @Excel(name = "状态")
    private String employeeStatus;

    @ApiModelProperty("薪资等级")
    @Excel(name = "薪资等级")
    private String salaryLevel;

    @ApiModelProperty("底薪")
    @Excel(name = "底薪",numFormat = "#.##")
    private BigDecimal baseSalary;

    @ApiModelProperty("津贴")
    @Excel(name = "津贴",numFormat = "#.##")
    private BigDecimal allowance;


    @ApiModelProperty("企业承担社保公积金")
    @Excel(name = "企业承担社保公积金",numFormat = "#.##")
    private BigDecimal socialSecurityBase;

    @ApiModelProperty("绩效")
    @Excel(name = "绩效",numFormat = "#.##")
    private BigDecimal bonus;

    @ApiModelProperty("差旅费")
    @Excel(name = "差旅费",numFormat = "#.##")
    private BigDecimal travelExpenses;

    @ApiModelProperty("全风险服务费")
    @Excel(name = "全风险服务费",numFormat = "#.##")
    private BigDecimal fullRiskFee;

    @ApiModelProperty("小记")
    @Excel(name = "小记",numFormat = "#.##")
    private BigDecimal total;

    @ApiModelProperty("岗位类型ID:1总督导 12大区总监 2区域总监 11省区总监 10区域经理")
    private String positionTypeId;

}


