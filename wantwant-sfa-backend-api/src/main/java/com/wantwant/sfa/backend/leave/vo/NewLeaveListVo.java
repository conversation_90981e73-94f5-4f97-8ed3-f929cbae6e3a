package com.wantwant.sfa.backend.leave.vo;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("请假列表返回")
public class NewLeaveListVo {

    @ApiModelProperty("表单编号")
    @Excel(name = "表单编号",replace = {"0_null"})
    private String businessNum;

    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
    @Excel(name = "申请时间",replace = {"-_null"}, exportFormat = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private LocalDateTime submitTime;

    @ApiModelProperty("组织id")
    private String organizationId;

    @ApiModelProperty("组织名称")
    private String organizationName;

    @ApiModelProperty("战区名称")
    @Excel(name = "战区",replace = {"0_null"})
    private String areaName;

    @ApiModelProperty("大区名称")
    @Excel(name = "大区",replace = {"-_null"})
    private String vareaOrganizationName;

    @ApiModelProperty("省区名称")
    @Excel(name = "省区",replace = {"-_null"})
    private String provinceOrganizationName;

    @ApiModelProperty("分公司名称")
    @Excel(name = "分公司",replace = {"-_null"})
    private String companyName;

    @ApiModelProperty("营业所名称")
    @Excel(name = "营业所",replace = {"-_null"})
    private String departmentName;

    private Integer postTypeId;

    @ApiModelProperty("岗位")
    @Excel(name = "岗位",replace = {"0_null"})
    private String positionName;

    @ApiModelProperty("请假人姓名")
    @Excel(name = "请假人姓名",replace = {"0_null"})
    private String employeeName;

    @ApiModelProperty("请假人工号")
    @Excel(name = "工号",replace = {"0_null"})
    private String employeeId;

    @ApiModelProperty("请假人手机号码")
    @Excel(name = "手机号",replace = {"0_null"})
    private String mobile;

    @ApiModelProperty("请假人头像")
    private String avatar;

    @ApiModelProperty("请假类型：0代表事假")
    private int leaveType;

    @ApiModelProperty("请假类型")
    @Excel(name = "请假类型",replace = {"0_null"})
    private String leaveTypeName;

    @ApiModelProperty(value = "请假开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Excel(name = "开始时间",replace = {"-_null"}, exportFormat = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime leaveStartTime;

    @ApiModelProperty(value = "请假结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Excel(name = "结束时间",replace = {"-_null"}, exportFormat = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime leaveEndTime;

    @ApiModelProperty("请假时长")
    private int leaveHours;

    @ApiModelProperty("请假时长")
    @Excel(name = "请假时长(天数)",replace = {"0_null"})
    private double leaveDays;

    @ApiModelProperty("当前考勤周期累计请假时长")
    private int monthAlreadyLeaveHours;

    @ApiModelProperty("当前考勤周期累计请假时长")
    @Excel(name = "当月累计请假时长(天数)",replace = {"0_null"})
    private double monthAlreadyLeaveDays;

    @ApiModelProperty("爱旺旺请假单号")
    @Excel(name = "爱旺旺请假单号",replace = {"-_null"})
    private String wantLeaveNum;

    @ApiModelProperty("爱旺旺请假截图")
    @Excel(name = "爱旺旺请假截图",replace = {"-_null"})
    private String wantImage;
    private List<String> wantImages;

    @ApiModelProperty("请假原因")
    @Excel(name = "请假原因",replace = {"0_null"})
    private String leaveReason;

    @ApiModelProperty("状态名称")
    @Excel(name = "状态",replace = {"0_null"})
    private String leaveStatusName;

    @ApiModelProperty("状态")
    private Integer leaveStatus;

    @ApiModelProperty("附件")
    private String appendix;

    @ApiModelProperty("附件")
    private String appendixName;

    @ApiModelProperty("图片")
    private String image;

    @ApiModelProperty("图片")
    private String imageName;


    private Integer applyEmployeeInfoId;

    private Integer auditEmployeeInfoId;

    //审批按钮权限
    @ApiModelProperty(value = "通过按钮是否高亮")
    private boolean bShowBtn1 = false;
    @ApiModelProperty(value = "驳回按钮是否高亮")
    private boolean bShowBtn2 = false;
    @ApiModelProperty(value = "撤回按钮是否高亮")
    private boolean bShowBtn3 = false;

    private String companyCode;

}
