#!/bin/bash
# 团队Cursor使用情况分析脚本

echo "🚀 团队Cursor使用情况分析"
echo "========================"

# 设置变量
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
OUTPUT_DIR="reports_${TIMESTAMP}"
COMPARISON_REPORT="team_comparison_${TIMESTAMP}.txt"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

echo "📊 开始批量分析..."
echo "📁 输出目录: $OUTPUT_DIR"
echo "📄 对比报告: $COMPARISON_REPORT"
echo ""

# 运行批量分析
python3 batch_analysis.py \
    --users-file users.txt \
    --output "$COMPARISON_REPORT" \
    --output-dir "$OUTPUT_DIR" \
    --save-individual

# 检查结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 分析完成!"
    echo "📊 对比报告: $COMPARISON_REPORT"
    echo "📁 详细报告目录: $OUTPUT_DIR"
    echo ""
    echo "📈 快速查看对比报告:"
    echo "head -50 $COMPARISON_REPORT"
    echo ""
    echo "📋 查看所有生成的文件:"
    echo "ls -la $OUTPUT_DIR/"
else
    echo ""
    echo "❌ 分析失败，请检查错误信息"
fi
