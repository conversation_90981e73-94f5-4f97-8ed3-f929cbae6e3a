#!/usr/bin/env python3
"""
批量分析脚本
支持同时分析多个用户的Cursor使用情况
"""

import os
import sys
from datetime import datetime
from typing import List, Dict
import argparse


class BatchAnalyzer:
    """批量分析器"""
    
    def __init__(self, repo_path: str = "."):
        self.repo_path = repo_path
        self.results = {}
        
    def analyze_user(self, username: str) -> Dict:
        """分析单个用户"""
        print(f"\n📊 正在分析用户: {username}")
        print("-" * 40)
        
        try:
            # 直接调用分析器
            from cursor_git_stats import CursorGitStatsAnalyzer
            from cursor_chat_analyzer import CursorChatAnalyzer

            # 运行Git分析
            git_analyzer = CursorGitStatsAnalyzer(self.repo_path, username)
            git_report = git_analyzer.run_analysis()

            # 提取Git统计数据
            git_stats = {}
            lines = git_report.split('\n')
            for line in lines:
                line = line.strip()
                if '总提交数:' in line:
                    git_stats['git_commits'] = int(line.split(':')[1].strip())
                elif '总添加行数:' in line:
                    git_stats['git_lines_added'] = int(line.split(':')[1].strip())
                elif '净增行数:' in line:
                    git_stats['git_net_lines'] = int(line.split(':')[1].strip().replace('+', ''))

            # 运行Cursor聊天分析
            cursor_analyzer = CursorChatAnalyzer(username)
            cursor_report = cursor_analyzer.run_analysis()

            cursor_stats = {
                'cursor_conversations': cursor_analyzer.conversations,
                'cursor_lines': cursor_analyzer.total_lines
            }
            
            # 提取关键指标
            result = {
                'username': username,
                'git_commits': git_stats.get('git_commits', 0),
                'git_lines_added': git_stats.get('git_lines_added', 0),
                'git_net_lines': git_stats.get('git_net_lines', 0),
                'cursor_conversations': cursor_stats.get('cursor_conversations', 0),
                'cursor_lines': cursor_stats.get('cursor_lines', 0),
                'adoption_rate': 0,
                'efficiency_score': 0
            }
            
            # 计算采纳率和效率分数
            if result['cursor_lines'] > 0:
                result['adoption_rate'] = min(100, (result['git_lines_added'] / result['cursor_lines']) * 100)
            
            if result['git_commits'] > 0:
                result['efficiency_score'] = result['cursor_lines'] / result['git_commits']
            
            print(f"✅ {username} 分析完成")
            print(f"   Git提交: {result['git_commits']}次")
            print(f"   Cursor对话: {result['cursor_conversations']}次")
            print(f"   采纳率: {result['adoption_rate']:.1f}%")
            
            return result
            
        except Exception as e:
            print(f"❌ {username} 分析失败: {e}")
            return {
                'username': username,
                'error': str(e),
                'git_commits': 0,
                'git_lines_added': 0,
                'git_net_lines': 0,
                'cursor_conversations': 0,
                'cursor_lines': 0,
                'adoption_rate': 0,
                'efficiency_score': 0
            }
    
    def analyze_users(self, usernames: List[str]) -> Dict:
        """批量分析多个用户"""
        print(f"🚀 开始批量分析 {len(usernames)} 个用户")
        print("=" * 50)
        
        results = {}
        
        for username in usernames:
            results[username] = self.analyze_user(username)
        
        return results
    
    def generate_comparison_report(self, results: Dict) -> str:
        """生成对比报告"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 过滤出成功的结果
        valid_results = {k: v for k, v in results.items() if 'error' not in v}
        
        if not valid_results:
            return "没有成功分析的用户数据"
        
        # 排序（按Git提交数排序）
        sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['git_commits'], reverse=True)
        
        report = f"""
团队Cursor使用情况对比报告
========================

生成时间: {timestamp}
分析用户数: {len(valid_results)}
统计周期: 上周一到今天

📊 团队排行榜:
=============

按Git提交数排序:
--------------
"""
        
        for i, (username, data) in enumerate(sorted_results, 1):
            report += f"""
{i}. {username}
   Git提交: {data['git_commits']}次
   添加代码: {data['git_lines_added']}行
   净增代码: {data['git_net_lines']:+d}行
   Cursor对话: {data['cursor_conversations']}次
   Cursor生成: {data['cursor_lines']}行
   采纳率: {data['adoption_rate']:.1f}%
   效率分数: {data['efficiency_score']:.1f}行/提交
"""
        
        # 团队统计
        total_commits = sum(d['git_commits'] for d in valid_results.values())
        total_git_lines = sum(d['git_lines_added'] for d in valid_results.values())
        total_cursor_lines = sum(d['cursor_lines'] for d in valid_results.values())
        total_conversations = sum(d['cursor_conversations'] for d in valid_results.values())
        
        avg_adoption_rate = sum(d['adoption_rate'] for d in valid_results.values()) / len(valid_results)
        
        report += f"""

📈 团队总体统计:
===============
总Git提交数: {total_commits}
总添加代码行数: {total_git_lines}
总Cursor对话数: {total_conversations}
总Cursor生成行数: {total_cursor_lines}
团队平均采纳率: {avg_adoption_rate:.1f}%
团队AI使用率: {(total_cursor_lines / total_git_lines * 100) if total_git_lines > 0 else 0:.1f}%

🏆 最佳表现:
===========
"""
        
        # 找出各项最佳
        if valid_results:
            most_commits = max(valid_results.items(), key=lambda x: x[1]['git_commits'])
            most_cursor_usage = max(valid_results.items(), key=lambda x: x[1]['cursor_lines'])
            best_adoption = max(valid_results.items(), key=lambda x: x[1]['adoption_rate'])
            most_efficient = max(valid_results.items(), key=lambda x: x[1]['efficiency_score'])
            
            report += f"""
最多提交: {most_commits[0]} ({most_commits[1]['git_commits']}次)
最多AI使用: {most_cursor_usage[0]} ({most_cursor_usage[1]['cursor_lines']}行)
最高采纳率: {best_adoption[0]} ({best_adoption[1]['adoption_rate']:.1f}%)
最高效率: {most_efficient[0]} ({most_efficient[1]['efficiency_score']:.1f}行/提交)
"""
        
        # 添加失败的分析
        failed_results = {k: v for k, v in results.items() if 'error' in v}
        if failed_results:
            report += f"""

❌ 分析失败的用户:
================
"""
            for username, data in failed_results.items():
                report += f"{username}: {data['error']}\n"
        
        return report
    
    def save_individual_reports(self, results: Dict, output_dir: str = ".") -> None:
        """保存个人详细报告"""
        print(f"\n📁 保存个人详细报告到: {output_dir}")
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        for username, data in results.items():
            if 'error' not in data:
                try:
                    # 生成个人详细报告
                    from cursor_git_stats import CursorGitStatsAnalyzer
                    from cursor_chat_analyzer import CursorChatAnalyzer

                    git_analyzer = CursorGitStatsAnalyzer(self.repo_path, username)
                    git_report = git_analyzer.run_analysis()

                    cursor_analyzer = CursorChatAnalyzer(username)
                    cursor_report = cursor_analyzer.run_analysis()

                    # 组合详细报告
                    detailed_report = f"""
{username}的Cursor使用情况详细报告
{'=' * (len(username) + 20)}

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Git提交记录分析:
{'=' * 60}
{git_report}

{'=' * 60}

Cursor聊天记录分析:
{'=' * 60}
{cursor_report}
"""
                    
                    # 保存到文件
                    filename = os.path.join(output_dir, f"{username}_detailed_{timestamp}.txt")
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(detailed_report)
                    
                    print(f"   ✅ {username}: {filename}")
                    
                except Exception as e:
                    print(f"   ❌ {username}: 保存失败 - {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量分析多个用户的Cursor使用情况')
    parser.add_argument('--users', type=str, nargs='+', help='用户名列表')
    parser.add_argument('--users-file', type=str, help='包含用户名的文件（每行一个）')
    parser.add_argument('--repo-path', type=str, default='.', help='Git仓库路径')
    parser.add_argument('--output', type=str, help='对比报告输出文件')
    parser.add_argument('--output-dir', type=str, default='.', help='个人详细报告输出目录')
    parser.add_argument('--save-individual', action='store_true', help='保存个人详细报告')
    
    args = parser.parse_args()
    
    # 获取用户列表
    users = []
    
    if args.users:
        users.extend(args.users)
    
    if args.users_file and os.path.exists(args.users_file):
        with open(args.users_file, 'r', encoding='utf-8') as f:
            file_users = [line.strip() for line in f if line.strip()]
            users.extend(file_users)
    
    if not users:
        print("❌ 请提供用户列表")
        print("使用方法:")
        print("  python3 batch_analysis.py --users zhengxu zhanghaijun panghuidong")
        print("  python3 batch_analysis.py --users-file users.txt")
        return 1
    
    print(f"🎯 批量分析Cursor使用情况")
    print(f"📁 仓库路径: {os.path.abspath(args.repo_path)}")
    print(f"👥 分析用户: {', '.join(users)}")
    
    # 运行批量分析
    analyzer = BatchAnalyzer(args.repo_path)
    results = analyzer.analyze_users(users)
    
    # 生成对比报告
    print(f"\n📊 生成对比报告...")
    comparison_report = analyzer.generate_comparison_report(results)
    
    # 输出对比报告
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(comparison_report)
        print(f"✅ 对比报告已保存到: {args.output}")
    else:
        print("\n" + "="*60)
        print(comparison_report)
    
    # 保存个人详细报告
    if args.save_individual:
        analyzer.save_individual_reports(results, args.output_dir)
    
    print(f"\n🎉 批量分析完成!")
    return 0


if __name__ == "__main__":
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        exit(1)
