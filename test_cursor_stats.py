#!/usr/bin/env python3
"""
Cursor统计工具测试脚本
用于验证工具是否正常工作
"""

import os
import sys
import subprocess
from datetime import datetime, timedelta


def test_git_availability():
    """测试Git是否可用"""
    try:
        result = subprocess.run(['git', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Git可用: {result.stdout.strip()}")
            return True
        else:
            print("✗ Git不可用")
            return False
    except FileNotFoundError:
        print("✗ Git未安装")
        return False


def test_git_repo():
    """测试当前目录是否是Git仓库"""
    if os.path.exists('.git'):
        print("✓ 当前目录是Git仓库")
        return True
    else:
        print("✗ 当前目录不是Git仓库")
        return False


def test_git_commits():
    """测试是否有Git提交记录"""
    try:
        # 获取上周一到今天的时间范围
        today = datetime.now()
        days_since_monday = today.weekday()
        days_to_last_monday = days_since_monday + 7
        last_monday = today - timedelta(days=days_to_last_monday)
        
        since = last_monday.strftime('%Y-%m-%d')
        until = today.strftime('%Y-%m-%d')
        
        result = subprocess.run([
            'git', 'log', 
            f'--since={since}', 
            f'--until={until}', 
            '--oneline'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            commits = result.stdout.strip().split('\n') if result.stdout.strip() else []
            commit_count = len([c for c in commits if c.strip()])
            print(f"✓ 找到 {commit_count} 个提交记录 (时间范围: {since} 到 {until})")
            return commit_count > 0
        else:
            print("✗ 无法获取Git提交记录")
            return False
    except Exception as e:
        print(f"✗ 检查Git提交时出错: {e}")
        return False


def test_python_modules():
    """测试Python模块是否可用"""
    required_modules = ['os', 'subprocess', 'datetime', 'sqlite3', 'json', 're']
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ Python模块 {module} 可用")
        except ImportError:
            print(f"✗ Python模块 {module} 不可用")
            return False
    
    return True


def test_cursor_data_paths():
    """测试Cursor数据路径"""
    possible_paths = [
        # macOS
        os.path.expanduser("~/Library/Application Support/Cursor"),
        # Windows
        os.path.expanduser("~/AppData/Roaming/Cursor"),
        os.path.expanduser("~/AppData/Local/Cursor"),
        # Linux
        os.path.expanduser("~/.config/Cursor"),
        os.path.expanduser("~/.cursor"),
    ]
    
    found_paths = []
    for path in possible_paths:
        if os.path.exists(path):
            found_paths.append(path)
            print(f"✓ 找到Cursor数据目录: {path}")
    
    if not found_paths:
        print("✗ 未找到Cursor数据目录")
        return False
    
    return True


def test_script_files():
    """测试脚本文件是否存在"""
    script_files = [
        'cursor_stats_analyzer.py',
        'cursor_git_stats.py',
        'run_cursor_stats.py'
    ]
    
    all_exist = True
    for script in script_files:
        if os.path.exists(script):
            print(f"✓ 脚本文件存在: {script}")
        else:
            print(f"✗ 脚本文件缺失: {script}")
            all_exist = False
    
    return all_exist


def run_quick_test():
    """运行快速测试"""
    print("运行Git统计工具快速测试...")
    
    try:
        # 导入并测试Git统计工具
        sys.path.insert(0, '.')
        from cursor_git_stats import CursorGitStatsAnalyzer
        
        analyzer = CursorGitStatsAnalyzer('.')
        
        # 测试时间范围计算
        start_time, end_time = analyzer._get_time_range()
        print(f"✓ 时间范围计算正常: {start_time.strftime('%Y-%m-%d')} 到 {end_time.strftime('%Y-%m-%d')}")
        
        # 测试Git命令
        output = analyzer._run_git_command(['git', 'status', '--porcelain'])
        print("✓ Git命令执行正常")
        
        print("✓ Git统计工具基本功能正常")
        return True
        
    except ImportError as e:
        print(f"✗ 导入Git统计工具失败: {e}")
        return False
    except Exception as e:
        print(f"✗ Git统计工具测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Cursor统计工具环境检测")
    print("=" * 40)
    
    tests = [
        ("Python模块检测", test_python_modules),
        ("脚本文件检测", test_script_files),
        ("Git可用性检测", test_git_availability),
        ("Git仓库检测", test_git_repo),
        ("Git提交记录检测", test_git_commits),
        ("Cursor数据路径检测", test_cursor_data_paths),
        ("工具功能测试", run_quick_test),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试出错: {e}")
    
    print(f"\n测试结果: {passed}/{total} 项通过")
    print("=" * 40)
    
    if passed == total:
        print("🎉 所有测试通过! 工具可以正常使用")
        print("\n建议使用方式:")
        print("1. 运行 'python run_cursor_stats.py' 使用交互式界面")
        print("2. 运行 'python cursor_git_stats.py' 直接进行Git统计")
    elif passed >= total - 2:
        print("⚠️  大部分测试通过，工具基本可用")
        print("建议先尝试Git统计功能")
    else:
        print("❌ 多项测试失败，请检查环境配置")
        
        if not test_git_availability():
            print("\n解决方案:")
            print("- 安装Git: https://git-scm.com/downloads")
        
        if not test_git_repo():
            print("\n解决方案:")
            print("- 在Git仓库目录下运行此脚本")
            print("- 或使用 --repo-path 参数指定Git仓库路径")


if __name__ == "__main__":
    main()
