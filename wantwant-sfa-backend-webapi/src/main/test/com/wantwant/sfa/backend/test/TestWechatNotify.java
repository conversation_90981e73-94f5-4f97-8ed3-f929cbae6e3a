package com.wantwant.sfa.backend.test;

import com.wantwant.arch.notification.api.dto.NotifyWeComMessageRequestDto;
import com.wantwant.arch.notification.api.dto.WcImSendTextContentDto;
import com.wantwant.arch.notification.api.enumeration.WcImMessageTypeEnum;
import com.wantwant.arch.notification.api.service.NotificationApi;
import com.wantwant.sfa.common.base.Helper;
import com.wantwant.sfa.common.base.dto.RpcResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestWechatNotify {
    @Resource
    private NotificationApi notificationApi;

    @Test
    public void test01(){
        for (int i=0;i<=100;i++){
            NotifyWeComMessageRequestDto notifyWeComMessageRequestDto = new NotifyWeComMessageRequestDto();
            notifyWeComMessageRequestDto.setRobotKey("f91c0828-e7d0-4a0d-8789-739ebe0a7868");
            notifyWeComMessageRequestDto.setMessageType(WcImMessageTypeEnum.TEXT_MESSAGE.getType());
            WcImSendTextContentDto wcImSendTextContentDto = new WcImSendTextContentDto();
            wcImSendTextContentDto.setContent("你好！大春");
            notifyWeComMessageRequestDto.setText(wcImSendTextContentDto);
            RpcResult<Boolean> rpcResult = notificationApi.notifyWeComMessage(notifyWeComMessageRequestDto);
            Helper.getResultData(rpcResult, true);
        }

    }
}
