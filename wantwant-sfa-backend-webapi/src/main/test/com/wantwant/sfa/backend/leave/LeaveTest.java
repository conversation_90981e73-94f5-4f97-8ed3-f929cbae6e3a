package com.wantwant.sfa.backend.leave;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.leave.controller.NewLeaveController;
import com.wantwant.sfa.backend.leave.request.LeaveAuditPopupRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveAuditRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveCommitInfoRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveDetailRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveListRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveRevocationRequest;
import com.wantwant.sfa.backend.leave.service.INewLeaveService;
import com.wantwant.sfa.backend.leave.vo.LeaveAuditPopupVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveCheckVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveListVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveRecordVo;
import com.wantwant.sfa.backend.util.RedisUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class LeaveTest {

    @Mock
    private INewLeaveService iLeaveService;

    @Mock
    private RedisUtil redisUtil;

    @InjectMocks
    private NewLeaveController newLeaveController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    /**
     * 注解 每次用例前的初始化
     */
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(newLeaveController).build();
        objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules();
    }

    /**
     * 测试请假审批弹窗接口
     */
    @Test
    void testLeaveAuditPopup() {
        // 准备测试数据
        LeaveAuditPopupRequest request = new LeaveAuditPopupRequest();
        request.setPerson("EMP001");

        LeaveAuditPopupVo mockVo = new LeaveAuditPopupVo();
        mockVo.setQuantity(3L);
        mockVo.setDays(2L);
        mockVo.setIsPopup(true);

        // Mock service方法
        when(iLeaveService.leaveAuditPopup(any(LeaveAuditPopupRequest.class))).thenReturn(mockVo);

        // 执行测试
        Response<LeaveAuditPopupVo> response = newLeaveController.leaveAuditPopup(request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(3L, response.getData().getQuantity());
        assertEquals(2L, response.getData().getDays());
        assertTrue(response.getData().getIsPopup());

        // 验证service方法被调用
        verify(iLeaveService, times(1)).leaveAuditPopup(any(LeaveAuditPopupRequest.class));
    }

    /**
     * 测试请假校验接口
     */
    @Test
    void testLeaveCheck() {
        // 准备测试数据
        String person = "EMP001";

        NewLeaveCheckVo mockVo = new NewLeaveCheckVo();
        mockVo.setEmployeeInfoId(1001);
        mockVo.setEmployeeName("张三");
        mockVo.setEmployeeId("EMP001");
        mockVo.setQuotaSurplusHours(16);
        mockVo.setBShowBtn(true);

        // Mock service方法
        when(iLeaveService.leaveCheck(anyString())).thenReturn(mockVo);

        // 执行测试
        Response<NewLeaveCheckVo> response = newLeaveController.leaveCheck(person);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals("张三", response.getData().getEmployeeName());
        assertEquals("EMP001", response.getData().getEmployeeId());
        assertEquals(16, response.getData().getQuotaSurplusHours());
        assertTrue(response.getData().isBShowBtn());

        // 验证service方法被调用
        verify(iLeaveService, times(1)).leaveCheck(person);
    }

    /**
     * 测试请假信息提交接口 - 成功场景
     */
    @Test
    void testLeaveCommit_Success() {
        // 准备测试数据
        NewLeaveCommitInfoRequest request = new NewLeaveCommitInfoRequest();
        request.setEmployeeInfoId(1001);
        request.setPerson("EMP001");
        request.setLeaveType(1);
        request.setLeaveStartTime(LocalDateTime.now().plusDays(1));
        request.setLeaveEndTime(LocalDateTime.now().plusDays(2));
        request.setLeaveHours(8);
        request.setLeaveReason("事假");

        // Mock Redis锁成功
        when(redisUtil.setLockIfAbsent(anyString(), anyString(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        doNothing().when(iLeaveService).leaveCommit(any(NewLeaveCommitInfoRequest.class));
        doNothing().when(redisUtil).unLock(anyString(), anyString());

        // 执行测试
        Response response = newLeaveController.leaveCommit(request);

        // 验证结果
        assertNotNull(response);
        // 成功场景：至少不返回错误信息
        assertNull(response.getMessage());

        // 验证方法调用
        verify(redisUtil, times(1)).setLockIfAbsent(anyString(), anyString(), anyLong(), any(TimeUnit.class));
        verify(iLeaveService, times(1)).leaveCommit(any(NewLeaveCommitInfoRequest.class));
        verify(redisUtil, times(1)).unLock(anyString(), anyString());
    }

    /**
     * 测试请假信息提交接口 - 获取锁失败
     */
    @Test
    void testLeaveCommit_LockFailed() {
        // 准备测试数据
        NewLeaveCommitInfoRequest request = new NewLeaveCommitInfoRequest();
        request.setEmployeeInfoId(1001);

        // Mock Redis锁失败
        when(redisUtil.setLockIfAbsent(anyString(), anyString(), anyLong(), any(TimeUnit.class))).thenReturn(false);

        // 执行测试
        Response response = newLeaveController.leaveCommit(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("请求正在处理中！", response.getMessage());

        // 验证service方法未被调用
        verify(iLeaveService, never()).leaveCommit(any(NewLeaveCommitInfoRequest.class));
    }

    /**
     * 测试请假列表查询接口
     */
    @Test
    void testLeaveList() {
        // 准备测试数据
        NewLeaveListRequest request = new NewLeaveListRequest();
        request.setPerson("EMP001");
        request.setPage(1);
        request.setRows(10);

        // 准备Mock数据
        NewLeaveListVo vo1 = new NewLeaveListVo();
        vo1.setBusinessNum("202412251530001");
        vo1.setEmployeeName("张三");

        NewLeaveListVo vo2 = new NewLeaveListVo();
        vo2.setBusinessNum("202412251530002");
        vo2.setEmployeeName("李四");

        IPage<NewLeaveListVo> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(vo1, vo2));
        mockPage.setTotal(2);

        // Mock service方法
        when(iLeaveService.leaveList(any(NewLeaveListRequest.class))).thenReturn(mockPage);

        // 执行测试
        Response<IPage<NewLeaveListVo>> response = newLeaveController.leaveList(request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(2, response.getData().getTotal());
        assertEquals(2, response.getData().getRecords().size());

        // 验证service方法被调用
        verify(iLeaveService, times(1)).leaveList(any(NewLeaveListRequest.class));
    }

    /**
     * 测试请假详情查询接口
     */
    @Test
    void testLeaveDetail() {
        // 准备测试数据
        NewLeaveDetailRequest request = new NewLeaveDetailRequest();
        request.setBusinessNum("202412251530001");
        request.setPerson("EMP001");

        NewLeaveListVo mockVo = new NewLeaveListVo();
        mockVo.setBusinessNum("202412251530001");
        mockVo.setEmployeeName("张三");
        mockVo.setLeaveType(1);
        mockVo.setLeaveTypeName("年假");

        // Mock service方法
        when(iLeaveService.leaveDetail(any(NewLeaveDetailRequest.class))).thenReturn(mockVo);

        // 执行测试
        Response<NewLeaveListVo> response = newLeaveController.leaveDetail(request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals("202412251530001", response.getData().getBusinessNum());
        assertEquals("张三", response.getData().getEmployeeName());

        // 验证service方法被调用
        verify(iLeaveService, times(1)).leaveDetail(any(NewLeaveDetailRequest.class));
    }

    /**
     * 测试请假签核记录查询接口
     */
    @Test
    void testLeaveRecordList() {
        // 准备测试数据
        String businessNum = "202412251530001";

        NewLeaveRecordVo record1 = new NewLeaveRecordVo();
        record1.setOperatorType(9); // 提交
        record1.setOperatorTypeName("提交");
        record1.setOperatorName("张三");

        NewLeaveRecordVo record2 = new NewLeaveRecordVo();
        record2.setOperatorType(1); // 通过
        record2.setOperatorTypeName("通过");
        record2.setOperatorName("李四");

        List<NewLeaveRecordVo> mockRecords = Arrays.asList(record1, record2);

        // Mock service方法
        when(iLeaveService.leaveRecordList(anyString())).thenReturn(mockRecords);

        // 执行测试
        Response<List<NewLeaveRecordVo>> response = newLeaveController.leaveRecordList(businessNum);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(2, response.getData().size());
        assertEquals("提交", response.getData().get(0).getOperatorTypeName());
        assertEquals("通过", response.getData().get(1).getOperatorTypeName());

        // 验证service方法被调用
        verify(iLeaveService, times(1)).leaveRecordList(businessNum);
    }

    /**
     * 测试请假审批接口 - 成功场景
     */
    @Test
    void testLeaveAudit_Success() {
        // 准备测试数据
        NewLeaveAuditRequest request = new NewLeaveAuditRequest();
        request.setBusinessNum("202412251530001");
        request.setPerson("EMP002");
        request.setOperatorType(1); // 通过

        // Mock Redis锁成功
        when(redisUtil.setLockIfAbsent(anyString(), anyString(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        doNothing().when(iLeaveService).leaveAudit(any(NewLeaveAuditRequest.class));
        doNothing().when(redisUtil).unLock(anyString(), anyString());

        // 执行测试
        Response response = newLeaveController.leaveAudit(request);

        // 验证结果
        assertNotNull(response);
        assertNull(response.getMessage());

        // 验证方法调用
        verify(redisUtil, times(1)).setLockIfAbsent(anyString(), anyString(), anyLong(), any(TimeUnit.class));
        verify(iLeaveService, times(1)).leaveAudit(any(NewLeaveAuditRequest.class));
        verify(redisUtil, times(1)).unLock(anyString(), anyString());
    }

    /**
     * 测试请假审批接口 - 获取锁失败
     */
    @Test
    void testLeaveAudit_LockFailed() {
        // 准备测试数据
        NewLeaveAuditRequest request = new NewLeaveAuditRequest();
        request.setBusinessNum("202412251530001");

        // Mock Redis锁失败
        when(redisUtil.setLockIfAbsent(anyString(), anyString(), anyLong(), any(TimeUnit.class))).thenReturn(false);

        // 执行测试
        Response response = newLeaveController.leaveAudit(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("请求正在处理中！", response.getMessage());

        // 验证service方法未被调用
        verify(iLeaveService, never()).leaveAudit(any(NewLeaveAuditRequest.class));
    }

    /**
     * 测试请假撤回接口 - 成功场景
     */
    @Test
    void testLeaveRevocation_Success() {
        // 准备测试数据
        NewLeaveRevocationRequest request = new NewLeaveRevocationRequest();
        request.setBusinessNum("202412251530001");
        request.setPerson("EMP001");
        request.setEmployeeInfoId(1001);
        request.setOperatorType(3); // 撤回

        // Mock Redis锁成功
        when(redisUtil.setLockIfAbsent(anyString(), anyString(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        doNothing().when(iLeaveService).leaveRevocation(any(NewLeaveRevocationRequest.class));
        doNothing().when(redisUtil).unLock(anyString(), anyString());

        // 执行测试
        Response response = newLeaveController.leaveRevocation(request);

        // 验证结果
        assertNotNull(response);
        assertNull(response.getMessage());

        // 验证方法调用
        verify(redisUtil, times(1)).setLockIfAbsent(anyString(), anyString(), anyLong(), any(TimeUnit.class));
        verify(iLeaveService, times(1)).leaveRevocation(any(NewLeaveRevocationRequest.class));
        verify(redisUtil, times(1)).unLock(anyString(), anyString());
    }

    /**
     * 测试请假撤回接口 - 获取锁失败
     */
    @Test
    void testLeaveRevocation_LockFailed() {
        // 准备测试数据
        NewLeaveRevocationRequest request = new NewLeaveRevocationRequest();
        request.setBusinessNum("202412251530001");

        // Mock Redis锁失败
        when(redisUtil.setLockIfAbsent(anyString(), anyString(), anyLong(), any(TimeUnit.class))).thenReturn(false);

        // 执行测试
        Response response = newLeaveController.leaveRevocation(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("请求正在处理中！", response.getMessage());

        // 验证service方法未被调用
        verify(iLeaveService, never()).leaveRevocation(any(NewLeaveRevocationRequest.class));
    }

    /**
     * 测试请假列表导出接口 - 这个测试比较复杂，涉及文件下载
     * 这里提供一个基础的测试框架，实际测试可能需要更复杂的Mock
     */
    @Test
    void testLeaveListExport() throws Exception {
        // 准备测试数据
        NewLeaveListRequest request = new NewLeaveListRequest();
        request.setPerson("EMP001");

        // 准备Mock数据
        NewLeaveListVo vo = new NewLeaveListVo();
        vo.setBusinessNum("202412251530001");
        vo.setEmployeeName("张三");

        IPage<NewLeaveListVo> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(vo));

        // Mock service方法
        when(iLeaveService.leaveList(any(NewLeaveListRequest.class))).thenReturn(mockPage);

        // 由于涉及文件下载和HttpServletResponse，这里主要验证service被调用
        // 实际的文件下载测试需要更复杂的Mock设置

        // 验证service方法会被调用
        verify(iLeaveService, times(0)).leaveList(any(NewLeaveListRequest.class)); // 初始状态未调用
    }
}
