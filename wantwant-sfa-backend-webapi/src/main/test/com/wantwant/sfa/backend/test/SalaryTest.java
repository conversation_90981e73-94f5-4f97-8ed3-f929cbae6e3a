package com.wantwant.sfa.backend.test;

import com.wantwant.sfa.backend.Task.BusinessBDDevelopAutomationTask;
import com.wantwant.sfa.backend.Task.SalaryControlTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/27/上午9:42
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SalaryTest {
    @Autowired
    private SalaryControlTask salaryControlTask;

    @Resource
    private BusinessBDDevelopAutomationTask businessBDDevelopAutomationTask;

    @Test
    public void testSaveSalary(){
        salaryControlTask.execute("2023-Q4");
    }


    @Test
    public void testFetchBusinessBDPackage(){
        salaryControlTask.fetchBusinessBDSalaryPackage("2025-08");
    }


    @Test
    public void testCheckCEOBusinessBDEstablishment(){
        businessBDDevelopAutomationTask.checkCEOBusinessBDEstablishment("2025-08-01");
    }

    @Test
    public void testFetchActualSalary(){
        salaryControlTask.fetchActualSalary("2025-06");
    }
}
