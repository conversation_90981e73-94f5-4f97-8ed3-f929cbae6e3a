package com.wantwant.sfa.backend.salary.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.BusinessBDApplication;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.interview.service.IBusinessBDService;
import com.wantwant.sfa.backend.salary.api.ZWSalaryApi;
import com.wantwant.sfa.backend.salary.request.*;
import com.wantwant.sfa.backend.salary.service.ISalaryMiddlewareService;
import com.wantwant.sfa.backend.salary.vo.*;
import com.wantwant.sfa.backend.service.ZWSalaryService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.BeanUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;

@RestController
@Slf4j
public class ZWSalaryController implements ZWSalaryApi {

    @Autowired
    private ZWSalaryService zWSalaryService;

    @Autowired
    private ISalaryMiddlewareService salaryMiddlewareService;

    @Resource
    private IBusinessBDService businessBDService;

    @Resource
    private BusinessBDApplication businessBDApplication;


    @Override
    public Response<ZWSalaryVo> zwSalaryList(ZWSalaryRequest request) {
        return Response.success(zWSalaryService.zwSalaryList(request));
    }

    @Override
    public Response<List<SalaryExpenseQueryPartnerVo>> salaryBranch(SalaryExpenseQueryRequest request) {
        return Response.success(zWSalaryService.salaryBranch(request));
    }

    @Override
    public Response<List<SalaryExpenseQueryCityManagerVo>> salaryDepartment(SalaryExpenseQueryRequest request) {
        return Response.success(zWSalaryService.salaryDepartment(request));
    }

    @Override
    public Response<List<SalaryExpenseQueryRegionalDirectorVo>> salaryCompany(SalaryExpenseQueryRequest request) {
        return Response.success(zWSalaryService.salaryCompany(request));
    }

    @Override
    public Response setSalaryControl(@Valid SalaryControlConfigRequest salaryControlConfigRequest) {
        log.info("【salary control config】request:{}", salaryControlConfigRequest);
        salaryMiddlewareService.setControl(salaryControlConfigRequest);
        return Response.success();
    }

    @ApiOperation(value = "薪资查询", notes = "薪资查询")
    @Override
    public Response<Page<SalaryVo>> searchSalary(@Valid SalarySearchRequest salarySearchRequest) {
        log.info("【salary search】request:{}",salarySearchRequest);

        Page<SalaryVo> page = zWSalaryService.searchSalary(salarySearchRequest);
        return Response.success(page);
    }

    @ApiOperation(value = "薪资查询-明细列表弹窗", notes = "薪资查询-明细列表弹窗")
    @Override
    public Response<List<SalaryDetailVo>> querySalaryDetailList(SalaryDetailSearchRequest request) {
        return Response.success(zWSalaryService.querySalaryDetailList(request));
    }

    @Override
    public Response<List<SalaryControlVo>> getAreaQuotaControl() {
        log.info("【get area salary control】");
        List<SalaryControlVo> list = salaryMiddlewareService.getAreaQuotaControl();
        return Response.success(list);
    }

    @Override
    public Response<List<SalaryConfigVo>> getSalaryQuotaConfig(SalaryControlSearchRequest salaryControlSearchRequest) {
        log.info("【get salary quota config】request:{}",salaryControlSearchRequest);

        List<SalaryConfigVo> list = salaryMiddlewareService.getSalaryQuotaConfig(salaryControlSearchRequest);

        return Response.success(list);
    }

    @Override
    public Response<List<SalaryControlConfigVo>> getQuotaControl(SalaryControlSearchRequest request) {
        log.info("【get quota control】request:{}",request);
        List<SalaryControlConfigVo> list = salaryMiddlewareService.getQuotaControl(request);
        return Response.success(list);
    }

    @Override
    public void uploadSalaryPackage(SalaryControlSearchRequest salaryControlSearchRequest) {
        log.info("upload salary package,request:{}",salaryControlSearchRequest);
        salaryMiddlewareService.uploadSalaryPackage(salaryControlSearchRequest);
    }

    @Override
    public Response<IPage<BusinessBDQuotaConfigVo>> getBusinessBDQuotaControl(SalaryControlSearchRequest salaryControlSearchRequest) {

        IPage<BusinessBDQuotaConfigVo> page = businessBDApplication.getBusinessBDQuotaControl(salaryControlSearchRequest);

        return Response.success(page);
    }

    @Override
    public void exportBusinessBDQuotaControl(SalaryControlSearchRequest salaryControlSearchRequest) {
        businessBDApplication.exportBusinessBDQuotaControl(salaryControlSearchRequest);
    }

    @Override
    public Response<String> getConfigOrgType(String organizationId) {
        log.info("【get config org type】organizationId:{}",organizationId);
        String orgType = salaryMiddlewareService.getConfigOrgType(organizationId);
        return Response.success(orgType);
    }

    @Override
    public Response<List<SalaryConfigHistoryVo>> getConfigHistory(String organizationId) {
        List<SalaryConfigHistoryVo> list = salaryMiddlewareService.getConfigHistory(organizationId);
        return Response.success(list);
    }

    @Override
    public Response<BigDecimal> currentOrgControl(String organizationId) {
        log.info("【get current control】orgCode:{}",organizationId);
        BigDecimal quota = salaryMiddlewareService.currentOrgControl(organizationId);
        return Response.success(quota);
    }

    @Override
    public Response<BigDecimal> provinceUsedControl(String organizationId) {
        log.info("【get province control】orgCode:{}",organizationId);
        BigDecimal quota = salaryMiddlewareService.provinceUsedControl(organizationId);
        return Response.success(quota);
    }

    @Override
    public Response<Page<SalarySchemeVo>> getSalaryScheme(SalarySchemeRequest salarySchemeRequest) {
        log.info("【get salary scheme】request:{}",salarySchemeRequest);

        Page<SalarySchemeVo> page = zWSalaryService.getSalaryScheme(salarySchemeRequest);

        return Response.success(page);
    }

    @Override
    public Response<SalaryControlPermissionVo> getSalaryControlPermission(String person) {
        log.info("【get salary control permission】person:{}",person);
        SalaryControlPermissionVo vo = zWSalaryService.getSalaryControlPermission(person);

        return Response.success(vo);
    }

    @Override
    public Response salaryPackageConfig(@Valid SalaryPackageConfigRequest request) {
        log.info("【salary package config】request:{}",request);

        salaryMiddlewareService.salaryPackageConfig(request);

        return Response.success();
    }

    @Override
    public Response<SalaryPackageVo> getSalaryPackageInfo(String person) {
        log.info("【salary package info】person:{}",person);

        SalaryPackageVo vo = salaryMiddlewareService.getSalaryPackageInfo(person);
        return Response.success(vo);
    }

    @Override
    public Response<List<SalaryAllocatedVo>> getAllocatedHistory(@Valid SearchAllocatedHistoryRequest searchAllocatedHistoryRequest) {
        log.info("【get allocated history】request:{}",searchAllocatedHistoryRequest);

        List<SalaryAllocatedVo> list = zWSalaryService.getAllocatedHistory(searchAllocatedHistoryRequest);
        return Response.success(list);
    }

    @Override
    public void uploadAllocatedHistory(SearchAllocatedHistoryRequest searchAllocatedHistoryRequest) {
        log.info("upload allocated history,request:{}",searchAllocatedHistoryRequest);
        zWSalaryService.uploadAllocatedHistory(searchAllocatedHistoryRequest);
    }

    @Override
    public Response<IPage<BusinessBDSalaryVo>> getAllocatedBusinessBDHistory(BusinessBDAllocatedRequest businessBDAllocatedRequest) {
        log.info("get allocated business bd history,request:{}",businessBDAllocatedRequest);

        IPage<BusinessBDSalaryVo> page =  businessBDApplication.getAllocatedBusinessBDHistory(businessBDAllocatedRequest);

        return Response.success(page);
    }

    @Override
    public void exportAllocatedBusinessBDHistory(BusinessBDAllocatedRequest businessBDAllocatedRequest) {
        businessBDApplication.exportAllocatedBusinessBDHistory(businessBDAllocatedRequest);
    }

    @Override
    public Response businessBDConfig(@Valid BusinessBDConfigRequest businessBDConfigRequest) {
        log.info("【business bd config】businessBDConfigRequest:{}",businessBDConfigRequest);

        businessBDService.businessBDConfig(businessBDConfigRequest);
        return Response.success();
    }

    @Override
    public Response<IPage<BusinessBDControlVo>> searchBusinessBDControl(@Valid BusinessBDControlSearchRequest businessBDControlSearchRequest) {
        log.info("【search business bd control】request:{}",businessBDControlSearchRequest);
        IPage<BusinessBDControlVo> page = businessBDService.searchBusinessBDControl(businessBDControlSearchRequest);
        return Response.success(page);
    }

    @Override
    public Response<Boolean> checkAddBusinessBD(String organizationId) {
        log.info("【check business bd】organizationId:{}",organizationId);
        Boolean canAdd = businessBDService.checkAddBusinessBD(organizationId);
        return Response.success(canAdd);
    }

    @Override
    public Response importBusinessBDRule(MultipartFile file, String person) {
        log.info("【import business bd rule】start");

        businessBDApplication.importRule(file,person);

        return Response.success();
    }

    @Override
    public Response<IPage<BusinessBDRuleVo>> searchBusinessBDRule(BusinessBDRuleSearchRequest request) {
        log.info("【search business bd rule】request:{}", JSONObject.toJSONString(request));
        IPage<BusinessBDRuleVo> page = businessBDApplication.searchBusinessBDRule(request);
        return Response.success(page);
    }

    @Override
    public void exportBusinessBDRule(BusinessBDRuleSearchRequest request) {
        log.info("【export business bd rule】request:{}", JSONObject.toJSONString(request));
        businessBDApplication.exportBusinessBDRule(request);
    }

    @Override
    public Response<List<BusinessBDDetailVO>> getBusinessBDDetail(String theYearMonth, String organizationId) {
        log.info("【get business bd detail】yearMonth:{},organizationId:{}",theYearMonth,organizationId);

        List<BusinessBDDetailVO> list = businessBDApplication.getBusinessBDDetail(theYearMonth,organizationId);
        return Response.success(list);
    }

    @Override
    public Response<IPage<BusinessBDDetailVO>> selectBusinessBDDetail(BusinessBDSearchRequest businessBDSearchRequest) {
        log.info("【select business bd detail】request:{}",JSONObject.toJSONString(businessBDSearchRequest));
        IPage<BusinessBDDetailVO> page = businessBDApplication.selectBusinessBDDetail(businessBDSearchRequest);
        return Response.success(page);
    }

    @Override
    public Response<IPage<BusinessBDOrgDetailVO>> selectBusinessBDOrgDetail(BusinessBDSearchRequest businessBDSearchRequest) {
        log.info("【select business bd org detail】request:{}",JSONObject.toJSONString(businessBDSearchRequest));
        IPage<BusinessBDOrgDetailVO> page = businessBDApplication.selectBusinessBDOrgDetail(businessBDSearchRequest);
        return Response.success(page);
    }

    @Override
    public void exportBusinessBDOrgDetail(BusinessBDSearchRequest businessBDSearchRequest) {
        log.info("【export business org detail】businessBDSearchRequest:{}",JSONObject.toJSONString(businessBDSearchRequest));
        businessBDApplication.exportBusinessOrgDetail(businessBDSearchRequest);
    }

    @Override
    public void exportBusinessDetail(BusinessBDSearchRequest businessBDSearchRequest) {
        log.info("【export business detail】businessBDSearchRequest:{}",JSONObject.toJSONString(businessBDSearchRequest));
        businessBDApplication.exportBusinessDetail(businessBDSearchRequest);
    }

    @Override
    public Response<IPage<SalaryPackageDetailVO>> selectSalaryPackageDetail(SalaryPackageSearchRequest salaryPackageSearchRequest) {
        log.info("【select salary package detail】request:{}",JSONObject.toJSONString(salaryPackageSearchRequest));
        IPage<SalaryPackageDetailVO> page = zWSalaryService.selectSalaryPackageDetail(salaryPackageSearchRequest);
        return Response.success(page);
    }

    @Override
    public Response<BigDecimal> getBusinessBDPackageSalary(String departmentId,Integer applyId) {
        log.info("get business bd package salary departmentId:{},applyId:{}",departmentId,applyId);

        BigDecimal businessBDPackageSalary = businessBDApplication.getBusinessBDPackageSalary(departmentId,applyId);

        return Response.success(businessBDPackageSalary);
    }

    @Override
    public Response<IPage<BusinessBDControlDetailVO>> selectBusinessBDControl(BusinessBDControlSearch businessBDControlSearch) {

        businessBDControlSearch.setBusinessGroup(RequestUtils.getBusinessGroup());

        IPage<BusinessBDControlDetailVO> page = businessBDApplication.selectBusinessBDControl(businessBDControlSearch);

        return Response.success(page);
    }

    @Override
    public void exportBusinessBDControl(BusinessBDControlSearch businessBDControlSearch) {
        businessBDApplication.exportBusinessBDControl(businessBDControlSearch);
    }


    @Override
    public Response<List<SalaryPackageRateLogVO>> getSalaryPackageLog(String departmentId) {
        log.info("get salary package log ,departmentId:{}",departmentId);

        List<SalaryPackageRateLogVO> list = businessBDApplication.getSalaryPackageLog(departmentId);

        return Response.success(list);
    }

    @Override
    public Response importSalaryPackageRatio(MultipartFile file, String employeeId) {

        businessBDApplication.importSalaryPackageRatio(file,employeeId);

        return Response.success();
    }

    @Override
    public Response setBusinessBDSalaryPackageConfig(BusinessBDSalaryPackageConfig businessBDSalaryPackageConfig) {
        log.info("set business bd salary package config request:{} ",businessBDSalaryPackageConfig);
        businessBDApplication.setBusinessBDSalaryPackageConfig(businessBDSalaryPackageConfig);

        return Response.success();
    }


}
