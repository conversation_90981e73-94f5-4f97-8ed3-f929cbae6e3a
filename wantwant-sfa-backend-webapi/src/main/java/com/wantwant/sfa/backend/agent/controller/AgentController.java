package com.wantwant.sfa.backend.agent.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.agent.api.AgentApi;
import com.wantwant.sfa.backend.agent.service.IAgentService;
import com.wantwant.sfa.backend.agent.vo.AgentVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/05/上午11:10
 */
@RestController
public class AgentController implements AgentApi {
    @Autowired
    private IAgentService agentService;

    @Override
    public Response<List<AgentVo>> getAgentList(String person,boolean cache, Integer isApp) {


        List<AgentVo> list = agentService.getAgentList(person,cache,isApp);
        return Response.success(list);
    }


}
