package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.domain.businessBd.DO.BusinessBdSalaryControlDO;
import com.wantwant.sfa.backend.domain.businessBd.service.BusinessBdSalaryControlService;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO;
import com.wantwant.sfa.backend.salary.entity.EmployeeActualSalaryEntity;
import com.wantwant.sfa.backend.salary.model.ActualSalaryModel;
import com.wantwant.sfa.backend.salary.model.BigTableSalaryControlModel;
import com.wantwant.sfa.backend.salary.model.BusinessBDPackageModel;
import com.wantwant.sfa.backend.salary.service.IEmployeeActualSalaryService;
import com.wantwant.sfa.backend.salary.service.ISalaryBigTableService;
import com.wantwant.sfa.backend.salary.service.ISalaryMiddlewareService;
import com.wantwant.sfa.backend.service.EmployeeSalaryService;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/26/下午3:46
 */
@Component
@Slf4j
public class SalaryControlTask {

    private static final String WORK_START_DATE = "-26";
    @Resource
    private ISalaryBigTableService salaryBigTableService;
    @Resource
    private ISalaryMiddlewareService salaryMiddlewareService;
    @Resource
    private BusinessBdSalaryControlService businessBdSalaryControlService;
    @Resource
    private EmployeeSalaryService employeeSalaryService;
    @Resource
    private IEmployeeActualSalaryService employeeActualSalaryService;

    @XxlJob("transferSalaryControl")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> execute(String param) {
        String theYearMonth = getYearMonth(param);
        log.info("【transfer salary control】yearMonth:{}",theYearMonth);

        List<BigTableSalaryControlModel> salaryControl = salaryBigTableService.getSalaryControl(theYearMonth);
        if(CollectionUtils.isEmpty(salaryControl)){
            log.info("【transfer salary control】empty record");
           return ReturnT.SUCCESS;
        }

        log.info("【transfer salary control】big table response:{}",salaryControl);


        salaryMiddlewareService.saveSalaryControlByBigTable(salaryControl);

        return ReturnT.SUCCESS;
    }

    private String getYearMonth(String param) {
        if(StringUtils.isNotBlank(param)){
            return param;
        }

        // 获取上一月
        return LocalDate.now().minusMonths(1L).toString().substring(0,7);

//        LocalDate currentDate = LocalDate.now();
//        int currentQuarter = (currentDate.getMonthValue() + 2) / 3;
//        LocalDate currentQuarterFirstMonth = LocalDate.of(currentDate.getYear(), currentQuarter * 3 - 2, 1);
//
//        LocalDate prevQuarterFirstMonth = currentQuarterFirstMonth.minusMonths(3L);
//        int quarter = (prevQuarterFirstMonth.getMonthValue() + 2) / 3;
//        return prevQuarterFirstMonth.getYear() +"-Q"+quarter;
    }


    @XxlJob("fetchBusinessBDSalaryPackage")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> fetchBusinessBDSalaryPackage(String param){
        String theYearMonth = LocalDate.now().toString().substring(0,7);
        if(StringUtils.isNotBlank(param)){
            theYearMonth = param;
        }

        log.info("fetch business bd salary package yearMonth:{}",theYearMonth);

        List<BusinessBDPackageModel> businessBDPackageModels = salaryBigTableService.selectBusinessBDPackage(theYearMonth);
        if(CollectionUtils.isEmpty(businessBDPackageModels)){
            log.info("fetch business bd salary package empty record");
            return ReturnT.SUCCESS;
        }

        for (BusinessBDPackageModel businessBDPackageModel : businessBDPackageModels) {
            BusinessBdSalaryControlDO businessBdSalaryControlDO = BusinessBdSalaryControlDO.builder().theYearMonth(theYearMonth)
                    .avgSalaryPackage(businessBDPackageModel.getAvgSalaryPackage())
                    .organizationId(businessBDPackageModel.getCompanyCode())
                    .processUserId("ROOT").processUserName("ROOT").build();
            businessBdSalaryControlService.saveOrUpdate(businessBdSalaryControlDO);
        }

        return ReturnT.SUCCESS;
    }


    @XxlJob("fetchActualSalary")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> fetchActualSalary(String param){
        try {
            // 获取执行月份
            String executeDate = getYearMonth(param);
            log.info("Start executing actual salary sync task, execute date: {}", executeDate);

            // 根据时间查询实际薪资
            List<ActualSalaryModel> actualSalaryModels = salaryBigTableService.selectActualSalary(executeDate);
            if(CollectionUtils.isEmpty(actualSalaryModels)){
                log.info("No actual salary data found, execute date: {}", executeDate);
                return ReturnT.SUCCESS;
            }
            log.info("Found actual salary data count: {}", actualSalaryModels.size());

            // 薪资开始日期必须在考勤日期之前
            LocalDate workStartDate = LocalDate.parse(executeDate + WORK_START_DATE);

            // 获取所有开始日期在考勤开始日期之前，且有效的薪资信息
            List<EmployeeSalaryPO> employeeSalaryPOList = employeeSalaryService.getValidSalaryByDeadline(workStartDate);
            if(CollectionUtils.isEmpty(employeeSalaryPOList)){
                log.info("No valid salary info found, work start date: {}", workStartDate);
                return ReturnT.SUCCESS;
            }
            log.info("Found valid salary info count: {}", employeeSalaryPOList.size());


            Map<Integer, ActualSalaryModel> actualSalaryMap = actualSalaryModels.stream()
                    .collect(Collectors.toMap(ActualSalaryModel::getEmployeeInfoId, Function.identity(), (existing, replacement) -> existing));


            List<EmployeeActualSalaryEntity> actualSalaryEntities = new ArrayList<>();

            for (EmployeeSalaryPO employeeSalaryPO : employeeSalaryPOList) {
                ActualSalaryModel actualSalaryModel = actualSalaryMap.get(employeeSalaryPO.getEmployeeInfoId());
                if(Objects.nonNull(actualSalaryModel)){
                    EmployeeActualSalaryEntity employeeActualSalaryEntity = new EmployeeActualSalaryEntity();
                    employeeActualSalaryEntity.setTheYearMonth(executeDate);
                    BeanUtils.copyProperties(actualSalaryModel, employeeActualSalaryEntity);
                    // 设置关联的薪资ID
                    employeeActualSalaryEntity.setSalaryId(employeeSalaryPO.getId());
                    // 设置为当前有效
                    employeeActualSalaryEntity.setCurrent(Boolean.TRUE);
                    actualSalaryEntities.add(employeeActualSalaryEntity);
                }
            }

            if(CollectionUtils.isEmpty(actualSalaryEntities)){
                log.info("No matching actual salary data to process");
                return ReturnT.SUCCESS;
            }

            // 先将当前实际薪资设置为过期，然后保存新数据
            employeeActualSalaryService.clearCurrentValidSalary();
            employeeActualSalaryService.saveBatch(actualSalaryEntities);

            log.info("Actual salary sync task completed, processed data count: {}", actualSalaryEntities.size());
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("Actual salary sync task failed, param: {}", param, e);
            return ReturnT.FAIL;
        }
    }
}
