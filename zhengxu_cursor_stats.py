#!/usr/bin/env python3
"""
zhengxu专用的Cursor代码修改统计脚本
专门统计zhengxu使用Cursor的代码修改情况
"""

import os
import sys
from datetime import datetime


def main():
    """zhengxu专用统计主函数"""
    print("🚀 zhengxu的Cursor代码修改统计工具")
    print("=" * 40)
    
    # 显示当前时间和统计范围
    now = datetime.now()
    print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查是否在Git仓库中
    if not os.path.exists('.git'):
        print("❌ 当前目录不是Git仓库")
        print("\n请在Git仓库目录下运行此脚本，或者使用:")
        print("python3 cursor_git_stats.py --author zhengxu --repo-path /path/to/repo")
        return 1
    
    print("✓ 检测到Git仓库")
    print("📊 正在统计 zhengxu 的代码修改情况...")
    
    try:
        from cursor_git_stats import CursorGitStatsAnalyzer
        
        # 使用zhengxu作为作者过滤
        analyzer = CursorGitStatsAnalyzer('.', 'zhengxu')
        report = analyzer.run_analysis()
        
        # 生成报告文件名
        timestamp = now.strftime('%Y%m%d_%H%M%S')
        output_file = f"zhengxu_cursor_stats_{timestamp}.txt"
        
        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ 统计完成! 报告已保存到: {output_file}")
        
        # 显示关键统计信息
        print("\n📈 zhengxu的代码修改统计摘要:")
        print("-" * 35)
        
        lines = report.split('\n')
        in_zhengxu_section = False
        
        for line in lines:
            line = line.strip()
            
            # 显示总体统计
            if any(keyword in line for keyword in ['总提交数:', '总添加行数:', '总删除行数:', '净增行数:']):
                print(f"📊 {line}")
            
            # 显示zhengxu的详细统计
            elif line == "zhengxu:":
                in_zhengxu_section = True
                print(f"\n👤 {line}")
            elif in_zhengxu_section and line.startswith(('提交数:', '文件变更:', '添加行数:', '删除行数:', '净增行数:')):
                print(f"   {line}")
            elif in_zhengxu_section and not line.startswith(' ') and line:
                in_zhengxu_section = False
        
        # 显示最近的提交（只显示前5个）
        print(f"\n📝 最近的提交:")
        print("-" * 20)
        
        in_recent_commits = False
        commit_count = 0
        max_commits = 5
        
        for line in lines:
            if "最近的提交:" in line:
                in_recent_commits = True
                continue
            elif in_recent_commits and commit_count < max_commits:
                if line.strip() and not line.startswith('-'):
                    if ' - zhengxu' in line or line.strip().startswith('  '):
                        print(line)
                        if ' - zhengxu' in line:
                            commit_count += 1
        
        print(f"\n📄 完整报告请查看: {output_file}")
        
        # 提供一些使用建议
        print(f"\n💡 使用建议:")
        print("- 这个报告统计了上周一到今天你使用Cursor的代码修改情况")
        print("- 净增行数反映了你的代码贡献量")
        print("- 可以定期运行此脚本来跟踪你的开发效率")
        
        return 0
        
    except ImportError:
        print("❌ 找不到cursor_git_stats.py文件")
        print("请确保cursor_git_stats.py文件在当前目录下")
        return 1
    except Exception as e:
        print(f"❌ 运行统计时出错: {e}")
        return 1


if __name__ == "__main__":
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("请尝试运行: python3 test_cursor_stats.py 来检查环境")
        exit(1)
