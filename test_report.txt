
Git代码修改统计报告 (基于Cursor开发活动)
=====================================

统计时间范围: 2025-08-11 到 2025-08-18
仓库路径: /Users/<USER>/project/sfa-backend

总体统计:
--------
总提交数: 68
总文件变更数: 204
总添加行数: 2950
总删除行数: 1655
净增行数: +1295
平均每次提交行数: 67.7 (如果有提交)

按作者统计:
----------

zhengxu:
  提交数: 38
  文件变更: 145
  添加行数: 1849
  删除行数: 1431
  净增行数: +418

zhanghaijun:
  提交数: 15
  文件变更: 30
  添加行数: 785
  删除行数: 151
  净增行数: +634

panghuidong:
  提交数: 12
  文件变更: 25
  添加行数: 302
  删除行数: 70
  净增行数: +232

wushaonan:
  提交数: 3
  文件变更: 4
  添加行数: 14
  删除行数: 3
  净增行数: +11

每日统计:
--------
2025-08-11: 2次提交, 8个文件, +98行净变更
2025-08-12: 11次提交, 61个文件, +378行净变更
2025-08-13: 23次提交, 51个文件, +748行净变更
2025-08-14: 21次提交, 36个文件, +182行净变更
2025-08-15: 11次提交, 48个文件, -111行净变更

文件类型统计:
-----------
.java: 166个文件, 43次提交
.xml: 37个文件, 25次提交
.yaml: 1个文件, 1次提交

最近的提交:
----------
2025-08-15 15:15 - panghuidong
  首页待办添加待审核终端建档
  1个文件, +2/-2行

2025-08-15 14:54 - panghuidong
  首页待办添加待审核终端建档
  6个文件, +126/-8行

2025-08-15 12:43 - panghuidong
  修改资源列表resourceID为空
  1个文件, +1/-0行

2025-08-15 12:18 - panghuidong
  升级版本
  1个文件, +1/-1行

2025-08-15 11:47 - zhengxu
  修改maven包
  1个文件, +1/-1行

2025-08-15 11:32 - panghuidong
  修改查询菜单接口
  5个文件, +75/-24行

2025-08-15 11:20 - zhengxu
  Merge branch 'hotfix_2025_08_11' into release_V13.5.0
  0个文件, +0/-0行

2025-08-15 11:15 - zhengxu
  消息发放修改
  3个文件, +40/-0行

2025-08-15 10:33 - zhengxu
  Merge branch 'feature/V13.5.0_part_agent' into release_V13.5.0
  0个文件, +0/-0行

2025-08-15 10:32 - zhengxu
  代办修改
  5个文件, +23/-7行

