#!/usr/bin/env python3
"""
Cursor代码修改统计工具 - 交互式版本
支持自由输入作者名字，统计Cursor生成的代码数量
"""

import os
import sys
from datetime import datetime


def get_author_input():
    """获取作者输入"""
    print("📝 作者设置:")
    print("1. 输入特定作者名字")
    print("2. 统计所有作者")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == "1":
        author = input("请输入作者名字: ").strip()
        if author:
            return author
        else:
            print("作者名字不能为空，将统计所有作者")
            return None
    else:
        return None


def get_repo_path():
    """获取仓库路径"""
    repo_path = input("请输入Git仓库路径 (回车使用当前目录): ").strip()
    if not repo_path:
        repo_path = "."
    
    if not os.path.exists(os.path.join(repo_path, '.git')):
        print(f"❌ {repo_path} 不是一个Git仓库")
        return None
    
    return repo_path


def get_output_settings(author):
    """获取输出设置"""
    save_to_file = input("是否保存报告到文件? (y/n): ").strip().lower()
    
    if save_to_file == 'y':
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        if author:
            default_filename = f"cursor_stats_{author}_{timestamp}.txt"
        else:
            default_filename = f"cursor_stats_all_{timestamp}.txt"
        
        filename = input(f"请输入文件名 (回车使用默认: {default_filename}): ").strip()
        if not filename:
            filename = default_filename
        
        return filename
    
    return None


def display_summary(report_content, author):
    """显示统计摘要"""
    lines = report_content.split('\n')
    
    print(f"\n📊 {'所有作者' if not author else author}的Cursor代码统计摘要:")
    print("=" * 50)
    
    # 显示总体统计
    for line in lines:
        line = line.strip()
        if any(keyword in line for keyword in ['总提交数:', '总添加行数:', '总删除行数:', '净增行数:']):
            print(f"📈 {line}")
    
    # 显示Cursor统计
    print("\n🤖 Cursor代码生成统计:")
    for line in lines:
        line = line.strip()
        if any(keyword in line for keyword in ['Cursor生成提交数:', 'Cursor生成代码行数:', '手动编写代码行数:']):
            print(f"   {line}")
    
    # 如果是特定作者，显示该作者的详细信息
    if author:
        print(f"\n👤 {author}的详细统计:")
        in_author_section = False
        for line in lines:
            line = line.strip()
            if line == f"{author}:":
                in_author_section = True
                continue
            elif in_author_section and line.startswith(('提交数:', '文件变更:', '添加行数:', '删除行数:', '净增行数:', 'Cursor')):
                print(f"   {line}")
            elif in_author_section and not line.startswith(' ') and line and not line.startswith(('提交数:', '文件变更:', '添加行数:', '删除行数:', '净增行数:', 'Cursor')):
                break
    
    # 显示最近的提交（前3个）
    print(f"\n📝 最近的提交:")
    in_recent_commits = False
    commit_count = 0
    max_commits = 3
    
    for line in lines:
        if "最近的提交:" in line:
            in_recent_commits = True
            continue
        elif in_recent_commits and commit_count < max_commits:
            if line.strip() and not line.startswith('-'):
                if (not author or f' - {author}' in line) or line.strip().startswith('  '):
                    print(f"   {line.strip()}")
                    if not author or f' - {author}' in line:
                        commit_count += 1


def main():
    """主函数"""
    print("🚀 Cursor代码修改统计工具 - 交互式版本")
    print("=" * 50)
    print("✨ 新功能: 统计Cursor生成的代码数量和占比")
    print("🎯 支持: 自由输入作者名字进行过滤")
    print()
    
    # 获取作者输入
    author = get_author_input()
    
    # 获取仓库路径
    repo_path = get_repo_path()
    if not repo_path:
        return 1
    
    # 获取输出设置
    output_file = get_output_settings(author)
    
    # 显示配置信息
    print(f"\n⚙️ 配置信息:")
    print(f"   仓库路径: {os.path.abspath(repo_path)}")
    print(f"   作者过滤: {'所有作者' if not author else author}")
    print(f"   输出文件: {'控制台输出' if not output_file else output_file}")
    
    # 运行统计
    print(f"\n🔄 正在分析...")
    
    try:
        from cursor_git_stats import CursorGitStatsAnalyzer
        
        analyzer = CursorGitStatsAnalyzer(repo_path, author)
        report = analyzer.run_analysis()
        
        # 保存或显示报告
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"✅ 报告已保存到: {output_file}")
        
        # 显示摘要
        display_summary(report, author)
        
        if output_file:
            print(f"\n📄 完整报告请查看: {output_file}")
        else:
            print(f"\n📄 完整报告:")
            print("=" * 50)
            print(report)
        
        # 使用建议
        print(f"\n💡 使用建议:")
        if author:
            print(f"- 这个报告统计了{author}在上周一到今天的代码修改情况")
            print(f"- Cursor代码占比反映了AI辅助编程的使用程度")
        else:
            print("- 这个报告统计了所有作者在上周一到今天的代码修改情况")
            print("- 可以对比不同作者的Cursor使用情况")
        print("- 建议定期运行来跟踪开发效率和AI辅助程度")
        
        return 0
        
    except ImportError:
        print("❌ 找不到cursor_git_stats.py文件")
        print("请确保cursor_git_stats.py文件在当前目录下")
        return 1
    except Exception as e:
        print(f"❌ 运行统计时出错: {e}")
        return 1


if __name__ == "__main__":
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("请尝试运行: python3 test_cursor_stats.py 来检查环境")
        exit(1)
