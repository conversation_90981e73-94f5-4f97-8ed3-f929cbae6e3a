# Cursor代码修改统计工具

这个工具集用于统计Cursor中多个对话的代码修改行数和采纳率，时间周期为上周一到今天。

## 🚀 快速开始

### 最简单的方式 (推荐)
```bash
# 1. 一键测试环境
python3 test_cursor_stats.py

# 2. 快速运行统计
python3 quick_start.py

# 3. 交互式使用
python3 run_cursor_stats.py
```

### 直接运行Git统计
```bash
# 在你的项目目录下运行
python3 cursor_git_stats.py

# 指定项目路径和输出文件
python3 cursor_git_stats.py --repo-path /path/to/your/repo --output report.txt
```

## 📊 实际运行示例

在当前项目中运行的真实结果：

```
Git代码修改统计报告 (基于Cursor开发活动)
=====================================

统计时间范围: 2025-08-11 到 2025-08-18
仓库路径: /Users/<USER>/project/sfa-backend

总体统计:
--------
总提交数: 68
总文件变更数: 204
总添加行数: 2950
总删除行数: 1655
净增行数: +1295
平均每次提交行数: 67.7

按作者统计:
----------
zhengxu:
  提交数: 38
  文件变更: 145
  添加行数: 1849
  删除行数: 1431
  净增行数: +418

zhanghaijun:
  提交数: 15
  文件变更: 30
  添加行数: 785
  删除行数: 151
  净增行数: +634
```

## 🛠️ 工具说明

### 1. cursor_git_stats.py (主要工具)
基于Git提交记录的统计工具，通过分析Git历史来统计代码修改情况。

### 2. cursor_stats_analyzer.py (高级工具)
直接分析Cursor应用数据的工具，尝试从Cursor的本地数据库中提取对话信息。

### 3. 辅助工具
- `run_cursor_stats.py`: 交互式界面
- `test_cursor_stats.py`: 环境检测工具
- `quick_start.py`: 一键运行工具

## ⚙️ 环境要求

- Python 3.6+
- Git (用于Git统计工具)
- 标准库模块: os, subprocess, datetime, sqlite3, json, re

**无需安装额外的第三方包！**

## 🚀 开始使用

1. 运行 `python3 test_cursor_stats.py` 检查环境
2. 运行 `python3 quick_start.py` 开始统计
3. 查看生成的报告文件

**祝你使用愉快！** 🎉
