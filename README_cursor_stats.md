# Cursor代码修改统计工具

这个工具集用于统计Cursor中多个对话的代码修改行数和采纳率，时间周期为上周一到今天。

## 工具说明

### 1. cursor_stats_analyzer.py
直接分析Cursor应用数据的高级工具，尝试从Cursor的本地数据库中提取对话和代码修改信息。

**特点:**
- 自动查找Cursor数据目录
- 支持SQLite和JSON数据格式
- 提供详细的对话级别统计
- 计算代码采纳率

**使用方法:**
```bash
python cursor_stats_analyzer.py
python cursor_stats_analyzer.py --data-path /path/to/cursor/data
python cursor_stats_analyzer.py --output report.txt
```

### 2. cursor_git_stats.py
基于Git提交记录的实用工具，通过分析Git历史来统计代码修改情况。

**特点:**
- 基于Git提交记录，数据更可靠
- 按作者、日期、文件类型分组统计
- 显示最近的提交记录
- 计算净增代码行数

**使用方法:**
```bash
python cursor_git_stats.py
python cursor_git_stats.py --repo-path /path/to/your/repo
python cursor_git_stats.py --output git_report.txt
```

## 快速开始

### 方法1: 使用Git统计 (推荐)

如果你的项目使用Git版本控制，这是最简单可靠的方法：

```bash
# 在你的项目目录下运行
cd /path/to/your/project
python cursor_git_stats.py

# 或者指定项目路径
python cursor_git_stats.py --repo-path /Users/<USER>/project/sfa-backend
```

### 方法2: 使用Cursor数据分析

如果你想直接分析Cursor的对话数据：

```bash
# 自动查找Cursor数据
python cursor_stats_analyzer.py

# 手动指定Cursor数据路径
python cursor_stats_analyzer.py --data-path "~/Library/Application Support/Cursor"
```

## 输出示例

### Git统计报告示例:
```
Git代码修改统计报告 (基于Cursor开发活动)
=====================================

统计时间范围: 2024-01-08 到 2024-01-15
仓库路径: /Users/<USER>/project/sfa-backend

总体统计:
--------
总提交数: 15
总文件变更数: 45
总添加行数: 1250
总删除行数: 380
净增行数: +870
平均每次提交行数: 108.7

按作者统计:
----------
张三:
  提交数: 8
  文件变更: 25
  添加行数: 750
  删除行数: 200
  净增行数: +550

李四:
  提交数: 7
  文件变更: 20
  添加行数: 500
  删除行数: 180
  净增行数: +320

每日统计:
--------
2024-01-08: 3次提交, 8个文件, +120行净变更
2024-01-09: 5次提交, 15个文件, +280行净变更
...

文件类型统计:
-----------
.java: 30个文件, 12次提交
.xml: 8个文件, 5次提交
.md: 4个文件, 3次提交
...
```

## 时间范围说明

两个工具都统计**上周一到今天**的数据：
- 如果今天是周三，则统计上周一到今天（共10天）
- 如果今天是周一，则统计上上周一到今天（共8天）

## 注意事项

1. **Git统计工具**:
   - 需要在Git仓库目录下运行
   - 统计的是Git提交记录，反映实际的代码变更
   - 更适合团队协作项目

2. **Cursor数据分析工具**:
   - 需要访问Cursor的本地数据
   - 可能需要管理员权限
   - 不同版本的Cursor数据格式可能不同

3. **采纳率计算**:
   - Git工具中，所有提交的代码都被认为是"采纳"的
   - Cursor数据工具尝试分析对话中的代码是否被实际使用

## 故障排除

### 常见问题:

1. **"不是Git仓库"错误**:
   ```bash
   cd /path/to/your/project
   git status  # 确认是Git仓库
   ```

2. **"找不到Cursor数据目录"错误**:
   ```bash
   # macOS
   ls ~/Library/Application\ Support/Cursor
   
   # Windows
   dir %APPDATA%\Cursor
   
   # Linux
   ls ~/.config/Cursor
   ```

3. **没有数据**:
   - 确认时间范围内有Git提交
   - 检查Cursor是否有对话记录

## 自定义和扩展

你可以修改脚本来：
- 调整时间范围
- 添加更多统计维度
- 支持其他数据源
- 自定义报告格式

## 依赖要求

- Python 3.6+
- Git (用于Git统计工具)
- 标准库模块: os, subprocess, datetime, sqlite3, json, re

无需安装额外的第三方包。
