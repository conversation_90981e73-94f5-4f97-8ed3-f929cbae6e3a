package com.wantwant.sfa.backend.domain.businessBd.DO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 业务BD额度管控数据对象
 * 用于业务层和持久层之间的数据传输
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessBdSalaryControlDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 年月，格式：yyyy-MM
     */
    private String theYearMonth;

    /**
     * 过期额度
     */
    private BigDecimal expiredQuota;
    
    /**
     * 组织ID
     */
    private String organizationId;
    /**
     * 上月结余
     */
    private BigDecimal lastMonthBalance;
    
    /**
     * 当季度每月用人费用包
     */
    private BigDecimal avgSalaryPackage;

    /**
     * 创建人工号
     */
    private String processUserId;
    
    /**
     * 创建人姓名
     */
    private String processUserName;

} 