package com.wantwant.sfa.backend.domain.businessBd.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.businessBd.DO.OrgSalaryDO;
import com.wantwant.sfa.backend.domain.businessBd.DO.SalaryAllocatedDO;
import com.wantwant.sfa.backend.domain.businessBd.repository.po.BusinessBdSalaryHistoryPO;
import com.wantwant.sfa.backend.salary.request.BusinessBDAllocatedRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDSalaryVo;
import com.wantwant.sfa.backend.salary.vo.SalaryAllocatedVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusinessBdSalaryHistoryMapper extends BaseMapper<BusinessBdSalaryHistoryPO> {

    /**
     * 历史费用使用查询
     *
     * @param theYearMonth
     * @return
     */
    List<OrgSalaryDO>  selectHistoryTotalSalary(@Param("theYearMonth") String theYearMonth);

    /**
     * 获取历史薪资明细
     *
     * @param page
     * @param businessBDAllocatedRequest
     * @return
     */
    List<SalaryAllocatedDO> selectHistory(@Param("page")IPage<BusinessBDSalaryVo> page, @Param("req") BusinessBDAllocatedRequest businessBDAllocatedRequest);
}
