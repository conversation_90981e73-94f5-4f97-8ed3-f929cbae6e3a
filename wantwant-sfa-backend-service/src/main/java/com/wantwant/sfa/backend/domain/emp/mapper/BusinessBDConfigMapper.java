package com.wantwant.sfa.backend.domain.emp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdConfigPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2025/03/04/下午3:40
 */
public interface BusinessBDConfigMapper extends BaseMapper<BusinessBdConfigPO> {

    List<String> selectOrgCodeByType(@Param("type") int type);

}
