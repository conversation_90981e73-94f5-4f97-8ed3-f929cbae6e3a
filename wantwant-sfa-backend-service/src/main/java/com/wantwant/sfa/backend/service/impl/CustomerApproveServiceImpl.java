package com.wantwant.sfa.backend.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.customer.request.*;
import com.wantwant.sfa.backend.customer.vo.*;
import com.wantwant.sfa.backend.entity.*;
import com.wantwant.sfa.backend.enums.ShopChannelTypeEnums;
import com.wantwant.sfa.backend.enums.ShopSalesRoomTypeEnums;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.positionRelation.PositionRegionMapper;
import com.wantwant.sfa.backend.market.service.ISmallMarketEmployeeService;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.positionRegion.vo.RegionInfoVo;
import com.wantwant.sfa.backend.positionRegion.vo.SmallMarketVo;
import com.wantwant.sfa.backend.service.CustomerApproveService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.ROOTConnectorUtil;
import com.wantwant.sfa.backend.vo.AreaPopulationVO;
import com.wantwant.sfa.backend.vo.CustomerInfoVO;
import com.wantwant.sfa.backend.vo.CustomerPartnerInfoVO;
import com.wantwant.sfa.backend.vo.RegionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 客户审批相关 serviceImpl
 *
 * @version 1.0
 * @date 2022-02-19 13:15
 */
@Slf4j
@Service
public class CustomerApproveServiceImpl implements CustomerApproveService {

    @Resource
    private CustomerApproveMapper mapper;

    @Resource
    private RealtimeDataMapper dataMapper;

    @Resource
    private CustomerInformationMapper customerInformationMapper;

    @Resource
    private ROOTConnectorUtil connectorUtil;

    @Resource
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;

    @Resource
    private SettingsMapper settingsMapper;

    @Autowired
    private SettingServiceImpl settingService;

    @Resource
    private SfaCustomerInfoVerifyMapper sfaCustomerInfoVerifyMapper;

    @Resource
    private SfaCustomerInfoVerifyDetailMapper sfaCustomerInfoVerifyDetailMapper;

    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Resource
    private CustomerInfoMapper customerInfoMapper;

    @Resource
    private  SfaAuditCertificationMapper sfaAuditCertificationMapper;

    @Resource
    private SfaDisplayApproveVerifyMapper displayVerifyMapper;

    @Resource
    private SfaDisplayApproveVerifyDetailMapper displayVerifyDetailMapper;

    @Autowired
    private IAuditService iAuditService;

    @Autowired
    private ConfigMapper configMapper;


    @Autowired
    private ISmallMarketEmployeeService smallMarketEmployeeService;

    @Autowired
    private PositionRegionMapper positionRegionMapper;

    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;


    /**
     * 8.3.0
     * @param query
     * @return
     */
    //@Override
    public IPage<CustomerApproveVO> queryCustomerApproveByPage1(CustomerApproveRequest query) {
        log.info("客户审批列表入参:{}", query);
        //操作人权限查询
        List<CeoBusinessOrganizationPositionRelation> relationList = ceoBusinessOrganizationPositionRelationMapper.selectByEmployeeIdByLine(query.getPerson(),RequestUtils.getBusinessGroup(),RequestUtils.getLoginInfo().getPositionTypeId());
        if (relationList == null || relationList.size() == 0) {
            throw new ApplicationException("操作人岗位查询失败");
        }
        if(ObjectUtils.isNull(query.getAreaOrganizationId()) && relationList.get(0).getPositionTypeId() != 7){
            query.setDeOrganizationIds(relationList.stream().map(CeoBusinessOrganizationPositionRelation::getOrganizationId).collect(Collectors.toList()));
        }
        query.setBusinessGroupId(RequestUtils.getBusinessGroup());
        query.setBusinessGroup(sfaBusinessGroupMapper.queryBusinessGroupCode(RequestUtils.getBusinessGroup()));
        //产品组
        Page<CustomerApproveVO> page = new Page<>(query.getPage(), query.getRows());
        List<String> ids = mapper.pageCustomerApprove1(page, query);
        if (CommonUtil.ListUtils.isEmpty(ids)) {
            ids.add("-1");
        }
        List<CustomerApproveVO> list = mapper.pageCustomerApproveIds1(ids, query);
        if (!CollectionUtils.isEmpty(list)) {
            list.stream().forEach(e -> {
                //审核有结果且旺铺那边待审核状态，标识重新提交，需要把之前的记录重置掉
                if(e.getResults() != null && e.getResults() != 0 && e.getCustomerStatus() == 1) {
                    sfaCustomerInfoVerifyMapper.updateExistAuditRecord(e.getCustomerId());
                    e.setProcessStep(null);
                    e.setResults(null);
                }
                e.setChannelTypeStr(ShopChannelTypeEnums.getShopChannelTypeDescByCode(e.getChannelType()));
                e.setSalesRoomTypeStr(ShopSalesRoomTypeEnums.getSaleRoomTypeDesc(e.getSalesRoomType()));
                e.setCustomerStatusStr(getAuditStatusStr1(e.getProcessStep(), e.getResults(),e.getCustomerStatus()));
//                ParentMessageVo parentMessage = customerInfoMapper.queryParentMessage(e.getCreator());
//                SelectAuditDto selectAuditDto = new SelectAuditDto();
//                selectAuditDto.setChannel(3);
//                selectAuditDto.setBusinessGroup(parentMessage.getBusinessGroup());
//                selectAuditDto.setCurrentOrganizationId(parentMessage.getOrganizationId());
//                selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
//                CeoBusinessOrganizationPositionRelation auditPerson = iAuditService.chooseAuditPerson(selectAuditDto);
//                if(auditPerson.getEmployeeId().equals(query.getPerson()) && parentMessage.getBusinessGroup() == RequestUtils.getBusinessGroup() && Objects.isNull(e.getProcessStep()) ){
//                    e.setBShowBtn(true);
//                }
//                List<String> parentList = customerInfoMapper.queryParentMessageTemporary(e.getCreator(),e.getBusinessGroupId());
//                if(parentList != null && parentList.size() > 0 && relationList.get(0).getPositionTypeId() != 7){
//                    List<String> list1 = relationList.stream().map(CeoBusinessOrganizationPositionRelation::getOrganizationId).collect(Collectors.toList());
//                    parentList.retainAll(list1);
//                    if(parentList.size()>0 && e.getProcessStep() == null){
//                        e.setBShowBtn(true);
//                    }else {
//                        e.setBShowBtn(false);
//                    }
//
//                }
                e.setBShowBtn(auditBtnEnable(e, query.getPerson()));

                if (!"".equals(e.getHeadStoreId())){
                    CustomerPartnerInfoVO infoVO = customerInfoMapper.queryHeadStoreName(e.getHeadStoreId());
                    e.setHeadStoreName(infoVO.getCustomerName());
                    e.setHeadStoreLabel(infoVO.getCustomerLabel());
                }
            });
        }
        page.setRecords(list);
        return page;
    }

    //审核按钮高亮逻辑
    private boolean auditBtnEnable(CustomerApproveVO approveVO, String person) {
        boolean result = false;
        if (Objects.nonNull(approveVO.getCustomerStatus()) && approveVO.getCustomerStatus() == 1) {//待审批情况下
            Integer currentPositionType = RequestUtils.getLoginInfo().getPositionTypeId();
            if (Objects.isNull(approveVO.getProcessStep())) {//第一审批
                if(Objects.nonNull(approveVO.getDepartmentEmployeeId()) && Objects.isNull(approveVO.getResults())) {//城市经理审批
                    result = currentPositionType.equals(10) && person.equals(approveVO.getDepartmentEmployeeId());
                }else if(Objects.nonNull(approveVO.getCompanyEmployeeId())) {//如果城市经理为空，判断分公司总监
                    result = currentPositionType.equals(2) && person.equals(approveVO.getCompanyEmployeeId());
                }else {//如果第一次审批，城市经理和分公司总监都没有的话，则只要不是总部的人，则都可以审批
                    result = !currentPositionType.equals(7);
                }
            }else if(Objects.nonNull(approveVO.getCompanyEmployeeId())) {//第二次审批
                // 既然有第二次审批，说明第一次肯定是城市经理审批的，如果分公司总监在岗位，只有它能审批，如果不在岗位的话，则除了总部都可以
                result = currentPositionType.equals(2) && person.equals(approveVO.getCompanyEmployeeId());
            }else {
                //如果第二次审批，分公司总监都没有的话，则只要不是总部和区域经理的人，则都可以审批
                result = !currentPositionType.equals(7) && !currentPositionType.equals(10);
            }
        }
        return result;
    }

    @Override
    public int queryCustomerApprovePendingAuditCount(String organizationId, String person) {
        //操作人权限查询
        CeoBusinessOrganizationPositionRelation relation = ceoBusinessOrganizationPositionRelationMapper.selectByEmployeeId(person);
        //是否为运营管理
        String customerTransferAccount = settingService.getValue("customer_info_audit");
        boolean operatorAudit = customerTransferAccount != null && customerTransferAccount.contains(person) ? true : false;
        CustomerApproveRequest query = new CustomerApproveRequest();
        query.setOrganizationId(organizationId);
        query.setPerson(person);
        List<CustomerApproveVO> list = mapper.customerModifyList(query);
        list.forEach(e -> {//大区经理
            if (relation.getPositionTypeId() == 2 && e.getProcessStep() == null) {
                e.setBShowBtn(true);
            }
            //总督导或者当前区域总监不存在的分公司
            if (relation.getPositionTypeId() == 1 && e.getProcessStep() == null ) {
                e.setBShowBtn(true);
            }
            //运营管理
            if (operatorAudit && e.getProcessStep() == null) {
                e.setBShowBtn(true);
            }
        });
        return (int) list.stream().filter(e -> e.isBShowBtn()).count();
    }

    /**
     * @description 统计可审核的客户数量（BShowBtn为true的数量）
     * <AUTHOR>
     * @date 2025-01-29 11:40
     **/
    @Override
    public Long countAuditableCustomers(LoginModel loginModel,String person) {
        CustomerApproveRequest query=new CustomerApproveRequest();
        // 操作人权限查询
        List<CeoBusinessOrganizationPositionRelation> relationList = ceoBusinessOrganizationPositionRelationMapper
                .selectByEmployeeIdByLine(person, loginModel.getBusinessGroup(), loginModel.getPositionTypeId());

        if (CollectionUtils.isEmpty(relationList)) {
            return 0L;
        }
        // 设置组织权限
        if (ObjectUtils.isNull(query.getAreaOrganizationId()) && relationList.get(0).getPositionTypeId() != 7) {
            query.setDeOrganizationIds(relationList.stream()
                    .map(CeoBusinessOrganizationPositionRelation::getOrganizationId)
                    .collect(Collectors.toList()));
        }

        query.setBusinessGroupId(loginModel.getBusinessGroup());
        query.setBusinessGroup(sfaBusinessGroupMapper.queryBusinessGroupCode(loginModel.getBusinessGroup()));
        // 直接通过SQL统计可审核数量
        return mapper.countAuditableCustomers(query, loginModel.getPositionTypeId(), query.getPerson());
    }

    /**
     * 客户审核列表导出
     *
     * @param query
     * @return: void
     * @author: zhouxiaowen
     * @date: 2022-02-19 22:13
     */
    @Override
    public void exportList(CustomerApproveRequest query) {
        log.info("客户审批列表导出入参:{}", query);
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletRequest request = servletRequestAttributes.getRequest();
        HttpServletResponse response = servletRequestAttributes.getResponse();

        List<CustomerApproveVO> list = mapper.listCustomerApprove(query);
        if (!CollectionUtils.isEmpty(list)) {
            list.stream().forEach(e -> {
                e.setCustomerStatusStr(getAuditStatusStr1(e.getProcessStep(), e.getResults(),e.getCustomerStatus()));
            });

        }
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), CustomerApproveVO.class, list);
        try {
            String fileName = "客户审核列表";
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    //获取当前的审核状态的字符串标识
    public String getAuditStatusStr(Integer processStep, Integer result) {
        if (processStep == null ) {
            return "待总监审核";
        } else if(processStep == 1) {//总监通过是不会暂存状态
            return "总监驳回";
        } else if (processStep == 2) {
            return result == 0 ? "待督导审核" : result == 1 ? "督导审核通过" : "督导驳回";
        } else if (processStep == 3) {
            return result == 0 ? "待运营审核" : result == 1 ? "运营审核通过" : "运营驳回";
        }
        return null;
    }


    //新：获取当前的审核状态的字符串标识
    public String getAuditStatusStr1(Integer processStep, Integer result, Integer customerStatus) {
            if (Objects.nonNull(customerStatus)){
                switch (customerStatus){
                    case 1 :
                        return "待审核";
                    case 2 :
                        return "审核通过";
                    case 3 :
                        return "已驳回";
                    case 4  :
                        return "已取消";
                    default:
                        return "";
                }
            }else {
                return "";
            }
    }

    /**
     * 根据ID审核详情
     *
     * @param customerId 客户编号
     * @return: com.wantwant.sfa.backend.customer.vo.CustomerApproveDetailVO
     * @date: 2022-02-19 17:18
     */
    @Override
    public CustomerApproveDetailVO getCustomerApproveById(String customerId, String person) {
        log.info("根据ID审核详情入参customerId:{} person:{}", customerId,person);
        CustomerApproveDetailVO detailVO = new CustomerApproveDetailVO();
        Integer storeType = mapper.getCustomerStoreType(customerId);
        CustomerApproveVO vo = mapper.getCustomerApproveById(customerId, storeType);
        vo.setChannelTypeStr(ShopChannelTypeEnums.getShopChannelTypeDescByCode(vo.getChannelType()));
        vo.setSalesRoomTypeStr(ShopSalesRoomTypeEnums.getSaleRoomTypeDesc(vo.getSalesRoomType()));
        vo.setCustomerReceiveAddress(mapper.getReceiveAddressById(customerId));
        CustomerInfo info = customerInfoMapper.selectOne(new QueryWrapper<CustomerInfo>().eq("customer_id", customerId).eq("delete_flag", "0"));
        //操作人权限查询
        List<CeoBusinessOrganizationPositionRelation> relationList = ceoBusinessOrganizationPositionRelationMapper.selectByEmployeeIdByLine(person, RequestUtils.getBusinessGroup(), RequestUtils.getLoginInfo().getPositionTypeId());
        CeoBusinessOrganizationPositionRelation relation = null;
        if(RequestUtils.getLoginInfo().getPositionTypeId() != 7){
            List<String> organizationList = relationList.stream().map(CeoBusinessOrganizationPositionRelation::getOrganizationId).collect(Collectors.toList());
            relation =   customerInfoMapper.queryCustomerParentMessage(String.valueOf(info.getCreator()),organizationList);
        }else {
            relation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_type_id", 7).eq("employee_id", person).eq("business_group", RequestUtils.getBusinessGroup()));

        }

        if (Objects.isNull(relation)) {
            throw new ApplicationException("操作人岗位查询失败");
        }
        List<String> lackOfCompanyCodeList = null;
        if(relation.getPositionTypeId() == 1) {//总督导,查找分公司缺岗的人
            List<CeoBusinessOrganizationPositionRelation> list = ceoBusinessOrganizationPositionRelationMapper.selectSubOrganizationNotPersonByOrganizationParentId(relation.getOrganizationId());
            lackOfCompanyCodeList = list.stream().map(CeoBusinessOrganizationPositionRelation::getOrganizationId).collect(Collectors.toList());
        }
        if(vo.getProcessStep() == null && (relation.getPositionTypeId() == 1 ) && lackOfCompanyCodeList.contains(vo.getCompanyCode())) {
            vo.setProcessStep(2);//
            vo.setResults(0);
        }
        if (!"".equals(vo.getHeadStoreId())){
            CustomerPartnerInfoVO infoVO = customerInfoMapper.queryHeadStoreName(vo.getHeadStoreId());
            vo.setHeadStoreName(infoVO.getCustomerName());
            vo.setHeadStoreLabel(infoVO.getCustomerLabel());
        }
        vo.setCustomerStatusStr(getAuditStatusStr1(vo.getProcessStep(), vo.getResults(),vo.getCustomerStatus()));
        List<CustomerWarehouseVO> warehouse = mapper.listWarehouse(customerId);
        List<CheckCustomerVO> checkVo = getCustomerVerifyById1(customerId);

        //1-非连锁、2-连锁
        List<String> markers = mapper.querySmallMarketByCustomerId(customerId);
        if(!ObjectUtils.isNull(markers) && markers.size() > 0){
            if (vo.getBusinessNature() == 1) {
                //非连锁 直接cha
                List<SmallMarketVo> smallMarket = smallMarketEmployeeService.getSmallMarketById(markers);
                if(!CollectionUtils.isEmpty(smallMarket)) {
                    vo.setCustomerArea(smallMarket.stream().map(SmallMarketVo::getRegionName).collect(Collectors.toList()));
                }
            } else if (vo.getBusinessNature() == 2) {
                //连锁
                List<RegionInfoVo> infoVos = positionRegionMapper.selectRegionInfo(markers);
                vo.setCustomerArea(infoVos.stream().map(RegionInfoVo::getVillageName).collect(Collectors.toList()));
            }
        }
        //客户产品组
        vo.setBusinessGroup(sfaBusinessGroupMapper.queryBusinessGroupByCustomerId(vo.getCustomerId()));

        List<AreaPopulationVO> areaPopulationVOS = customerInfoMapper.selectBusinessAreaByCustomerId(vo.getCustomerId(),vo.getStoreType());
        List<String> area = new ArrayList<>();
        AtomicInteger i = new AtomicInteger(1);
            areaPopulationVOS.forEach(x2->{
                if(ObjectUtils.isNotEmpty(x2)){
                if (ObjectUtils.isNotEmpty(vo.getStoreType())&&(vo.getStoreType() == 0 || vo.getStoreType() == 1)) {
                    area.add(StringUtils.isEmpty(x2.getPopulation()) ? x2.getSmallMarketName() + i + "-" + x2.getArea() : x2.getSmallMarketName() + i + "-" + x2.getArea() + "(" + x2.getPopulation() + ")");
                    i.getAndIncrement();
                } else {
                    area.add(StringUtils.isEmpty(x2.getPopulation()) ? x2.getArea() : x2.getArea() + "(" + x2.getPopulation() + ")");
                }
                }
            });
        vo.setScope(area);
        detailVO.setCustomerInfo(vo);
        detailVO.setWarehouse(warehouse);
        detailVO.setVerify(checkVo);
        return detailVO;
    }

    //设置四级地
    private void setRegion(String r, StringBuilder sb) {
        RegionVO region = customerInformationMapper.getParentCodeBy(r);
        if (Objects.isNull(region)) {
            return;
        } else {
            sb.insert(0, region.getRegionName() + "-");
            this.setRegion(region.getParentCode(), sb);
        }
    }

    /**
     * 客户审批connectorUtil
     *
     * @param request
     * @return: java.lang.Boolean
     * @date: 2/21/22 7:48 PM
     */
    @Override
    public Boolean customerAudit(CustomerAuditRequest request) {
        return connectorUtil.customerAudit(request);
    }


    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public void customerInfoAudit(CustomerAuditRequest request) {
        log.info("CustomerAuditRequest {}", request);
        SfaCustomerInfoVerify infoVerify = sfaCustomerInfoVerifyMapper.selectOne(new QueryWrapper<SfaCustomerInfoVerify>().eq("customer_id", request.getCustomerId()).eq("delete_flag",0));
        CustomerInfo customerInfo = customerInfoMapper.selectOne(new QueryWrapper<CustomerInfo>().eq("customer_id",request.getCustomerId()).eq("delete_flag",0));
        if(Objects.isNull(customerInfo)){
            throw new ApplicationException("客户id不存在");
        }else if(customerInfo.getCustomerStatus() == 2 || customerInfo.getCustomerStatus() == 3) {
            throw new ApplicationException("当前客户已经审核过");
        }
        if(RequestUtils.getLoginInfo().getPositionTypeId().equals(10) && Objects.nonNull(infoVerify)) {
            throw new ApplicationException("区域经理审核已通过");
        }
        //判断是否新增
        if(Objects.isNull(infoVerify)) {
            //生成申请节点，sfa_customer_info_verify和sfa_customer_info_verify_detail插入数据
            createApplyNode1(request, customerInfo);
            infoVerify = sfaCustomerInfoVerifyMapper.selectOne(new QueryWrapper<SfaCustomerInfoVerify>().eq("customer_id", request.getCustomerId()).eq("delete_flag",0));

        }else if(infoVerify.getResult() != 0) {
            throw new ApplicationException("当前客户已审批，请勿重新提交");
        }
        if(Objects.isNull(infoVerify)) {
            throw new ApplicationException("审核流程表获取失败");
        }
        SfaCustomerInfoVerifyDetail infoVerifyDetail = sfaCustomerInfoVerifyDetailMapper.selectById(infoVerify.getDetailId());
        if(Objects.isNull(infoVerifyDetail)) {
            throw new ApplicationException("待审核记录获取失败");
        }
        //操作人信息
//        String operatorPositionId = null;
        String operatorUserName = null;
        String operatorEmployeeId = null;
        //操作人权限查询
        List<CeoBusinessOrganizationPositionRelation> relationList = ceoBusinessOrganizationPositionRelationMapper.selectByEmployeeIdByLine(request.getAuditor(),RequestUtils.getBusinessGroup(),RequestUtils.getLoginInfo().getPositionTypeId());
        if (relationList == null || relationList.isEmpty()) {
            throw new ApplicationException("操作人岗位查询失败");
        }
//        operatorPositionId = relationList.get(0).getPositionId();
        operatorUserName = relationList.get(0).getEmployeeName();
        operatorEmployeeId = relationList.get(0).getEmployeeId();
        request.setAuditorName(operatorUserName);
        request.setAuditorIdentity(ceoBusinessOrganizationPositionRelationMapper.queryPositionTypeById(RequestUtils.getLoginInfo().getPositionTypeId()));

        //审核通过，且当前审核的节点是城市经理
        if(request.getCustomerStatus() == 2 && RequestUtils.getLoginInfo().getPositionTypeId().equals(10) && operatorEmployeeId.equals(request.getAuditor())) {
            //新增节点(区域总监及以上)
            SfaCustomerInfoVerifyDetail newInfoVerifyDetail = new SfaCustomerInfoVerifyDetail();
            newInfoVerifyDetail.setVerifyId(infoVerify.getId());
            newInfoVerifyDetail.setPrevId(infoVerifyDetail.getId());
            newInfoVerifyDetail.setCreateTime(LocalDateTime.now());
            sfaCustomerInfoVerifyDetailMapper.insert(newInfoVerifyDetail);

            //更新第一个节点(城市经理)
            infoVerifyDetail.setNextId(newInfoVerifyDetail.getId());
//            infoVerifyDetail.setAuditPositionId(operatorPositionId);
            infoVerifyDetail.setAuditUserName(operatorUserName);
            infoVerifyDetail.setReason(request.getRejectReason());
            infoVerifyDetail.setProcessTime(LocalDateTime.now());
            sfaCustomerInfoVerifyDetailMapper.updateById(infoVerifyDetail);

            //更新流程表process
            infoVerify.setResult(0);
            infoVerify.setDetailId(newInfoVerifyDetail.getId());
            infoVerify.setUpdateTime(LocalDateTime.now());
            infoVerify.setProcessStep(1);//下一个节点
            sfaCustomerInfoVerifyMapper.updateById(infoVerify);
        }else {//此次审批结束审批流程
            int processStep = getProcessStepByAuditor(RequestUtils.getLoginInfo().getPositionTypeId());
            if(request.getCustomerStatus() == 2){
                //更新流程表
                infoVerify.setResult(1);
                infoVerify.setUpdateTime(LocalDateTime.now());
                infoVerify.setProcessStep(processStep);
                sfaCustomerInfoVerifyMapper.updateById(infoVerify);

                //更新详情表
//                infoVerifyDetail.setAuditPositionId(operatorPositionId);
                infoVerifyDetail.setAuditUserName(operatorUserName);
                infoVerifyDetail.setReason(request.getRejectReason());
                infoVerifyDetail.setProcessTime(LocalDateTime.now());
                sfaCustomerInfoVerifyDetailMapper.updateById(infoVerifyDetail);
            }else {
                //更新流程表
                infoVerify.setResult(2);
                infoVerify.setUpdateTime(LocalDateTime.now());
                infoVerify.setProcessStep(processStep);
                sfaCustomerInfoVerifyMapper.updateById(infoVerify);

                //更新详情表
//                infoVerifyDetail.setAuditPositionId(operatorPositionId);
                infoVerifyDetail.setAuditUserName(operatorUserName);
                infoVerifyDetail.setReason(request.getRejectReason());
                infoVerifyDetail.setProcessTime(LocalDateTime.now());
                sfaCustomerInfoVerifyDetailMapper.updateById(infoVerifyDetail);
            }
            if (infoVerify.getResult() != 0) {//驳回
                connectorUtil.customerAudit(request);
            }

        }
    }

    //根据审批人设置processStep
    private int getProcessStepByAuditor(Integer positionType) {
        switch (positionType){
            case 2 :
                return 1;
            case 11 :
            case 12  :
                return 2;
            case 1  :
                return 3;
            default:
                return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public void customerInfoAudit1(CustomerAuditRequest request) {
        log.info("CustomerAuditRequest {}", request);
        SfaCustomerInfoVerify infoVerify = sfaCustomerInfoVerifyMapper.selectOne(new QueryWrapper<SfaCustomerInfoVerify>().eq("customer_id", request.getCustomerId()).eq("delete_flag",0));
        //判断是否新增
        if(Objects.isNull(infoVerify)) {
            CustomerInfo customerInfo = customerInfoMapper.selectOne(new QueryWrapper<CustomerInfo>().eq("customer_id",request.getCustomerId()).eq("delete_flag",0));
            if(Objects.isNull(customerInfo)) {
                throw new ApplicationException("客户id不存在");
            }else if(customerInfo.getCustomerStatus() == 2 || customerInfo.getCustomerStatus() == 3) {
                throw new ApplicationException("当前客户已经审核过");
            }
            //生成申请节点，sfa_customer_info_verify和sfa_customer_info_verify_detail插入数据
            createApplyNode1(request, customerInfo);
            infoVerify = sfaCustomerInfoVerifyMapper.selectOne(new QueryWrapper<SfaCustomerInfoVerify>().eq("customer_id", request.getCustomerId()).eq("delete_flag",0));

        }else if(infoVerify.getResult() != 0) {
            throw new ApplicationException("当前客户已审批，请勿重新提交");
        }
        if(Objects.isNull(infoVerify)) {
            throw new ApplicationException("审核流程表获取失败");
        }
        SfaCustomerInfoVerifyDetail infoVerifyDetail = sfaCustomerInfoVerifyDetailMapper.selectById(infoVerify.getDetailId());
        if(Objects.isNull(infoVerifyDetail)) {
            throw new ApplicationException("待审核记录获取失败");
        }

        //操作人信息
        String operatorPositionId = null;
        String operatorUserName = null;
        CeoBusinessOrganizationPositionRelation relation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("employee_id", request.getAuditor()).eq("channel", RequestUtils.getChannel()));
        if (Objects.isNull(relation) || relation.getPositionId() == null) {
            throw new ApplicationException("操作人工号不正确");
        }
        //运营管理账号控制
        String customerAuditAccount = settingsMapper.getSfaSettingsByCode("customer_info_audit");
        boolean operatorAudit = customerAuditAccount != null && customerAuditAccount.contains(request.getAuditor()) ? true : false;

        if (relation.getPositionTypeId() == 1 || relation.getPositionTypeId()==2 || relation.getPositionTypeId()==10  || operatorAudit){
            operatorPositionId = relation.getPositionId();
            operatorUserName = relation.getEmployeeName();
        }

        log.info("进入审批流程==infoVerify:{},verifyDetail:{},{}",infoVerify.getProcessStep(), infoVerify.getResult());
       //现在只有两个选择 同意 ：驳回  customerStatus  2-审核通过、3 - 驳回
        if(request.getCustomerStatus() == 2){
            //更新流程表
            infoVerify.setResult(1);
            infoVerify.setUpdateTime(LocalDateTime.now());
            infoVerify.setProcessStep(3);
            sfaCustomerInfoVerifyMapper.updateById(infoVerify);

            //更新详情表
            infoVerifyDetail.setAuditPositionId(operatorPositionId);
            infoVerifyDetail.setAuditUserName(operatorUserName);
            infoVerifyDetail.setReason(request.getRejectReason());
            infoVerifyDetail.setProcessTime(LocalDateTime.now());
            sfaCustomerInfoVerifyDetailMapper.updateById(infoVerifyDetail);
        }else {
            //更新流程表
            infoVerify.setResult(2);
            infoVerify.setUpdateTime(LocalDateTime.now());
            infoVerify.setProcessStep(3);
            sfaCustomerInfoVerifyMapper.updateById(infoVerify);

            //更新详情表
            infoVerifyDetail.setAuditPositionId(operatorPositionId);
            infoVerifyDetail.setAuditUserName(operatorUserName);
            infoVerifyDetail.setReason(request.getRejectReason());
            infoVerifyDetail.setProcessTime(LocalDateTime.now());
            sfaCustomerInfoVerifyDetailMapper.updateById(infoVerifyDetail);
        }

        //运营管理通过调用原接口(驳回或者运营最终审核通过会调用接口)
        if ((infoVerify.getProcessStep() == 3 && infoVerify.getResult() == 1) || request.getCustomerStatus() == 3) {//驳回
            connectorUtil.customerAudit(request);
        }


    }

    @Override
    public IPage<DisplayCustomerApproveVO> queryDisplayCustomerApproveByPage(DisplayCustomerApproveRequest query) {
        log.info("陈列客户审核入参:{}", query);
        //操作人权限查询
        CeoBusinessOrganizationPositionRelation relation = ceoBusinessOrganizationPositionRelationMapper.selectByEmployeeId(query.getPerson());
        if (Objects.isNull(relation)) {
            throw new ApplicationException("操作人岗位查询失败");
        }
        //是否为运营管理
        String customerTransferAccount = settingService.getValue("customer_info_audit");
        boolean operatorAudit = customerTransferAccount != null && customerTransferAccount.contains(query.getPerson()) ? true : false;
        //分页
        Page<DisplayCustomerApproveVO> page = new Page<>(query.getPage(), query.getRows());
        //查询数据库
        List<DisplayCustomerApproveVO> list = customerInfoMapper.queryDisplayCustomerApprove(query);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(e ->{
                if ((relation.getPositionTypeId() == 1 || relation.getPositionTypeId() == 2 || relation.getPositionTypeId() == 10 || operatorAudit) && e.getCustomerStatus()== 0) {
                    e.setBShowBtn(true);
                }
            });

        }
        page.setRecords(list);
        page.setTotal(list.size());
        return page;
    }

    @Override
    public DisplayCustomerApproveDetailVO getDisplayCustomerApproveById(String displayCustomerId, String person) {
        log.info("陈列客户审核入参displayCustomerId:{},person:{}",displayCustomerId,person);
        //操作人权限查询
        CeoBusinessOrganizationPositionRelation relation = ceoBusinessOrganizationPositionRelationMapper.selectByEmployeeId(person);
        if (Objects.isNull(relation)) {
            throw new ApplicationException("操作人岗位查询失败");
        }
        //是否为运营管理
        String customerTransferAccount = settingService.getValue("customer_info_audit");
        boolean operatorAudit = customerTransferAccount != null && customerTransferAccount.contains(person) ? true : false;
        DisplayCustomerApproveDetailVO detailVO = new DisplayCustomerApproveDetailVO();
        DisplayCustomerApproveVO vo = customerInfoMapper.queryDisplayCustomerById(displayCustomerId);
        if ((relation.getPositionTypeId() == 1 || relation.getPositionTypeId() == 2 || operatorAudit ) && vo.getCustomerStatus()== 0) {
            vo.setBShowBtn(true);
        }
        CustomerBusinessVO business = dataMapper.getBusinessById(displayCustomerId, null);
        if (Objects.nonNull(business) && null != business.getPartnerMemberKey()) {
            List<String> regionCodeList = customerInformationMapper.getRegionListByMemberKey(business.getPartnerMemberKey());//合伙人key获取四级地code
            //根据四级地code换取上海市-闵行区-浦江镇
            List<String> regionList = Lists.newArrayList();
            regionCodeList.forEach(r -> {
                StringBuilder sb = new StringBuilder();
                setRegion(r, sb);
                regionList.add(sb.deleteCharAt(sb.length() - 1).toString());
            });
            business.setResponsibleArea(regionList);
        }
        List<DisplayApproveVO> approveVO = customerInfoMapper.queryDisplayVerifyById(displayCustomerId);
        if(!Objects.isNull(approveVO)){
            detailVO.setVerify(approveVO);
        }
        detailVO.setCustomerVo(vo);
        detailVO.setBusiness(business);
        return detailVO;
    }

    @Override
    public void displayApprove(DisplayCustomerRequest request) {
        log.info("陈列客户提审入参:{}",request);
        if (ObjectUtils.isNotEmpty(request)) {
            try {
                DisplayCustomerApproveVO displayCustomerApproveVO = customerInfoMapper.queryDisplayCustomerById(request.getCustomerId());
                if (Objects.isNull(displayCustomerApproveVO)) {
                    customerInfoMapper.insetDisplayCustomer(request);
                } else {
                    customerInfoMapper.updateDisplayCustomer(request);
                    List<SfaDisplayApproveVerify> displayApproveVerifyList = displayVerifyMapper.selectList(new QueryWrapper<SfaDisplayApproveVerify>().eq("customer_id", request.getCustomerId()));
                    if(ObjectUtils.isNotEmpty(displayApproveVerifyList)){
                        for (SfaDisplayApproveVerify sfaDisplayApproveVerify : displayApproveVerifyList) {
                            displayVerifyMapper.delete(new QueryWrapper<SfaDisplayApproveVerify>().eq("id",sfaDisplayApproveVerify.getId()));
                            displayVerifyDetailMapper.delete(new QueryWrapper<SfaDisplayApproveVerifyDetail>().eq("id",sfaDisplayApproveVerify.getDetailId()));
                        }
                   }
                }
            } catch (Exception e) {
                throw new ApplicationException("陈列客户提审失败");
            }
        }

    }

    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public void displayCustomerAudit(DisplayCustomerAuditRequest request) {
        log.info("陈列客户审批:{}",request);
        SfaDisplayApproveVerify verify = displayVerifyMapper.selectOne(new QueryWrapper<SfaDisplayApproveVerify>().eq("customer_id", request.getCustomerId()));
        if(Objects.isNull(verify)){
            DisplayCustomerApproveVO displayCustomerApproveVO = customerInfoMapper.queryDisplayCustomerById(request.getCustomerId());
            if(Objects.isNull(displayCustomerApproveVO)){
                throw new ApplicationException("客户id不存在");
            }
            //生成节点
            creatDisplayNode(request,displayCustomerApproveVO);
            verify = displayVerifyMapper.selectOne(new QueryWrapper<SfaDisplayApproveVerify>().eq("customer_id",request.getCustomerId()).eq("delete_flag",0));
        } else if (verify.getProcessResult() != 0) {
            throw new ApplicationException("当前客户已审批，请勿重新提交");
        }
        if(Objects.isNull(verify)){
            throw  new ApplicationException("陈列客户流程表获取失败");
        }
        SfaDisplayApproveVerifyDetail verifyDetail = displayVerifyDetailMapper.selectById(verify.getDetailId());
        if(Objects.isNull(verifyDetail)){
            throw  new ApplicationException("陈列客户详情表获取失败");
        }
        //操作人信息
        String operatorPositionId = null;
        String operatorUserName = null;
        CeoBusinessOrganizationPositionRelation relation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("employee_id", request.getAuditor()).eq("channel", RequestUtils.getChannel()));
        if (Objects.isNull(relation) || relation.getPositionId() == null) {
            throw new ApplicationException("操作人工号不正确");
        }
        //运营管理账号控制
        String customerAuditAccount = settingsMapper.getSfaSettingsByCode("customer_info_audit");
        boolean operatorAudit = customerAuditAccount != null && customerAuditAccount.contains(request.getAuditor()) ? true : false;

        if (relation.getPositionTypeId() == 1 || relation.getPositionTypeId() == 2  || operatorAudit) {
            operatorPositionId = relation.getPositionId();
            operatorUserName = relation.getEmployeeName();
        }
        //审核通过 2  审核驳回 3
        if (request.getCustomerStatus() == 2) {
            verify.setProcessResult(2);
        } else {
            verify.setProcessResult(3);
        }
        //更新流程
        verify.setUpdateTime(LocalDateTime.now());
        displayVerifyMapper.updateById(verify);
        //更新详情
        verifyDetail.setAuditPositionId(operatorPositionId);
        verifyDetail.setAuditUserName(operatorUserName);
        verifyDetail.setReason(request.getRejectReason());
        verifyDetail.setProcessTime(LocalDateTime.now());
        displayVerifyDetailMapper.updateById(verifyDetail);
        connectorUtil.displayCustomerAudit(request);

    }

    @Override
    public void displayExportList(DisplayCustomerApproveRequest query) {
        log.info("陈列客户审批列表导出入参:{}", query);
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletRequest request = servletRequestAttributes.getRequest();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        List<DisplayCustomerApproveVO> list = customerInfoMapper.queryDisplayCustomerApprove(query);
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), DisplayCustomerApproveVO.class, list);
        try {
            String fileName = "客户审核列表";
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    public CheckCustomerAuditor checkCustomerAuditor(String customerId) {
        CheckCustomerAuditor auditor = new CheckCustomerAuditor();
        CustomerInfoVO infoVO = customerInfoMapper.getByCustomerIdNew(customerId);
        ParentMessageVo parentMessage = customerInfoMapper.queryParentMessageAndGroup(infoVO.getCreator(),infoVO.getAuditProductGroupId());
        if(ObjectUtils.isEmpty(parentMessage)){
            throw  new ApplicationException("根据Creator查询上级失败");
        }
        SelectAuditDto selectAuditDto = new SelectAuditDto();
        selectAuditDto.setChannel(3);
        selectAuditDto.setBusinessGroup(parentMessage.getBusinessGroup());
        selectAuditDto.setCurrentOrganizationId(parentMessage.getOrganizationId());
        selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
        CeoBusinessOrganizationPositionRelation auditPerson = iAuditService.chooseAuditPerson(selectAuditDto);
        if(ObjectUtils.isEmpty(auditPerson)){
            throw  new ApplicationException("查询审核人员失败");
        }
        auditor.setAuditorName(auditPerson.getEmployeeName());
        auditor.setAuditorPositionType(ceoBusinessOrganizationPositionRelationMapper.queryPositionTypeById(auditPerson.getPositionTypeId()));
        return auditor;
    }


    public void  creatDisplayNode(DisplayCustomerAuditRequest request, DisplayCustomerApproveVO displayCustomerApproveVO){
        SfaDisplayApproveVerify approveVerify = new SfaDisplayApproveVerify();
        approveVerify.setCustomerId(request.getCustomerId());
        approveVerify.setProcessResult(0);
        approveVerify.setCreateTime(displayCustomerApproveVO.getCommitTime());
        displayVerifyMapper.insert(approveVerify);

        SfaDisplayApproveVerifyDetail verifyDetail = new SfaDisplayApproveVerifyDetail();
        verifyDetail.setVerifyId(approveVerify.getId());
        verifyDetail.setCreateTime(approveVerify.getCreateTime());
        verifyDetail.setProcessTime(LocalDateTime.now());
        displayVerifyDetailMapper.insert(verifyDetail);

        approveVerify.setDetailId(verifyDetail.getId());
        displayVerifyMapper.updateById(approveVerify);
    }

    public void createApplyNode1(CustomerAuditRequest request, CustomerInfo customerInfo) {
        SfaCustomerInfoVerify infoVerify = new SfaCustomerInfoVerify();
        infoVerify.setCustomerId(request.getCustomerId());
        infoVerify.setResult(0);//默认待审核
        infoVerify.setCreateTime(customerInfo.getCreateTime());
        int processStep = 0;//城市经理审核
        infoVerify.setProcessStep(processStep);
        sfaCustomerInfoVerifyMapper.insert(infoVerify);
        //构建审核详情表
        SfaCustomerInfoVerifyDetail infoVerifyDetail = new SfaCustomerInfoVerifyDetail();
        infoVerifyDetail.setVerifyId(infoVerify.getId());
        infoVerifyDetail.setCreateTime(infoVerify.getCreateTime());
        sfaCustomerInfoVerifyDetailMapper.insert(infoVerifyDetail);

        infoVerify.setDetailId(infoVerifyDetail.getId());
        sfaCustomerInfoVerifyMapper.updateById(infoVerify);
    }



    public List<CheckCustomerVO> getCustomerVerifyById1(String customerId) {
        log.info("getCustomerVerifyById: {}", customerId);
        List<CheckCustomerVO> retList = new ArrayList<>();
        SfaCustomerInfoVerify infoVerify = sfaCustomerInfoVerifyMapper.selectOne(new QueryWrapper<SfaCustomerInfoVerify>().eq("customer_id", customerId).eq("delete_flag",0));
        if (Objects.isNull(infoVerify)) {
            return null;
        }
        SfaCustomerInfoVerifyDetail verifyDetail = sfaCustomerInfoVerifyDetailMapper.selectById(infoVerify.getDetailId());
        if (Objects.isNull(verifyDetail)) {
            throw new ApplicationException("审核记录获取失败");
        }
        Integer employeeInfoId = customerInformationMapper.getEmployeeId(customerId);
        SfaEmployeeInfoModel infoModel = sfaEmployeeInfoMapper.selectById(employeeInfoId);

        //根据处理时间排序，获取审批通过或者驳回
        List<SfaCustomerInfoVerifyDetail> list = sfaCustomerInfoVerifyDetailMapper.selectList(new QueryWrapper<SfaCustomerInfoVerifyDetail>().eq("verify_id", verifyDetail.getVerifyId()).eq("delete_flag",0).orderByAsc("id"));
        if(!CollectionUtils.isEmpty(list)) {
            list.forEach(e->{
                CheckCustomerVO checkCustomerVO = new CheckCustomerVO();
                checkCustomerVO.setProcessTime(e.getProcessTime());
                checkCustomerVO.setAuditUserName(e.getAuditUserName());
                checkCustomerVO.setReason(e.getReason());
                //根据岗位信息获取审核人信息
//                CeoBusinessOrganizationPositionRelation relation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id", e.getAuditPositionId()));
//                if(relation.getPositionTypeId() ==1) {//大区
//                    checkCustomerVO.setRegion(infoModel.getAreaName());
//                }else if(relation.getPositionTypeId() ==7) {//运营审核
//                    checkCustomerVO.setRegion("总部");
//                }else if (relation.getPositionTypeId() == 12) { //虚拟大区
//                    checkCustomerVO.setRegion(infoModel.getAreaName());
//                    checkCustomerVO.setVarea(infoModel.getVareaOrganizationName());
//                }else if (relation.getPositionTypeId() == 11) { //省总监
//                    checkCustomerVO.setRegion(infoModel.getAreaName());
//                    checkCustomerVO.setVarea(infoModel.getVareaOrganizationName());
//                    checkCustomerVO.setProvince(infoModel.getProvinceOrganizationName());
//                }else if(relation.getPositionTypeId() == 2 ) {//分公司
//                    checkCustomerVO.setVarea(infoModel.getVareaOrganizationName());
//                    checkCustomerVO.setProvince(infoModel.getProvinceOrganizationName());
//                    checkCustomerVO.setCompany(infoModel.getCompanyName());
//                    checkCustomerVO.setRegion(infoModel.getAreaName());
//                }else if(relation.getPositionTypeId() ==10){ //区域经理
//                    checkCustomerVO.setVarea(infoModel.getVareaOrganizationName());
//                    checkCustomerVO.setProvince(infoModel.getProvinceOrganizationName());
//                    checkCustomerVO.setCompany(infoModel.getCompanyName());
//                    checkCustomerVO.setRegion(infoModel.getAreaName());
//                    checkCustomerVO.setDepartment(infoModel.getDepartmentName());
//                }
                    checkCustomerVO.setVarea(infoModel.getVareaOrganizationName());
                    checkCustomerVO.setProvince(infoModel.getProvinceOrganizationName());
                    checkCustomerVO.setCompany(infoModel.getCompanyName());
                    checkCustomerVO.setRegion(infoModel.getAreaName());
                    checkCustomerVO.setDepartment(infoModel.getDepartmentName());
                //审核意见(通过或驳回)
                retList.add(checkCustomerVO);
            });
        }
        //审核意见(通过或驳回)最后一个根据流程表，其它肯定是通过的
        //修改后只有一条审核记录
        for (int i = 0; i < retList.size(); i++) {
            CheckCustomerVO checkCustomerVO = retList.get(i);
            if(i != retList.size() - 1) {//之前节点肯定是通过的
                checkCustomerVO.setResult("通过");
            }else if(Objects.nonNull(checkCustomerVO.getProcessTime())) {
                String str = "待处理";
                if(infoVerify.getResult() == 1) {
                    str = "通过";
                }else if(infoVerify.getResult() == 2) {
                    str = "驳回";
                }
                checkCustomerVO.setResult(str);
            }
        }
        log.info("返回数据结果:{}",retList);
        return retList;
    }
}
