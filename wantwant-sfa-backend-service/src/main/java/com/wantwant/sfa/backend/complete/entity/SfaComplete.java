package com.wantwant.sfa.backend.complete.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_complete")
@ApiModel(value = "SfaComplete对象", description = "通关表")
public class SfaComplete extends CommonEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long ruleId;

    private Integer employeeInfoId;

    private Integer employeeStatus;

    private Integer completeStatus;

    private Integer attendanceId;

    private LocalDateTime completeTime;

    private String province;

    private String city;

    private String district;

    private String address;

    private String longitude;

    private String latitude;

    private String signUpPicUrl;

    private BigDecimal faceSimilarScore;

    private String image;

    private String imageName;

    private String auditEmployeeId;

    private Integer auditStatus;

    private Integer bizAuditStatus;

    private String reason;

    private Boolean timeout;

//    @TableField(exist = false)
//    @ApiModelProperty("审核人工号")
//    private String auditEmployeeName;

}
