package com.wantwant.sfa.backend.complete.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.arch.model.AccountModel;
import com.wantwant.sfa.backend.arch.request.SAccountRequest;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.businessGroup.request.BusinessGroupRequest;
import com.wantwant.sfa.backend.businessGroup.service.impl.BusinessGroupService;
import com.wantwant.sfa.backend.businessGroup.vo.BusinessGroupVo;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.complete.entity.SfaComplete;
import com.wantwant.sfa.backend.complete.entity.SfaCompleteRule;
import com.wantwant.sfa.backend.complete.entity.SfaCompleteRuleRange;
import com.wantwant.sfa.backend.complete.request.CompleteAuditRequest;
import com.wantwant.sfa.backend.complete.request.CompleteClockRequest;
import com.wantwant.sfa.backend.complete.request.CompleteDetailRequest;
import com.wantwant.sfa.backend.complete.request.CompleteListRequest;
import com.wantwant.sfa.backend.complete.request.CompleteRuleCommitRequest;
import com.wantwant.sfa.backend.complete.request.CompleteRuleListRequest;
import com.wantwant.sfa.backend.complete.request.CompleteRuleRevocationRequest;
import com.wantwant.sfa.backend.complete.request.CompleteTodayLatestRequest;
import com.wantwant.sfa.backend.complete.service.CompleteService;
import com.wantwant.sfa.backend.complete.vo.CompleteDetailVo;
import com.wantwant.sfa.backend.complete.vo.CompleteListVo;
import com.wantwant.sfa.backend.complete.vo.CompleteRuleListVo;
import com.wantwant.sfa.backend.complete.vo.CompleteTodayLatestListVo;
import com.wantwant.sfa.backend.complete.vo.CompleteTodayLatestVo;
import com.wantwant.sfa.backend.dict.request.DictCodeRequest;
import com.wantwant.sfa.backend.dict.service.DictCodeService;
import com.wantwant.sfa.backend.dict.vo.DictCodeVo;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.mapper.ApplyMemberMapper;
import com.wantwant.sfa.backend.mapper.AttendanceMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessPositionTypeMapper;
import com.wantwant.sfa.backend.mapper.EmployeeMapper;
import com.wantwant.sfa.backend.mapper.NotifyMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SettingsMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.arch.AccountMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.complete.SfaCompleteMapper;
import com.wantwant.sfa.backend.mapper.complete.SfaCompleteRuleMapper;
import com.wantwant.sfa.backend.mapper.complete.SfaCompleteRuleRangeMapper;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.model.attendanceTask.Attendance;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.util.AuthService;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.common.architecture.utils.StreamCommonUtil;
import com.wantwant.sfa.common.base.CommonConstant;
import com.wantwant.sfa.backend.common.OrganizationPositionRelationEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompleteServiceImpl extends ServiceImpl<SfaCompleteMapper, SfaComplete> implements CompleteService {


    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private BusinessGroupService businessGroupService;

    @Autowired
    private SfaCompleteRuleMapper sfaCompleteRuleMapper;
    @Autowired
    private SfaCompleteRuleRangeMapper sfaCompleteRuleRangeMapper;
    @Autowired
    private SfaCompleteMapper sfaCompleteMapper;
    @Autowired
    private AttendanceMapper attendanceMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Autowired
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private CeoBusinessPositionTypeMapper ceoBusinessPositionTypeMapper;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private NotifyMapper notifyMapper;
    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private SettingsMapper settingsMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Autowired
    private AuthService authService;

    @Autowired
    private ApplyMemberMapper applyMemberMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void completeRuleCommit(CompleteRuleCommitRequest request) {
        log.info("completeRuleCommit request:{}", request);

        checkRole(request.getPerson(), "没有发起通关权限");

        String employeeName = employeeMapper.getEmployeeNameByEmployeeIdFor123(request.getPerson(), RequestUtils.getChannel());

        // 当前时间
        LocalDateTime nowDateTime = LocalDateTime.now();

        // 开始时间
        LocalDateTime completeStartTime = request.getCompleteStartTime();
        if (completeStartTime.isBefore(nowDateTime)) {
            completeStartTime = nowDateTime.withSecond(0).withNano(0);
        }
        LocalDate completeStartDate = completeStartTime.toLocalDate();

        // 结束时间
        LocalDateTime completeEndTime = request.getCompleteEndTime();
        LocalDate completeEndDate = completeEndTime.toLocalDate();

        if (!completeEndTime.isAfter(nowDateTime)) {
            throw new ApplicationException("通关结束时间已过，不能发起通关");
        }
        if (completeStartTime.isAfter(completeEndTime)) {
            throw new ApplicationException("通关开始时间不能晚于结束时间");
        }
        if (!completeStartDate.isEqual(completeEndDate)) {
            throw new ApplicationException("通关时间不能跨日");
        }
        long minutes = Long.parseLong(settingsMapper.getSfaSettingsByCode("complete_interval_minutes"));
        if (completeStartTime.until(completeEndTime, ChronoUnit.MINUTES) < minutes) {
            throw new ApplicationException("通关间隔时间不能短于" + minutes + "分钟");
        }
        String completeNum = completeStartDate.toString().replace("-", "") + "01";
        SfaCompleteRule exists = sfaCompleteRuleMapper.selectOne(new LambdaQueryWrapper<SfaCompleteRule>()
                .eq(SfaCompleteRule::getCompleteDate, completeStartDate)
                .eq(SfaCompleteRule::getDeleteFlag, 0)
                .orderByDesc(SfaCompleteRule::getCompleteNum)
                .last("limit 1")
        );
        if (Objects.nonNull(exists)) {
            completeNum = String.valueOf(Long.parseLong(exists.getCompleteNum()) + 1);
        }

        SfaCompleteRule rule = new SfaCompleteRule();
        rule.init(request.getPerson(), employeeName, nowDateTime);
        rule.setCompleteNum(completeNum);
        rule.setCompleteDate(completeStartDate);
        rule.setCompleteStartTime(completeStartTime);
        rule.setCompleteEndTime(completeEndTime.plusMinutes(1));
        rule.setStatus(Boolean.TRUE);
        sfaCompleteRuleMapper.insert(rule);

        List<SfaBusinessGroupEntity> businessGroupList = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>()
                .eq(SfaBusinessGroupEntity::getDeleteFlag, 0)
                .eq(SfaBusinessGroupEntity::getStatus, 1)
                .ne(SfaBusinessGroupEntity::getId, 99)
        );
        for (SfaBusinessGroupEntity sfaBusinessGroupEntity : businessGroupList) {

            Map<String, List<CeoBusinessOrganizationEntity>> orgMap = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .eq(CeoBusinessOrganizationEntity::getChannel, 3)
                    .eq(CeoBusinessOrganizationEntity::getBusinessGroup, sfaBusinessGroupEntity.getId())
                    .ne(CeoBusinessOrganizationEntity::getOrganizationType, OrganizationTypeEnum.ZB.getOrganizationType())
                    .ne(CeoBusinessOrganizationEntity::getOrganizationType, "branch")
            ).stream().collect(Collectors.groupingBy(CeoBusinessOrganizationEntity::getOrganizationType));
            orgMap.forEach((organizationType, v) -> {
                List<String> orgCodes = v.stream().map(CeoBusinessOrganizationEntity::getOrganizationId).collect(Collectors.toList());
                String organizationCodes = String.join(",", orgCodes);
                SfaCompleteRuleRange range = new SfaCompleteRuleRange();
                range.init(request.getPerson(), employeeName, nowDateTime);
                range.setRuleId(rule.getId());
                range.setBusinessGroup(sfaBusinessGroupEntity.getId());
                range.setOrganizationType(organizationType);
                range.setOrganizationCode(organizationCodes);
                sfaCompleteRuleRangeMapper.insert(range);
            });
        }

    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void completeRuleRevocation(CompleteRuleRevocationRequest request) {
        log.info("completeRuleRevocation request: {}", request);

        String employeeName = employeeMapper.getEmployeeNameByEmployeeIdFor123(request.getPerson(), RequestUtils.getChannel());

        checkRole(request.getPerson(), "没有取消通关权限");

        SfaCompleteRule exists = sfaCompleteRuleMapper.selectOne(new LambdaQueryWrapper<SfaCompleteRule>()
                .eq(SfaCompleteRule::getCompleteNum, request.getCompleteNum())
                .eq(SfaCompleteRule::getStatus, Boolean.TRUE)
                .eq(SfaCompleteRule::getDeleteFlag, 0)
        );
        if (Objects.isNull(exists)) {
            throw new ApplicationException("通关编号不存在");
        }
        // 当前时间
        LocalDateTime nowDateTime = LocalDateTime.now();
        if (exists.getStartStatus()) {
            throw new ApplicationException("通关已开始，不能取消");
        }
        exists.setStatus(Boolean.FALSE);
        exists.update(request.getPerson(), employeeName, nowDateTime);
        sfaCompleteRuleMapper.updateById(exists);

    }

    @Override
    public IPage<CompleteRuleListVo> completeRuleList(CompleteRuleListRequest request) {
        log.info("completeRuleList request:{}", request);

        List<String> account30List = getAccountList();

        Page<CompleteRuleListVo> page = new Page<>(request.getPage(), request.getRows());
        List<CompleteRuleListVo> list = sfaCompleteRuleMapper.queryCompleteRuleList(page, request);
        list.forEach(rule -> {
            rule.setIntervalHours(rule.getCompleteStartTime().until(rule.getCompleteEndTime(), ChronoUnit.HOURS));
            rule.setIntervalMinutes(rule.getCompleteStartTime().until(rule.getCompleteEndTime(), ChronoUnit.MINUTES));
            rule.setCompleteTimePeriod(LocalDateTimeUtils.formatTime(rule.getCompleteStartTime(), "yyyy-MM-dd HH:mm") + "--" + LocalDateTimeUtils.formatTime(rule.getCompleteEndTime(), "HH:mm") + "(" + rule.getIntervalMinutes() + "分钟)");
            rule.setStatusName(!rule.getStartStatus() ? "未开始" : "已开始");
            rule.setBShowBtn1(!rule.getStartStatus() && account30List.contains(request.getPerson()));
        });
        page.setRecords(list);
        return page;
    }

    @Override
    public IPage<CompleteListVo> completeList(CompleteListRequest request) {
        log.info("completeList request:{}", request);

        Page<CompleteListVo> page = new Page<>(request.getPage(), request.getRows());

        List<String> account30List = getAccountList();
        if (Objects.nonNull(request.getType()) && request.getType() == 1) {
            if (!account30List.contains(request.getPerson())) {
                return page;
            } else {
                request.setCompleteStatus(DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_1);
                request.setAuditStatus(DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_0);
            }
        }

        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setTimezone(loginInfo.getTimezone());
        if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType()) && CommonUtil.StringUtils.isBlank(request.getOrganizationId())) {
            List<String> employeeOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
            if (CollectionUtils.isEmpty(employeeOrganizationIds)) {
                throw new ApplicationException(BizExceptionLanguageEnum.ORGANIZATION_INFO_NOT_EXIST_OPERATOR.getTextMsg());
            }
            request.setOrganizationIds(employeeOrganizationIds);
        }
        if (Objects.isNull(request.getBusinessGroup()) && !request.getIsAllBusinessGroup()) {
            request.setBusinessGroup(loginInfo.getBusinessGroup());
        }

        List<CompleteListVo> list = sfaCompleteMapper.queryCompleteList(page, request);

        if (!CollectionUtils.isEmpty(list)) {

            Map<String, String> auditEmployeeMap = new HashMap<>();
            Set<String> auditEmployeeIds = list.stream().map(CompleteListVo::getAuditEmployeeId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(auditEmployeeIds)) {
                List<CeoBusinessOrganizationPositionRelation> ceoBusinessOrganizationPositionRelationList = ceoBusinessOrganizationPositionRelationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                        .in(CeoBusinessOrganizationPositionRelation::getEmployeeId, auditEmployeeIds)
                        .eq(CeoBusinessOrganizationPositionRelation::getChannel, 3)
                        .groupBy(CeoBusinessOrganizationPositionRelation::getEmployeeId)
                );
                auditEmployeeMap = ceoBusinessOrganizationPositionRelationList.stream().collect(
                        Collectors.toMap(CeoBusinessOrganizationPositionRelation::getEmployeeId, CeoBusinessOrganizationPositionRelation::getEmployeeName));
            }

//            Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
//                    .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));

            Map<Integer, String> businessGroupMap = businessGroupService.getBusinessGroupList(new BusinessGroupRequest())
                    .stream().collect(Collectors.toMap(BusinessGroupVo::getBusinessGroup, BusinessGroupVo::getBusinessGroupName));

            Map<Integer, DictCodeVo> completeStatusMap = StreamCommonUtil.toMap(dictCodeService.getCodeItem(DictCodeRequest.builder().classCd(DictCodeConstants.CLASSCD_COMPLETE_STATUS).build()),
                    model -> Integer.valueOf(model.getItemValue()));

            Map<Integer, DictCodeVo> completeAuditStatusMap = StreamCommonUtil.toMap(dictCodeService.getCodeItem(DictCodeRequest.builder().classCd(DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS).build()),
                    model -> Integer.valueOf(model.getItemValue()));

            Map<Integer, DictCodeVo> completeEmployeeStatusMap = StreamCommonUtil.toMap(dictCodeService.getCodeItem(DictCodeRequest.builder().classCd(DictCodeConstants.CLASSCD_COMPLETE_EMPLOYEE_STATUS).build()),
                    model -> Integer.valueOf(model.getItemValue()));

            String language = RequestUtils.getLoginInfo().getLanguage();

            Map<String, String> finalAuditEmployeeMap = auditEmployeeMap;
            list.stream().forEach(complete -> {

                complete.setBusinessGroupName(businessGroupMap.get(complete.getBusinessGroup()));
//                complete.setPositionName(positionTypeMap.get(complete.getPositionTypeId()));
                complete.setIntervalHours(complete.getCompleteStartTime().until(complete.getCompleteEndTime(), ChronoUnit.HOURS));
                complete.setIntervalMinutes(complete.getCompleteStartTime().until(complete.getCompleteEndTime(), ChronoUnit.MINUTES));
                complete.setCompleteTimePeriod(LocalDateTimeUtils.formatTime(complete.getCompleteStartTime(), "yyyy-MM-dd HH:mm")
                        + "--" + LocalDateTimeUtils.formatTime(complete.getCompleteEndTime(), "HH:mm") + "(" + complete.getIntervalMinutes() + ComonLanguageEnum.MINUTES.getDesc() + ")");
                if (completeStatusMap.containsKey(complete.getCompleteStatus())) {
                    complete.setCompleteStatusName(CommonConstant.LANGUAGE_CHINESE.equals(language) ? completeStatusMap.get(complete.getCompleteStatus()).getItemContent() : completeStatusMap.get(complete.getCompleteStatus()).getItemInfo());
                }
                if (completeEmployeeStatusMap.containsKey(complete.getEmployeeStatus())) {
                    complete.setEmployeeStatusName(CommonConstant.LANGUAGE_CHINESE.equals(language) ? completeEmployeeStatusMap.get(complete.getEmployeeStatus()).getItemContent() : completeEmployeeStatusMap.get(complete.getEmployeeStatus()).getItemInfo());
                }
                if (completeAuditStatusMap.containsKey(complete.getAuditStatus())) {
                    complete.setAuditStatusName(CommonConstant.LANGUAGE_CHINESE.equals(language) ? completeAuditStatusMap.get(complete.getAuditStatus()).getItemContent() : completeAuditStatusMap.get(complete.getAuditStatus()).getItemInfo());
                }
                complete.setPositionName(OrganizationPositionRelationEnums.getPositionNameByEnv(complete.getPositionName()));
                complete.setAuditEmployeeName(finalAuditEmployeeMap.get(complete.getAuditEmployeeId()));
                complete.setBShowBtn1(DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_1.equals(complete.getCompleteStatus())
                        && DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_0.equals(complete.getAuditStatus())
                        && account30List.contains(request.getPerson()));

            });
        }

        page.setRecords(list);
        return page;
    }

    @Override
    public List<CompleteTodayLatestListVo> completeList1(CompleteTodayLatestRequest request) {
        log.info("completeList1 request:{}", request);

        SfaEmployeeInfoModel sfaEmployeeInfoEntity = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getEmployeeId, request.getPerson()));
        if (Objects.isNull(sfaEmployeeInfoEntity)) {
            return null;
        }
        request.setEmployeeInfoId(sfaEmployeeInfoEntity.getId());
        if (Objects.isNull(request.getCompleteDate())) {
            request.setCompleteDate(LocalDate.now());
        }

        return sfaCompleteMapper.queryCompleteTodayLatest(request);
    }

    @Override
    public CompleteTodayLatestVo completeTodayLatest(CompleteTodayLatestRequest request) {
        log.info("completeTodayLatest request:{}", request);

        CompleteTodayLatestVo detail = null;

        SfaEmployeeInfoModel sfaEmployeeInfoEntity = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getEmployeeId, request.getPerson()));
        if (Objects.isNull(sfaEmployeeInfoEntity)) {
            throw new ApplicationException(BizExceptionLanguageEnum.CLEARANCE_NO_PUNCH_PERMISSION.getTextMsg());
        }
        request.setEmployeeInfoId(sfaEmployeeInfoEntity.getId());
        if (Objects.isNull(request.getCompleteDate())) {
            request.setCompleteDate(LocalDate.now());
        }

        // type=1 即app弹窗获取，当前没有需要通关，返回null
        // other，即打卡页面：当前有需要通关，detail返回当前需要通关的那条；当前没有需要通关，detail返回当天第一条。总是有list返回当天所有
        boolean notNeedList = Objects.nonNull(request.getType()) && request.getType() == 1;

        request.setType(1);
        List<CompleteTodayLatestListVo> detailList = sfaCompleteMapper.queryCompleteTodayLatest(request);

        if (notNeedList) {
            if (!CollectionUtils.isEmpty(detailList)) {
                detail = new CompleteTodayLatestVo();
                BeanUtils.copyProperties(detailList.get(0), detail);
                if (DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_0.equals(detail.getCompleteStatus())) {
                    LocalDateTime nowDateTime = LocalDateTime.now();
                    if (!nowDateTime.isBefore(detail.getCompleteStartTime()) && !nowDateTime.isAfter(detail.getCompleteEndTime())) {
                        detail.setBShowBtn1(true);
                    }
                }
            }
        } else {
            detail = new CompleteTodayLatestVo();
            if (!CollectionUtils.isEmpty(detailList)) {
                BeanUtils.copyProperties(detailList.get(0), detail);
                if (DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_0.equals(detail.getCompleteStatus())) {
                    LocalDateTime nowDateTime = LocalDateTime.now();
                    if (!nowDateTime.isBefore(detail.getCompleteStartTime()) && !nowDateTime.isAfter(detail.getCompleteEndTime())) {
                        detail.setBShowBtn1(true);
                    }
                }
            }

            request.setType(null);
            List<CompleteTodayLatestListVo> allList = sfaCompleteMapper.queryCompleteTodayLatest(request);
            if (!CollectionUtils.isEmpty(allList)) {
                if (CollectionUtils.isEmpty(detailList)) {
                    BeanUtils.copyProperties(allList.get(0), detail);
                }
                detail.setTodayList(allList);
            }
        }
        return detail;
    }

    @Override
    public CompleteDetailVo completeDetail(CompleteDetailRequest request) {
        log.info("completeDetail request:{}", request);

        CompleteDetailVo detail = sfaCompleteMapper.queryCompleteDetail(request);
        if (Objects.isNull(detail)) {
            throw new ApplicationException("通关编号不存在");
        } else {
            if (DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_0.equals(detail.getCompleteStatus()) && request.getPerson().equals(detail.getEmployeeId())) {
                LocalDateTime nowDateTime = LocalDateTime.now();
                if (!nowDateTime.isBefore(detail.getCompleteStartTime()) && !nowDateTime.isAfter(detail.getCompleteEndTime())) {
                    detail.setBShowBtn1(true);
                }
            }
        }
        return detail;
    }

    @Override
    public List<CompleteListVo> completeRecordList(CompleteListRequest request) {
        log.info("completeRecordList request:{}", request);
        return sfaCompleteMapper.queryCompleteList(request);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void completeClock(CompleteClockRequest request) {
        log.info("completeClock request: {}", request);

        SfaEmployeeInfoModel sfaEmployeeInfoEntity = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getEmployeeId, request.getPerson()));
        if (Objects.isNull(sfaEmployeeInfoEntity)) {
            throw new ApplicationException(BizExceptionLanguageEnum.CLEARANCE_NO_PUNCH_PERMISSION.getTextMsg());
        }
        request.setEmployeeInfoId(sfaEmployeeInfoEntity.getId());

        CompleteDetailRequest detailRequest = new CompleteDetailRequest();
        BeanUtils.copyProperties(request, detailRequest);
        CompleteDetailVo detail = sfaCompleteMapper.queryCompleteDetail(detailRequest);
        if (Objects.isNull(detail)) {
            throw new ApplicationException(BizExceptionLanguageEnum.CLEARANCE_NOT_EXIST.getTextMsg());
        }
        if (!DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_0.equals(detail.getCompleteStatus())) {
            throw new ApplicationException(BizExceptionLanguageEnum.CLEARANCE_STATUS_NOT_ALLOW_PUNCH.getTextMsg());
        }
        LocalDateTime nowDateTime = LocalDateTime.now();
        if (nowDateTime.isBefore(detail.getCompleteStartTime()) || nowDateTime.isAfter(detail.getCompleteEndTime())) {
            throw new ApplicationException(BizExceptionLanguageEnum.CLEARANCE_NOT_IN_TIME_PERIOD.getTextMsg());
        }
        LocalDate nowDate = nowDateTime.toLocalDate();
        Integer nowYear = nowDateTime.getYear();
        Integer nowMonth = nowDateTime.getMonthValue();
        Integer nowDay = nowDateTime.getDayOfMonth();

        Attendance attendance = new Attendance();
        attendance.setCalendarDate(nowDate);
        attendance.setCalendarYear(nowYear);
        attendance.setCalendarMonth(nowMonth);
        attendance.setCalendarDay(nowDay);
        attendance.setWorkDayType(0);
        attendance.setEmployeeInfoId(sfaEmployeeInfoEntity.getId());
        attendance.setEmployeeId(sfaEmployeeInfoEntity.getEmployeeId());
        attendance.setEmployeeName(sfaEmployeeInfoEntity.getEmployeeName());
        attendance.setAttendanceType(DictCodeConstants.CLASSCD_ATTENDANCE_TYPE_ITEMVALUE_3);
        attendance.setAttendanceStatus(0);
        attendance.setAttendanceTime(nowDateTime);
        attendance.setProvince(request.getProvince());
        attendance.setCity(request.getCity());
        attendance.setDistrict(request.getDistrict());
        attendance.setStreet(request.getAddress());
        attendance.setLongitude(request.getLongitude());
        attendance.setLatitude(request.getLatitude());
        attendance.setPicUrl(request.getImage());
        attendance.setPicName(request.getImageName());
        attendance.setCreateType(0);
        attendance.setCreatePerson(sfaEmployeeInfoEntity.getEmployeeId());
        attendance.setCreateTime(nowDateTime);
        attendance.setUpdatePerson(sfaEmployeeInfoEntity.getEmployeeId());
        attendance.setUpdatedTime(nowDateTime);
        attendanceMapper.insert(attendance);

        SfaComplete sfaComplete = sfaCompleteMapper.selectById(detail.getId());
        sfaComplete.update(sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
        sfaComplete.setCompleteStatus(DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_1);
        sfaComplete.setAttendanceId(attendance.getId());
        sfaComplete.setCompleteTime(nowDateTime);
        sfaComplete.setProvince(request.getProvince());
        sfaComplete.setCity(request.getCity());
        sfaComplete.setDistrict(request.getDistrict());
        sfaComplete.setAddress(request.getAddress());
        sfaComplete.setLongitude(request.getLongitude());
        sfaComplete.setLatitude(request.getLatitude());
        sfaComplete.setImage(request.getImage());
        sfaComplete.setImageName(request.getImageName());
        sfaComplete.setAuditStatus(DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_0);
        sfaComplete.setBizAuditStatus(DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_0);
        if (Objects.nonNull(sfaEmployeeInfoEntity.getApplicationId())) {
            ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoEntity.getApplicationId());
            if (Objects.nonNull(applyMemberPo)) {
                sfaComplete.setSignUpPicUrl(applyMemberPo.getPicUrl());
            }
        }
        sfaCompleteMapper.updateById(sfaComplete);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void completeAudit(CompleteAuditRequest request) {
        log.info("completeAudit request:{}", request);

        checkRole(request.getPerson(), BizExceptionLanguageEnum.CLEARANCE_NO_AUDIT_PERMISSION.getTextMsg());

        String employeeName = employeeMapper.getEmployeeNameByEmployeeIdFor123(request.getPerson(), RequestUtils.getChannel());

        CompleteDetailVo detail = sfaCompleteMapper.queryCompleteDetailById(request.getId());

        if (!request.getCompleteNum().equals(detail.getCompleteNum())) {
            throw new ApplicationException(BizExceptionLanguageEnum.CLEARANCE_CODE_NOT_EXIST.getTextMsg());
        }
        if (!(DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_1.equals(detail.getCompleteStatus()) && DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_0.equals(detail.getAuditStatus()))) {
            throw new ApplicationException(BizExceptionLanguageEnum.CLEARANCE_STATUS_NOT_ALLOW_AUDIT.getTextMsg());
        }
        SfaComplete sfaComplete = sfaCompleteMapper.selectById(detail.getId());
        sfaComplete.update(request.getPerson(), employeeName);
        sfaComplete.setAuditEmployeeId(request.getPerson());
        sfaComplete.setAuditStatus(request.getAuditStatus());
        sfaComplete.setReason(request.getReason());
        sfaCompleteMapper.updateById(sfaComplete);

        if (DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_2.equals(request.getAuditStatus())) {
            NotifyPO po = new NotifyPO();
            String formatContent = MessageFormat.format("{0}的通关打卡稽核异常，请查看！", detail.getEmployeeName());
            po.setTitle(formatContent);
            po.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
            po.setContent(formatContent);
            po.setCode(MessageFormat.format("/PassLevelList?employeeInfo={0}&completeNum={1}&auditStatus={2}", detail.getEmployeeId(), detail.getCompleteNum(), request.getAuditStatus()));
            po.setEmployeeId(detail.getEmployeeId());
            po.setCreateTime(LocalDateTime.now());
            po.setCreateBy("-1");
            po.setUpdateTime(LocalDateTime.now());
            po.setUpdateBy("-1");
            notifyMapper.insert(po);
        }
    }

    @Override
    public BigDecimal faceSimilar(String url) {
        BigDecimal score = null;
        SfaComplete complete = sfaCompleteMapper.selectOne(new LambdaQueryWrapper<SfaComplete>().eq(SfaComplete::getImage, url).eq(SfaComplete::getDeleteFlag, 0));
        if (Objects.nonNull(complete) && Objects.nonNull(complete.getSignUpPicUrl()) && Objects.nonNull(complete.getImage())) {
            String faceScore = authService.compareFace(complete.getSignUpPicUrl(), complete.getImage());
            if (Objects.nonNull(faceScore)) {
                score = new BigDecimal(faceScore);
                complete.setFaceSimilarScore(score);
                sfaCompleteMapper.updateById(complete);
            }
        }
        return score;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAudit(List<CompleteAuditRequest> list) {
        CompleteAuditRequest item = list.get(0);
        checkRole(item.getPerson(), BizExceptionLanguageEnum.CLEARANCE_NO_AUDIT_PERMISSION.getTextMsg());
        String employeeName = employeeMapper.getEmployeeNameByEmployeeIdFor123(item.getPerson(), RequestUtils.getChannel());
        // list 根据ID 去重复
        list = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CompleteAuditRequest::getId))), ArrayList::new));
        for (CompleteAuditRequest request : list) {

            CompleteDetailVo detail = sfaCompleteMapper.queryCompleteDetailById(request.getId());
            if (Objects.isNull(detail)){
                throw new ApplicationException(BizExceptionLanguageEnum.CLEARANCE_NOT_EXIST.getTextMsg() + ",ID："+request.getId());
            }
            if (!request.getCompleteNum().equals(detail.getCompleteNum())) {
                throw new ApplicationException(BizExceptionLanguageEnum.CLEARANCE_CODE_NOT_EXIST.getTextMsg() + ",ID："+request.getId());
            }
            if (!(DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_1.equals(detail.getCompleteStatus()) && DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_0.equals(detail.getAuditStatus()))) {
                throw new ApplicationException(BizExceptionLanguageEnum.CLEARANCE_STATUS_NOT_ALLOW_AUDIT.getTextMsg() + ",ID："+request.getId());
            }
            SfaComplete sfaComplete = sfaCompleteMapper.selectById(detail.getId());
            sfaComplete.update(request.getPerson(), employeeName);
            sfaComplete.setAuditEmployeeId(request.getPerson());
            sfaComplete.setAuditStatus(request.getAuditStatus());
            sfaComplete.setReason(request.getReason());
            sfaCompleteMapper.updateById(sfaComplete);

            if (DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_2.equals(request.getAuditStatus())) {
                NotifyPO po = new NotifyPO();
                String formatContent = MessageFormat.format("{0}的通关打卡稽核异常，请查看！", detail.getEmployeeName());
                po.setTitle(formatContent);
                po.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
                po.setContent(formatContent);
                po.setCode(MessageFormat.format("/PassLevelList?employeeInfo={0}&completeNum={1}&auditStatus={2}", detail.getEmployeeId(), detail.getCompleteNum(), request.getAuditStatus()));
                po.setEmployeeId(detail.getEmployeeId());
                po.setCreateTime(LocalDateTime.now());
                po.setCreateBy("-1");
                po.setUpdateTime(LocalDateTime.now());
                po.setUpdateBy("-1");
                notifyMapper.insert(po);
            }
        }
    }

    @Override
    public List<String> getCompleteNumList(CompleteListRequest request) {
        request.setRows(10000);
        IPage<CompleteListVo>  page= this.completeList(request);
        if (page==null||CollectionUtils.isEmpty(page.getRecords())){
            return Collections.emptyList();
        }
        return page.getRecords().stream().map(CompleteListVo::getCompleteNum).distinct().collect(Collectors.toList());
    }

    private void checkRole(String person, String msg) {
        List<String> account30List = getAccountList();
        if (!account30List.contains(person)) {
            throw new ApplicationException(msg);
        }
    }

    private List<String> getAccountList() {
        SAccountRequest accountRequest = new SAccountRequest();
        accountRequest.setRows(1000);
        accountRequest.setRoleId(30);
        List<AccountModel> accountList = accountMapper.selectList(accountRequest, RequestUtils.getChannel(), RequestUtils.getBusinessGroup());
        return accountList.stream().map(AccountModel::getEmployeeId).collect(Collectors.toList());
    }

}
