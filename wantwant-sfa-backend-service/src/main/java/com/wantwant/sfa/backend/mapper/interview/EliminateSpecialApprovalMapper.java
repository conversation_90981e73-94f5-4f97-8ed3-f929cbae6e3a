package com.wantwant.sfa.backend.mapper.interview;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wantwant.sfa.backend.interview.entity.EliminateSpecialApprovalEntity;
import com.wantwant.sfa.backend.replacing.vo.ReplacingWarningSpecialApprovalVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/18/下午3:50
 */
public interface EliminateSpecialApprovalMapper extends BaseMapper<EliminateSpecialApprovalEntity> {
    int selectSpecialApproval(@Param("employeeInfoId") String employeeInfoId);

    /**
     * 检查考核周期内是否有汰换
     *
     * @param employeeInfoId
     * @param currentQuarterMonths
     * @return
     */
    Integer checkSpecialApprovalDuringEvaluationPeriod(@Param("employeeInfoId") Integer employeeInfoId, @Param("currentQuarterMonths") List<String> currentQuarterMonths);

    List<ReplacingWarningSpecialApprovalVo> selectSpecialApprovalList(@Param("employeeInfoId") Integer employeeInfoId);
}
