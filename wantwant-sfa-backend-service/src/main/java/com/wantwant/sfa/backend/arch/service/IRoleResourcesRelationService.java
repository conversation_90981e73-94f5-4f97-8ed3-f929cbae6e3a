package com.wantwant.sfa.backend.arch.service;

import com.wantwant.sfa.backend.arch.request.MenuModuleRequest;
import com.wantwant.sfa.backend.arch.vo.ModuleVo;
import com.wantwant.sfa.backend.arch.vo.ResourcesListVo;
import com.wantwant.sfa.backend.arch.vo.ResourcesVo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/20/下午2:20
 */
public interface IRoleResourcesRelationService {
    /**
     * 绑定资源
     *
     * @param id
     * @param resources
     * @param personInfo
     */
    void bindResources(Integer id, List<Integer> resources, CeoBusinessOrganizationPositionRelation personInfo);

    /**
     * 返回资源列表
     *
     * @param terminal
     * @param roleId
     * @return
     */
    List<ResourcesVo> getResources(Integer terminal, Integer roleId);
    /**
     * 返回资源-包含app和pc
     * @return
     */
    List<ResourcesListVo> getResourcesList();

    /**
     * 获取菜单模块
     *
     * @param person
     * @param key
     * @return
     */
    List<ModuleVo> getMenuModule(MenuModuleRequest request);

    /**
     * 获取提示数量
     *
     * @param person
     * @return
     */
    int getPromptCount(String person);

}
