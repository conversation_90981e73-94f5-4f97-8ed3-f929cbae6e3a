package com.wantwant.sfa.backend.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.customer.request.*;
import com.wantwant.sfa.backend.customer.vo.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户审批相关 service
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
public interface CustomerApproveService {

    IPage<CustomerApproveVO> queryCustomerApproveByPage1(CustomerApproveRequest query);
    /*
    * 根据组织和工号查询待审核的数量
    */
    int queryCustomerApprovePendingAuditCount(String organizationId, String person);
    
    /**
     * @description 统计可审核的客户数量（BShowBtn为true的数量）
     * <AUTHOR>
     * @date 2025-01-29 11:40
     **/
    Long countAuditableCustomers(LoginModel loginModel, String person);

    void exportList(CustomerApproveRequest query);

    CustomerApproveDetailVO getCustomerApproveById(String customerId, String person);

    Boolean customerAudit(CustomerAuditRequest request);

    void customerInfoAudit(CustomerAuditRequest request);

    void customerInfoAudit1(CustomerAuditRequest request);

    IPage<DisplayCustomerApproveVO> queryDisplayCustomerApproveByPage(DisplayCustomerApproveRequest query);

    DisplayCustomerApproveDetailVO getDisplayCustomerApproveById(String displayCustomerId, String person);

    void displayApprove(DisplayCustomerRequest request);

    void displayCustomerAudit(DisplayCustomerAuditRequest request);

    void displayExportList(DisplayCustomerApproveRequest query);

    CheckCustomerAuditor checkCustomerAuditor(String customerId);
}
