package com.wantwant.sfa.backend.domain.businessBd.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 业务BD薪资历史表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_business_bd_salary_history")
public class BusinessBdSalaryHistoryPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属月份:yyyy-MM
     */
    private String theYearMonth;

    /**
     * 营业所ID
     */
    private String departmentId;

    /**
     * 分公司ID
     */
    private String companyId;

    /**
     * sfa_employee_info表主键
     */
    private Integer employeeInfoId;

    /**
     * 薪资对应岗位(sfa_employee_salary_structure的position)
     */
    private Integer salaryPosition;

    /**
     * 薪资方案等级AB
     */
    private String salaryLevel;

    /**
     * 标准底薪
     */
    private BigDecimal employeeBaseSalary;

    /**
     * 合伙人奖金
     */
    private BigDecimal employeeBonus;

    /**
     * 合伙人岗位津贴
     */
    private BigDecimal employeeAllowance;

    /**
     * 社保基数
     */
    private BigDecimal socialSecurityBase;

    /**
     * 差旅费
     */
    private BigDecimal travelExpenses;

    /**
     * 全风险服务费
     */
    private BigDecimal fullRiskFee;

    /**
     * 开始日期
     */
    private LocalDateTime startDate;

    /**
     * 结束日期
     */
    private LocalDateTime endDate;

    /**
     * 是否删除(1.是）
     */
    @TableLogic(value = "0",delval = "1")
    private Boolean deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}