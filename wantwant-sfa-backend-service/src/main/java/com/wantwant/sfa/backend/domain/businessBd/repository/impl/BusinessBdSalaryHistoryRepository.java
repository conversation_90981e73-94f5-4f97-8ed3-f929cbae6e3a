package com.wantwant.sfa.backend.domain.businessBd.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wantwant.sfa.backend.domain.businessBd.DO.OrgSalaryDO;
import com.wantwant.sfa.backend.domain.businessBd.DO.SalaryAllocatedDO;
import com.wantwant.sfa.backend.domain.businessBd.mapper.BusinessBdSalaryHistoryMapper;
import com.wantwant.sfa.backend.domain.businessBd.repository.facade.IBusinessBdSalaryHistoryRepository;
import com.wantwant.sfa.backend.domain.businessBd.repository.po.BusinessBdSalaryHistoryPO;
import com.wantwant.sfa.backend.salary.request.BusinessBDAllocatedRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDSalaryVo;
import com.wantwant.sfa.backend.salary.vo.SalaryAllocatedVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
public class BusinessBdSalaryHistoryRepository extends ServiceImpl<BusinessBdSalaryHistoryMapper, BusinessBdSalaryHistoryPO> implements IBusinessBdSalaryHistoryRepository {
    @Override
    @Transactional
    public void batchInsert(List<BusinessBdSalaryHistoryPO> businessBdSalaryHistoryPOS) {
        this.saveBatch(businessBdSalaryHistoryPOS, businessBdSalaryHistoryPOS.size());
    }

    @Override
    public List<OrgSalaryDO> selectHistoryTotalSalary(String theYearMonth) {
        return baseMapper.selectHistoryTotalSalary(theYearMonth);
    }

    @Override
    public void deleteByTheYearMonth(String theYearMonth) {
        baseMapper.delete(new LambdaQueryWrapper<BusinessBdSalaryHistoryPO>()
                .eq(BusinessBdSalaryHistoryPO::getTheYearMonth,theYearMonth)
                .eq(BusinessBdSalaryHistoryPO::getDeleteFlag,0)
        );
    }

    @Override
    public List<SalaryAllocatedDO> selectHistory(IPage<BusinessBDSalaryVo> page, BusinessBDAllocatedRequest businessBDAllocatedRequest) {
        return baseMapper.selectHistory(page,businessBDAllocatedRequest);
    }

    @Override
    public List<BusinessBdSalaryHistoryPO> selectByOrgIdAndDate(String companyId, String theYearMonth) {

        return baseMapper.selectList(new LambdaQueryWrapper<BusinessBdSalaryHistoryPO>()
                .eq(BusinessBdSalaryHistoryPO::getCompanyId,companyId)
                .eq(BusinessBdSalaryHistoryPO::getTheYearMonth,theYearMonth)
                .eq(BusinessBdSalaryHistoryPO::getDeleteFlag,0)
        );
    }

    @Override
    public List<BusinessBdSalaryHistoryPO> selectByOrgIdAndDateRange(String companyCode, String startYearMonth, String endYearMonth) {
        return baseMapper.selectList(new LambdaQueryWrapper<BusinessBdSalaryHistoryPO>()
                .eq(BusinessBdSalaryHistoryPO::getCompanyId,companyCode)
                .ge(BusinessBdSalaryHistoryPO::getTheYearMonth,startYearMonth)
                .le(BusinessBdSalaryHistoryPO::getTheYearMonth,endYearMonth)
                .eq(BusinessBdSalaryHistoryPO::getDeleteFlag,0)
        );
    }
}
