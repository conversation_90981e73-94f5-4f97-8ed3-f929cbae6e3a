package com.wantwant.sfa.backend.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wantwant.sfa.backend.domain.businessBd.DO.EmpSalaryDO;
import com.wantwant.sfa.backend.domain.businessBd.DO.OrgSalaryDO;
import com.wantwant.sfa.backend.interview.vo.SalaryStructureVo;
import com.wantwant.sfa.backend.marketAndPersonnel.request.SalaryQueryRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.request.SalaryUpdateRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.request.StructureUpdateRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.SalaryVO;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO;
import com.wantwant.sfa.backend.model.marketAndPersonnel.ManagerSalaryModel;
import com.wantwant.sfa.backend.salary.model.OrganizationUsedSalaryModel;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 合伙人薪资方案 服务类
 * @since 2022-04-20
 */
public interface EmployeeSalaryService extends IService<EmployeeSalaryPO> {

    IPage<SalaryVO> querySalaryPage(SalaryQueryRequest query);

    int updateSalary(Integer id, SalaryUpdateRequest request);

    List<String> importSalary(MultipartFile file, String updatedBy);

    List<String> importManagerSalary(MultipartFile file, String person, int type);

    List<ManagerSalaryModel> getManagerSalaryList(String month);

    void exportManagerSalaryList(String month, HttpServletResponse response);

    int updateSalaryByStructureId(Integer structureId,StructureUpdateRequest request);

    int addSalaryByEmpId(SfaEmployeeInfoModel sfaEmployeeInfoModel,Integer id, Integer structureId, String createdBy, LocalDate executeTime, Integer paymentType);

    /**
     * 特批薪资处理
     *
     * @param employeeInfoId
     * @return
     */
    int specialSalary(Integer employeeInfoId,String person);
    /**
     * 离职结束薪资方案
     *
     * @param employeeInfoId sfa_employee_info.id
     * @param offTime 离职时间
     * @return: void
     * @date: 9/1/22 3:18 PM
     */
    void updateSalaryByEmpId(Integer employeeInfoId, LocalDateTime offTime);

    List<SalaryStructureVo> selectSalaryList(String organizationId,int salaryPositionByCondition);


    SalaryStructureVo selectSalaryById(Integer salaryId);

    /**
     * 获取总薪资
     *
     * @param salaryStructureId
     * @return
     */
    BigDecimal selectTotalSalaryById(Integer salaryStructureId);

    /**
     * 查询所有在职业务BD的总薪资
     *
     * @param departmentCodes
     * @return
     */
    List<OrganizationUsedSalaryModel> selectOnBoardBusinessBDSalary(List<String> departmentCodes,List<Integer>excludeEmployeeInfoIds);

    /**
     * 获取异动中业务BD薪资
     *
     * @return
     */
    List<OrganizationUsedSalaryModel> selectPendingBusinessBDSalary(List<String> departmentCodes,Integer applyId);

    /**
     * 查询异动中的BD
     *
     * @param companyCodeList
     * @return
     */
    List<EmpSalaryDO> selectTransactionBD(List<String> companyCodeList,String theYearMonth);

    /**
     * 查询入职中的业务BD薪资
     *
     * @param deptCodes
     * @param theYearMonth
     * @param excludeApplyId
     * @return
     */
    List<OrgSalaryDO> selectOnBoardProcessingSalary(List<String> deptCodes, String theYearMonth, Integer excludeApplyId);

    /**
     * 获取异动中的人员薪资合计
     *
     * @param transactionEmpList
     * @return
     */
    List<OrgSalaryDO> selectTransferringSalary(List<EmpSalaryDO> transactionEmpList);

    /**
     * 根据截止日期获取当前有效的薪资
     *
     * @param workStartDate
     * @return
     */
    List<EmployeeSalaryPO> getValidSalaryByDeadline(LocalDate workStartDate);

    /**
     * 获取最低薪资级别
     *
     * @param organizationId
     * @param salaryPosition
     * @return
     */
    int getMinimumLevel(String organizationId, Integer salaryPosition);

    /**
     * 获取过滤后的薪资列表
     * 根据最低等级过滤薪资方案
     *
     * @param organizationId 组织ID
     * @param salaryPosition 薪资职位
     * @param minimumLevel 最低等级
     * @param fillSocialSecurityBase 是否填充社保基数
     * @return 过滤后的薪资方案列表
     */
    List<SalaryStructureVo> getFilteredSalaryList(String organizationId, int salaryPosition, int minimumLevel, boolean fillSocialSecurityBase);

    /**
     * 获取过滤后的薪资列表
     * 自动获取最低等级并过滤薪资方案
     *
     * @param organizationId 组织ID
     * @param salaryPosition 薪资职位
     * @param fillSocialSecurityBase 是否填充社保基数
     * @return 过滤后的薪资方案列表
     */
    List<SalaryStructureVo> getFilteredSalaryList(String organizationId, int salaryPosition, boolean fillSocialSecurityBase);

}
