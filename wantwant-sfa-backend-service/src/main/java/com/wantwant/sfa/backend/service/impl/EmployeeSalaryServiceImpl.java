package com.wantwant.sfa.backend.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.domain.businessBd.DO.EmpSalaryDO;
import com.wantwant.sfa.backend.domain.businessBd.DO.OrgSalaryDO;
import com.wantwant.sfa.backend.domain.flow.service.impl.GradeUtils;
import com.wantwant.sfa.backend.interview.vo.SalaryStructureVo;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryMapper;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryStructureMapper;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.ManagerSalaryMapper;
import com.wantwant.sfa.backend.marketAndPersonnel.request.*;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.SalaryVO;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.StructureVO;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.OrganizationModel;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryStructurePO;
import com.wantwant.sfa.backend.model.marketAndPersonnel.ManagerSalaryModel;
import com.wantwant.sfa.backend.salary.model.OrganizationUsedSalaryModel;
import com.wantwant.sfa.backend.salary.service.ISalaryMiddlewareService;
import com.wantwant.sfa.backend.service.EmployeeSalaryService;
import com.wantwant.sfa.backend.service.EmployeeSalaryStructureService;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.EasyPoiUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
* 合伙人薪资方案
*
* @since 2022-04-20
*/
@Service
@Slf4j
public class EmployeeSalaryServiceImpl extends ServiceImpl<EmployeeSalaryMapper, EmployeeSalaryPO> implements EmployeeSalaryService {

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private EmployeeMapper employeeMapper;

    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Resource
    private ApplyMemberMapper applyMemberMapper;

    @Resource
    private EmployeeSalaryStructureService structureService;

    @Autowired
    private EmployeeSalaryStructureMapper employeeSalaryStructureMapper;

    @Resource
    private ManagerSalaryMapper managerSalaryMapper;

    @Resource
    private SettingsMapper settingsMapper;

    @Autowired
    private ICheckCustomerService checkCustomerService;

    @Resource
    private ISalaryMiddlewareService salaryMiddlewareService;



    /**
     * 分页合伙人薪资方案
     *
     * @param query
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.wantwant.sfa.backend.marketAndPersonnel.vo.SalaryVO>
     * @date: 4/21/22 11:23 AM
     */
    @Override
    public Page<SalaryVO> querySalaryPage(SalaryQueryRequest query) {
        Page<SalaryVO> page = new Page<>(query.getPage(), query.getRows());
        List<SalaryVO> list = baseMapper.selectPageBySql(page, query);
        page.setRecords(list);
        return page;
    }

    /**
     * 根据ID修改合伙人薪资
     *
     * @param id
     * @param request
     * @return: int
     * @date: 4/21/22 2:37 PM
     */
    @Override
    public int updateSalary(Integer id, SalaryUpdateRequest request) {
        EmployeeSalaryPO po = new EmployeeSalaryPO();
        BeanUtils.copyProperties(request,po);
        if (request.getSalaryDescribe().startsWith("1")){
            EmployeeSalaryPO salaryPO = this.getById(id);
            //查询分公司底薪和奖金包
            EmployeeSalaryStructurePO structurePO = structureService.getById(salaryPO.getStructureId());
            if (Objects.isNull(structurePO)){
                throw new ApplicationException(salaryPO.getOrganizationId()+"分公司无可用底薪和奖金包!");
            }else {
                po.setEmployeeBaseSalary(structurePO.getBaseSalary());
                po.setEmployeeBonus(structurePO.getBonus());
            }
        }else{
            po.setEmployeeBonus(BigDecimal.ZERO);
        }
        po.setId(id);
        po.setUpdatedTime(LocalDateTime.now());
        return baseMapper.updateById(po);
    }

    /**
     * 批量导入合伙人薪资
     *
     * <AUTHOR>
     * @date 4/21/22 2:39 PM
     * @version 1.0
     */
    @Override
    public List<String> importSalary(MultipartFile file, String updatedBy) {
        ImportParams params = new ImportParams();
        List<String> res = Lists.newArrayList();
        List<SalaryImportDTO> list = Lists.newArrayList();
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SalaryImportDTO.class, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CommonUtil.ListUtils.isEmpty(list)){
                throw new ApplicationException("请添加数据！");
            }
            Set<String> companyNames = list.stream().map(SalaryImportDTO::getCompany).collect(Collectors.toSet());
            //分公司name换取id
            Map<String, OrganizationModel> companyMap = organizationMapper.getOrganization(companyNames, RequestUtils.getChannel());
            if (MapUtils.isEmpty(companyMap)){
                throw new ApplicationException("未找到对应分公司信息！");
            }
            Set<String> empMobile = list.stream().map(SalaryImportDTO::getMobile).collect(Collectors.toSet());
            //员工name换取id
            Map<String, SfaEmployeeInfoModel> empMap = employeeMapper.getEmployee(empMobile,RequestUtils.getChannel());
            if (MapUtils.isEmpty(empMap)){
                throw new ApplicationException("未找到对应员工信息！");
            }
        try {
            for(SalaryImportDTO s : list){
                EmployeeSalaryPO p = new EmployeeSalaryPO();
                BeanUtils.copyProperties(s,p);
                p.setUpdatedBy(updatedBy);
                p.setPosition(1);
                if (!"1-全职".equals(s.getSalaryDescribe()) && !"2-仅有底薪".equals(s.getSalaryDescribe())
                        && !"3-签约旺旺".equals(s.getSalaryDescribe()) && !"4-兼职".equals(s.getSalaryDescribe())){
                    res.add("合伙人:"+s.getEmployeeName()+",导入失败,错误信息:" + "薪资方案格式有误！");
                    continue;
                }
                try {
                    p.setOrganizationId(companyMap.get(s.getCompany()).getOrganizationId());
                } catch (Exception e) {
                    res.add("合伙人:"+s.getEmployeeName()+",导入失败,错误信息:" + "请检查分公司！");
                }
                try {
                    p.setEmployeeInfoId(empMap.get(s.getMobile()).getId());
                } catch (Exception e) {
                    res.add("合伙人:"+s.getEmployeeName()+",导入失败,错误信息:" + "请检查手机号！");
                }
                try {
                    p.setStartDate(LocalDate.parse(s.getStartDate()+"01", DateTimeFormatter.ofPattern("yyyyMMdd")));
                } catch (Exception e) {
                    res.add("合伙人:"+s.getEmployeeName()+",导入失败,错误信息:" + "请修改日期格式为yyyyMM！");
                }
                //不能早于本月
                if (LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).isAfter(p.getStartDate())){
                    res.add("合伙人:"+s.getEmployeeName()+",导入失败,错误信息:" + "开始时间不能早于本月！");
                    continue;
                }
                //查询分公司底薪和奖金包
                StructureQueryRequest request = new StructureQueryRequest();
                request.setCompanyOrganizationId(p.getOrganizationId());
                request.setYyyyMM(DateTimeFormatter.ofPattern("yyyy-MM").format(p.getStartDate()));
                List<StructureVO> structureVOS = structureService.listStructure(request);
                if (CommonUtil.ListUtils.isEmpty(structureVOS)){
                    res.add("合伙人:"+s.getEmployeeName()+",导入失败,错误信息:" +s.getStartDate()+ "该分公司无可用底薪和奖金包！");
                    continue;
                }
                StructureVO structure = structureVOS.get(0);
                p.setStructureId(structure.getId());
                /**
                 * 1-底薪+奖金，自动匹配分公司标准底薪和标准绩效奖金包；
                 * 2-仅有底薪，取导入的底薪、奖金为0；
                 * 3-签约旺旺，取导入的底薪、奖金为0；
                 * 4-兼职，底薪及奖金均为0
                 */
                if (s.getSalaryDescribe().startsWith("1")){
                    p.setEmployeeBaseSalary(structure.getBaseSalary());
                    p.setEmployeeBonus(structure.getBonus());
                }else if (s.getSalaryDescribe().startsWith("2") || s.getSalaryDescribe().startsWith("3")){
                    p.setEmployeeBaseSalary(s.getEmployeeBaseSalary());
                    p.setEmployeeBonus(BigDecimal.ZERO);
                }else {
                    p.setEmployeeBaseSalary(BigDecimal.ZERO);
                    p.setEmployeeBonus(BigDecimal.ZERO);
                }
                //查询当月或之前未结束的数据
                EmployeeSalaryPO oldPo = baseMapper.selectIncompleteSalary(p.getEmployeeInfoId(),s.getStartDate());
                if (Objects.nonNull(oldPo)){
                    String startDateStr = DateTimeFormatter.ofPattern("yyyyMM").format(oldPo.getStartDate());
                    if (startDateStr.equals(s.getStartDate())){
                        //当月修改
                        p.setId(oldPo.getId());
                        p.setUpdatedTime(LocalDateTime.now());
                        baseMapper.updateById(p);
                    }else{
                        //结束上月,新增
                        oldPo.setEndDate(p.getStartDate().minusMonths(1));
                        oldPo.setUpdatedBy(updatedBy);
                        oldPo.setUpdatedTime(LocalDateTime.now());
                        baseMapper.updateById(oldPo);
                        p.setCreatedBy(updatedBy);
                        p.setCreatedTime(LocalDateTime.now());
                        baseMapper.insert(p);
                    }
                }else{
                    //新增
                    p.setCreatedBy(updatedBy);
                    p.setCreatedTime(LocalDateTime.now());
                    p.setUpdatedTime(LocalDateTime.now());
                    baseMapper.insert(p);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    /*
    *
    * 总监薪资导入
    */
    @Transactional
    @Override
    public List<String> importManagerSalary(MultipartFile file, String person, int type) {
        String hrAccounts = settingsMapper.getSfaSettingsByCode("zw_hr_auto_account");
        if(Objects.isNull(hrAccounts) || !hrAccounts.contains(person)) {
            throw new ApplicationException("请配置导入权限");
        }
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation relation = checkCustomerService.getPersonInfo(person,loginInfo);
        if(Objects.isNull(relation)) {
            throw new ApplicationException("未找到对应岗位人员");
        }
        ImportParams params = new ImportParams();
        List<ManagerSalaryImportDTO> list = Lists.newArrayList();
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), ManagerSalaryImportDTO.class, params);
        } catch (Exception e) {
            log.error("excel data transfer error");
            e.printStackTrace();
        }
        //清除month为空
        list = list.stream().filter(e-> CommonUtil.StringUtils.isNotEmpty(e.getMonth())).collect(Collectors.toList());
        if (CommonUtil.ListUtils.isEmpty(list)){
            throw new ApplicationException("请添加正确的数据！");
        }
        List<String> retList = new ArrayList<>();
        //检测list的月份是否一致
        ManagerSalaryImportDTO firstImportDTO = list.get(0);
        String month = firstImportDTO.getMonth();
        if(CommonUtil.StringUtils.isEmpty(month)) {
            throw new ApplicationException("月份格式传入不正确");
        }
        AtomicBoolean diffMonth = new AtomicBoolean(false);
        list.stream().forEach(e->{
            if(Objects.isNull(e.getMonth()) || !e.getMonth().equals(month)) {
                diffMonth.set(true);
            }
        });
        if(diffMonth.get()) {
            throw new ApplicationException("请导入相同月份的数据");
        }
        List<String> positionTypeNameList = baseMapper.selectBusinessPositionTypeName();
        boolean clearMonthData = false;
        //遍历list有则逻辑，无则插入
        for (ManagerSalaryImportDTO e: list) {
            if(StringUtils.isNotBlank(e.getPositionTypeName()) && !positionTypeNameList.contains(e.getPositionTypeName())) {
                throw new ApplicationException("导入的" + e.getPositionTypeName() +  "不正确");
            }
            if(Objects.nonNull(e.getRemark()) && e.getRemark().length() > 100) {
                throw new ApplicationException("备注字段超过100字符");
            }
            int count = sfaEmployeeInfoMapper.getCompanyEmployeeInfoCount(e.getEmployeeName(),e.getEmployeeId(),e.getMemberKey());
            if(count == 0) {
                throw new ApplicationException("工号：" + e.getEmployeeId() + " memberKey：" + e.getMemberKey() + " 姓名：" + e.getEmployeeName() + "员工信息不存在，请检查后在重试");
            }else if(count > 1) {
                throw new ApplicationException("工号：" + e.getEmployeeId() + " memberKey：" + e.getMemberKey() + " 姓名：" + e.getEmployeeName() + "员工信息存在多条，请检查后在重试");
            }
            if(type == 1) {
                //查询
                ManagerSalaryModel originModel = managerSalaryMapper.selectOne(new QueryWrapper<ManagerSalaryModel>()
                        .eq("month",e.getMonth()).eq("employee_id",e.getEmployeeId())
                        .eq("employee_name",e.getEmployeeName()).eq("delete_flag",0));
                if(!Objects.isNull(originModel)) {
                    retList.add(e.getMonth() + " " + e.getEmployeeId() + " " + e.getEmployeeName() + "薪资数据已存在");
                }
            }else if(type == 2) {
                //导入
                if(!clearMonthData) {//逻辑删除当月已存在的数据
                    clearMonthData = true;
                    managerSalaryMapper.updateManagerSalaryDeleteFlag(e.getMonth(), relation.getEmployeeName());
                }
                ManagerSalaryModel model = new ManagerSalaryModel();
                BeanUtils.copyProperties(e, model);
                model.setCreateTime(LocalDateTime.now());
                model.setCreatePerson(relation.getEmployeeName());
                model.setDeleteFlag(0);
                managerSalaryMapper.insert(model);
            }
        }
        return retList;
    }

    @Override
    public List<ManagerSalaryModel> getManagerSalaryList(String month) {
        log.info("getManagerSalaryList month: {}", month);
        List<ManagerSalaryModel> list = managerSalaryMapper.selectList(new QueryWrapper<ManagerSalaryModel>().eq("month",month).eq("delete_flag",0));
        return list;
    }

    @Override
    public void exportManagerSalaryList(String month, HttpServletResponse response) {
        List<ManagerSalaryModel> list = managerSalaryMapper.selectList(new QueryWrapper<ManagerSalaryModel>().eq("month",month).eq("delete_flag",0));
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"),ManagerSalaryModel.class,list);
        EasyPoiUtil.downLoadExcel(month + "薪资列表.xls",response,workbook);
    }

    /**
     * 根据structureId修改合伙人薪资
     * 1-底薪+奖金，自动匹配分公司标准底薪和标准绩效奖金包；
     * 2-仅有底薪，取导入的底薪、奖金为0；
     * 3-签约旺旺，取导入的底薪、奖金为0；
     * 4-兼职，底薪及奖金均为0
     *
     * @param request
     * @return: int
     * @author: zhouxiaowen
     * @date: 4/22/22 9:02 PM
     */
    @Override
    public int updateSalaryByStructureId(Integer structureId,StructureUpdateRequest request) {
        List<EmployeeSalaryPO> salaryPOS = baseMapper.selectList(new QueryWrapper<EmployeeSalaryPO>().eq("structure_id", structureId));
        salaryPOS.forEach(s -> {
            if (s.getSalaryDescribe().startsWith("1")){
                s.setEmployeeBaseSalary(request.getBaseSalary());
                s.setEmployeeBonus(request.getBonus());
            }
            s.setUpdatedTime(LocalDateTime.now());
            s.setUpdatedBy(request.getUpdatedBy());
            baseMapper.updateById(s);
        });
        return 0;
    }

    /**
     * 入职添加薪资方案
     * 合伙人兼职，4-兼职
     * 合伙人入职公司旺旺，3-签约旺旺
     * 总监面试底薪
     *
     * @param id 员工id
     * @param createdBy 创建人
     * @param structureId 薪资方案ID
     * @return: int
     * @date: 5/7/22 3:45 PM
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addSalaryByEmpId(SfaEmployeeInfoModel sfaEmployeeInfoModel,Integer id,Integer structureId,String createdBy,LocalDate executeTime,Integer paymentType){
        log.info("method addSalaryByEmpId,id:{},structureId:{}",id,structureId);
        EmployeeSalaryPO po = new EmployeeSalaryPO();
        po.setCreatedBy(createdBy);
        po.setUpdatedBy(createdBy);
        po.setCreatedTime(LocalDateTime.now());
        po.setUpdatedTime(LocalDateTime.now());
        po.setStructureId(0);
        po.setPaymentType(paymentType);
        SfaEmployeeInfoModel employee = sfaEmployeeInfoModel;
        if(Objects.isNull(employee)){
            employee = sfaEmployeeInfoMapper.selectById(id);
        }



        if (Objects.nonNull(employee)){

            salaryMiddlewareService.cancel(employee.getApplicationId());

            po.setEmployeeInfoId(employee.getId());
            po.setEmployeeName(employee.getEmployeeName());
            po.setOrganizationId(employee.getCompanyCode());
            if(Objects.nonNull(executeTime)){
                po.setStartDate(executeTime);
//                多次异动且异动的其实日期相同，则将历史的日期逻辑删除
                List<Integer> idList =  baseMapper.selectSalaryByStartDate(executeTime, String.valueOf(employee.getId()));
                if(!CollectionUtils.isEmpty(idList)) {
                    idList.forEach(e->{
                        baseMapper.updateInvalidateSalaryById(e);
                    });
                }
            }else{
                po.setStartDate(employee.getOnboardTime().toLocalDate());
            }


            po.setContractCompany(employee.getJoiningCompany());
            po.setPaymentCompany(employee.getContractCompany());
            po.setPosition(1);
            // 企业合伙人
            if(employee.getType() == 2){
                po.setStructureId(0);
                po.setSalaryDescribe("5-企业");
                po.setEmployeeBaseSalary(null);
                po.setEmployeeBonus(null);
            }else if(employee.getType() == 6 || employee.getType() == 7){
                ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(employee.getApplicationId());
                int positionId = PositionEnum.getPositionId(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
                String positionName = PositionEnum.getPositionName(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
                //根据岗位等级查询分公司底薪和奖金包
                EmployeeSalaryStructurePO employeeSalaryStructurePO = employeeSalaryStructureMapper.selectById(structureId);
                po.setSalaryDescribe(positionId+"-"+positionName);

                po.setContractCompany(employee.getJoiningCompany());
                po.setPaymentCompany(employee.getContractCompany());


                if (Objects.nonNull(employeeSalaryStructurePO)) {
                    po.setStructureId(employeeSalaryStructurePO.getId());
                    po.setEmployeeBaseSalary(employeeSalaryStructurePO.getBaseSalary());
                    po.setEmployeeBonus(employeeSalaryStructurePO.getBonus());
                    po.setEmployeeAllowance(employeeSalaryStructurePO.getAllowance());
                    po.setSalaryLevel(employeeSalaryStructurePO.getGrade());
                    po.setSocialSecurityBase(employeeSalaryStructurePO.getSocialSecurityBase());
                    po.setTravelExpenses(employeeSalaryStructurePO.getTravelExpenses());
                    po.setFullRiskFee(employeeSalaryStructurePO.getFullRiskFee());
                }else{
                    //分公司没有配置底薪和奖金包 薪资设置0
                    po.setEmployeeBaseSalary(BigDecimal.ZERO);
                    po.setEmployeeBonus(BigDecimal.ZERO);
                    po.setEmployeeAllowance(BigDecimal.ZERO);
                }
            }
            else{
                OrganizationModel organizationModel = organizationMapper.getOrganizationByPositionId(employee.getPositionId());
                //根据岗位等级查询分公司底薪和奖金包
                EmployeeSalaryStructurePO employeeSalaryStructurePO = employeeSalaryStructureMapper.selectById(structureId);



                if (Objects.nonNull(employeeSalaryStructurePO)) {
                    po.setStructureId(employeeSalaryStructurePO.getId());
                    po.setSalaryDescribe("1-全职");
                    po.setEmployeeBaseSalary(employeeSalaryStructurePO.getBaseSalary());
                    po.setEmployeeBonus(employeeSalaryStructurePO.getBonus());

                    po.setEmployeeAllowance(employeeSalaryStructurePO.getAllowance());
                    po.setSalaryLevel(employeeSalaryStructurePO.getGrade());
                    po.setSocialSecurityBase(employeeSalaryStructurePO.getSocialSecurityBase());
                    po.setTravelExpenses(employeeSalaryStructurePO.getTravelExpenses());
                    po.setFullRiskFee(employeeSalaryStructurePO.getFullRiskFee());
                }else{
                    //分公司没有配置底薪和奖金包 薪资设置0
                    po.setEmployeeBaseSalary(BigDecimal.ZERO);
                    po.setEmployeeBonus(BigDecimal.ZERO);
                    po.setEmployeeAllowance(BigDecimal.ZERO);

                }
                if (organizationModel.getPositionTypeId() == 10){
                    po.setPosition(4);
                    po.setSalaryDescribe("7-区域经理");
                }else if(organizationModel.getPositionTypeId() == 11 || organizationModel.getPositionTypeId() == 12){
                    ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(employee.getApplicationId());
                    po.setOrganizationId(applyMemberPo.getWorkPlace());
                    if(organizationModel.getPositionTypeId() == 11 ){
                        po.setSalaryDescribe("10-省区总监");
                        po.setPosition(6);
                    }else{
                        po.setSalaryDescribe("13-大区总监");
                        po.setPosition(5);
                    }


                }else if(organizationModel.getPositionTypeId() == 1 ){
                    Integer businessGroupById = null;
                    businessGroupById = organizationMapper.getBusinessGroupById(employee.getAreaCode());
                    po.setSalaryDescribe("9-总督导");
                    String zbOrganizationIdByBusinessGroup = organizationMapper.getZbOrganizationIdByBusinessGroup(businessGroupById);
                    po.setOrganizationId(zbOrganizationIdByBusinessGroup);
                    po.setPosition(3);
                }
                else if(organizationModel.getPositionTypeId() == 2){
                    po.setPosition(2);
                    po.setSalaryDescribe("8-区域总监");
                }else if (organizationModel.getPositionTypeId() == 3){
                    po.setPosition(1);
                    if (3 == employee.getType()){
                        po.setSalaryDescribe("6-承揽");
                    }else if(CommonUtil.StringUtils.isNotBlank(employee.getJoiningCompany())
                            && employee.getJoiningCompany().contains("旺旺")){
                        po.setSalaryDescribe("3-签约旺旺");
                    }else if (2 == employee.getType()) {
                        po.setSalaryDescribe("5-企业");
                    }else if (2 == employee.getPostType()){
                        po.setSalaryDescribe("4-兼职");
                    }
                }
            }
            log.info("method addSalaryByEmpId insert po:{}",po);
            return baseMapper.insert(po);
        }
        return 0;
    }

    @Override
    public int specialSalary(Integer employeeInfoId,String person) {
        log.info("【special salary】 employeeId:{}",employeeInfoId);
        EmployeeSalaryPO po = new EmployeeSalaryPO();
        po.setCreatedBy(person);
        po.setUpdatedBy(person);
        po.setCreatedTime(LocalDateTime.now());
        po.setUpdatedTime(LocalDateTime.now());
        po.setStructureId(0);
        //兼职
        po.setSalaryDescribe("4-兼职");
        po.setEmployeeBaseSalary(null);
        po.setEmployeeBonus(null);

        return baseMapper.insert(po);
    }

    /**
     * 离职结束薪资方案
     *
     * @param employeeInfoId sfa_employee_info.id
     * @param offTime 离职时间
     * @return: void
     * @date: 9/1/22 3:18 PM
     */
    @Override
    public void updateSalaryByEmpId(Integer employeeInfoId,LocalDateTime offTime){
        log.info("method updateSalaryByEmpId,id:{}",employeeInfoId);
        EmployeeSalaryPO po = baseMapper.selectSalaryByEmpId(employeeInfoId);


        LocalDate startDate = po.getStartDate();


        if (Objects.nonNull(po)){
            po.setEndDate(offTime.toLocalDate());
            po.setUpdatedTime(LocalDateTime.now());
            if(startDate.isAfter(offTime.toLocalDate())){
                po.setIsDelete(1);
            }
            baseMapper.updateById(po);
        }
    }

    @Override
    public List<SalaryStructureVo> selectSalaryList(String organizationId,int salaryPositionByCondition) {
        int businessGroup = RequestUtils.getBusinessGroup();

        if(salaryPositionByCondition == 301){
            String zbOrganizationIdByBusinessGroup = organizationMapper.getZbOrganizationIdByBusinessGroup(businessGroup);
            organizationId = zbOrganizationIdByBusinessGroup;
        }

        return baseMapper.selectSalaryList(organizationId,salaryPositionByCondition,businessGroup);
    }

    @Override
    public SalaryStructureVo selectSalaryById(Integer salaryId) {
        SalaryStructureVo salaryStructureVo = new SalaryStructureVo();
        EmployeeSalaryStructurePO employeeSalaryStructurePO = employeeSalaryStructureMapper.selectById(salaryId);
        if(Objects.isNull(employeeSalaryStructurePO)){
            return salaryStructureVo;
        }
        salaryStructureVo.setId(salaryId);
        salaryStructureVo.setLevel(employeeSalaryStructurePO.getGrade());


        BigDecimal allowance = employeeSalaryStructurePO.getAllowance();
        if(Objects.nonNull(allowance)){
            salaryStructureVo.setAllowance(allowance.toString());
        }


        BigDecimal baseSalary = Optional.ofNullable(employeeSalaryStructurePO.getBaseSalary()).orElse(BigDecimal.ZERO);
        BigDecimal socialSecurityBase = Optional.ofNullable(employeeSalaryStructurePO.getSocialSecurityBase()).orElse(BigDecimal.ZERO);
        BigDecimal travelExpenses = Optional.ofNullable(employeeSalaryStructurePO.getTravelExpenses()).orElse(BigDecimal.ZERO);
        BigDecimal bonus = Optional.ofNullable(employeeSalaryStructurePO.getBonus()).orElse(BigDecimal.ZERO);
        allowance = Optional.ofNullable(allowance).orElse(BigDecimal.ZERO);
        salaryStructureVo.setBaseSalary(baseSalary.toString());
        salaryStructureVo.setSocialSecurityBase(socialSecurityBase.add(travelExpenses).add(bonus).toString());
        salaryStructureVo.setTotal(socialSecurityBase.add(travelExpenses).add(bonus).add(baseSalary).add(allowance).toString());

        return salaryStructureVo;
    }

    @Override
    public BigDecimal selectTotalSalaryById(Integer salaryStructureId) {

        EmployeeSalaryStructurePO employeeSalaryStructurePO = employeeSalaryStructureMapper.selectById(salaryStructureId);
        if(Objects.isNull(employeeSalaryStructurePO)){
            return BigDecimal.ZERO;
        }

        BigDecimal baseSalary = Optional.ofNullable(employeeSalaryStructurePO.getBaseSalary()).orElse(BigDecimal.ZERO);
        BigDecimal bonus = Optional.ofNullable(employeeSalaryStructurePO.getBonus()).orElse(BigDecimal.ZERO);
        BigDecimal fullRiskFee = Optional.ofNullable(employeeSalaryStructurePO.getFullRiskFee()).orElse(BigDecimal.ZERO);
        BigDecimal socialSecurityBase = Optional.ofNullable(employeeSalaryStructurePO.getSocialSecurityBase()).orElse(BigDecimal.ZERO);
        BigDecimal travelExpenses = Optional.ofNullable(employeeSalaryStructurePO.getTravelExpenses()).orElse(BigDecimal.ZERO);
        BigDecimal allowance = Optional.ofNullable(employeeSalaryStructurePO.getAllowance()).orElse(BigDecimal.ZERO);

        return baseSalary.add(bonus).add(fullRiskFee).add(socialSecurityBase).add(travelExpenses).add(allowance);
    }

    @Override
    public List<OrganizationUsedSalaryModel> selectOnBoardBusinessBDSalary(List<String> deptCodes,List<Integer>excludeEmployeeInfoIds) {
        return employeeSalaryStructureMapper.selectOnBoardBusinessBDSalary(deptCodes,excludeEmployeeInfoIds);
    }


    @Override
    public List<OrganizationUsedSalaryModel> selectPendingBusinessBDSalary(List<String> departmentId,Integer applyId) {
        return employeeSalaryStructureMapper.selectPendingBusinessBDSalary(departmentId,applyId);
    }

    @Override
    public List<EmpSalaryDO> selectTransactionBD(List<String> companyCodes,String theYearMonth) {
        return employeeSalaryStructureMapper.selectTransactionBD(companyCodes,theYearMonth);
    }

    @Override
    public List<OrgSalaryDO> selectOnBoardProcessingSalary(List<String> deptCodes, String theYearMonth, Integer excludeApplyId) {
        return employeeSalaryStructureMapper.selectOnBoardProcessingSalary(deptCodes,theYearMonth,excludeApplyId);
    }

    @Override
    public List<OrgSalaryDO> selectTransferringSalary(List<EmpSalaryDO> transactionEmpList) {
        if(CollectionUtils.isEmpty(transactionEmpList)){
            return null;
        }
        return employeeSalaryStructureMapper.selectTransferringSalary(transactionEmpList);
    }

    @Override
    public List<EmployeeSalaryPO> getValidSalaryByDeadline(LocalDate workStartDate) {
        return baseMapper.selectList(new LambdaQueryWrapper<EmployeeSalaryPO>().eq(EmployeeSalaryPO::getIsDelete, 0)
                .lt(EmployeeSalaryPO::getStartDate, workStartDate).ne(EmployeeSalaryPO::getPosition, 1)
                .isNull(EmployeeSalaryPO::getEndDate)
        );
    }

    @Override
    public int getMinimumLevel(String organizationId, Integer salaryPosition) {

        String minimumLevel = Optional.ofNullable(baseMapper.selectMinimumLevel(organizationId, salaryPosition)).orElse("0");

        return GradeUtils.extractGradeNumber(minimumLevel);
    }

    /**
     * 获取过滤后的薪资列表
     * 根据最低等级过滤薪资方案
     *
     * @param organizationId 组织ID
     * @param salaryPosition 薪资职位
     * @param minimumLevel 最低等级
     * @param fillSocialSecurityBase 是否填充社保基数
     * @return 过滤后的薪资方案列表
     */
    @Override
    public List<SalaryStructureVo> getFilteredSalaryList(String organizationId, int salaryPosition, int minimumLevel, boolean fillSocialSecurityBase) {
        List<SalaryStructureVo> salaryList = Optional.ofNullable(selectSalaryList(organizationId, salaryPosition))
                .orElse(Collections.emptyList());

        return salaryList.stream()
                .filter(f -> Objects.nonNull(f.getLevel()) && GradeUtils.extractGradeNumber(f.getLevel()) >= minimumLevel)
                .peek(e -> {
                    if (fillSocialSecurityBase && StringUtils.isBlank(e.getSocialSecurityBase())) {
                        e.setSocialSecurityBase("请咨询人资");
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取过滤后的薪资列表
     * 自动获取最低等级并过滤薪资方案
     *
     * @param organizationId 组织ID
     * @param salaryPosition 薪资职位
     * @param fillSocialSecurityBase 是否填充社保基数
     * @return 过滤后的薪资方案列表
     */
    @Override
    public List<SalaryStructureVo> getFilteredSalaryList(String organizationId, int salaryPosition, boolean fillSocialSecurityBase) {
        // 自动获取最低等级
        int minimumLevel = getMinimumLevel(organizationId, salaryPosition);
        
        // 调用已有的方法进行过滤
        return getFilteredSalaryList(organizationId, salaryPosition, minimumLevel, fillSocialSecurityBase);
    }


}
