package com.wantwant.sfa.backend.interview.task.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.application.businessBd.BusinessBdOrgQuotaAppService;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.arch.service.impl.AccountService;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.AuditService;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.businessBd.DO.BusinessBdEmployeeQuotaOperationValue;
import com.wantwant.sfa.backend.domain.businessBd.enums.QuotaOperationTypeEnum;
import com.wantwant.sfa.backend.domain.emp.DO.EstablishSearchDO;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdConfigPO;
import com.wantwant.sfa.backend.domain.emp.service.IBusinessBDRuleService;
import com.wantwant.sfa.backend.domain.notify.DO.NotifyDO;
import com.wantwant.sfa.backend.domain.notify.service.INotifyPushService;
import com.wantwant.sfa.backend.domain.recruit.service.IRecruitService;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.infrastructure.jobTransfer.MemberClient;
import com.wantwant.sfa.backend.interview.dto.*;
import com.wantwant.sfa.backend.interview.enums.EmpEvent;
import com.wantwant.sfa.backend.interview.enums.ProcessResult;
import com.wantwant.sfa.backend.interview.enums.ProcessType;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.interview.process.CityManagerInterviewProcessStep;
import com.wantwant.sfa.backend.interview.process.OpenAccountProcess;
import com.wantwant.sfa.backend.interview.process.dto.OnboardRequestDto;
import com.wantwant.sfa.backend.interview.request.InterviewOperateRequest;
import com.wantwant.sfa.backend.interview.service.IBusinessBDService;
import com.wantwant.sfa.backend.interview.service.IEmployeeInfoProcessService;
import com.wantwant.sfa.backend.interview.service.InterviewService;
import com.wantwant.sfa.backend.interview.strategy.impl.ZWOnboardStrategyImpl;
import com.wantwant.sfa.backend.interview.task.IJobPositionChangeTask;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.mapper.market.SmallMarketEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.*;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.owt.model.OwtCustomerImportModel;
import com.wantwant.sfa.backend.policy.service.IPolicyService;
import com.wantwant.sfa.backend.position.dto.PositionRelationDTO;
import com.wantwant.sfa.backend.position.service.IPositionRelationService;
import com.wantwant.sfa.backend.rabbitMQ.QueueConstant;
import com.wantwant.sfa.backend.rabbitMQ.RabbitMQSender;
import com.wantwant.sfa.backend.rabbitMQ.config.OnboardBenefitTopicRabbitConfig;
import com.wantwant.sfa.backend.service.*;
import com.wantwant.sfa.backend.service.command.SaveCustomerInvoke;
import com.wantwant.sfa.backend.service.command.impl.BhCustomerSaveCommand;
import com.wantwant.sfa.backend.service.impl.EmployeeService2Impl;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.*;
import enums.StoreChannel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;


import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 入职任务
 * @Auther: zhengxu
 * @Date: 2023/01/03/上午11:30
 */
@Component
@Slf4j
public class OnBoardTask implements IJobPositionChangeTask {
    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private SfaCustomerMapper sfaCustomerMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private ZWOnboardStrategyImpl zwOnboardStrategy;
    @Autowired
    private LoginUserService loginUserService;
    @Autowired
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Autowired
    private ROOTConnectorUtil rootConnectorUtil;
    @Autowired
    private InterviewService interviewService;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private EmployeeService2Impl employeeService2;
    @Autowired
    private EmployeeSalaryService salaryService;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private ApplyMemberService applyMemberService;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private AccountService accountService;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private RecruitmentNeedsService recruitmentNeedsService;
    @Autowired
    private MEMBERConnectorUtil memberConnectorUtil;
    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Autowired
    private IPositionRelationService positionRelationService;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Autowired
    private BhCustomerSaveCommand bhCustomerSaveCommand;
    @Autowired
    private OpenAccountProcess openAccountProcess;
    @Autowired
    private IBusinessBDService businessBDService;
    @Autowired
    private IPolicyService policyService;
    @Resource
    private GeTuiService geTuiService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private CityManagerInterviewProcessStep cityManagerInterviewProcessStep;
    @Resource
    private AuditService auditService;
    @Resource
    private INotifyPushService notifyPushService;
    @Resource
    private IEmployeeInfoProcessService employeeInfoProcessService;
    @Resource
    private MemberClient memberClient;
    @Resource
    private IBusinessBDRuleService businessBDRuleService;
    @Resource
    private IRecruitService recruitService;
    @Resource
    private BusinessBdOrgQuotaAppService businessBdOrgQuotaAppService;
    @Resource
    private RabbitMQSender rabbitMQSender;
    @Resource
    private OrganizationMapper organizationMapper;
    @Override
    @Transactional(propagation = Propagation.NESTED)
    public void execute(JobPositionChangeDto jobPositionChangeDto) {

        log.info("【on board task】request:{}",jobPositionChangeDto);
        // 获取面试申请记录
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(jobPositionChangeDto.getId());
        if(Objects.isNull(applyMemberPo)){
            throw new ApplicationException("面试申请信息获取失败");
        }

        // 获取面试流程主表
        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new QueryWrapper<SfaInterviewProcessModel>().eq("application_id", applyMemberPo.getId()));
        if(Objects.isNull(sfaInterviewProcessModel)){
            throw new ApplicationException("面试记录获取失败");
        }

        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordMapper.selectById(sfaInterviewProcessModel.getInterviewRecordId());
        if(Objects.isNull(sfaInterviewProcessRecordModel)){
            throw new ApplicationException("面试记录明细获取失败");
        }

        // 修改社保信息
        applyMemberPo.setSocialInsuranceProvince(jobPositionChangeDto.getSocialInsuranceProvince());
        applyMemberPo.setSocialInsuranceCity(jobPositionChangeDto.getSocialInsuranceCity());
        applyMemberPo.setSocialInsuranceDistrict(jobPositionChangeDto.getSocialInsuranceDistrict());
        applyMemberMapper.updateById(applyMemberPo);

        String currentOrgCode = applyMemberPo.getBranchOrganizationId();
        PositionEnum positionEnum = PositionEnum.getEnum(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
        if(Objects.equals(positionEnum.getId(), PositionEnum.MANAGER.getId())){
            currentOrgCode = applyMemberPo.getCompanyOrganizationId();
        }else if(Objects.equals(positionEnum.getId(), PositionEnum.PROVINCE_MANAGER.getId())){
            currentOrgCode = applyMemberPo.getProductionGroupCode();
        }else if(Objects.equals(positionEnum.getId(), PositionEnum.VAREA_MANAGER.getId())){
            currentOrgCode = applyMemberPo.getVareaOrganizationId();
        }else if(Objects.equals(positionEnum.getId(), PositionEnum.AREA_MANAGER.getId())){
            currentOrgCode = applyMemberPo.getAreaOrganizationId();
        }

        // 检查是否可招聘
        boolean restrict = recruitService.checkPositionRestrict(applyMemberPo.getBusinessGroup(), currentOrgCode, positionEnum);
        if(restrict){
            throw new ApplicationException("招聘岗位已被关闭");
        }


        Integer position = applyMemberPo.getPosition();
        String memberKey = StringUtils.EMPTY;
        int  paymentType = 1;
        if(Objects.nonNull(sfaInterviewProcessRecordModel.getPaymentType())){
            paymentType = sfaInterviewProcessRecordModel.getPaymentType();
        }
        StopWatch sw = new StopWatch();
        log.info("【start create MemberKey】");
        sw.start("【entry company】step 1: create memberKey");
        // 造旺合伙人
        if (1 == position) {
            memberKey = ceoOnboard(jobPositionChangeDto, jobPositionChangeDto.getProcessUserId(),jobPositionChangeDto.getProcessUserName(), applyMemberPo,jobPositionChangeDto.getExecuteDate());
            // 发放消息公告
            policyService.onBoardCeoSendPolicy(Long.parseLong(memberKey),applyMemberPo.getBusinessGroup());
        }
        // 区域经理人
        else if(4 == position){
            // 检查sfaEmployeeInfo是否有创建。
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMobile, applyMemberPo.getUserMobile()).last("limit 1"));
            // 如果sfaEmployeeInfoModel是空则为跳过试岗
            if(Objects.isNull(sfaEmployeeInfoModel)){
                OnboardRequestDto onboardRequestDto = new OnboardRequestDto();
                onboardRequestDto.setProcessUserId(jobPositionChangeDto.getProcessUserId());
                onboardRequestDto.setProcessUserName(jobPositionChangeDto.getProcessUserName());
                onboardRequestDto.setApplyMemberPo(applyMemberPo);
                onboardRequestDto.setProbation(false);

                memberKey = cityManagerInterviewProcessStep.ceoManagerOnboard(onboardRequestDto);
            }else{
                memberKey = cityManagerOnboard(jobPositionChangeDto, jobPositionChangeDto.getProcessUserId(),jobPositionChangeDto.getProcessUserName(), applyMemberPo);
            }
        }
        else if(7 == position || position == 8){
            String contextEmp = sfaInterviewProcessRecordModel.getContextEmp();
            // 检查是否超额度
            memberKey = businessBdOnboard(applyMemberPo,jobPositionChangeDto,contextEmp);
            // 设置interview表入职时间
            sfaInterviewProcessModel.setOnboardTime(new Date());
            sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);



        }
        else {
            // 造旺总监,省区总监,大区,战区
            memberKey = ceoManagerOnboard(jobPositionChangeDto, jobPositionChangeDto.getProcessUserId(), jobPositionChangeDto.getProcessUserName(), applyMemberPo);
        }


        sw.stop();

        log.info("【start bind recruitment】");
        sw.start("【entry company】step 2: bind recruitment");
        // 绑定招聘需求
        bindRecruitment(position, sfaInterviewProcessRecordModel, applyMemberPo);
        sw.stop();


        log.info("【syn employee info】");
        sw.start("【entry company】step 3: syn employee info");
        if (StringUtils.isNotBlank(memberKey) && position != 1) {
            String gender = applyMemberPo.getGender() == 1 ? "M" : "F";
            String workDate = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
            if(Objects.nonNull(jobPositionChangeDto.getExecuteDate())){
                workDate = jobPositionChangeDto.getExecuteDate().toLocalDate().toString();
            }
            // 同步旺铺会员信息
            rootConnectorUtil.updateEmployeeInfo(
                    memberKey,
                    applyMemberPo.getPicUrl(),
                    applyMemberPo.getUserName(),
                    applyMemberPo.getBirthDate(),
                    gender,
                    workDate,
                    applyMemberPo.getPartnerCompanyName());
        }
        sw.stop();


        log.info("【start bind salary structure】");
        sw.start("【entry company】step 4:bind salary structure");
        SfaEmployeeInfoModel sfaEmployeeInfoModel =
                sfaEmployeeInfoMapper.selectOne(
                        new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applyMemberPo.getId()));
        if (Objects.isNull(sfaEmployeeInfoModel)) {
            throw new ApplicationException("获取员工信息表失败");
        }
        log.info("【on board employee info】employee:{}",sfaEmployeeInfoModel);
        // 修改入职公司/实际入职公司
        sfaEmployeeInfoModel.setJoiningCompany(jobPositionChangeDto.getJoiningCompany());
        sfaEmployeeInfoModel.setContractCompany(jobPositionChangeDto.getContractCompany());
        sfaEmployeeInfoModel.setOnboardTime(jobPositionChangeDto.getExecuteDate());
        sfaEmployeeInfoModel.setActualJoiningCompany(jobPositionChangeDto.getActualJoiningCompany());
        sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);


        // 薪资方案
        salaryService.addSalaryByEmpId(null,sfaEmployeeInfoModel.getId(),sfaInterviewProcessRecordModel.getSalaryStructureId(), jobPositionChangeDto.getProcessUserId(),null,paymentType);
        sw.stop();

        // 绑定业务bd关联人员
        if(7 == position){
            String contextEmp = sfaInterviewProcessRecordModel.getContextEmp();
            if(StringUtils.isNotBlank(contextEmp)){
                Optional<Integer> optionalInteger = Arrays.asList(contextEmp.split(",")).stream().findFirst().map(Integer::valueOf);
                BusinessBDRetainDTO businessBDRetainDTO = new BusinessBDRetainDTO();
                businessBDRetainDTO.setRetainEmpIds(Arrays.asList(optionalInteger.get()));
                businessBDRetainDTO.setEmployeeInfoId(sfaEmployeeInfoModel.getId());
                businessBDRetainDTO.setProcessUserId(jobPositionChangeDto.getProcessUserId());
                businessBDRetainDTO.setOrganizationId(applyMemberPo.getBranchOrganizationId());
                businessBDService.retainServer(businessBDRetainDTO);
            }
        }



        //backend的6-3的状态，会把信息推给第三方ehr系统，ehr系统通过后，会同步给backend
        //为了防止backend再次把信息同步给ehr，所以从ehr发起的入职，通过参数控制不需要去调用backend，
        //ehr调用backend自己重置自己的状态为已入职
        if(!jobPositionChangeDto.isCallBackFromEhrFalg()) {
            log.info("【start syn ehr】");
            sw.start("【entry company】step 5: syn ehr");
            applyMemberService.syn(
                    applyMemberPo,
                    null,
                    ProcessType.DO_ONBOARD.getProcessCode(),
                    ProcessResult.PASS.getResultCode(),
                    StringUtils.EMPTY,
                    applyMemberPo.getHighestEducation(),
                    applyMemberPo.getIdCardNum(),
                    applyMemberPo.getBirthDate(),
                    jobPositionChangeDto.getExecuteDate(),
                    null);
            sw.stop();
        }

        log.info("【start benefit】");
        sw.start("【entry company】step 6:benefit");

        // 发送下级入职开会信息
        if(position != 1 && position != 7 && position != 8){
            String positionId = sfaEmployeeInfoModel.getPositionId();
            SelectAuditDto selectAuditDto = new SelectAuditDto();
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getPositionId,positionId));
            selectAuditDto.setCurrentOrganizationId(ceoBusinessOrganizationPositionRelation.getOrganizationParentId());
            selectAuditDto.setChannel(3);
            selectAuditDto.setBusinessGroup(applyMemberPo.getBusinessGroup());
            selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
            CeoBusinessOrganizationPositionRelation auditPerson = auditService.chooseAuditPerson(selectAuditDto);


            NotifyDO notifyDO = new NotifyDO();
            notifyDO.setEmpIds(Arrays.asList(auditPerson.getEmployeeId()));
            notifyDO.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
            notifyDO.setTitle("您有下级已入职！可以召开相关会议，已召开请忽略");
            notifyDO.setContent("您有下级已入职！可以召开相关会议，已召开请忽略");
            notifyDO.setCode("/offlineMeetings");
            notifyDO.setCreateBy("-1");
            notifyDO.setUpdateBy("-1");
            notifyPushService.saveBatch(notifyDO);
        }

        // 发送入职奖励
        // 2023-02-01取消兼职合伙人的发放
        int positionId = PositionEnum.getPositionId(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
        log.info("【onboard process】applyId:{},positionId:{}",applyMemberPo.getId(),positionId);
        // V7.7.0只有造旺总监及区域经理发放旺金币


        OrgDTO orgDTO = new OrgDTO();

        orgDTO.setOrgCode(currentOrgCode);
        orgDTO.setBusinessGroupId(organizationMapper.getBusinessGroupById(currentOrgCode));

        if(positionId == PositionEnum.CITY_MANAGER.getId() || positionId == PositionEnum.MANAGER.getId() || positionId == PositionEnum.PROVINCE_MANAGER.getId()
                || positionId == PositionEnum.AREA_MANAGER.getId() || positionId == PositionEnum.VAREA_MANAGER.getId()){
            EmpOperationCommand empOperationCommand = EmpOperationCommand.builder().employeeInfoId(sfaEmployeeInfoModel.getId())
                    .eventType(EmpEvent.ON_BOARD.getType()).changeOrgList(Collections.singletonList(orgDTO)).build();
            // 创建账号类型通知
            rabbitMQSender.sendMessage(QueueConstant.EMP_EVENT_EXCHANGE, QueueConstant.EMP_EVENT_QUEUE,null,empOperationCommand,null);
        }




        Integer businessGroup = applyMemberPo.getBusinessGroup();

        if(positionId == PositionEnum.MANAGER.getId() && businessGroup != 3){
            JSONObject obj = new JSONObject();
            obj.put("memberKey", memberKey);
            obj.put("position",applyMemberPo.getPosition());
            log.info(
                    "入职奖励方法推送消息,exchange:{},queue:{},obj:{}",
                    OnboardBenefitTopicRabbitConfig.onboardBenefitExchange,
                    OnboardBenefitTopicRabbitConfig.onboardBenefit,
                    obj);
            rabbitTemplate.convertAndSend(
                    OnboardBenefitTopicRabbitConfig.onboardBenefitExchange,
                    OnboardBenefitTopicRabbitConfig.onboardBenefit,obj);
            sw.stop();
            log.info(sw.prettyPrint());
        }

        // 发送入职短信
        // 发送短信
//        String onBoardNoticeMsg = configMapper.getValueByCode("onBoard_notice_msg");
//        Map<String,String> smsParam = new HashMap<>();
//        smsParam.put("employeeName",applyMemberPo.getUserName());
//        List<String>recNum = new ArrayList<>();
//        recNum.add(sfaEmployeeInfoModel.getMobile());
//        try {
//            geTuiService.SmsPushList(onBoardNoticeMsg,smsParam,recNum);
//        } catch (Exception e) {
//            log.error("【入职短信发送失败】ex:{}",e.getMessage());
//        }

    }


    private String businessBdOnboard(ApplyMemberPo applyMemberPo, JobPositionChangeDto jobPositionChangeDto, String contextEmp) {

        // 更具组织选择编织检查模式
        BusinessBdConfigPO businessBdConfigPO = businessBDRuleService.getConfig(applyMemberPo.getBranchOrganizationId());
        if(Objects.isNull(businessBdConfigPO)){
            throw new ApplicationException("当前组织未配置规则");
        }
        Integer checkType = businessBdConfigPO.getCheckType();

        PositionEnum positionEnum = PositionEnum.getEnum(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());

        EstablishSearchDO establishSearch = EstablishSearchDO.builder()
                .applyId(applyMemberPo.getId())
                .serverEmployeeInfoId(Integer.parseInt(contextEmp))
                .positionEnum(positionEnum)
                .theYearMonth(LocalDate.now().toString().substring(0,7))
                .businessGroup(applyMemberPo.getBusinessGroup())
                .changeServer(false)
                .departmentId(applyMemberPo.getBranchOrganizationId())
                .checkType(businessBdConfigPO.getCheckType()).build();

        businessBDRuleService.checkOvershootEstablished(establishSearch);



        InterviewOperateRequest request = new InterviewOperateRequest();
        request.setApplicationId(applyMemberPo.getId());
        request.setGender(applyMemberPo.getGender());
        request.setUserMobile(applyMemberPo.getUserMobile());
        request.setUserName(applyMemberPo.getUserName());
        request.setProvince(applyMemberPo.getAgentProvince());
        request.setCity(applyMemberPo.getAgentCity());
        request.setDistrict(applyMemberPo.getAgentDistrict());
        request.setCeoType(applyMemberPo.getCeoType());
        request.setPosition(applyMemberPo.getPosition());
        request.setJobsType(applyMemberPo.getJobsType());
        request.setEmployeeId(jobPositionChangeDto.getEmployeeId());
        request.setContractCompany(jobPositionChangeDto.getContractCompany());
        request.setJoiningCompany(jobPositionChangeDto.getJoiningCompany());
        openAccountProcess.synEmployee(request,applyMemberPo);

        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getApplicationId, applyMemberPo.getId()));

        // 设置入职时间
        sfaEmployeeInfoModel.setOnboardTime(jobPositionChangeDto.getExecuteDate());
        sfaEmployeeInfoModel.setEmployeeId(jobPositionChangeDto.getEmployeeId());
        sfaEmployeeInfoModel.setJoiningCompany(jobPositionChangeDto.getJoiningCompany());
        sfaEmployeeInfoModel.setContractCompany(jobPositionChangeDto.getContractCompany());
        sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);
        sfaEmployeeInfoModel.setEmployeeStatus(2);
        // 设置工作模式
        sfaEmployeeInfoModel.setWorkMode(applyMemberPo.getWorkMode());
        sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);

        // 设置入职公司
        rootConnectorUtil.updateSigningCompany(sfaEmployeeInfoModel.getMemberKey(),jobPositionChangeDto.getJoiningCompany());


        if(checkType == 2){
            // 调用调整额度
            BusinessBdEmployeeQuotaOperationValue businessBdEmployeeQuotaOperationValue = BusinessBdEmployeeQuotaOperationValue.builder().employeeInfoId(sfaEmployeeInfoModel.getId())
                    .curPositionId(positionEnum.getId())
                    .type(QuotaOperationTypeEnum.ENTRY.getCode())
                    .organizationId(applyMemberPo.getBranchOrganizationId())
                    .userId("ROOT")
                    .userName("ROOT")
                    .remark("")
                    .overStaffHandle(false).build();
            businessBdOrgQuotaAppService.adjustQuotaByEmployeeOperation(businessBdEmployeeQuotaOperationValue);

        }



        return sfaEmployeeInfoModel.getMemberKey().toString();
    }

    private String cityManagerOnboard(JobPositionChangeDto jobPositionChangeDto, String processUserId, String processUserName, ApplyMemberPo applyMemberPo) {
        SfaEmployeeInfoModel sfaEmployeeInfoModel =
                sfaEmployeeInfoMapper.selectOne(
                        new QueryWrapper<SfaEmployeeInfoModel>().eq("mobile", applyMemberPo.getUserMobile()));


        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation =
                ceoBusinessOrganizationPositionRelationMapper.selectOne(
                        new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                .eq("organization_id", applyMemberPo.getBranchOrganizationId())
                                .eq("channel",3));

        Integer employeeStatus = sfaEmployeeInfoModel.getEmployeeStatus();


        if(Objects.isNull(ceoBusinessOrganizationPositionRelation) || (StringUtils.isNotBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId()) && !ceoBusinessOrganizationPositionRelation.getEmployeeId().equals(sfaEmployeeInfoModel.getEmployeeId())) ){
            throw new ApplicationException("应聘岗位不存在或岗位暂时未空缺");
        }

        SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new LambdaQueryWrapper<SfaCustomer>().eq(SfaCustomer::getMemberKey, sfaEmployeeInfoModel.getMemberKey()).last("limit 1"));

        // 只有离职返聘跳过试岗需要修改岗位表
        if(employeeStatus > 2){
            // 修改岗位表
            employeeEntry(ceoBusinessOrganizationPositionRelation.getPositionId(),sfaEmployeeInfoModel.getMobile(),
                    sfaEmployeeInfoModel.getEmployeeName(), processUserId, jobPositionChangeDto.getExecuteDate());

            // 插入兼岗表
            // 记录岗位表
            PositionRelationDTO positionRelationDTO = PositionRelationDTO.init(ceoBusinessOrganizationPositionRelation.getPositionId(), ceoBusinessOrganizationPositionRelation.getPositionTypeId(), sfaEmployeeInfoModel.getId(), ceoBusinessOrganizationPositionRelation.getOrganizationId(), 0, LocalDateTime.now(), RequestUtils.getChannel());
            positionRelationDTO.setBusinessGroup(applyMemberPo.getBusinessGroup());
            positionRelationDTO.setEmpId(sfaEmployeeInfoModel.getEmployeeId());
            positionRelationDTO.setPostType(sfaEmployeeInfoModel.getPostType());
            positionRelationDTO.setType(sfaEmployeeInfoModel.getType());
            positionRelationDTO.setParentOrganizationCode(applyMemberPo.getCompanyOrganizationId());
            positionRelationDTO.setOrganizationInfo(applyMemberPo.getBranchOrganizationId(),applyMemberPo.getBranchOrganizationName(),"department");
            positionRelationDTO.setOrganizationInfo(applyMemberPo.getCompanyOrganizationId(),applyMemberPo.getCompany(),"company");
            positionRelationDTO.setOrganizationInfo(applyMemberPo.getProvinceOrganizationId(),applyMemberPo.getProvinceOrganizationName(),"province");
            positionRelationDTO.setOrganizationInfo(applyMemberPo.getVareaOrganizationId(),applyMemberPo.getVareaOrganizationName(),"varea");
            positionRelationDTO.setOrganizationInfo(applyMemberPo.getAreaOrganizationId(),applyMemberPo.getArea(),"area");
            positionRelationService.insertPositionRelation(positionRelationDTO);
        }



        // 修改sfa_employee_info,sfacustome的信息
        EmployeeInfoUpdateDto dto = new EmployeeInfoUpdateDto();
        dto.setApplyMemberPo(applyMemberPo);
        dto.setEmployeeInfoModel(sfaEmployeeInfoModel);
        dto.setOnboardDate(null);
        dto.setCeoType(1);
        dto.setCeoBusinessOrganizationPositionRelation(ceoBusinessOrganizationPositionRelation);
        dto.setSfaCustomer(sfaCustomer);

        String contractCompany = StringUtils.EMPTY;
        JoiningCompanyConfigDto joiningCompanyConfigDto = sfaEmployeeInfoMapper.selectJoiningCompany(applyMemberPo.getCompanyOrganizationId(), applyMemberPo.getPosition());
        if(Objects.nonNull(joiningCompanyConfigDto)){
            contractCompany = joiningCompanyConfigDto.getContractCompany();
        }
        // 设置为在职
        sfaEmployeeInfoModel.setProbationTime(LocalDate.now().toString());
        sfaEmployeeInfoModel.setEmployeeStatus(2);
        sfaEmployeeInfoModel.setEmployeeId(applyMemberPo.getUserMobile());

        // 检查账号是否存在
        boolean isExist = loginUserService.checkAccountExist(sfaEmployeeInfoModel.getMobile());
        if(!isExist){
            loginUserService.createAccount(sfaEmployeeInfoModel.getMobile(),sfaEmployeeInfoModel.getId());
        }

        dto.setContractCompany(contractCompany);
        employeeInfoProcessService.updateEmployeeInfo(dto);


        // 根据memberKey全部设置为非汰换
        rootConnectorUtil.openMember(sfaEmployeeInfoModel.getMemberKey().toString());

        // 调用旺铺更新接口
        memberClient.synEmployeeInfo(sfaEmployeeInfoModel.getId(), LocalDate.now().atStartOfDay(),LocalDate.now());
        
        // 发送入职短信
        interviewService.sendMessage(
                sfaEmployeeInfoModel.getCompanyName(),
                PositionEnum.CITY_MANAGER.getPositionName(),
                sfaEmployeeInfoModel.getEmployeeName(),
                sfaEmployeeInfoModel.getMobile(),
                ProcessType.DO_ONBOARD.getProcessCode());

        return sfaEmployeeInfoModel.getMemberKey().toString();


    }


    private String ceoOnboard(
            JobPositionChangeDto dto, String processUserId, String processUserName, ApplyMemberPo applyMemberPo, LocalDateTime executeDate) {

        SfaEmployeeInfoModel sfaEmployeeInfoModel =
                sfaEmployeeInfoMapper.selectOne(
                        new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applyMemberPo.getId()));
        employeeEntry(
                sfaEmployeeInfoModel.getPositionId(),
                dto.getEmployeeId(),
                dto.getEmployeeName(),
                processUserId,
                dto.getExecuteDate());

        sfaEmployeeInfoModel.setOnboardTime(dto.getExecuteDate());


        sfaEmployeeInfoModel.setEmployeeId(dto.getEmployeeId());
        sfaEmployeeInfoModel.setEmployeeName(dto.getEmployeeName());


        sfaEmployeeInfoModel.setContractCompany(dto.getContractCompany());
        sfaEmployeeInfoModel.setEmployeeStatus(2);

        sfaEmployeeInfoModel.setJoiningCompany(dto.getJoiningCompany());
        sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);

        // 发送入职短信
        interviewService.sendMessage(
                sfaEmployeeInfoModel.getCompanyName(),
                "造旺合伙人",
                sfaEmployeeInfoModel.getEmployeeName(),
                sfaEmployeeInfoModel.getMobile(),
                ProcessType.DO_ONBOARD.getProcessCode());

        return sfaEmployeeInfoModel.getMemberKey().toString();
    }

    private void employeeEntry(
            String positionId,
            String employeeId,
            String employeeName,
            String person,
            LocalDateTime onboardTime) {

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation =
                ceoBusinessOrganizationPositionRelationMapper.selectOne(
                        new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                .eq("position_id", positionId));

        if (ceoBusinessOrganizationPositionRelation == null) {
            throw new ApplicationException("未查询到该岗位！");
        } else if (ceoBusinessOrganizationPositionRelation.getEmployeeId() != null
                && !"".equals(ceoBusinessOrganizationPositionRelation.getEmployeeId())) {
            throw new ApplicationException("该岗位存在员工！");
        } else if (StringUtils.isNotBlank(employeeId)
                && employeeMapper.getEmployeeByEmployeeIdFor123(employeeId, 3) != null) {
            throw new ApplicationException("该员工已绑定岗位！");
        }



        CeoBusinessOrganizationPositionRelation personInfo = null;
        personInfo = new CeoBusinessOrganizationPositionRelation();
        personInfo.setEmployeeId("ROOT");
        personInfo.setEmployeeName("系统自动");

        ProcessUserDO processUserDO = new ProcessUserDO();
        processUserDO.setEmployeeId(personInfo.getEmployeeId());
        processUserDO.setEmployeeName(personInfo.getEmployeeName());

        Integer positionTypeId = ceoBusinessOrganizationPositionRelation.getPositionTypeId();
        if(positionTypeId == 2 || positionTypeId == 10 || positionTypeId == 11 || positionTypeId == 1 || positionTypeId == 12){
            // 战区
            if(positionTypeId == 1){
                accountService.bindRole(employeeId, Arrays.asList(2),processUserDO,positionId);
            }
            // 大区
            if(positionTypeId == 12){
                accountService.bindRole(employeeId, Arrays.asList(6),processUserDO,positionId);
            }
            // 省区
            if(positionTypeId == 11){
                accountService.bindRole(employeeId, Arrays.asList(7),processUserDO,positionId);
            }
            // 分公司
            if(positionTypeId == 2){
                accountService.bindRole(employeeId, Arrays.asList(3),processUserDO,positionId);
            }

            // 区域经理
            if(positionTypeId == 10){
                accountService.bindRole(employeeId, Arrays.asList(5),processUserDO,positionId);
            }


            String organizationId = ceoBusinessOrganizationPositionRelation.getOrganizationId();
            DepartEntity entity = deptMapper.selectOne(new QueryWrapper<DepartEntity>().eq("organization_id", organizationId).eq("delete_flag", 0));
            if(Objects.nonNull(entity)){
                // 修改部门
                accountService.bindDept(employeeId, Arrays.asList(entity.getId()),processUserDO);
            }

        }



        // 添加relation业务员相关信息
        employeeMapper.onBoardFor123(positionId, employeeId, employeeName, onboardTime, person, 3);
        // 更新客户表业务员相关信息
        employeeMapper.updateCustomerEmployee(positionId);
        if (StringUtils.isNotBlank(employeeId)) {
            // 预发环境相关操作
            // 添加auth_um_user_group相关信息
            employeeMapper.insertGroup(employeeId);
            // 调整旧库
            employeeService2.employeeEntry(positionId, employeeId, employeeName, person);
        }
    }

    private SfaCustomer checkIsExist(String employeeId, String mobile, int channel) {
        SfaCustomer sfaCustomer =
                sfaCustomerMapper.selectOne(
                        new QueryWrapper<SfaCustomer>().eq("mobile_number", mobile).eq("channel", channel));
        if (Objects.nonNull(sfaCustomer)) {
            return sfaCustomer;
        }

        return null;
    }


    private String ceoManagerOnboard(
            JobPositionChangeDto dto,
            String processUserId,
            String processUserName,
            ApplyMemberPo applyMemberPo) {

        String memberKey;

        String organizationRelationId = null;
        int positionType = 0;

        Integer currentPosition = applyMemberPo.getPosition();


        // 分公司
        if(2 == currentPosition){
            organizationRelationId = applyMemberPo.getCompanyOrganizationId();
            positionType = 2;
        }
        // 省区总监
        else if(6 == currentPosition){
            organizationRelationId = applyMemberPo.getProvinceOrganizationId();
            positionType = 11;
        }
        // 大区总监
        else if(5 == currentPosition){
            organizationRelationId = applyMemberPo.getVareaOrganizationId();
            positionType = 12;
        }
        // 战区总监
        else if(3 == currentPosition){
            organizationRelationId = applyMemberPo.getAreaOrganizationId();
            positionType = 1;
        }else{
           throw new ApplicationException("错误的岗位类型");
        }


        // 检查对应岗位有没有人
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation =
                ceoBusinessOrganizationPositionRelationMapper.selectOne(
                        new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                .eq("organization_id", organizationRelationId)
                                .eq("channel", 3));
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
            throw new ApplicationException("岗位不存在");
        }

        if (StringUtils.isNotBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())) {
            throw new ApplicationException("该岗位目前暂无空缺");
        }

        // 更具手机号检查岗位报表是否存在
        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMobile, applyMemberPo.getUserMobile()).orderByDesc(SfaEmployeeInfoModel::getId).last("limit 1"));
        if(Objects.isNull(sfaEmployeeInfoModel)){
            // 创建SfaEmployeeInfoModel信息
            sfaEmployeeInfoModel =
                    createSfaEmployeeInfoModel(
                            applyMemberPo,
                            dto,
                            ceoBusinessOrganizationPositionRelation,
                            processUserId,
                            processUserName);
            sfaEmployeeInfoMapper.insert(sfaEmployeeInfoModel);
        }else{
            sfaEmployeeInfoModel.setEmployeeStatus(2);
            String employeeId = dto.getEmployeeId();
            if(StringUtils.isNotBlank(employeeId)){
                sfaEmployeeInfoModel.setEmployeeId(employeeId);
            }else{
                sfaEmployeeInfoModel.setEmployeeId(sfaEmployeeInfoModel.getMobile());
            }
            sfaEmployeeInfoModel.setApplicationId(applyMemberPo.getId());
            sfaEmployeeInfoModel.setPositionId(ceoBusinessOrganizationPositionRelation.getPositionId());
            sfaEmployeeInfoModel.setAreaCode(applyMemberPo.getAreaOrganizationId());
            sfaEmployeeInfoModel.setAreaName(applyMemberPo.getArea());
            sfaEmployeeInfoModel.setVareaOrganizationId(applyMemberPo.getVareaOrganizationId());
            sfaEmployeeInfoModel.setVareaOrganizationName(applyMemberPo.getVareaOrganizationName());
            sfaEmployeeInfoModel.setProvinceOrganizationId(applyMemberPo.getProvinceOrganizationId());
            sfaEmployeeInfoModel.setProvinceOrganizationName(applyMemberPo.getProvinceOrganizationName());
            sfaEmployeeInfoModel.setCompanyCode(applyMemberPo.getCompanyOrganizationId());
            sfaEmployeeInfoModel.setCompanyName(applyMemberPo.getCompany());
            sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);
        }



        // 插入岗位表信息
        PositionRelationDTO positionRelationDTO = PositionRelationDTO.init(sfaEmployeeInfoModel.getPositionId(), positionType, sfaEmployeeInfoModel.getId(), organizationRelationId, 0, LocalDateTime.now(),ceoBusinessOrganizationPositionRelation.getChannel());
        positionRelationDTO.setBusinessGroup(ceoBusinessOrganizationPositionRelation.getBusinessGroup());


        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new QueryWrapper<SfaInterviewProcessModel>().eq("application_id", sfaEmployeeInfoModel.getApplicationId()));
        if(Objects.isNull(sfaInterviewProcessModel)){
            throw new ApplicationException("面试流程无法获取");
        }
        // 获取实际入职日期
        String onboardTime = "";
        if(Objects.nonNull(sfaInterviewProcessModel.getOnboardTime())){
            positionRelationDTO.setStartValidDate(LocalDateTimeUtils.convertDateToLDT(sfaInterviewProcessModel.getOnboardTime()));
        }

        Integer positionTypeId = ceoBusinessOrganizationPositionRelation.getPositionTypeId();
        if(positionTypeId == 10){
            positionRelationDTO.setParentOrganizationCode(sfaEmployeeInfoModel.getCompanyCode());
        }else if(positionTypeId == 2){
            positionRelationDTO.setParentOrganizationCode(sfaEmployeeInfoModel.getProvinceOrganizationId());
        }else if(positionTypeId == 11){
            positionRelationDTO.setParentOrganizationCode(sfaEmployeeInfoModel.getVareaOrganizationId());
        }else if(positionTypeId == 12){
            positionRelationDTO.setParentOrganizationCode(sfaEmployeeInfoModel.getAreaCode());
        }
        positionRelationDTO.setOrganizationInfo(sfaEmployeeInfoModel.getDepartmentCode(),sfaEmployeeInfoModel.getDepartmentName(),"department");
        positionRelationDTO.setOrganizationInfo(sfaEmployeeInfoModel.getCompanyCode(),sfaEmployeeInfoModel.getCompanyName(),"company");
        positionRelationDTO.setOrganizationInfo(sfaEmployeeInfoModel.getProvinceOrganizationId(),sfaEmployeeInfoModel.getProvinceOrganizationName(),"province");
        positionRelationDTO.setOrganizationInfo(sfaEmployeeInfoModel.getVareaOrganizationId(),sfaEmployeeInfoModel.getVareaOrganizationName(),"varea");
        positionRelationDTO.setOrganizationInfo(sfaEmployeeInfoModel.getAreaCode(),sfaEmployeeInfoModel.getAreaName(),"area");
        positionRelationDTO.setEmpId(dto.getEmployeeId());
        positionRelationService.insertPositionRelation(positionRelationDTO);



        employeeEntry(
                sfaEmployeeInfoModel.getPositionId(),
                dto.getEmployeeId(),
                dto.getEmployeeName(),
                processUserId,
                dto.getExecuteDate());


        // 修改审核人信息
        zwOnboardStrategy.changeValidPerson(
                sfaEmployeeInfoModel.getEmployeeId(), sfaEmployeeInfoModel.getPositionId(),sfaEmployeeInfoModel.getEmployeeName());


        Pattern p = Pattern.compile("^1[3-9]\\d{9}$", Pattern.CASE_INSENSITIVE);
        Matcher m = p.matcher(dto.getEmployeeId());
        // 区域经理人需要创建账号
        if(4 == currentPosition || m.matches()){
            loginUserService.createAccount(sfaEmployeeInfoModel.getMobile(),sfaEmployeeInfoModel.getId());
        }


        Integer position = applyMemberPo.getPosition();

        if(Objects.nonNull(sfaEmployeeInfoModel.getMemberKey())){
            memberKey = sfaEmployeeInfoModel.getMemberKey().toString();

            // 修改sfa_employee_info,sfacustome的信息
            EmployeeInfoUpdateDto employeeInfoUpdateDto = new EmployeeInfoUpdateDto();
            employeeInfoUpdateDto.setApplyMemberPo(applyMemberPo);
            // 设置为入职
            sfaEmployeeInfoModel.setEmployeeStatus(2);
            employeeInfoUpdateDto.setEmployeeInfoModel(sfaEmployeeInfoModel);
            if(Objects.nonNull(sfaInterviewProcessModel.getOnboardTime())){
                employeeInfoUpdateDto.setOnboardDate(LocalDateTimeUtils.convertDateToLDT(sfaInterviewProcessModel.getOnboardTime()).toLocalDate().toString());
            }else{
                employeeInfoUpdateDto.setOnboardDate(LocalDate.now().toString());
            }
            employeeInfoUpdateDto.setCeoType(1);
            employeeInfoUpdateDto.setCeoBusinessOrganizationPositionRelation(ceoBusinessOrganizationPositionRelation);
            SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new LambdaQueryWrapper<SfaCustomer>().eq(SfaCustomer::getMemberKey, memberKey).last("limit 1"));
            employeeInfoUpdateDto.setSfaCustomer(sfaCustomer);
            String contractCompany = dto.getContractCompany();

            employeeInfoUpdateDto.setContractCompany(contractCompany);
            employeeInfoProcessService.updateEmployeeInfo(employeeInfoUpdateDto);
            // 根据memberKey全部设置为非汰换
            rootConnectorUtil.openMember(memberKey);
            // 修改信息
            memberClient.synEmployeeInfo(sfaEmployeeInfoModel.getId(),LocalDate.parse(employeeInfoUpdateDto.getOnboardDate()).atStartOfDay(),null);


        }else{
            // 造旺总监开户
            memberKey = openStore(sfaEmployeeInfoModel,dto.getJoiningCompany(),applyMemberPo.getCeoType(),position,applyMemberPo.getJobsType(),applyMemberPo.getAgentTown(),dto.getContractCompany());
            // 保存memberKey
            sfaEmployeeInfoModel.setMemberKey(Long.valueOf(memberKey));
            sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);
            // 创建sfa_customer
            SaveCustomerInvoke invoke = new SaveCustomerInvoke(bhCustomerSaveCommand);
            invoke.call(createCustomerModel(sfaEmployeeInfoModel, memberKey));
        }



        // 发送短信
        // 发送入职短信
        interviewService.sendMessage(
                sfaEmployeeInfoModel.getCompanyName(),
                PositionEnum.MANAGER.getPositionName(),
                sfaEmployeeInfoModel.getEmployeeName(),
                sfaEmployeeInfoModel.getMobile(),
                ProcessType.DO_ONBOARD.getProcessCode());
        return memberKey;
    }


    private SfaEmployeeInfoModel createSfaEmployeeInfoModel(
            ApplyMemberPo applyMemberPo,
            JobPositionChangeDto dto,
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation,
            String processUserId,
            String processUserName) {
        SfaEmployeeInfoModel model = new SfaEmployeeInfoModel();


        CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>()
                .eq(CeoBusinessOrganizationViewEntity::getOrganizationId, ceoBusinessOrganizationPositionRelation.getOrganizationId())
        );

        model.setAreaName(viewEntity.getOrgName3());
        model.setAreaCode(viewEntity.getOrgId3());
        model.setPositionId(ceoBusinessOrganizationPositionRelation.getPositionId());
        model.setVareaOrganizationId(viewEntity.getVirtualAreaId());
        model.setVareaOrganizationName(viewEntity.getVirtualAreaName());
        model.setProvinceOrganizationId(viewEntity.getProvinceId());
        model.setProvinceOrganizationName(viewEntity.getProvinceName());
        model.setCompanyCode(viewEntity.getOrgId2());
        model.setCompanyName(viewEntity.getOrgName2());
        model.setDepartmentName(viewEntity.getDepartmentName());
        model.setDepartmentCode(viewEntity.getDepartmentId());
        model.setApplicationId(applyMemberPo.getId());
        model.setGender(applyMemberPo.getGender());
        model.setMobile(applyMemberPo.getUserMobile());
        model.setEmployeeStatus(2);
        model.setAffiliation(2);
        model.setPostType(1);
        model.setOnboardTime(dto.getExecuteDate());
        model.setDistrict(applyMemberPo.getAgentDistrict());
        model.setCity(applyMemberPo.getAgentCity());
        model.setProvince(applyMemberPo.getAgentProvince());
        model.setEmployeeName(applyMemberPo.getUserName());
        model.setStatus(1);
        model.setEmployeeId(dto.getEmployeeId());
        model.setChannel(3);
        model.setType(1);
        model.setCreateUserName(processUserName);
        model.setCreateUserId(processUserId);
        model.setCreateTime(new Date());
        model.setJoiningCompany(dto.getJoiningCompany());
        model.setContractCompany(dto.getContractCompany());
        return model;
    }


    private String openStore(SfaEmployeeInfoModel sfaEmployeeInfoModel, String joinCompany, Integer ceoType, Integer position, int jobsType, String agentTown, String contractCompany) {
        // 根据手机号判断是否账号已存在
        SfaCustomer sfaCustomer =
                checkIsExist(
                        sfaEmployeeInfoModel.getEmployeeId(),
                        sfaEmployeeInfoModel.getMobile(),
                       3);
        if (Objects.nonNull(sfaCustomer)) {
            throw new ApplicationException("申请人账号已开通");
        }

        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new QueryWrapper<SfaInterviewProcessModel>().eq("application_id", sfaEmployeeInfoModel.getApplicationId()));
        if(Objects.isNull(sfaInterviewProcessModel)){
            throw new ApplicationException("面试流程无法获取");
        }
        String inductionDate = "";
        LocalDateTime recommendOnboardTime = sfaInterviewProcessModel.getRecommendOnboardTime();
        if(Objects.nonNull(recommendOnboardTime)){
            inductionDate = recommendOnboardTime.toString();
        }
        // 获取实际入职日期

        String onboardTime = "";
        if(Objects.nonNull(sfaEmployeeInfoModel.getOnboardTime())){
            onboardTime = LocalDateTimeUtils.formatTime(sfaEmployeeInfoModel.getOnboardTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss);
        }

        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());


        OpenAccountModel openAccountModel = new OpenAccountModel();
        openAccountModel.setBaseInfo(sfaEmployeeInfoModel.getMobile(),sfaEmployeeInfoModel.getEmployeeName(),sfaEmployeeInfoModel.getGender(),null,applyMemberPo.getBirthDate(),applyMemberPo.getPicUrl());
        openAccountModel.setRealInductionDate(onboardTime);
        openAccountModel.setProcessDate(LocalDateTimeUtils.formatNow(LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
        openAccountModel.setImageName(applyMemberPo.getPicUrl());
        openAccountModel.setCompanyName(joinCompany);
        Integer gender = applyMemberPo.getGender();
        if(gender == 1){
            openAccountModel.setGender("M");
        }else{
            openAccountModel.setGender("F");
        }
        // 设置为入职
        openAccountModel.setEx2(2);
        // 设置ex1
        openAccountModel.setEx1(applyMemberPo.getCeoType(),applyMemberPo.getJobsType(),applyMemberPo.getPosition());
        // 设置产品组信息
        MemberProductGroupInfoVo memberProductGroupInfoVo = new MemberProductGroupInfoVo();
        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(applyMemberPo.getBusinessGroup());
        memberProductGroupInfoVo.setProductGroupId(sfaBusinessGroupEntity.getBusinessGroupCode());
        // 设置产品组为当前业务组下的所有产品
//        List<ProductionGroupEntity> productionGroupEntities = productionGroupMapper.selectList(new QueryWrapper<ProductionGroupEntity>().eq("business_group", sfaBusinessGroupEntity.getId()).eq("status", 1));
//        List<String> categoryIds = productionGroupEntities.stream().map(ProductionGroupEntity::getCategoryId).collect(Collectors.toList());
//        memberProductGroupInfoVo.setCategoryIds(categoryIds);
        memberProductGroupInfoVo.setArea(sfaEmployeeInfoModel.getAreaName());
        memberProductGroupInfoVo.setAreaCode(sfaEmployeeInfoModel.getAreaCode());
        memberProductGroupInfoVo.setRegion(Optional.ofNullable(sfaEmployeeInfoModel.getVareaOrganizationName()).orElse(""));
        memberProductGroupInfoVo.setRegionCode(Optional.ofNullable(sfaEmployeeInfoModel.getVareaOrganizationId()).orElse(""));
        memberProductGroupInfoVo.setProvinceCode(Optional.ofNullable(sfaEmployeeInfoModel.getProvinceOrganizationId()).orElse(""));
        memberProductGroupInfoVo.setProvinceArea(Optional.ofNullable(sfaEmployeeInfoModel.getProvinceOrganizationName()).orElse(""));
        memberProductGroupInfoVo.setCompanyCode(Optional.ofNullable(sfaEmployeeInfoModel.getCompanyCode()).orElse(""));
        memberProductGroupInfoVo.setCompany(Optional.ofNullable(sfaEmployeeInfoModel.getCompanyName()).orElse(""));
        memberProductGroupInfoVo.setBranch(Optional.ofNullable(sfaEmployeeInfoModel.getDepartmentName()).orElse(""));
        memberProductGroupInfoVo.setBranchCode(Optional.ofNullable(sfaEmployeeInfoModel.getDepartmentCode()).orElse(""));
        memberProductGroupInfoVo.setProvince(applyMemberPo.getAgentProvince());
        memberProductGroupInfoVo.setCity(applyMemberPo.getAgentCity());
        memberProductGroupInfoVo.setDistrict(applyMemberPo.getAgentDistrict());
        memberProductGroupInfoVo.setStreet(applyMemberPo.getStreet());
        memberProductGroupInfoVo.setSigningCompany(joinCompany);
        memberProductGroupInfoVo.setEx1(openAccountModel.getEx1());
        memberProductGroupInfoVo.setEx2(openAccountModel.getEx2());
        memberProductGroupInfoVo.setEntryTime(LocalDateTimeUtils.formatTime(LocalDateTime.now(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
        List<MemberProductGroupInfoVo> list = Collections.singletonList(memberProductGroupInfoVo);
        openAccountModel.setMemberProductGroupInfos(list);

        return String.valueOf(rootConnectorUtil.openAccount(openAccountModel));
    }

    private String createStoreName(int appliacationId, String companyName, String employeeName) {
        return appliacationId + companyName + employeeName;
    }


    private String applicationMemberKey(
            int applicationId,
            String positionId,
            String companyName,
            String mobile,
            String employeeName) {

        String key = memberConnectorUtil.sendQueryByMobileToMEMBER(mobile);
        if (StringUtils.isNotBlank(key)) {
            // 转换成非汰换
            rootConnectorUtil.openMember(key);
            return key;
        }

        Long memberKey = null;
        try {
            memberKey =
                    rootConnectorUtil.operStore(
                            mobile,
                            StoreChannel.RETAILERS.getCode(),
                            positionId,
                            createStoreName(applicationId, companyName, employeeName));
        } catch (Exception e) {
            throw new ApplicationException(e.getMessage());
        }

        if (Objects.isNull(memberKey)) {
            throw new ApplicationException("旺铺创建客户失败");
        }
        return memberKey.toString();
    }

    private OwtCustomerImportModel createCustomerModel(
            SfaEmployeeInfoModel sfaEmployeeInfoModel, String memberKey) {
        OwtCustomerImportModel model = new OwtCustomerImportModel();
        model.setPositionId(sfaEmployeeInfoModel.getPositionId());
        model.setMemberKey(memberKey);
        if (Objects.nonNull(sfaEmployeeInfoModel.getGender())) {
            model.setSex(sfaEmployeeInfoModel.getGender().toString());
        }

        model.setAddress(
                sfaEmployeeInfoModel.getProvince()
                        + sfaEmployeeInfoModel.getCity()
                        + sfaEmployeeInfoModel.getDistrict());
        model.setAreaName(sfaEmployeeInfoModel.getAreaName());
        model.setCompanyName(sfaEmployeeInfoModel.getCompanyName());
        model.setBranchName(sfaEmployeeInfoModel.getBranchName());
        model.setChannel("3");
        model.setProvince(sfaEmployeeInfoModel.getProvince());
        model.setCity(sfaEmployeeInfoModel.getCity());
        model.setDistrict(sfaEmployeeInfoModel.getDistrict());
        model.setEmployeeId(sfaEmployeeInfoModel.getEmployeeId());
        model.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());
        model.setCustomerName(
                createStoreName(
                        sfaEmployeeInfoModel.getApplicationId(),
                        sfaEmployeeInfoModel.getCompanyName(),
                        sfaEmployeeInfoModel.getEmployeeName()));
        model.setMobile(sfaEmployeeInfoModel.getMobile());

        return model;
    }


    private void bindRecruitment(
            Integer position,
            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel,
            ApplyMemberPo applyMemberPo) {
        // 合伙人
        if (1 == position || 4 == position) {
            Integer recruitmentId = sfaInterviewProcessRecordModel.getRecruitmentId();
            if (Objects.nonNull(recruitmentId) && recruitmentId != 0) {
                recruitmentNeedsService.bind(applyMemberPo.getId(), recruitmentId);
            }
        } else if(2 == position){
            // 造旺总监
            String companyCode = sfaInterviewProcessRecordModel.getCompanyCode();
            if (StringUtils.isNotBlank(companyCode)) {

                RecruitmentNeedsPO recruitmentNeedsPO =
                        recruitmentNeedsService.selectByCompanyCode(companyCode, position);
                if (Objects.nonNull(recruitmentNeedsPO)) {
                    recruitmentNeedsService.bind(applyMemberPo.getId(), recruitmentNeedsPO.getId());
                }
            }
        }
    }
}
