package com.wantwant.sfa.backend.interview.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyService;
import com.wantwant.sfa.backend.applyMember.dto.ApplyMemberRequest;
import com.wantwant.sfa.backend.applyMember.dto.InductionInfoDTO;
import com.wantwant.sfa.backend.applyMember.vo.ApplyMemberVO;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.BusinessGroupEnum;
import com.wantwant.sfa.backend.common.CommonConstants;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.domain.emp.DO.EstablishSearchDO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdConfigPO;
import com.wantwant.sfa.backend.domain.emp.service.IBusinessBDRuleService;
import com.wantwant.sfa.backend.estimate.vo.CityManagerProbationVO;
import com.wantwant.sfa.backend.index.vo.DataOverviewVo;
import com.wantwant.sfa.backend.interview.dto.JobPositionChangeDto;
import com.wantwant.sfa.backend.interview.dto.JobPositionTaskDto;
import com.wantwant.sfa.backend.interview.dto.MemberExperienceDTO;
import com.wantwant.sfa.backend.interview.dto.SaveOrUpdateCeoDto;
import com.wantwant.sfa.backend.interview.entity.BusinessBdServerEntity;
import com.wantwant.sfa.backend.interview.entity.LaborContractEntity;
import com.wantwant.sfa.backend.interview.entity.SfaEmployeeInfoAdditional;
import com.wantwant.sfa.backend.interview.entity.SfaGroupRelationshipEntity;
import com.wantwant.sfa.backend.interview.enums.*;
import com.wantwant.sfa.backend.interview.model.*;
import com.wantwant.sfa.backend.interview.request.MarketResearchReportModel;
import com.wantwant.sfa.backend.interview.request.*;
import com.wantwant.sfa.backend.interview.service.*;
import com.wantwant.sfa.backend.interview.task.impl.OnBoardTask;
import com.wantwant.sfa.backend.interview.vo.*;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.businessBD.BusinessBdServerMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.interview.*;
import com.wantwant.sfa.backend.mapper.market.*;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletLogMapper;
import com.wantwant.sfa.backend.market.entity.SmallMarketEmployeeRelationEntity;
import com.wantwant.sfa.backend.market.entity.SmallMarketV2Entity;
import com.wantwant.sfa.backend.market.request.CeoMarketRelationRequest;
import com.wantwant.sfa.backend.market.request.PositionMarketRelationRequest;
import com.wantwant.sfa.backend.market.service.MarketService;
import com.wantwant.sfa.backend.market.service.impl.MarketManagementService;
import com.wantwant.sfa.backend.model.*;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO;
import com.wantwant.sfa.backend.notify.entity.NotifyContentEntity;
import com.wantwant.sfa.backend.notify.enums.NotifyTemplateTypeEnum;
import com.wantwant.sfa.backend.notify.model.NotifyDetailModel;
import com.wantwant.sfa.backend.notify.template.impl.ProbationNotifyContent;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.salary.service.ISalaryMiddlewareService;
import com.wantwant.sfa.backend.service.*;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.util.*;
import com.wantwant.sfa.backend.wallet.request.RetrieveRequest;
import com.wantwant.sfa.backend.wallet.service.IWalletApplicationService;
import com.wantwant.sfa.backend.zw.request.CompanyScopeRequest;
import com.wantwant.sfa.backend.zw.vo.PartnerInfoVo;
import com.wantwant.sfa.backend.zw.vo.RecruitmentNeedsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static com.wantwant.sfa.backend.transaction.enums.PositionEnum.getPositionId;

/**
 * @Description: 面试流程用Service实例。 @Auther: zhengxu @Date: 2021/11/05/下午4:09
 */
@Service
@Slf4j
public class InterviewServiceImpl implements InterviewService {
    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper
            ceoBusinessOrganizationPositionRelationMapper;

    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private MarketService marketService;

    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private SfaEmployeeRehireMapper sfaEmployeeRehireMapper;
    @Autowired
    private ROOTConnectorUtil rootConnectorUtil;
    @Autowired
    private SfaCustomerMapper sfaCustomerMapper;
    @Autowired
    private SfaInterviewReasonMapper sfaInterviewReasonMapper;
    @Autowired
    private SfaInterviewReasonRelationMapper sfaInterviewReasonRelationMapper;
    @Autowired
    private SmallMarketPositionMapper smallMarketPositionMapper;
    @Autowired
    private MEMBERConnectorUtil memberConnectorUtil;
    @Autowired
    private SfaMemberEditLogMapper sfaMemberEditLogMapper;
    @Autowired
    private SfaApplyResignMapper sfaApplyResignMapper;
    @Autowired
    private CeoTerminalMemberMapper ceoTerminalMemberMapper;
    @Autowired
    private MemberExperienceService experienceService;
    @Autowired
    private RedisUtil redisUtil;


    public static final String LOCK_APPLY_MEMBER = "applyMember";

    private static final String LOCK_APPLY_MEMBER_OPTION_KEY = "option:lock";
    @Autowired
    private ApplyMemberService applyMemberService;
    @Autowired
    private SfaInterviewProcessConfigMapper processconfigMapper;
    @Autowired
    private ResearchReportMapper researchReportMapper;
    @Autowired
    SettingServiceImpl settingServiceImpl;
    @Autowired
    private RecruitmentNeedsMapper recruitmentNeedsMapper;
    private static final String recruitMsg = "No.{0}-{1}{2}{3}";
    @Autowired
    private SfaRecruitmentRelationMapper sfaRecruitmentRelationMapper;
    @Autowired
    private GeTuiService geTuiService;
    @Autowired
    private CustomerManagementDetailService customerManagementDetailService;
    @Autowired
    private ResignService resignService;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private SmallMarketV2Mapper smallMarketV2Mapper;
    @Autowired
    private SmallMarketRegionRelationMapper smallMarketRegionRelationMapper;
    @Autowired
    private LaborContractMapper laborContractMapper;
    @Autowired
    ILaborContractService laborContractService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private MarketManagementService marketManagementService;
    @Autowired
    private NotifyContentMapper notifyContentMapper;
    @Autowired
    private TalentConnectorUtil talentConnectorUtil;
    @Autowired
    private SmallMarketEmployeeRelationMapper smallMarketEmployeeRelationMapper;
    @Autowired
    private SfaApplyMemberAdditionMapper sfaApplyMemberAdditionMapper;

    private String titleTemplate = "人员变更通知内容{0}";

    private String messageTemplate = "您好，{0}，系统提醒您人员变更清单如下，烦请进行处理，如已处理请忽略。";

    @Value("${URL.talent.login}")
    private String talentLoginUrl;
    @Autowired
    private IAuditService auditService;

    @Autowired
    private IEmployeeInfoProcessService employeeInfoProcessService;
    @Autowired
    private OnBoardTask onBoardTask;
    @Autowired
    private ISfaJobPositionTaskService sfaJobPositionTaskService;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SfaEmployeeInfoAdditionalMapper sfaEmployeeInfoAdditionalMapper;
    @Autowired
    private ISalaryMiddlewareService salaryMiddlewareService;

    @Autowired
    private BusinessBdServerMapper businessBdServerMapper;
    @Autowired
    private EmployeeSalaryMapper employeeSalaryMapper;
    @Autowired
    private GroupRelationMapper groupRelationMapper;
    @Resource
    private InterviewBigTableServiceImpl interviewBigTableService;
    @Resource
    private LoginUserService loginUserService;
    @Resource
    private IBusinessBDRuleService businessBDRuleService;
    @Resource
    private WantWalletLogMapper wantWalletLogMapper;
    @Resource
    private IWalletApplicationService walletApplicationService;
    @Resource
    private IPenaltyService penaltyService;
    @Resource
    private ICheckInterviewService checkInterviewService;


    private String delayMessageTemplate = "因{0}原因，预计入职日期调整为 {1}";

    private String IMPORT_ERROR_MESSAGE = "第{0}行，错误信息:{1}";

    @Override
    @Transactional
    public void createInterviewProcess(int applicationId, int channel) {
        log.info("【create interview process】applicationId:{}", applicationId);
        // 检查数据合法性
        ApplyMemberPo applyMemberInfo = getApplyMemberInfo(applicationId);

        Integer position = applyMemberInfo.getPosition();

        // 获取分公司
        String companyCode = applyMemberInfo.getCompanyOrganizationId();

        // 获取sfa大区组织信息
        String areaCode = applyMemberInfo.getAreaOrganizationId();

        String vareaOrganizationId = applyMemberInfo.getVareaOrganizationId();

        String provinceOrganizationId = applyMemberInfo.getProvinceOrganizationId();


        log.info("创建面试流程信息，分公司:{},大区:{}", companyCode, areaCode);
        // 选择审核人
        CeoBusinessOrganizationPositionRelation positionRelation = null;
        if (position == 2 || position == 4 || position == 6 || position == 1 || position == 7 || position == 8) {
            // 选择处理人
            SelectAuditDto dto = new SelectAuditDto();
            dto.setChannel(RequestUtils.getChannel());
            // 区域总监
            if (position == 2) {
                dto.setCurrentOrganizationId(provinceOrganizationId);
            }
            // 区域经理
            else if (position == 4) {
                dto.setCurrentOrganizationId(companyCode);
            } else if (position == 6) {
                dto.setCurrentOrganizationId(vareaOrganizationId);
            } else {
                dto.setCurrentOrganizationId(applyMemberInfo.getBranchOrganizationId());
            }
            String employeeId = configMapper.getValueByCode(ZW_HR_EMPLOYEE_ID_CODE);
            dto.setStandbyEmployeeId(employeeId);
            dto.setBusinessGroup(applyMemberInfo.getBusinessGroup());
            positionRelation = auditService.chooseAuditPerson(dto);
        } else if (position == 3 || position == 5) {
            // 一面审核人招聘助理
            String assistantHt = configMapper.getValueByCode("assistant_ht");
            if (StringUtils.isBlank(assistantHt)) {
                throw new ApplicationException("招聘助理未配置");
            }

            positionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, assistantHt).eq(CeoBusinessOrganizationPositionRelation::getChannel, 3).eq(CeoBusinessOrganizationPositionRelation::getBusinessGroup, applyMemberInfo.getBusinessGroup()).last("limit 1"));

        }


        // 创建面试过程
        SfaInterviewProcessModel sfaInterviewProcessModel = saveInterviewProcess(applicationId);
        // 创建面试记录
        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel =
                saveInterviewProcessRecord(
                        sfaInterviewProcessModel, positionRelation, applyMemberInfo);
        // 维护面试记录
        sfaInterviewProcessModel.setInterviewRecordId(sfaInterviewProcessRecordModel.getId());
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
    }

    @Override
    public InterviewEmployeeInfoVo applyInfo(InterviewApplyInfoRequest request) {
        log.info("apply info:{}", request);
        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = null;
        if (request.getIsDetails() == 0) {
            // 检查记录与员工号是否匹配
            sfaInterviewProcessRecordModel =
                    sfaInterviewProcessRecordMapper.selectOne(
                            new QueryWrapper<SfaInterviewProcessRecordModel>()
                                    .eq("id", request.getInterviewProcessRecordId()));
        } else {
            sfaInterviewProcessRecordModel =
                    sfaInterviewProcessRecordMapper.selectOne(
                            new QueryWrapper<SfaInterviewProcessRecordModel>()
                                    .eq("id", request.getInterviewProcessRecordId()));
        }
        if (Objects.isNull(sfaInterviewProcessRecordModel)) {
            throw new ApplicationException("获取面试记录信息失败！");
        }
        SfaInterviewProcessModel sfaInterviewProcessModel =
                sfaInterviewProcessMapper.selectById(
                        sfaInterviewProcessRecordModel.getInterviewProcessId());
        if (Objects.isNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("获取面试流程信息失败！");
        }

        ApplyMemberPo applyMemberPo =
                applyMemberMapper.selectById(sfaInterviewProcessModel.getApplicationId());
        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("获取面试申请信息失败！");
        }


        ResignApplyModel resignApplyModel =
                sfaApplyResignMapper.selectById(sfaInterviewProcessModel.getResignId());

        InterviewEmployeeInfoVo vo = new InterviewEmployeeInfoVo();

        ApplyMemberAdditionPo applyMemberAdditionPo = sfaApplyMemberAdditionMapper.selectOne(new LambdaQueryWrapper<ApplyMemberAdditionPo>().eq(ApplyMemberAdditionPo::getApplyId, applyMemberPo.getId()).last("limit 1"));
        if (Objects.nonNull(applyMemberAdditionPo) && Objects.nonNull(applyMemberAdditionPo.getProvince()) && Objects.nonNull(applyMemberAdditionPo.getCity())) {
            String residentialStr = applyMemberAdditionPo.getProvince() + applyMemberAdditionPo.getCity();
            if (Objects.nonNull(applyMemberAdditionPo.getDistrict())) {
                residentialStr += applyMemberAdditionPo.getDistrict();
            }
            if (Objects.nonNull(applyMemberAdditionPo.getStreet())) {
                residentialStr += applyMemberAdditionPo.getStreet();
            }
            vo.setResidentialAddress(residentialStr);
        }

        LocalDate delayOnBoardDate = sfaInterviewProcessModel.getDelayOnBoardDate();
        if (Objects.nonNull(delayOnBoardDate)) {
            String message = MessageFormat.format(delayMessageTemplate, sfaInterviewProcessModel.getDelayOnBoardReason(), delayOnBoardDate.toString());
            vo.setDelayOnBoardMessage(message);
        }

        BeanUtils.copyProperties(applyMemberPo, vo);
        String workPlace = applyMemberPo.getWorkPlace();
        if (StringUtils.isNotBlank(workPlace)) {
            vo.setWorkPlaceName(organizationMapper.getOrganizationName(workPlace));
        }
        // 设置集团内亲属关系
        settingGroupRelation(vo, applyMemberPo.getId());

        vo.setExternalComment(sfaInterviewProcessModel.getExternalComment());
        vo.setApplicationId(applyMemberPo.getId());
        vo.setEmployeeId(applyMemberPo.getEmployId());
        vo.setProvince(applyMemberPo.getAgentProvince());
        vo.setCity(applyMemberPo.getAgentCity());
        vo.setDistrict(applyMemberPo.getAgentDistrict());
        vo.setComment(sfaInterviewProcessRecordModel.getComment());
        vo.setPendingReason(sfaInterviewProcessRecordModel.getPendingReason());
        vo.setPendingReasonType(sfaInterviewProcessRecordModel.getPendingReasonType());
        //C组入职申请页面，满足无建议入职时间且有试岗开始时间
//        if(applyMemberPo.getBusinessGroup() == 3 && sfaInterviewProcessModel.getRecommendOnboardTime() == null &&
//                sfaInterviewProcessModel.getProbationStartDay() != null &&
//                sfaInterviewProcessModel.getProcessType() == ProcessType.APPLY_ONBOARD.getProcessCode() &&
//                sfaInterviewProcessModel.getProcessResult() == ProcessResult.NOT_PROCESS.getResultCode()
//        ) {
//            String probationStartDate = DateUtil.format(sfaInterviewProcessModel.getProbationStartDay(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss);
//            vo.setRecommendOnboardTime(LocalDateTime.parse(probationStartDate, DateTimeFormatter.ofPattern(LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)));
//        }else {
//            vo.setRecommendOnboardTime(sfaInterviewProcessModel.getRecommendOnboardTime());
//        }
        vo.setRecommendOnboardTime(sfaInterviewProcessModel.getRecommendOnboardTime());
        vo.setAttitudeIntentionScore(sfaInterviewProcessRecordModel.getAttitudeIntention());
        vo.setExperienceAbilityScore(sfaInterviewProcessRecordModel.getExperienceAbility());
        vo.setCustomerResourcesScore(sfaInterviewProcessRecordModel.getCustomerResources());
        vo.setOnboardTime(sfaInterviewProcessModel.getOnboardTime());
        vo.setOffTime(sfaInterviewProcessModel.getOffTime());
        vo.setOptionalRetestTime(sfaInterviewProcessModel.getOptionalRetestTime());
        vo.setSalaryStructureId(sfaInterviewProcessRecordModel.getSalaryStructureId());
        vo.setWorkMode(applyMemberPo.getWorkMode());

        String marketChannel = applyMemberPo.getMarketChannel();
        if (StringUtils.isNotBlank(marketChannel)) {
            List<Integer> marketChannels = Arrays.asList(marketChannel.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
            vo.setMarketChannel(marketChannels);
        }

        // 设置实际入职公司
        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", sfaInterviewProcessModel.getApplicationId()));
        if (Objects.nonNull(sfaEmployeeInfoModel)) {
            vo.setActualJoiningCompany(sfaEmployeeInfoModel.getActualJoiningCompany());
            // 设置岗位信息
            settingPositionDescription(applyMemberPo, sfaEmployeeInfoModel, vo);
            // 设置业务BD相关信息
            settingBusinessBDInfo(sfaInterviewProcessRecordModel, sfaEmployeeInfoModel, vo);

        } else {
            settingPositionDescription(applyMemberPo, null, vo);
            settingBusinessBDInfo(sfaInterviewProcessRecordModel, null, vo);
        }
        Integer ceoType = applyMemberPo.getCeoType();
        Integer jobsType = applyMemberPo.getJobsType();


        if (ceoType == 1 && jobsType == 2) {
            vo.setCeoType(4);
        }
        // 7.全职业务BD 8.兼职业务BD 9.承揽业务BD
        else if (ceoType == 6 && jobsType == 1) {
            vo.setCeoType(7);
        } else if (ceoType == 6 && jobsType == 2) {
            vo.setCeoType(8);
        } else if (ceoType == 7) {
            vo.setCeoType(9);
        } else {
            vo.setCeoType(ceoType);
        }

        // 设置小标市场名称
        settingRegionNames(vo, sfaInterviewProcessRecordModel);

        List<MarketResearchReportModel> researchReportModels =
                researchReportMapper.selectResearchReport(sfaInterviewProcessModel.getApplicationId());
        if (!CollectionUtils.isEmpty(researchReportModels)) {
            vo.setMarketResearchReportUrl(researchReportModels);
        }

        LocalDateTime applyTime = applyMemberPo.getApplyTime();
        if (Objects.nonNull(applyTime)) {
            LocalDate localDate = applyTime.toLocalDate();
            vo.setApplyTime(localDate.toString());
        }

        if (null != sfaInterviewProcessModel.getResignId() && Objects.nonNull(resignApplyModel)) {
            vo.setAdviceDepartureTime(resignApplyModel.getAdviceDepartureTime());
        }


        vo.setAreaCode(applyMemberPo.getAreaOrganizationId());
        vo.setAreaName(applyMemberPo.getArea());
        vo.setVareaCode(applyMemberPo.getVareaOrganizationId());
        vo.setVareaName(applyMemberPo.getVareaOrganizationName());
        vo.setProvinceCode(applyMemberPo.getProvinceOrganizationId());
        vo.setProvinceName(applyMemberPo.getProvinceOrganizationName());


        if (StringUtils.isNotBlank(applyMemberPo.getCompanyOrganizationId())) {

            vo.setCompanyCode(applyMemberPo.getCompanyOrganizationId());
            vo.setCompanyName(applyMemberPo.getCompany());
            if (Objects.nonNull(applyMemberPo.getBranchOrganizationId())) {

                vo.setBranchCode(applyMemberPo.getBranchOrganizationId());
                vo.setBranchName(applyMemberPo.getBranchOrganizationName());
            }
        }

        vo.setWwPosition(applyMemberPo.getWwPosition());
        // 设置产品组信息
//        settingProductionGroup(applyMemberPo, sfaInterviewProcessRecordModel, vo);

        // 设置面试记录
        List<InterviewProcessRecordVo> list = new ArrayList<>();

        // 获取所有上级
        if (request.getIsDetails() == 0) {
            // 获取不包含当前节点的记录
            List<InterviewProcessRecordVo> processRecordListWithOutCurrent =
                    getProcessRecordListWithOutCurrent(
                            sfaInterviewProcessRecordModel.getPrevProcessId(), list, request.getIsDetails(), applyMemberPo);
            vo.setInterviewProcessRecordVoList(processRecordListWithOutCurrent);
            // 可操作薪资结构
            vo.setShowSalaryInfo(true);
        } else {
            // 获取包含当前节点的记录
            List<InterviewProcessRecordVo> processRecordListContainsCurrent =
                    getProcessRecordListContainsCurrent(
                            sfaInterviewProcessRecordModel, list, request.getIsDetails(), applyMemberPo);
            vo.setInterviewProcessRecordVoList(processRecordListContainsCurrent);
        }
        vo.setInterviewProcessRecordId(request.getInterviewProcessRecordId());

        SfaEmployeeInfoModel employeeInfoModel =
                sfaEmployeeInfoMapper.selectOne(
                        new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applyMemberPo.getId()));
        if (Objects.nonNull(employeeInfoModel)) {
            vo.setJoiningCompany(employeeInfoModel.getJoiningCompany());
            vo.setEmployeeId(employeeInfoModel.getEmployeeId());
            vo.setWorkMode(employeeInfoModel.getWorkMode());
        }

        //新增返聘次数
        if (!Objects.isNull(employeeInfoModel)) {
            List<SfaEmployeeRehireModel> rehireModelList = sfaEmployeeRehireMapper.selectList(new QueryWrapper<SfaEmployeeRehireModel>()
                    .eq("employee_info_id", employeeInfoModel.getId())
                    .eq("delete_flag", 0)
                    .isNull("transaction_id")
            );
            vo.setRehireCount(rehireModelList.size());
        }


        // 设置原因信息
        List<InterviewReasonModel> interviewReasonModels =
                sfaInterviewReasonMapper.selectList(
                        new QueryWrapper<InterviewReasonModel>()
                                .eq("process_type", sfaInterviewProcessRecordModel.getProcessType())
                                .eq("status", 1));
        if (!CollectionUtils.isEmpty(interviewReasonModels)) {
            List<InterviewReasonVo> reasonList = new ArrayList<>();
            interviewReasonModels.forEach(
                    e -> {
                        InterviewReasonVo reasonVo = new InterviewReasonVo();
                        BeanUtils.copyProperties(e, reasonVo);
                        reasonList.add(reasonVo);
                    });
            vo.setReasonVOList(reasonList);
        } else {
            vo.setReasonVOList(ListUtils.EMPTY_LIST);
        }

        List<InterviewReasonRelationModel> interviewReasonRelationModels =
                sfaInterviewReasonRelationMapper.selectList(
                        new QueryWrapper<InterviewReasonRelationModel>()
                                .eq("process_record_id", sfaInterviewProcessRecordModel.getId())
                                .eq("status", 1));
        if (!CollectionUtils.isEmpty(interviewReasonRelationModels)) {
            List<Integer> reasonCodes = new ArrayList<>();
            interviewReasonRelationModels.forEach(
                    e -> {
                        reasonCodes.add(e.getId());
                    });
            vo.setReasons(reasonCodes);
        } else {
            vo.setReasons(ListUtils.EMPTY_LIST);
        }


        // 设置劳动合同
        LaborContractEntity laborContractEntity = laborContractMapper.selectOne(new QueryWrapper<LaborContractEntity>().eq("application_id", applyMemberPo.getId()).eq("delete_flag", 0));
        if (Objects.nonNull(laborContractEntity)) {
            vo.setLaborContract(laborContractEntity.getContract());
        }

        // 获取工作经历
        vo.setExperiences(experienceService.listExperienceByApplyId(applyMemberPo.getId()));

        // 获取招聘需求信息
        settingRecruitmentInfo(applyMemberPo.getId(), sfaInterviewProcessModel, vo);

        // 人员面试,入职申请 跳过提示文字
        if (sfaInterviewProcessRecordModel.getProcessType() == 4) {

            List<SfaInterviewProcessConfigModel> processConfig =
                    processconfigMapper.selectList(
                            new QueryWrapper<SfaInterviewProcessConfigModel>()
                                    .eq("company_code", applyMemberPo.getCompanyOrganizationId())
                                    .eq("interview_type", applyMemberPo.getPosition())
                                    .eq("process_type", 5)
                                    .eq("status", 0));
            if (CommonUtil.ListUtils.isNotEmpty(processConfig)) {
                vo.setMsg("此入职申请无需大区审核，将直接提交人资办理入职");
            }

        } else if (sfaInterviewProcessRecordModel.getProcessType() == 1) {

            List<SfaInterviewProcessConfigModel> processConfig =
                    processconfigMapper.selectList(
                            new QueryWrapper<SfaInterviewProcessConfigModel>()
                                    .eq("company_code", applyMemberPo.getCompanyOrganizationId())
                                    .eq("interview_type", applyMemberPo.getPosition())
                                    .eq("process_type", 2)
                                    .eq("status", 0));
            if (CommonUtil.ListUtils.isNotEmpty(processConfig)) {
                vo.setMsg("面试通过后即开始试岗，无需大区主管审核");
            }

        }


        if (request.getIsDetails() == 1) {
            LoginModel loginInfo = RequestUtils.getLoginInfo();
            Integer positionType = loginInfo.getPositionTypeId();

            // 仅登录用户为总部主管或总部运营组(interview_business_operator)、人资组才显示底薪
            settingServiceImpl.fresh();
            String interview_human_resources =
                    settingServiceImpl.getValue("interview_human_resources"); // 总部人资组
            String interview_business_operator =
                    settingServiceImpl.getValue("interview_business_operator"); // 总部运营组
            String interview_zb_manager = settingServiceImpl.getValue("interview_zb_manager"); // 总部主管组

            if (interview_human_resources != null && interview_human_resources.contains(request.getProcessUserId()) &&
                    sfaInterviewProcessModel.getProcessType() >= ProcessType.DO_ONBOARD.getProcessCode()) {
                vo.setHumanResourceFlag(true);
            }
            if (interview_human_resources.indexOf(request.getProcessUserId()) == -1
                    && interview_business_operator.indexOf(request.getProcessUserId()) == -1
                    && interview_zb_manager.indexOf(request.getProcessUserId()) == -1) {
                vo.setShowSalaryInfo(false);
                vo.setAdviceSalary(null);
                vo.setSalaryExpectation("");
            } else {
                vo.setShowSalaryInfo(true);
            }
            // 仅登录用户为大区总管或总部主管或总部运营组才显示面试复核评
            if (interview_zb_manager.indexOf(request.getProcessUserId()) == -1
                    && interview_business_operator.indexOf(request.getProcessUserId()) == -1) {
                List<InterviewProcessRecordVo> recordVoList = vo.getInterviewProcessRecordVoList();
                if (positionType != 1) {
                    List<InterviewProcessRecordVo> temp = new ArrayList<>();
                    recordVoList.forEach(
                            e -> {
                                if (e.getStep() == 2 || e.getStep() == 5) {
                                    e.setAttitudeIntentionScore(null);
                                    e.setCustomerResourcesScore(null);
                                    e.setExperienceAbilityScore(null);
                                }
                                temp.add(e);
                            });
                    recordVoList = temp;
                }
                vo.setInterviewProcessRecordVoList(recordVoList);
            }
        }
        return vo;
    }

    private void settingGroupRelation(InterviewEmployeeInfoVo vo, Integer id) {
        List<GroupRelationShipRequest> groupRelationShipRequests = new ArrayList<>();
        List<SfaGroupRelationshipEntity> sfaGroupRelationshipEntities = groupRelationMapper.selectList(new LambdaQueryWrapper<SfaGroupRelationshipEntity>().eq(SfaGroupRelationshipEntity::getApplyMemberId, id).eq(SfaGroupRelationshipEntity::getDeleteFlag, 0));
        if (!CollectionUtils.isEmpty(sfaGroupRelationshipEntities)) {
            sfaGroupRelationshipEntities.forEach(e -> {
                GroupRelationShipRequest groupRelationShipRequest = new GroupRelationShipRequest();
                BeanUtils.copyProperties(e, groupRelationShipRequest);
                groupRelationShipRequests.add(groupRelationShipRequest);
            });

        }
        vo.setGroupRelationShipRequests(groupRelationShipRequests);
    }

    private void settingBusinessBDInfo(SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel, SfaEmployeeInfoModel sfaEmployeeInfoModel, InterviewEmployeeInfoVo vo) {
        if (Objects.nonNull(sfaEmployeeInfoModel) && (sfaEmployeeInfoModel.getType() == 6 || sfaEmployeeInfoModel.getType() == 7)) {
            List<BusinessBdServerEntity> businessBdServerEntities = businessBdServerMapper.selectList(new LambdaQueryWrapper<BusinessBdServerEntity>().eq(BusinessBdServerEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId()).eq(BusinessBdServerEntity::getDeleteFlag, 0).eq(BusinessBdServerEntity::getStatus, 1));
            if (!CollectionUtils.isEmpty(businessBdServerEntities)) {
                List<Integer> collect = businessBdServerEntities.stream().map(BusinessBdServerEntity::getServerEmployeeInfoId).collect(Collectors.toList());
                vo.setContextEmpIdList(collect);
            }

            EmployeeSalaryPO employeeSalaryPO = employeeSalaryMapper.selectOne(new LambdaQueryWrapper<EmployeeSalaryPO>().eq(EmployeeSalaryPO::getEmployeeInfoId, sfaEmployeeInfoModel.getId()).eq(EmployeeSalaryPO::getIsDelete, 0).orderByDesc(EmployeeSalaryPO::getId).last("limit 1"));
            if (Objects.nonNull(employeeSalaryPO)) {
                vo.setPaymentType(employeeSalaryPO.getPaymentType());
            }

        } else {
            String contextEmp = sfaInterviewProcessRecordModel.getContextEmp();

            Integer paymentType = sfaInterviewProcessRecordModel.getPaymentType();

            vo.setPaymentType(paymentType);

            if (StringUtils.isNotBlank(contextEmp)) {
                List<Integer> collect = Arrays.asList(contextEmp.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
                vo.setContextEmpIdList(collect);
            }
        }
    }

    private void settingPositionDescription(ApplyMemberPo applyMemberPo, SfaEmployeeInfoModel sfaEmployeeInfoModel, InterviewEmployeeInfoVo vo) {
        String positionName = com.wantwant.sfa.backend.transaction.enums.PositionEnum.getPositionName(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());

        if (Objects.nonNull(sfaEmployeeInfoModel)) {
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id", sfaEmployeeInfoModel.getPositionId()));
            if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
                throw new ApplicationException("岗位信息获取失败");
            }
            // 设置主岗信息
            vo.setMainPositionDescription(getPositionDescription(ceoBusinessOrganizationPositionRelation.getPositionTypeId(), positionName, sfaEmployeeInfoModel.getAreaName(), sfaEmployeeInfoModel.getVareaOrganizationName(), sfaEmployeeInfoModel.getProvinceOrganizationName(), sfaEmployeeInfoModel.getCompanyName(), sfaEmployeeInfoModel.getDepartmentName()));

            List<SfaPositionRelationEntity> sfaPositionRelationEntities = sfaPositionRelationMapper.selectList(new QueryWrapper<SfaPositionRelationEntity>().eq("employee_info_id", sfaEmployeeInfoModel.getId()).eq("status", 1).eq("delete_flag", 0));
            if (!CollectionUtils.isEmpty(sfaPositionRelationEntities)) {
                // 合伙人设置产品组信息
                if (applyMemberPo.getPosition() == 1 || applyMemberPo.getPosition() == 7) {
                    List<Integer> ids = sfaPositionRelationEntities.stream().map(SfaPositionRelationEntity::getBusinessGroup).collect(Collectors.toList());
                    List<SfaBusinessGroupEntity> sfaBusinessGroupEntities = sfaBusinessGroupMapper.selectBatchIds(ids);
                    List<String> businessGroupNames = sfaBusinessGroupEntities.stream().map(SfaBusinessGroupEntity::getBusinessGroupName).collect(Collectors.toList());
                    vo.setBusinessGroupNames(businessGroupNames);
                }
                // 非合伙人设置兼岗信息
                else {
                    List<String> positionList = new ArrayList<>();
                    sfaPositionRelationEntities.stream().filter(f -> f.getPartTime() == 1).forEach(e -> {
                        String positionDescription = getPositionDescription(e.getPositionTypeId(), positionName, e.getAreaName(), e.getVareaName(), e.getProvinceName(), e.getCompanyName(), e.getDepartmentName());
                        positionList.add(positionDescription);
                    });
                    vo.setPartTimePositionDescription(positionList);
                }
            }


        } else {
            Integer businessGroupById = organizationMapper.getBusinessGroupById(applyMemberPo.getAreaOrganizationId());
            SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(businessGroupById);
            vo.setBusinessGroupNames(Arrays.asList(sfaBusinessGroupEntity.getBusinessGroupName()));
            // 合伙人设置产品组信息
            if (applyMemberPo.getPosition() == 1) {
                vo.setMainPositionDescription(getPositionDescription(7, positionName, applyMemberPo.getArea()
                        , applyMemberPo.getVareaOrganizationName(), applyMemberPo.getProvinceOrganizationName()
                        , applyMemberPo.getCompany(), applyMemberPo.getBranchOrganizationName()));
            } else if (applyMemberPo.getPosition() == 4) {
                vo.setMainPositionDescription(getPositionDescription(10, positionName, applyMemberPo.getArea()
                        , applyMemberPo.getVareaOrganizationName(), applyMemberPo.getProvinceOrganizationName()
                        , applyMemberPo.getCompany(), applyMemberPo.getBranchOrganizationName()));
            } else if (applyMemberPo.getPosition() == 7) {
                vo.setMainPositionDescription(com.wantwant.sfa.backend.transaction.enums.PositionEnum.getPositionName(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition()));
            } else if (applyMemberPo.getPosition() == 8) {
                vo.setMainPositionDescription(com.wantwant.sfa.backend.transaction.enums.PositionEnum.getPositionName(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition()));
            } else if (applyMemberPo.getPosition() == 2) {
                vo.setMainPositionDescription(getPositionDescription(2, positionName, applyMemberPo.getArea()
                        , applyMemberPo.getVareaOrganizationName(), applyMemberPo.getProvinceOrganizationName()
                        , applyMemberPo.getCompany(), applyMemberPo.getBranchOrganizationName()));
            } else if (applyMemberPo.getPosition() == 3) {
                vo.setMainPositionDescription(getPositionDescription(1, positionName, applyMemberPo.getArea()
                        , null, null, null, null));
            } else if (applyMemberPo.getPosition() == 5) {
                vo.setMainPositionDescription(getPositionDescription(12, positionName, applyMemberPo.getArea()
                        , applyMemberPo.getVareaOrganizationName(), null, null, null));
            } else if (applyMemberPo.getPosition() == 6) {
                vo.setMainPositionDescription(getPositionDescription(11, positionName, applyMemberPo.getArea()
                        , applyMemberPo.getVareaOrganizationName(), applyMemberPo.getProvinceOrganizationName(), null, null));
            }

        }

    }


    private String getPositionDescription(Integer positionTypeId, String positionName, String areaName, String vareaName, String provinceName, String companyName, String departmentName) {
        if (positionTypeId == 1) {
            return areaName + "/战区督导";
        } else if (positionTypeId == 12) {
            return areaName + "/" + vareaName + "/大区总监";
        } else if (positionTypeId == 11) {
            return areaName + "/" + vareaName + "/" + provinceName + "/省区总监";
        } else if (positionTypeId == 2) {
            return areaName + "/" + vareaName + "/" + provinceName + "/" + companyName + "/分公司总监";
        } else if (positionTypeId == 10) {
            return areaName + "/" + vareaName + "/" + provinceName + "/" + companyName + "/" + departmentName + "/区域经理";
        } else {
            return companyName + "/" + departmentName + "/" + positionName;
        }
    }


    private void settingRegionNames(InterviewEmployeeInfoVo vo, SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel) {
        String smallMarketCodes = sfaInterviewProcessRecordModel.getSmallMarketCodes();

        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getApplicationId, sfaInterviewProcessRecordModel.getId()));
        if (Objects.nonNull(sfaEmployeeInfoModel)) {
            List<SmallMarketEmployeeRelationEntity> smallMarketEmployeeRelationEntities = smallMarketEmployeeRelationMapper.selectList(new LambdaQueryWrapper<SmallMarketEmployeeRelationEntity>().eq(SmallMarketEmployeeRelationEntity::getEmployeeId, sfaEmployeeInfoModel.getId()).eq(SmallMarketEmployeeRelationEntity::getStatus, 1));
            if (!CollectionUtils.isEmpty(smallMarketEmployeeRelationEntities)) {

                smallMarketCodes = String.join(",", smallMarketEmployeeRelationEntities.stream()
                        .map(SmallMarketEmployeeRelationEntity::getSmallMarketId).map(String::valueOf)
                        .collect(Collectors.toList()));
            }
        }

        if (StringUtils.isNotBlank(smallMarketCodes)) {
            StringBuffer names = new StringBuffer("");
            String[] split = smallMarketCodes.split(",");

            vo.setSmallMarketCodes(Arrays.asList(split).stream().map(e -> {
                return Integer.valueOf(e);
            }).collect(Collectors.toList()));

            List<SmallMarketV2Entity> smallMarketV2Entities = smallMarketV2Mapper.selectList(new QueryWrapper<SmallMarketV2Entity>().in("id", split));
            if (!CollectionUtils.isEmpty(smallMarketV2Entities)) {
                smallMarketV2Entities.forEach(e -> {
                    if (names.length() > 0) {
                        names.append(",");
                    }
                    names.append(e.getSmallMarketName());
                });
            }
            vo.setRegionNames(names.toString());
        }
    }

//    private void settingProductionGroup(ApplyMemberPo applyMemberPo, SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel, InterviewEmployeeInfoVo vo) {
//
//        List<Integer> productionGroupCodes = new ArrayList<>();
//
//        String companyOrganizationId = applyMemberPo.getCompanyOrganizationId();
//        if(StringUtils.isEmpty(companyOrganizationId)) {//分公司以上的直接return
//            return;
//        }
//        OrganizationRelationModel organizationRelationModel = organizationBindRelationMapper.selectOne(new QueryWrapper<OrganizationRelationModel>()
//                .eq("sfa_org_code", companyOrganizationId)
//                .eq("status", 1)
//                .eq("channel", RequestUtils.getChannel())
//        );
//
//        if(Objects.isNull(organizationRelationModel)){
//            throw new ApplicationException("组织信息绑定错误");
//        }
//
//        String productionGroupCode = applyMemberPo.getProductionGroupCode();
//        if(StringUtils.isNotBlank(productionGroupCode)){
//            productionGroupCodes = Arrays.asList(productionGroupCode.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
//        }
//
//        // 非试点分公司默认全选
//        String experimental_company = configMapper.getValueByCode("experimental_company");
//        if(!experimental_company.contains(companyOrganizationId)){
//            List<OrganizationRelationModel> organizationRelationModels = organizationBindRelationMapper.selectList(new QueryWrapper<OrganizationRelationModel>()
//                    .eq("org_code", organizationRelationModel.getOrgCode())
//                    .eq("business_group", organizationRelationModel.getBusinessGroup())
//                    .eq("channel",RequestUtils.getChannel())
//                    .eq("status", 1)
//            );
//            // 非分公司线别拆分,返回空集合
//            if(!CollectionUtils.isEmpty(organizationRelationModels) && organizationRelationModels.size() == 1){
//                return;
//            }
//        }
//        vo.setProductionGroup(productionGroupCodes);
//    }


    private void settingRecruitmentInfo(
            Integer applyId,
            SfaInterviewProcessModel sfaInterviewProcessModel,
            InterviewEmployeeInfoVo vo) {
        Integer processType = sfaInterviewProcessModel.getProcessType();
        Integer processResult = sfaInterviewProcessModel.getProcessResult();
        // 从流程记录表中获取需求ID
        if (processType < ProcessType.DO_ONBOARD.getProcessCode()
                || (processType == ProcessType.DO_ONBOARD.getProcessCode()
                && !processResult.equals(ProcessResult.PASS.getResultCode()))) {
            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel =
                    sfaInterviewProcessRecordMapper.selectById(
                            sfaInterviewProcessModel.getInterviewRecordId());
            if (Objects.isNull(sfaInterviewProcessRecordModel)) {
                throw new ApplicationException("流程记录获取失败");
            }
            if (Objects.nonNull(sfaInterviewProcessRecordModel.getRecruitmentId())
                    && sfaInterviewProcessRecordModel.getRecruitmentId() != 0) {
                RecruitmentNeedsPO recruitmentNeedsPO =
                        recruitmentNeedsMapper.selectById(sfaInterviewProcessRecordModel.getRecruitmentId());
                if (Objects.isNull(recruitmentNeedsPO)) {
                    throw new ApplicationException("招聘需求获取失败");
                }

                String msg =
                        MessageFormat.format(
                                recruitMsg,
                                recruitmentNeedsPO.getId(),
                                recruitmentNeedsPO.getProvinceName(),
                                recruitmentNeedsPO.getCityName(),
                                recruitmentNeedsPO.getDistrictName());
                vo.setRecruitmentTitle(msg);
                vo.setRecruitmentId(recruitmentNeedsPO.getId());
            }
        } else {
            SfaRecruitmentRelationModel sfaRecruitmentRelationModel =
                    sfaRecruitmentRelationMapper.selectOne(
                            new QueryWrapper<SfaRecruitmentRelationModel>()
                                    .eq("apply_id", applyId)
                                    .eq("status", 1));
            if (Objects.nonNull(sfaRecruitmentRelationModel)) {
                RecruitmentNeedsPO recruitmentNeedsPO =
                        recruitmentNeedsMapper.selectById(sfaRecruitmentRelationModel.getRecruitmentId());
                if (Objects.isNull(recruitmentNeedsPO)) {
                    throw new ApplicationException("招聘需求获取失败");
                }
                String msg =
                        MessageFormat.format(
                                recruitMsg,
                                recruitmentNeedsPO.getId(),
                                recruitmentNeedsPO.getProvinceName(),
                                recruitmentNeedsPO.getCityName(),
                                recruitmentNeedsPO.getDistrictName());
                vo.setRecruitmentTitle(msg);
                vo.setRecruitmentId(recruitmentNeedsPO.getId());
            }
        }
    }

    private List<InterviewProcessRecordVo> getProcessRecordListContainsCurrent(
            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel,
            List<InterviewProcessRecordVo> list,
            Integer isDetails,
            ApplyMemberPo applyMemberPo) {
        // 获取当前节点
        getProcessRecordList(sfaInterviewProcessRecordModel.getId(), list, 1, applyMemberPo);
        // 递归获取上一节点
        getProcessRecordList(sfaInterviewProcessRecordModel.getPrevProcessId(), list, 0, applyMemberPo);


        if (!CollectionUtils.isEmpty(list)) {
            list =
                    list.stream()
                            .filter(e -> e.getStep() != ProcessType.PROBATION.getStep() && e.getStep() != ProcessType.ZB_VERIFY.getStep()
                                    )
                            .collect(Collectors.toList());
            Collections.reverse(list);
        }

        // 过滤重复的入职信息
        list = filterOnBoard(list);

        return list;
    }

    private List<InterviewProcessRecordVo> getProcessRecordListWithOutCurrent(
            Integer prevProcessId, List<InterviewProcessRecordVo> list, Integer isDetails, ApplyMemberPo applyMemberPo) {
        // 递归获取上一节点信息
        getProcessRecordList(prevProcessId, list, 0, applyMemberPo);
        if (!CollectionUtils.isEmpty(list)) {
            list = list.stream().filter(e -> e.getStep() != ProcessType.PROBATION.getStep() && e.getStep() != ProcessType.ZB_VERIFY.getStep()
                    ).collect(Collectors.toList());
            Collections.reverse(list);
        }
        // 过滤重复的入职信息
        list = filterOnBoard(list);

        return list;
    }

    private List<InterviewProcessRecordVo> filterOnBoard(List<InterviewProcessRecordVo> list) {
        List<InterviewProcessRecordVo> newList = new ArrayList<>();

        list.forEach(
                e -> {
                    if (e.getStep() == 7) {
                        Optional<InterviewProcessRecordVo> first =
                                newList.stream().filter(x -> x.getStep() == 7).findFirst();
                        if (!first.isPresent()) {
                            newList.add(e);
                        }
                    } else {
                        newList.add(e);
                    }
                });

        return newList;
    }


    @Override
    @Transactional(propagation = Propagation.NESTED)
    public void onboard(InterviewOnboardModel model, String processUserId, String processUserName, boolean callBackFromEhrFlag) {
        // 根据手机号获取申请信
        ApplyMemberPo applyMemberPo =
                applyMemberMapper.selectApplyInfoByMobile(model.getMobileNumber());
        // 修改报名表的建议底薪
        applyMemberPo.setAdviceSalary(model.getAdviceSalary());


        applyMemberMapper.updateById(applyMemberPo);

        if (2 == applyMemberPo.getPosition() && StringUtils.isBlank(model.getEmployeeNo()) && model.getJoiningCompany().equals("旺旺")) {
            throw new ApplicationException("造旺总监必须填写员工号");
        }

        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("无法通过手机号找到申请信息");
        }
        // 获取申请的岗位信息
        Integer position = applyMemberPo.getPosition();
        if (Objects.isNull(position)) {
            throw new ApplicationException("申请岗位信息错误");
        }

        SfaInterviewProcessModel sfaInterviewProcessModel =
                sfaInterviewProcessMapper.selectOne(
                        new QueryWrapper<SfaInterviewProcessModel>()
                                .eq("application_id", applyMemberPo.getId()));
        if (Objects.isNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("未找到对应的面试流程");
        }

        List<SfaInterviewProcessRecordModel> processRecordModelList =
                sfaInterviewProcessRecordMapper.selectList(
                        new QueryWrapper<SfaInterviewProcessRecordModel>()
                                .eq("interview_process_id", sfaInterviewProcessModel.getId())
                                .eq("process_type", ProcessType.DO_ONBOARD.getProcessCode())
                                .orderByDesc("id"));
        if (CollectionUtils.isEmpty(processRecordModelList)) {
            throw new ApplicationException("未找到试岗记录");
        }
        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = processRecordModelList.get(0);
        if (Objects.isNull(sfaInterviewProcessRecordModel)) {
            throw new ApplicationException("未找到对应的面试记录");
        }

        if (sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.CLOSE.getResultCode()
                || sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.FAILED.getResultCode()
                || sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.PASS.getResultCode()
                || sfaInterviewProcessRecordModel.getProcessResult()
                == ProcessResult.CLOSE.getResultCode()) {
            throw new ApplicationException("请勿重复操作");
        }

        if (StringUtils.isBlank(model.getEmployeeNo())) {
            model.setEmployeeNo(applyMemberPo.getUserMobile());
        }

        // 如果是区域经理或者是造旺总监，检查下岗位是否被占用
        if (position == 2) {
            String orgCode = position == 2 ? applyMemberPo.getCompanyOrganizationId() : applyMemberPo.getBranchOrganizationId();
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", orgCode).eq("channel", RequestUtils.getChannel()));
            if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
                throw new ApplicationException("岗位不存在");
            }
            if (StringUtils.isNotBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())) {
                throw new ApplicationException("岗位暂无空缺");
            }

            // 检查执行计划中是否有待入职的
            sfaJobPositionTaskService.checkPosition(position, orgCode);
        }

        LocalDate executeDate = LocalDate.parse(model.getOnboardDate());


        // 保存面试流程记录
        sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.PASS.getResultCode());
        sfaInterviewProcessRecordModel.setProcessUserId(processUserId);
        sfaInterviewProcessRecordModel.setProcessUserName(processUserName);
        sfaInterviewProcessRecordModel.setProcessDate(new Date());
        sfaInterviewProcessRecordModel.setComment(model.getComment());
        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);
        // 保存面试流程
        sfaInterviewProcessModel.setAssessStartTime(new Date());
        sfaInterviewProcessModel.setProcessResult(ProcessResult.PASS.getResultCode());
        sfaInterviewProcessModel.setOnboardTime(DateUtil.parse(model.getOnboardDate(), DATE_FORMAT));
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);

        // 保存劳动合同
        if (StringUtils.isNotBlank(model.getLaborContract())) {
            laborContractService.saveLaborContract(applyMemberPo.getId(), model.getLaborContract(), processUserId, processUserName);
        }


        if (BusinessGroupEnum.O.getBusinessGroupId().intValue() != RequestUtils.getBusinessGroup() &&
                BusinessGroupEnum.P.getBusinessGroupId().intValue() != RequestUtils.getBusinessGroup()) {
            // 立即执行时
            com.wantwant.sfa.backend.transaction.enums.PositionEnum positionEnum = com.wantwant.sfa.backend.transaction.enums.PositionEnum.getEnum(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
            if (positionEnum.getId() == com.wantwant.sfa.backend.transaction.enums.PositionEnum.BUSINESS_BD.getId() || positionEnum.getId() == com.wantwant.sfa.backend.transaction.enums.PositionEnum.BUSINESS_BD_CONTRACT.getId()) {
                String contextEmp = sfaInterviewProcessRecordModel.getContextEmp();
                if (StringUtils.isBlank(contextEmp)) {
                    throw new ApplicationException("无服务对象");
                }
                String theYearMonth = LocalDate.now().toString().substring(0, 7);


                // 更具组织选择编织检查模式
                BusinessBdConfigPO businessBdConfigPO = businessBDRuleService.getConfig(applyMemberPo.getBranchOrganizationId());
                if (Objects.isNull(businessBdConfigPO)) {
                    throw new ApplicationException("当前组织未配置规则");
                }

                EstablishSearchDO establishSearch = EstablishSearchDO.builder()
                        .serverEmployeeInfoId(Integer.parseInt(contextEmp.split(",")[0]))
                        .applyId(applyMemberPo.getId())
                        .positionEnum(positionEnum)
                        .theYearMonth(theYearMonth)
                        .businessGroup(applyMemberPo.getBusinessGroup())
                        .changeServer(false)
                        .departmentId(applyMemberPo.getBranchOrganizationId())
                        .checkType(businessBdConfigPO.getCheckType()).build();

                businessBDRuleService.checkOvershootEstablished(establishSearch);
            }
        }

        if (StringUtils.isBlank(model.getActualJoiningCompany())) {
            model.setActualJoiningCompany(model.getJoiningCompany());
        }

        // 报名岗位(1:造旺合伙人,2:造旺区域总监,3:大区总(不能报名该岗位),4:城市经理人) 5 大区总监 6 省区总监 7业务BD
        if(applyMemberPo.getPosition() != 1 && applyMemberPo.getPosition() != 7){
            ApplyMemberPo applyMember = applyMemberMapper.selectById(applyMemberPo.getId());
            String curOrgCode = StringUtils.EMPTY;
            switch (applyMember.getPosition()){
                case 2:curOrgCode = applyMember.getCompanyOrganizationId();
                break;
                case 6:curOrgCode = applyMember.getProvinceOrganizationId();
                break;
                case 5:curOrgCode = applyMember.getVareaOrganizationId();
                break;
                case 4:curOrgCode = applyMember.getBranchOrganizationId();
                break;
                case 3:curOrgCode = applyMember.getAreaOrganizationId();
                break;
            }

            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, curOrgCode).last("limit 1"));
            if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
                throw new ApplicationException("岗位获取失败");
            }
            // 校验入职时间是否冲突
            checkInterviewService.checkDateConflict(executeDate, ceoBusinessOrganizationPositionRelation.getPositionId(), applyMemberPo.getUserMobile());
        }

        // 保存任务
        if (executeDate.isAfter(LocalDate.now())) {
            JobPositionTaskDto dto = new JobPositionTaskDto();
            if (position == 4) {
                dto.setEmployeeId(applyMemberPo.getUserMobile());
            } else {
                dto.setEmployeeId(model.getEmployeeNo());

            }
            dto.setApplyId(applyMemberPo.getId());
            dto.setContractCompany(model.getContractCompany());
            dto.setJoiningCompany(model.getJoiningCompany());
            dto.setType(1);
            dto.setActualJoiningCompany(model.getActualJoiningCompany());
            dto.setSocialInsuranceProvince(model.getSocialInsuranceProvince());
            dto.setSocialInsuranceCity(model.getSocialInsuranceCity());
            dto.setSocialInsuranceDistrict(model.getSocialInsuranceDistrict());
            dto.setProcessUserId(processUserId);
            dto.setProcessUserName(processUserName);
            dto.setExecuteDate(LocalDate.parse(model.getOnboardDate()).atStartOfDay());
            sfaJobPositionTaskService.saveTask(dto);
        } else {


            JobPositionChangeDto jobPositionChangeDto = new JobPositionChangeDto();
            jobPositionChangeDto.setId(applyMemberPo.getId().longValue());
            jobPositionChangeDto.setEmployeeId(model.getEmployeeNo());
            jobPositionChangeDto.setEmployeeName(model.getName());
            jobPositionChangeDto.setContractCompany(model.getContractCompany());
            jobPositionChangeDto.setJoiningCompany(model.getJoiningCompany());
            jobPositionChangeDto.setActualJoiningCompany(model.getActualJoiningCompany());
            jobPositionChangeDto.setMobile(model.getMobileNumber());
            jobPositionChangeDto.setProcessUserId(processUserId);
            jobPositionChangeDto.setProcessUserName(processUserName);
            jobPositionChangeDto.setSocialInsuranceProvince(model.getSocialInsuranceProvince());
            jobPositionChangeDto.setSocialInsuranceCity(model.getSocialInsuranceCity());
            jobPositionChangeDto.setSocialInsuranceDistrict(model.getSocialInsuranceDistrict());
            jobPositionChangeDto.setExecuteDate(LocalDate.parse(model.getOnboardDate()).atStartOfDay());
            jobPositionChangeDto.setCallBackFromEhrFalg(callBackFromEhrFlag);
            onBoardTask.execute(jobPositionChangeDto);
        }

    }


    @Override
    @Transactional(propagation = Propagation.NESTED)
    public void processing(int processRecordId, String processUserId, String processUserName) {

        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel =
                sfaInterviewProcessRecordMapper.selectById(processRecordId);
        if (Objects.isNull(sfaInterviewProcessRecordModel)) {
            throw new ApplicationException("未找到对应的面试记录");
        }
        if (sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.CLOSE.getResultCode()
                || sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.FAILED.getResultCode()
                || sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.PASS.getResultCode()
                || sfaInterviewProcessRecordModel.getProcessResult()
                == ProcessResult.CLOSE.getResultCode()) {
            throw new ApplicationException("请勿重复操作");
        }

        SfaInterviewProcessModel sfaInterviewProcessModel =
                sfaInterviewProcessMapper.selectById(
                        sfaInterviewProcessRecordModel.getInterviewProcessId());
        if (Objects.isNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("未找到对应的面试流程");
        }

        sfaInterviewProcessModel.setProcessResult(ProcessResult.PROCESSING.getResultCode());
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);

        sfaInterviewProcessRecordModel.setProcessUserName(processUserName);
        sfaInterviewProcessRecordModel.setProcessUserId(processUserId);
        sfaInterviewProcessRecordModel.setProcessDate(new Date());
        sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.PROCESSING.getResultCode());
        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);
    }

    @Override
    @Transactional(propagation = Propagation.NESTED)
    public void cancel(
            int processRecordId,
            int processResult,
            String comment,
            String processUserId,
            String processUserName) {
        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel =
                sfaInterviewProcessRecordMapper.selectById(processRecordId);
        if (Objects.isNull(sfaInterviewProcessRecordModel)) {
            throw new ApplicationException("未找到对应的面试记录");
        }
        if (sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.CLOSE.getResultCode()
                || sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.FAILED.getResultCode()
                || sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.PASS.getResultCode()
                || sfaInterviewProcessRecordModel.getProcessResult()
                == ProcessResult.CLOSE.getResultCode()) {
            throw new ApplicationException("请勿重复操作");
        }

        SfaInterviewProcessModel sfaInterviewProcessModel =
                sfaInterviewProcessMapper.selectById(
                        sfaInterviewProcessRecordModel.getInterviewProcessId());
        if (Objects.isNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("未找到对应的面试流程");
        }

        ApplyMemberPo applyMemberPo =
                applyMemberMapper.selectById(sfaInterviewProcessModel.getApplicationId());
        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("获取申请记录失败");
        }

        sfaInterviewProcessModel.setProcessResult(processResult);
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);

        sfaInterviewProcessRecordModel.setProcessUserName(processUserName);
        sfaInterviewProcessRecordModel.setProcessUserId(processUserId);
        sfaInterviewProcessRecordModel.setProcessDate(new Date());
        sfaInterviewProcessRecordModel.setProcessResult(processResult);
        sfaInterviewProcessRecordModel.setComment(comment);
        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);

        if (applyMemberPo.getPosition() == 1) {
            SfaEmployeeInfoModel sfaEmployeeInfoModel =
                    sfaEmployeeInfoMapper.selectOne(
                            new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applyMemberPo.getId()));
            // 获取客户信息
            SfaCustomer customer =
                    sfaCustomerMapper.selectOne(
                            new QueryWrapper<SfaCustomer>()
                                    .eq("position_id", sfaEmployeeInfoModel.getPositionId())
                                    .eq("channel", RequestUtils.getChannel()));
            if (Objects.isNull(customer)) {
                throw new ApplicationException("获取客户信息失败");
            }
            log.info("获取导的客户memberKey:{}", customer.getMemberKey());
            rootConnectorUtil.closeMember(customer.getMemberKey() + "", 7, LocalDateTimeUtils.formatTime(LocalDateTime.now(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));

            // 设置员工状态
            sfaEmployeeInfoModel.setEmployeeStatus(EmployeeStatus.ONBOARD_TERMINATE.getType());
            sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);
        }
    }

    @Override
    @Transactional
    public void probationCancel(ProbationCancelRequest request) {
        log.info("【试岗关闭接口】调用参数request:{}", request);
        int processRecordId = request.getProcessRecordId();
        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel =
                sfaInterviewProcessRecordMapper.selectById(request.getProcessRecordId());
        if (Objects.isNull(sfaInterviewProcessRecordModel)
                || sfaInterviewProcessRecordModel.getProcessType() != ProcessType.PROBATION.getProcessCode()
                || sfaInterviewProcessRecordModel.getProcessResult()
                != ProcessResult.PROCESSING.getResultCode()) {
            throw new ApplicationException("无法获取试岗记录");
        }

        Integer interviewProcessId = sfaInterviewProcessRecordModel.getInterviewProcessId();
        SfaInterviewProcessModel sfaInterviewProcessModel =
                sfaInterviewProcessMapper.selectById(interviewProcessId);
        if (Objects.isNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("无法获取面试流程");
        }

        // 获取申请记录信息
        Integer applicationId = sfaInterviewProcessModel.getApplicationId();
        SfaEmployeeInfoModel sfaEmployeeInfoModel =
                sfaEmployeeInfoMapper.selectOne(
                        new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applicationId));
        if (Objects.isNull(sfaEmployeeInfoModel)) {
            throw new ApplicationException("无法获取员工记录");
        }


        CeoBusinessOrganizationPositionRelation positionRelation = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());

        if (Objects.isNull(positionRelation)) {
            throw new ApplicationException("无法审核人信息");
        }
        // 保存面试记录
        updateProcessRecord(request, sfaInterviewProcessRecordModel, positionRelation);
        // 保存流程信息并修改试岗结束日期
        updateProcess(sfaInterviewProcessRecordModel, sfaInterviewProcessModel, sfaEmployeeInfoModel);
        // 修改人员信息并汰换
        updateSfaEmployeeInfo(sfaEmployeeInfoModel, positionRelation);

        // 将对应小标关闭
        smallMarketRegionRelationMapper.closeSmallMarket(sfaEmployeeInfoModel.getId());
        // 释放岗位
        List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId())
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0).eq(SfaPositionRelationEntity::getStatus, 1));
        if (CollectionUtils.isEmpty(positionRelationEntityList)) {
            throw new ApplicationException("岗位获取失败");
        }

        loginUserService.offAccount(sfaEmployeeInfoModel.getEmployeeId());

        positionRelationEntityList.forEach(e -> {
            e.setStatus(0);
            e.setEndValidDate(LocalDate.now().atStartOfDay());
            sfaPositionRelationMapper.updateById(e);

            // 修改岗位为离职
            String positionId = e.getPositionId();
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getPositionId, positionId));
            if (Objects.nonNull(ceoBusinessOrganizationPositionRelation)) {
                ceoBusinessOrganizationPositionRelation.setEmployeeName(null);
                ceoBusinessOrganizationPositionRelation.setEmployeeId(null);
                ceoBusinessOrganizationPositionRelation.setOnboardTime(null);
                ceoBusinessOrganizationPositionRelation.setOffTime(LocalDateTime.now());
                ceoBusinessOrganizationPositionRelationMapper.updateById(ceoBusinessOrganizationPositionRelation);

            }
        });

        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(applicationId);

        // 保存消息
        saveNotifyCount(sfaEmployeeInfoModel);

        applyMemberService.syn(
                applyMemberPo,
                null,
                sfaInterviewProcessModel.getProcessType(),
                sfaInterviewProcessModel.getProcessResult(),
                "体验终止",
                applyMemberPo.getHighestEducation(),
                applyMemberPo.getIdCardNum(),
                applyMemberPo.getBirthDate(),
                sfaInterviewProcessModel.getRecommendOnboardTime(),
                null);
    }

    private void saveNotifyCount(SfaEmployeeInfoModel sfaEmployeeInfoModel) {

        String day = DateUtil.format(new Date(), "yyyy年-MM月-dd日");
        String title = MessageFormat.format(titleTemplate, day);
        String message = MessageFormat.format(messageTemplate, day);


        String companyCode = sfaEmployeeInfoModel.getCompanyCode();
        String areaCode = sfaEmployeeInfoModel.getAreaCode();
        // 分公司
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", companyCode).eq("channel", 3));
        if (Objects.nonNull(companyPosition) && StringUtils.isNotBlank(companyPosition.getEmployeeId())) {
            doSendMessage(sfaEmployeeInfoModel, title, message, companyPosition.getEmployeeId());
        }

        // 大区
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", areaCode).eq("channel", 3));
        if (Objects.nonNull(areaPosition) && StringUtils.isNotBlank(areaPosition.getEmployeeId())) {
            doSendMessage(sfaEmployeeInfoModel, title, message, areaPosition.getEmployeeId());
        }

    }

    private void doSendMessage(SfaEmployeeInfoModel sfaEmployeeInfoModel, String title, String message, String employeeId) {
        NotifyPO notifyPO = notifyService.saveNotify(2, NotifyTemplateTypeEnum.EMP_TRANSACTION.getType(), title, employeeId, message);
        ProbationNotifyContent probationNotifyContent = new ProbationNotifyContent();

        NotifyDetailModel notifyDetailModel = new NotifyDetailModel();
        notifyDetailModel.setTemplateId(notifyPO.getTemplateId());
        notifyDetailModel.setDepartmentName(sfaEmployeeInfoModel.getDepartmentName());
        notifyDetailModel.setAreaName(sfaEmployeeInfoModel.getAreaName());
        notifyDetailModel.setCompanyName(sfaEmployeeInfoModel.getCompanyName());
        notifyDetailModel.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());


        notifyDetailModel.setStatus("试岗失败");

        Integer type = sfaEmployeeInfoModel.getType();
        if (type == 1 && sfaEmployeeInfoModel.getPostType() == 1) {
            notifyDetailModel.setPosition("全职合伙人");
        } else if (type == 1 && sfaEmployeeInfoModel.getPostType() == 2) {
            notifyDetailModel.setPosition("兼职合伙人");
        } else {
            notifyDetailModel.setPosition("企业合伙人");
        }


        notifyDetailModel.setMobile(sfaEmployeeInfoModel.getMobile());
        NotifyContentEntity notifyContentEntity = probationNotifyContent.buildNotifyContent(notifyDetailModel);
        notifyContentMapper.insert(notifyContentEntity);
    }


    @Override
    @Transactional
    public void edit(EmployeeInfoEditRequest request) {
        log.info("【编辑客户信息】请求参数request:{}", request);
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(request.getApplicationId());
        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("未找到申请记录");
        }

        SfaInterviewProcessModel sfaInterviewProcessModel =
                sfaInterviewProcessMapper.selectOne(
                        new QueryWrapper<SfaInterviewProcessModel>()
                                .eq("application_id", request.getApplicationId()));
        if (Objects.isNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("未找到流程信息");
        }
        SfaCustomer sfaCustomer =
                sfaCustomerMapper.selectOne(
                        new QueryWrapper<SfaCustomer>()
                                .eq("mobile_number", applyMemberPo.getUserMobile())
                                .eq("channel", RequestUtils.getChannel())
                                .eq("is_frozen", 0));
        if (Objects.isNull(sfaCustomer)) {
            throw new ApplicationException("无法找到对应客户信息");
        }
        // 获取操作人信息
        CeoBusinessOrganizationPositionRelation positionRelation =
                ceoBusinessOrganizationPositionRelationMapper.selectOne(
                        new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                .eq("employee_id", request.getPerson())
                                .eq("channel", RequestUtils.getChannel()));
        if (Objects.isNull(positionRelation)) {
            throw new ApplicationException("无法获取操作人信息");
        }

        CeoTerminalMember terminalMember =
                ceoTerminalMemberMapper.selectOne(
                        new QueryWrapper<CeoTerminalMember>().eq("customerId", sfaCustomer.getId()));
        if (Objects.isNull(terminalMember)) {
            throw new ApplicationException("客户审核表无法获取");
        }

        // 修改手机号
        if (!applyMemberPo.getUserMobile().equals(request.getMobile())) {
            // 检查手机号是否可用
            if (!checkMobile(request.getMobile())) {
                throw new ApplicationException("手机号已被使用");
            }

            // 通知旺铺修改手机号
            memberConnectorUtil.sendUpdateMobileByKeyToMEMBER(
                    sfaCustomer.getMemberKey().toString(), request.getMobile());

            applyMemberPo.setUserMobile(request.getMobile());
      /*applyMemberPo.setBirthDate(
      LocalDateTime.parse(
          ceoPassInterviewOperationCommandImpl.getBirthday(applyMemberPo.getIdCardNum())));*/

            // 修改客户表手机
            sfaCustomer.setMobileNumber(request.getMobile());
            sfaCustomerMapper.updateById(sfaCustomer);

            // 修改审核表手机号
            terminalMember.setMobileNumber(request.getMobile());
            ceoTerminalMemberMapper.updateById(terminalMember);

            // 修改员工信息表
            SfaEmployeeInfoModel sfaEmployeeInfoModel =
                    sfaEmployeeInfoMapper.selectOne(
                            new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applyMemberPo.getId()));
            if (Objects.isNull(sfaEmployeeInfoModel)) {
                throw new ApplicationException("员工信息表获取失败");
            }
            sfaEmployeeInfoModel.setMobile(request.getMobile());
            sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);

            Integer resignId = sfaInterviewProcessModel.getResignId();
            if (Objects.nonNull(resignId)) {
                ResignApplyModel resignApplyModel = sfaApplyResignMapper.selectById(resignId);
                if (Objects.nonNull(resignApplyModel)) {
                    resignApplyModel.setMobile(request.getMobile());
                    sfaApplyResignMapper.updateById(resignApplyModel);
                }
            }
        }

        // 修改小标
        PositionMarketRelationRequest positionMarketRelationRequest =
                new PositionMarketRelationRequest();
        positionMarketRelationRequest.setSmallMarketCodes(request.getSmallMarketCodes());
        positionMarketRelationRequest.setPositionId(sfaCustomer.getPositionId());
        positionMarketRelationRequest.setEmpId(request.getPerson());
        marketService.updatePositionMarketRelation(positionMarketRelationRequest);

        // 保存操作记录
        sfaMemberEditLogMapper.insert(buildMemberEditLog(request, positionRelation));
        BeanUtils.copyProperties(request, applyMemberPo);
        // 保存修改记录
        applyMemberMapper.updateById(applyMemberPo);
    }

    @Override
    public void revert(Integer id) {
        if (!redisUtil.setLockIfAbsent(
                LOCK_APPLY_MEMBER, LOCK_APPLY_MEMBER_OPTION_KEY, 5, TimeUnit.SECONDS)) {
            throw new ApplicationException("当前数据正在被操作");
        }

        try {
            log.info("【需要回退的面试流程】id:{}", id);
            SfaInterviewProcessModel sfaInterviewProcessModel =
                    sfaInterviewProcessMapper.selectOne(
                            new QueryWrapper<SfaInterviewProcessModel>().eq("application_id", id));
            if (Objects.isNull(sfaInterviewProcessModel)) {
                throw new ApplicationException("面试流程未找到");
            }

            Integer processType = sfaInterviewProcessModel.getProcessType();
            Integer processResult = sfaInterviewProcessModel.getProcessResult();
            if (processType != 1 || processResult != 0) {
                throw new ApplicationException("面试流程已更改，无法回退");
            }

            sfaInterviewProcessModel.setProcessResult(ProcessResult.CLOSE.getResultCode());
            sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);

            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel =
                    sfaInterviewProcessRecordMapper.selectById(
                            sfaInterviewProcessModel.getInterviewRecordId());
            sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.CLOSE.getResultCode());
            sfaInterviewProcessRecordModel.setProcessDate(new Date());
            sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);
        } finally {
            redisUtil.unLock(LOCK_APPLY_MEMBER, LOCK_APPLY_MEMBER_OPTION_KEY);
        }
    }

    @Override
    public String getRecommendation(Integer applicationId) {
        log.info("getRecommendation: {}", applicationId);
        return applyMemberMapper.getRecommendation(applicationId);
    }

    @Override
    @Transactional
    public void syn(SynProcessRequest request) {

        long start = System.currentTimeMillis();

        Long applyId = request.getApplyId();
        if (Objects.isNull(applyId)) {
            throw new ApplicationException("申请信息不存在");
        }
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(applyId);

        SfaInterviewProcessModel sfaInterviewProcessModel =
                sfaInterviewProcessMapper.selectOne(
                        new QueryWrapper<SfaInterviewProcessModel>()
                                .eq("application_id", applyMemberPo.getId())
                                .eq("process_type", request.getProcessType()));
        if (Objects.isNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("流程信息不存在");
        }

        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel =
                sfaInterviewProcessRecordMapper.selectById(sfaInterviewProcessModel.getInterviewRecordId());
        if (Objects.isNull(sfaInterviewProcessRecordModel)) {
            throw new ApplicationException("流程记录不存在");
        }


        Integer processResult = sfaInterviewProcessRecordModel.getProcessResult();

        // 如果是合伙人已经入职了直接返回
        if (applyMemberPo.getPosition() == 1) {
            return;
        }
        applyMemberPo.setBirthDate(request.getBirthday());
        applyMemberPo.setIdCardNum(request.getIdCardNum());
        applyMemberMapper.updateById(applyMemberPo);

        DateTime date = null;
        if (request.getResult() == ProcessResult.PASS.getResultCode()) {
            date = DateUtil.parse(request.getProcessTime(), "yyyy-MM-dd");
        }


        if (request.getResult() != ProcessResult.PASS.getResultCode()) {
            sfaInterviewProcessModel.setProcessResult(request.getResult());
        }
        sfaInterviewProcessModel.setExternalComment(request.getComment());


        sfaInterviewProcessRecordModel.setProcessResult(sfaInterviewProcessModel.getProcessResult());
        sfaInterviewProcessRecordModel.setProcessDate(new Date());
        sfaInterviewProcessRecordModel.setProcessUserId(null);
        sfaInterviewProcessRecordModel.setProcessUserName("第三方人资系统修改");

        if (request.getResult() == 4 || request.getResult() == 2) {
            // 关闭薪资中间表
            salaryMiddlewareService.cancel(applyMemberPo.getId());
            // 释放岗位及关闭兼岗关闭账号
            release(sfaInterviewProcessModel, sfaInterviewProcessRecordModel, request.getResult(), applyMemberPo.getId());
            return;
        }

        if (request.getProcessType() == ProcessType.DO_ONBOARD.getProcessCode()) {
            sfaInterviewProcessModel.setOnboardTime(date);
        } else {
            sfaInterviewProcessModel.setOffTime(date);
        }
        sfaInterviewProcessRecordModel.setComment(StringUtils.EMPTY);


        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);
        // 更新入职公司
        SfaEmployeeInfoModel employeeInfoModel =
                sfaEmployeeInfoMapper.selectOne(
                        new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applyMemberPo.getId()));
        if (Objects.nonNull(employeeInfoModel)) {
            employeeInfoModel.setJoiningCompany(request.getJoiningCompany());
            sfaEmployeeInfoMapper.updateById(employeeInfoModel);
        }
        String hrAccounts = settingServiceImpl.getValue("zw_hr_auto_account"); // 第三方默认账号
        if (hrAccounts == null) {
            log.info("人资账号没配置，自动入离职停止");
            return;
        }
        String[] hrAccountArr = hrAccounts.split(",");
        String hrAccount = hrAccountArr[0];
        if (StringUtils.isEmpty(hrAccount)) {


            log.info("人资账号为空，自动入离职停止");
            return;
        }
        // 1、如果填写的实际入职日期与建议入职日期相同，则跳过总部人资直接完成入职办理；
        // 2、如果填写的实际离职日期与建议离职日期相同，则跳过总部人资直接完成离职办理；
        // 推荐系统如果选择通过的话，入参的状态会变更成3
        if (request.getResult() == ProcessResult.PASS.getResultCode()) {
            if (request.getProcessType() == ProcessType.DO_ONBOARD.getProcessCode()) {
                Integer position = applyMemberPo.getPosition();
                // 写入入职公司 只处理非区域经理
                if (position == 4 || position == 1 || position == 2
                        || position == 11 || position == 7 || position == 6) {
                    SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applyMemberPo.getId()));
                    if (Objects.nonNull(sfaEmployeeInfoModel)) {
                        sfaEmployeeInfoModel.setJoiningCompany(request.getJoiningCompany());
                        sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);
                    }

                    // 建议入职时间
                    String onboardTimeStr = date.toDateStr();

                    sfaInterviewProcessModel.setOnboardTime(DateUtil.parseDate(onboardTimeStr));
                    sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
                }


                if (sfaInterviewProcessModel.getRecommendOnboardTime() != null && date != null) {
                    String onboardTimeStr = date.toDateStr();

                    // 获取岗位ID
                    String positionId = initPositionId(applyMemberPo);
                    if (StringUtils.isNotBlank(positionId)) {
                        // 校验入职时间是否冲突
                        checkInterviewService.checkDateConflict(LocalDateTimeUtils.convertDateToLDT(date).toLocalDate(), positionId, applyMemberPo.getUserMobile());
                    }


                    log.info("推荐系统，【自动入职】");
                    InductionInfoDTO inductionInfoDTO = new InductionInfoDTO();
                    inductionInfoDTO.setName(applyMemberPo.getUserName());
                    inductionInfoDTO.setMobileNumber(applyMemberPo.getUserMobile());
                    inductionInfoDTO.setOnboardTime(onboardTimeStr);
                    inductionInfoDTO.setJoiningCompany(request.getJoiningCompany());
                    inductionInfoDTO.setActualJoiningCompany(request.getActualJoiningCompany());
                    inductionInfoDTO.setContractCompany(request.getContractCompany());
                    inductionInfoDTO.setEmployId(null); // 第三方入职，没有工号
                    inductionInfoDTO.setProcessUserId(hrAccount);
                    inductionInfoDTO.setAdviceSalary(applyMemberPo.getAdviceSalary()); // 合伙人在入职办理不能填写建议薪资
                    inductionInfoDTO.setComment("第三方推荐系统通过后，自动入职");
                    inductionInfoDTO.setSocialInsuranceProvince(request.getSocialInsuranceProvince());
                    inductionInfoDTO.setSocialInsuranceCity(request.getSocialInsuranceCity());
                    inductionInfoDTO.setSocialInsuranceDistrict(request.getSocialInsuranceDistrict());
                    inductionInfoDTO.setCallBackFromEhrFalg(true);
                    applyMemberService.induction(inductionInfoDTO);

                }

                // 保存劳动合同
                laborContractService.saveLaborContract(applyMemberPo.getId(), request.getLaborContract(), "ROOT", "第三方入职公司");

            } else { // 离职
                ResignApplyModel resignApplyModel =
                        sfaApplyResignMapper.selectById(sfaInterviewProcessModel.getResignId());
                if (resignApplyModel != null
                        && resignApplyModel.getAdviceDepartureTime() != null
                        && date != null) {
                    String offTimeStr = date.toDateStr();
                    String adviceDepartureTime =
                            resignApplyModel
                                    .getAdviceDepartureTime()
                                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    if (offTimeStr.equals(adviceDepartureTime)) {
                        log.info("推荐系统，【自动离职】");
                        ResignSubmitRequest submitRequest = new ResignSubmitRequest();
                        submitRequest.setInterviewProcessRecordId(sfaInterviewProcessRecordModel.getId());
                        submitRequest.setProcessResult(1);
                        submitRequest.setOffTime(offTimeStr);
                        submitRequest.setComment("第三方推荐系统通过后，自动离职");
                        submitRequest.setCallBackFromEhrFalg(true);
                        resignService.pass(submitRequest);
                    }
                }
            }
        }
        // 当前操作回到0
        if (request.getResult() == ProcessResult.NOT_PROCESS.getResultCode()) {
            sfaInterviewProcessModel.setProcessResult(ProcessResult.NOT_PROCESS.getResultCode());
            sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
            sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.NOT_PROCESS.getResultCode());
            sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);
        }

        long end = System.currentTimeMillis();
        log.info("【第三方入职系统信息同步耗时:{}毫秒】", end - start);
    }

    private String initPositionId(ApplyMemberPo applyMemberPo) {
        Integer position = applyMemberPo.getPosition();
        if (position == 4) {
            return ceoBusinessOrganizationPositionRelationMapper.getPositionIdByOrgCode(applyMemberPo.getBranchOrganizationId());
        } else if (position == 2) {
            return ceoBusinessOrganizationPositionRelationMapper.getPositionIdByOrgCode(applyMemberPo.getCompanyOrganizationId());
        } else if (position == 6) {
            return ceoBusinessOrganizationPositionRelationMapper.getPositionIdByOrgCode(applyMemberPo.getProvinceOrganizationId());
        } else if (position == 5) {
            return ceoBusinessOrganizationPositionRelationMapper.getPositionIdByOrgCode(applyMemberPo.getVareaOrganizationId());
        } else if (position == 3) {
            return ceoBusinessOrganizationPositionRelationMapper.getPositionIdByOrgCode(applyMemberPo.getAreaOrganizationId());
        }
        return StringUtils.EMPTY;
    }

    private void release(SfaInterviewProcessModel sfaInterviewProcessModel, SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel, Integer result, Integer applyId) {
        sfaInterviewProcessModel.setProcessResult(result);
        sfaInterviewProcessRecordModel.setProcessResult(result);
        sfaInterviewProcessRecordModel.setProcessDate(new Date());
        sfaInterviewProcessRecordModel.setComment("第三方系统关闭流程");

        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);

        // 检查是否有开账号
        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getApplicationId, applyId).last("limit 1"));
        if (Objects.nonNull(sfaEmployeeInfoModel)) {
            sfaEmployeeInfoModel.setEmployeeStatus(7);
            sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);

            // 关闭账号
            SfaCustomer sc =
                    sfaCustomerMapper.selectOne(
                            new QueryWrapper<SfaCustomer>()
                                    .eq("memberKey", sfaEmployeeInfoModel.getMemberKey())
                                    .eq("channel", 3));
            if (Objects.nonNull(sc)) {
                // 关闭sfa_customer
                sc.setIsFrozen(1);
                sc.setUpdatedPerson("第三方系统关闭流程");
                sc.setUpdatedTime(LocalDateTime.now());
                sfaCustomerMapper.updateById(sc);
            }


            // 岗位表设置为无效，及结束时间
            List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new QueryWrapper<SfaPositionRelationEntity>()
                    .eq("employee_info_id", sfaEmployeeInfoModel.getId())
                    .eq("status", 1).eq("delete_flag", 0)
            );
            if (CollectionUtils.isEmpty(positionRelationEntityList)) {
                throw new ApplicationException("岗位表获取信息失败");
            }

            loginUserService.offAccount(sfaEmployeeInfoModel.getEmployeeId());

            positionRelationEntityList.forEach(e -> {

                String positionId = e.getPositionId();
                CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getPositionId, positionId));
                ceoBusinessOrganizationPositionRelation.setEmployeeId(null);
                ceoBusinessOrganizationPositionRelation.setEmployeeName(null);
                ceoBusinessOrganizationPositionRelation.setOffTime(LocalDateTime.now());
                ceoBusinessOrganizationPositionRelationMapper.updateById(ceoBusinessOrganizationPositionRelation);

                e.setEndValidDate(LocalDateTime.now());
                e.setStatus(0);
                sfaPositionRelationMapper.updateById(e);

                // 旺金币额度转移
                wantCoinsRetrieve(e.getOrganizationCode());
            });


            log.info("调用closeMember,memberKey:{}", sfaEmployeeInfoModel.getMemberKey().toString());
            try {
                log.info("【旺铺汰换接口】 start..");

                rootConnectorUtil.closeMember(
                        sc.getMemberKey().toString(), sfaEmployeeInfoModel.getEmployeeStatus(), LocalDateTimeUtils.formatTime(LocalDateTime.now(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
            } catch (Exception e) {
                log.error("旺铺汰换接口调用失败:memberKey={}", sfaEmployeeInfoModel.getMemberKey());
                e.printStackTrace();
            }
        }
    }

    public List<RecruitmentNeedsVO> getRecruitmentList(String companyCode, String employeeId, int position) {
        CompanyScopeRequest companyScopeRequest = new CompanyScopeRequest();
        companyScopeRequest.setPosition(position);
//        companyScopeRequest.setOrganizationId(companyCode);
        companyScopeRequest.setEmployeeId(employeeId);
        companyScopeRequest.setCompanyOrganizationId(companyCode);
        companyScopeRequest.setUndone(1);
        List<RecruitmentNeedsVO> recruitmentNeedsVOList =
                recruitmentNeedsMapper.listNeeds(companyScopeRequest, RequestUtils.getLoginInfo());
        return recruitmentNeedsVOList;
    }

    @Override
    public void sendMessage(
            String companyName, String position, String emplyoeeName, String emplyeeMobile, int type) {

        String smsTemplateId = StringUtils.EMPTY;
        // 入职模版
        if (ProcessType.DO_ONBOARD.getProcessCode() == type) {
            settingServiceImpl.getValue("sfa_onboard_message_smsTemplateId");
        }

        if (ProcessType.DO_RESIGN.getProcessCode() == type) {
            settingServiceImpl.getValue("sfa_resign_message_smsTemplateId");
        }

        HashMap<String, String> param = Maps.newHashMap();
        param.put("company", companyName);
        param.put("position", position);
        param.put("name", emplyoeeName);
        param.put("mobile", emplyeeMobile);

        try {
            ArrayList<String> mobilePhone = new ArrayList<>();
            SfaEmployeeInfoModel sfaEmployeeInfoModel =
                    sfaEmployeeInfoMapper.selectOne(
                            new QueryWrapper<SfaEmployeeInfoModel>().eq("mobile", emplyeeMobile));
            // 获取分公司总监手机号
            if (position.equals(PositionEnum.CEO)) {
                String m =
                        sfaEmployeeInfoMapper.selectMobileByOrg(
                                sfaEmployeeInfoModel.getCompanyCode(), RequestUtils.getChannel());
                if (StringUtils.isNotBlank(m)) {
                    mobilePhone.add(m);
                }
            }

            // 大区总手机号获取
            String m =
                    sfaEmployeeInfoMapper.selectMobileByOrg(
                            sfaEmployeeInfoModel.getAreaCode(), RequestUtils.getChannel());
            if (StringUtils.isNotBlank(m) && !m.startsWith("0")) {
                mobilePhone.add(m);
            }

            if (!CollectionUtils.isEmpty(mobilePhone)) {
                geTuiService.SmsPushList(smsTemplateId, param, mobilePhone);
            }
        } catch (Exception e) {
            log.error("【发送短信失败】exception:{}", e);
        }
    }

    private InterviewEditLogModel buildMemberEditLog(
            EmployeeInfoEditRequest request, CeoBusinessOrganizationPositionRelation positionRelation) {
        InterviewEditLogModel model = new InterviewEditLogModel();
        BeanUtils.copyProperties(request, model);
        model.setCreateTime(LocalDateTime.now());
        model.setCreateUserId(positionRelation.getEmployeeId());
        model.setCreateUserName(positionRelation.getEmployeeName());
        return model;
    }

    private boolean checkMobile(String mobile) {
        // 检查手机号是否被使用
        String memberKey = memberConnectorUtil.sendQueryByMobileToMEMBER(mobile);
        if (StringUtils.isNotBlank(memberKey)) {
            return false;
        }

        List<ApplyMemberVO> applyMemberVOS = applyMemberMapper.selectListBySql(mobile);
        if (!CollectionUtils.isEmpty(applyMemberVOS)) {
            return false;
        }
        return true;
    }

    private void updateSfaEmployeeInfo(
            SfaEmployeeInfoModel sfaEmployeeInfoModel,
            CeoBusinessOrganizationPositionRelation positionRelation) {
        sfaEmployeeInfoModel.setEmployeeStatus(EmployeeStatus.PROBATION_FAILED.getType());
        sfaEmployeeInfoModel.setUpdateTime(new Date());
        sfaEmployeeInfoModel.setUpdateUserName(positionRelation.getEmployeeName());
        sfaEmployeeInfoModel.setUpdateUserId(positionRelation.getEmployeeId());
        sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);

        // 修改客户信息为已冻结
        SfaCustomer sfaCustomer =
                sfaCustomerMapper.selectOne(
                        new QueryWrapper<SfaCustomer>()
                                .eq("mobile_number", sfaEmployeeInfoModel.getMobile())
                                .eq("channel", RequestUtils.getChannel()));
        sfaCustomer.setIsFrozen(1);
        sfaCustomer.setUpdatedTime(LocalDateTime.now());
        sfaCustomer.setUpdatedPerson(positionRelation.getEmployeeId());
        sfaCustomerMapper.updateById(sfaCustomer);

        // 关闭所选小标市场
        smallMarketPositionMapper.closeSmallMarketByPositionId(sfaEmployeeInfoModel.getPositionId());
        log.info("【试岗关闭接口】调用旺铺汰换接口,memberKey=", sfaCustomer.getMemberKey());
        // 调用旺铺汰换接口
        rootConnectorUtil.closeMember(sfaCustomer.getMemberKey().toString(), 7, LocalDateTimeUtils.formatTime(LocalDateTime.now(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
    }

    private void updateProcess(
            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel,
            SfaInterviewProcessModel sfaInterviewProcessModel,
            SfaEmployeeInfoModel employeeInfoModel) {
        sfaInterviewProcessModel.setProcessResult(sfaInterviewProcessRecordModel.getProcessResult());
        sfaInterviewProcessModel.setProbationEndDay(new Date());
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);

        String positionId = employeeInfoModel.getPositionId();
        // 根据岗位ID获取岗位信息
        CeoBusinessOrganizationPositionRelation positionRelation =
                ceoBusinessOrganizationPositionRelationMapper.selectOne(
                        new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                .eq("position_id", positionId)
                                .eq("channel", RequestUtils.getChannel()));
        positionRelation.setProbationEndTime(LocalDateTime.now());
        ceoBusinessOrganizationPositionRelationMapper.updateById(positionRelation);
    }

    private void updateProcessRecord(
            ProbationCancelRequest request,
            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel,
            CeoBusinessOrganizationPositionRelation positionRelation) {
        int processResultCode = 0;
        sfaInterviewProcessRecordModel.setProcessUserName(positionRelation.getEmployeeName());
        sfaInterviewProcessRecordModel.setProcessDate(new Date());
        sfaInterviewProcessRecordModel.setProcessUserId(positionRelation.getEmployeeId());

        if (request.getType() == 1) {
            processResultCode = ProcessResult.FAILED.getResultCode();
        } else {
            processResultCode = ProcessResult.CLOSE.getResultCode();
        }
        sfaInterviewProcessRecordModel.setProcessResult(processResultCode);
        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);
    }


    // 0.查询上一级，1只查自己
    private List<InterviewProcessRecordVo> getProcessRecordList(
            Integer prevProcessId, List<InterviewProcessRecordVo> list, Integer isDetails, ApplyMemberPo applyMemberPo) {
        if (Objects.isNull(prevProcessId)) {
            return list;
        }

        InterviewProcessRecordAndReasonModel record = sfaInterviewProcessRecordMapper.selectProcessRecordById(prevProcessId);


        if (isDetails == 1) {
            if (Objects.isNull(record)) {
                return list;
            }
        } else {
            if (Objects.isNull(record) || record.getProcessResult() == ProcessResult.PROCESSING.getResultCode()) {
                return list;
            }
        }

        InterviewProcessRecordVo currentInterviewVo = null;
        InterviewProcessRecordVo interviewProcessRecordVo = new InterviewProcessRecordVo();
        interviewProcessRecordVo.setInterviewRecordId(prevProcessId);
        interviewProcessRecordVo.setProcessUserName(record.getProcessUserName());
        interviewProcessRecordVo.setStepName(ProcessType.findProcessTypeByCode(record.getProcessType()).getProcessName());
        Integer processResult = record.getProcessResult();
        interviewProcessRecordVo.setStep(ProcessType.findProcessTypeByCode(record.getProcessType()).getStep());
        if (processResult == 5) {
            currentInterviewVo = interviewProcessRecordVo;
            if (record.getProcessType() == 3) {
                currentInterviewVo.setProcessResult(ProcessResult.PROCESSING.getProcessName());
            } else {
                currentInterviewVo.setProcessResult(ProcessResult.NOT_PROCESS.getProcessName());
            }

            // 插入一条面试取消记录
            interviewProcessRecordVo = new InterviewProcessRecordVo();
            interviewProcessRecordVo.setInterviewRecordId(prevProcessId);
            interviewProcessRecordVo.setProcessTime(record.getProcessDate());
            interviewProcessRecordVo.setStepName("取消入职");
            interviewProcessRecordVo.setComment(record.getComment());
            interviewProcessRecordVo.setReason(record.getReason());
            interviewProcessRecordVo.setProcessUserName(applyMemberPo.getUserName());
        } else {
            interviewProcessRecordVo.setProcessTime(record.getProcessDate());
            interviewProcessRecordVo.setComment(record.getComment());
            interviewProcessRecordVo.setReason(record.getReason());
        }
        interviewProcessRecordVo.setProcessResult(ProcessResult.findProcessResultByCode(processResult).getProcessName());


        ProcessLabel processLabelByType = ProcessLabel.getProcessLabelByType(record.getProcessType());
        if (Objects.nonNull(processLabelByType)) {
            interviewProcessRecordVo.setCommentLabel(processLabelByType.getCommentLable());
            interviewProcessRecordVo.setDateLabel(processLabelByType.getDateLable());
            interviewProcessRecordVo.setResultLabel(processLabelByType.getResultLable());
        }
        interviewProcessRecordVo.setAttitudeIntentionScore(record.getAttitudeIntentionScore());
        interviewProcessRecordVo.setExperienceAbilityScore(record.getExperienceAbilityScore());
        interviewProcessRecordVo.setCustomerResourcesScore(record.getCustomerResourcesScore());

        if (record.getProcessType() == ProcessType.DO_ONBOARD.getProcessCode()) {
            // 添加离职申请
            addApplyOff(list, record);
        }

        if (record.getProcessType() == ProcessType.APPLY_RESIGN.getProcessCode()) {

            // 从上级记录中获取离职申请信息
            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordMapper.selectById(record.getPrevProcessId());
            if (Objects.isNull(sfaInterviewProcessRecordModel)) {
                throw new ApplicationException("上级面试记录获取失败");
            }

            ResignApplyModel resignApplyModel = sfaApplyResignMapper.selectById(sfaInterviewProcessRecordModel.getResignApplyId());
            if (Objects.isNull(resignApplyModel)) {
                throw new ApplicationException("离职申请信息获取失败");
            }
            if (resignApplyModel.getResignType() == 3 && record.getProcessResult() == 2) {
                interviewProcessRecordVo.setProcessResult("申请保留,免于汰换");
            } else if (resignApplyModel.getResignType() == 3 && record.getProcessResult() == 1) {
                interviewProcessRecordVo.setProcessResult("同意汰换");
            }
        }

        if (ProcessType.ZB_RESIGN_VERIFY.getProcessCode() == record.getProcessType()
                || ProcessType.BOSS_RESIGN_VERIFY.getProcessCode() == record.getProcessType() || ProcessType.BUSINESS_GROUP_MANAGER_VERIFY.getProcessCode() == record.getProcessType()) {

            ResignApplyModel resignApplyModel = null;
            if (Objects.nonNull(record.getResignApplyId())) {
                resignApplyModel = sfaApplyResignMapper.selectById(record.getResignApplyId());
            } else {
                SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordMapper.selectById(record.getPrevProcessId());
                if (Objects.isNull(sfaInterviewProcessRecordModel)) {
                    throw new ApplicationException("上级面试记录获取失败");
                }

                resignApplyModel = sfaApplyResignMapper.selectById(sfaInterviewProcessRecordModel.getResignApplyId());

            }


            if (Objects.isNull(resignApplyModel)) {
                throw new ApplicationException("离职申请信息获取失败");
            }


            if (resignApplyModel.getResignType() == 3 && record.getProcessResult() == 1) {
                interviewProcessRecordVo.setProcessResult("同意汰换");
            } else if (resignApplyModel.getResignType() == 3 && record.getProcessResult() == 2) {
                interviewProcessRecordVo.setProcessResult("同意保留,免于汰换");
            } else {

                interviewProcessRecordVo.setProcessResult(ProcessResult.findProcessResultByCode(record.getProcessResult()).getProcessName());
            }
        }


        // 运营汰换审核文案修改
        if (record.getProcessType() == ProcessType.ZB_RESIGN_VERIFY.getProcessCode()) {
            // 向上找2层获取离职申请信息
            // 大区审核记录
            SfaInterviewProcessRecordModel areaAuditRecord = sfaInterviewProcessRecordMapper.selectById(record.getPrevProcessId());

            SfaInterviewProcessRecordModel onboardRecord = sfaInterviewProcessRecordMapper.selectById(areaAuditRecord.getPrevProcessId());

            ResignApplyModel resignApplyModel = sfaApplyResignMapper.selectById(onboardRecord.getResignApplyId());
            if (Objects.isNull(resignApplyModel)) {
                throw new ApplicationException("离职申请信息获取失败");
            }

            Integer resignType = resignApplyModel.getResignType();
            if (3 == resignType) {
                interviewProcessRecordVo.setDateLabel("运营汰换审核");
                interviewProcessRecordVo.setStepName("运营汰换审核");
            }
        }

        list.add(interviewProcessRecordVo);
        if (Objects.nonNull(currentInterviewVo)) {
            list.add(currentInterviewVo);
        }

        if (isDetails == 0) {
            if (Objects.nonNull(record.getPrevProcessId())) {
                return getProcessRecordList(record.getPrevProcessId(), list, 0, applyMemberPo);
            } else {
                return list;
            }
        } else {
            return list;
        }
    }

    private void addApplyOff(
            List<InterviewProcessRecordVo> list, InterviewProcessRecordAndReasonModel record) {
        Integer resignApplyId = record.getResignApplyId();
        if (Objects.isNull(resignApplyId)) {
            return;
        }

        ResignApplyModel resignApplyModel = sfaApplyResignMapper.selectById(resignApplyId);
        if (Objects.isNull(resignApplyModel)) {
            throw new ApplicationException("离职申请信息获取失败");
        }

        InterviewProcessRecordVo interviewProcessRecordVo = new InterviewProcessRecordVo();
        interviewProcessRecordVo.setProcessTime(
                resignApplyModel.getCreateTime().toLocalDate().toString());
        interviewProcessRecordVo.setProcessUserName(resignApplyModel.getCreateUserName());

        Integer resignType = resignApplyModel.getResignType();
        if (1 == resignType) {
            interviewProcessRecordVo.setProcessResult("员工辞职");
        } else if (2 == resignType) {
            interviewProcessRecordVo.setProcessResult("主动汰换");
        } else {
            interviewProcessRecordVo.setProcessResult("自动汰换");
        }

        // 离职原因
        //  interviewProcessRecordVo.setComment(covertResignReason(resignApplyModel.getReason()));
        interviewProcessRecordVo.setComment(resignApplyModel.getRemark());

        interviewProcessRecordVo.setStepName(ProcessLabel.APPLY_OFF.getProcessName());

        ProcessLabel applyOff = ProcessLabel.APPLY_OFF;
        interviewProcessRecordVo.setCommentLabel(applyOff.getCommentLable());
        interviewProcessRecordVo.setDateLabel(applyOff.getDateLable());
        interviewProcessRecordVo.setResultLabel(applyOff.getResultLable());

        list.add(interviewProcessRecordVo);


        // 增加一条离职记录
        InterviewProcessRecordVo offBoardRecord = new InterviewProcessRecordVo();
        offBoardRecord.setStepName("离职记录");
        String comment = "离职类型:" + interviewProcessRecordVo.getProcessResult() + "<br>";

        String reason = org.apache.commons.lang3.StringUtils.isBlank(resignApplyModel.getRemark()) ? "" : resignApplyModel.getRemark();

        comment = comment + "  离职原因:" + reason;

        offBoardRecord.setComment(comment);

        list.add(offBoardRecord);
    }


    private SfaInterviewProcessRecordModel saveInterviewProcessRecord(
            SfaInterviewProcessModel sfaInterviewProcessModel,
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation,
            ApplyMemberPo applyMemberInfo) {
        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel =
                new SfaInterviewProcessRecordModel();
        sfaInterviewProcessRecordModel.setInterviewProcessId(sfaInterviewProcessModel.getId());
        sfaInterviewProcessRecordModel.setProcessType(ProcessType.INTERVIEW.getProcessCode());
        sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.NOT_PROCESS.getResultCode());
        sfaInterviewProcessRecordModel.setProcessUserId(
                ceoBusinessOrganizationPositionRelation.getEmployeeId());
        sfaInterviewProcessRecordModel.setProcessUserName(
                ceoBusinessOrganizationPositionRelation.getEmployeeName());
        sfaInterviewProcessRecordModel.setCreateTime(new Date());
        sfaInterviewProcessRecordModel.setOrganizationId(
                ceoBusinessOrganizationPositionRelation.getOrganizationId());
        sfaInterviewProcessRecordModel.setProcessUserId(
                ceoBusinessOrganizationPositionRelation.getEmployeeId());
        sfaInterviewProcessRecordModel.setProcessUserName(
                ceoBusinessOrganizationPositionRelation.getEmployeeName());
        sfaInterviewProcessRecordModel.setAreaCode(applyMemberInfo.getAreaOrganizationId());
        sfaInterviewProcessRecordModel.setCompanyCode(applyMemberInfo.getCompanyOrganizationId());
        sfaInterviewProcessRecordModel.setAreaName(applyMemberInfo.getArea());
        sfaInterviewProcessRecordModel.setCompanyName(applyMemberInfo.getCompany());

        String branchOrganizationId = applyMemberInfo.getBranchOrganizationId();
        if (StringUtils.isNotBlank(branchOrganizationId)) {
            sfaInterviewProcessRecordModel.setBranchCode(branchOrganizationId);
            sfaInterviewProcessRecordModel.setBranchName(applyMemberInfo.getBranchOrganizationName());
        }

        sfaInterviewProcessRecordMapper.insert(sfaInterviewProcessRecordModel);
        return sfaInterviewProcessRecordModel;
    }

    private SfaInterviewProcessModel saveInterviewProcess(int applicationId) {
        SfaInterviewProcessModel model = new SfaInterviewProcessModel();
        model.setApplicationId(applicationId);
        model.setProcessType(ProcessType.INTERVIEW.getProcessCode());
        model.setProcessResult(ProcessResult.NOT_PROCESS.getResultCode());
        model.setCreateTime(new Date());
        sfaInterviewProcessMapper.insert(model);
        return model;
    }

    private CeoBusinessOrganizationPositionRelation chooseProcessUser(
            String areaCode, String companyCode, Integer position, int channel) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = null;

        // 合伙人 或 区域经理人
        if (position == 1 || position == 4) {
            // 获取区域经理信息
            ceoBusinessOrganizationPositionRelation =
                    ceoBusinessOrganizationPositionRelationMapper.selectOne(
                            new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                    .eq("organization_id", companyCode)
                                    .eq("channel", channel));
            // 如果没有对应的审核人，则向上传递
            if (Objects.isNull(ceoBusinessOrganizationPositionRelation)
                    || StringUtils.isBlank(ceoBusinessOrganizationPositionRelation.getEmployeeName())) {
                // 大区经理信息
                ceoBusinessOrganizationPositionRelation =
                        ceoBusinessOrganizationPositionRelationMapper.selectOne(
                                new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                        .eq("organization_id", areaCode)
                                        .eq("channel", channel));
            }
        }
        // 大区经理信息
        else {

            ceoBusinessOrganizationPositionRelation =
                    ceoBusinessOrganizationPositionRelationMapper.selectOne(
                            new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                    .eq("organization_id", areaCode)
                                    .eq("channel", channel));
        }
        // 无法获取审核人，试用配置项目
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)
                || StringUtils.isBlank(ceoBusinessOrganizationPositionRelation.getEmployeeName())) {
            String employeeId = configMapper.getValueByCode(ZW_HR_EMPLOYEE_ID_CODE);
            if (StringUtils.isBlank(employeeId)) {
                throw new ApplicationException("获取审核人信息失败");
            }
            ceoBusinessOrganizationPositionRelation =
                    ceoBusinessOrganizationPositionRelationMapper.selectOne(
                            new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                    .eq("employee_id", employeeId)
                                    .eq("channel", channel));
        }

        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
            throw new ApplicationException("获取审核人信息失败");
        }
        return ceoBusinessOrganizationPositionRelation;
    }

    private ApplyMemberPo getApplyMemberInfo(int applicationId) {
        if (applicationId <= 0) {
            throw new ApplicationException("错误的申请ID");
        }

        // 检查是否已创建过
        SfaInterviewProcessModel sfaInterviewProcessModel =
                sfaInterviewProcessMapper.selectOne(
                        new QueryWrapper<SfaInterviewProcessModel>().eq("application_id", applicationId));
        if (Objects.nonNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("申请已提交过，请勿重复提交");
        }

        // 检查申请ID是否正确
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(applicationId);
        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("获取申请信息失败!");
        }

        return applyMemberPo;
    }

    @Override
    public List<PartnerInfoVo> getCEOList(String key, String departMentCode) {
        List<PartnerInfoVo> retList = sfaCustomerMapper.getZWCEOList(key, departMentCode);
        retList.forEach(
                e -> {
                    PartnerInfoVo info =
                            customerManagementDetailService.getPartnerInfoByMemberKey(e.getMemberKey());
                    if (info != null) {
                        e.setRegionList(info.getRegionList());
                        e.setProductionGroup(info.getProductionGroup());
                    }
                });
        return retList;
    }

    @Override
    @Transactional
    public void reject(InterviewRejectRequest request) {
        // 操作人信息
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), loginInfo);


        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordMapper.selectById(request.getInterviewProcessRecordId());
        if (Objects.isNull(sfaInterviewProcessRecordModel)) {
            throw new ApplicationException("面试记录无法获取");
        }

        // 检查是否操作过
        if (sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.PASS.getResultCode() || sfaInterviewProcessRecordModel.getProcessResult() == ProcessResult.FAILED.getResultCode()) {
            throw new ApplicationException("请勿重复操作");
        }

        Integer interviewProcessId = sfaInterviewProcessRecordModel.getInterviewProcessId();
        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectById(interviewProcessId);
        if (Objects.isNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("流程主表信息获取失败");
        }

        salaryMiddlewareService.cancel(sfaInterviewProcessModel.getApplicationId());

        // 设置当前流程记录信息
        sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.FAILED.getResultCode());
        sfaInterviewProcessRecordModel.setProcessUserId(personInfo.getEmployeeId());
        sfaInterviewProcessRecordModel.setProcessUserName(personInfo.getEmployeeName());
        sfaInterviewProcessRecordModel.setProcessDate(new Date());
        sfaInterviewProcessRecordModel.setComment(request.getComment());


        // 获取上级节点
        Integer prevProcessId = sfaInterviewProcessRecordModel.getPrevProcessId();
        SfaInterviewProcessRecordModel prevProcessRecord = sfaInterviewProcessRecordMapper.selectById(prevProcessId);
        if (Objects.isNull(prevProcessRecord)) {
            throw new ApplicationException("无法获取上级审核节点信息");
        }

        SfaInterviewProcessRecordModel oldProcessRecord = sfaInterviewProcessRecordMapper.selectById(prevProcessRecord.getId());
        if (Objects.isNull(oldProcessRecord)) {
            throw new ApplicationException("无法获取上级审核节点信息");
        }


        // 创建新的流程
        SfaInterviewProcessRecordModel next = new SfaInterviewProcessRecordModel();
        org.springframework.beans.BeanUtils.copyProperties(oldProcessRecord, next, "id", "createTime", "processDate", "nextProcessId");
        // 检查审核人是否还在岗
        loginInfo.setCurrentOrganizationId(next.getOrganizationId());
        CeoBusinessOrganizationPositionRelation verifyPerson = checkCustomerService.getPersonInfo(next.getProcessUserId(), loginInfo);

        if (Objects.isNull(verifyPerson) || StringUtils.isBlank(verifyPerson.getEmployeeName())) {
            // 设置审核人为制定审核人
            String employeeId = configMapper.getValueByCode(ZW_HR_EMPLOYEE_ID_CODE);
            loginInfo.setCurrentOrganizationId(null);
            loginInfo.setPositionTypeId(7);
            verifyPerson = checkCustomerService.getPersonInfo(employeeId, loginInfo);
        }

        next.setOrganizationId(verifyPerson.getOrganizationId());
        next.setProcessUserId(verifyPerson.getEmployeeId());
        next.setProcessResult(ProcessResult.NOT_PROCESS.getResultCode());
        next.setCreateTime(new Date());
        next.setPrevProcessId(sfaInterviewProcessRecordModel.getId());
        sfaInterviewProcessRecordMapper.insert(next);

        // 绑定流程关系
        sfaInterviewProcessRecordModel.setNextProcessId(next.getId());
        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);

        sfaInterviewProcessModel.setInterviewRecordId(next.getId());
        sfaInterviewProcessModel.setProcessType(next.getProcessType());
        sfaInterviewProcessModel.setProcessResult(next.getProcessResult());
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);


        // 关闭薪资中间表
        salaryMiddlewareService.cancel(sfaInterviewProcessModel.getApplicationId());
    }

    @Override
    public void sendMsg(InterviewInfoSynRequest request) {
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(request.getApplicationId());
        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("获取面试申请失败");
        }

        SfaInterviewProcessModel sfaInterviewProcessModel =
                sfaInterviewProcessMapper.selectOne(
                        new QueryWrapper<SfaInterviewProcessModel>()
                                .eq("application_id", applyMemberPo.getId()));
        // 推送至人才库
        talentConnectorUtil.push(request, applyMemberPo, sfaInterviewProcessModel);


        // 发送短信
        String interview_msg_template = configMapper.getValueByCode("interview_msg_template");
        Map<String, String> smsParam = new HashMap<>();
        smsParam.put("employeeName", applyMemberPo.getUserName());
        smsParam.put("url", talentLoginUrl);

        List<String> recNum = new ArrayList<>();
        recNum.add(request.getUserMobile());
        try {
            geTuiService.SmsPushList(interview_msg_template, smsParam, recNum);
        } catch (Exception e) {
            throw new ApplicationException("发送短信失败");
        }
    }


    @Override
    @Transactional
    public void interviewInfoSyn(InterviewInfoSynRequest request) {
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(request.getApplicationId());
        String originalIdCard = applyMemberPo.getIdCardNum();

        String userMobile = applyMemberPo.getUserMobile();
        String originalUserName = applyMemberPo.getUserName();

        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("员工信息获取失败");
        }
        BeanUtils.copyProperties(request, applyMemberPo);
        applyMemberPo.setUserMobile(userMobile);
        // 食品经验处理
        String foodExperience = request.getFoodExperience();
        if (StringUtils.isNotBlank(foodExperience) && "1".equals(foodExperience)) {
            applyMemberPo.setFoodExperience("有");
        } else {
            applyMemberPo.setFoodExperience("无");
        }


        applyMemberMapper.updateById(applyMemberPo);

        List<MemberExperienceDTO> experiences = request.getExperiences();
        if (!CollectionUtils.isEmpty(experiences)) {
            // 保存工作经历
            experienceService.batchSave(request.getExperiences(), request.getApplicationId());
        }
        //更新复试日期
        if (StringUtils.isNotEmpty(request.getRetestDate())) {
            sfaInterviewProcessMapper.updateOptionalRetestTime(request.getRetestDate(), request.getApplicationId());
        }


        SfaEmployeeInfoAdditional additional = sfaEmployeeInfoAdditionalMapper.selectOne(new QueryWrapper<SfaEmployeeInfoAdditional>().eq("apply_id", applyMemberPo.getId()));


        Integer position = applyMemberPo.getPosition();
        if (position != 4) {
            // 插入
            if (Objects.isNull(additional)) {
                additional = new SfaEmployeeInfoAdditional();
                additional.setUserName(request.getUserName());
                additional.setMobile(request.getUserMobile());
                additional.setIdCardNo(request.getIdCardNum());
                additional.setIdCardFrontUrl(request.getIdCardFrontUrl());
                additional.setIdCardBackUrl(request.getIdCardBackUrl());
                additional.setApplyId(applyMemberPo.getId());
                additional.setCreateTime(LocalDateTime.now());
                additional.setUpdateTime(LocalDateTime.now());
                sfaEmployeeInfoAdditionalMapper.insert(additional);
            }
            // 修改
            else {
                additional.setMobile(request.getUserMobile());
                additional.setUserName(request.getUserName());
                additional.setIdCardNo(request.getIdCardNum());
                additional.setIdCardFrontUrl(request.getIdCardFrontUrl());
                additional.setIdCardBackUrl(request.getIdCardBackUrl());
                additional.setApplyId(applyMemberPo.getId());
                additional.setUpdateTime(LocalDateTime.now());
                sfaEmployeeInfoAdditionalMapper.updateById(additional);
            }
        }


        String idCardNo = request.getIdCardNum();


        if (!idCardNo.equals(originalIdCard)) {
            applyMemberPo.setOriginalIdCardNum(originalIdCard);
            applyMemberPo.setIdCardNum(idCardNo);
        }


        String userName = request.getUserName();

        if (!userName.equals(originalUserName)) {
            applyMemberPo.setOriginalUserName(originalUserName);
            applyMemberPo.setUserName(userName);
        }
        applyMemberMapper.updateById(applyMemberPo);
    }

    @Override
    @Transactional
    public void tryReemploy(ReemployInfoRequest request) {
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(request.getApplicationId());
        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("未找到符合条件的员工");
        }
        if (CollectionUtils.isEmpty(request.getProductionGroupList())) {
            throw new ApplicationException("请选择产品组");
        }
        if (CollectionUtils.isEmpty(request.getSmallMarketList())) {
            throw new ApplicationException("请选择小标");
        }
        SfaEmployeeInfoModel sfaEmployeeInfoModel =
                sfaEmployeeInfoMapper.selectOne(
                        new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", request.getApplicationId()).ne("employee_status", 2).eq("status", 1));
        if (Objects.isNull(sfaEmployeeInfoModel)) {
            throw new ApplicationException("该员工不符合返聘的条件");
        }
        List<ResignApplyModel> resignApplyModelList = sfaApplyResignMapper.selectList(new QueryWrapper<ResignApplyModel>().eq("employee_info_id", sfaEmployeeInfoModel.getId()).eq("status", 1));
        if (CollectionUtils.isEmpty(resignApplyModelList)) {
            throw new ApplicationException("未找到改员工的离职信息");
        }
        for (ResignApplyModel resignApplyModel : resignApplyModelList) {
            resignApplyModel.setStatus(0);
            sfaApplyResignMapper.updateById(resignApplyModel);
        }
        //获取操作人身份,判断人员是否有操作权限
        CeoBusinessOrganizationPositionRelation operatorPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(
                new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("employee_id", request.getEmployeeId()).eq("channel", 3));
        if (Objects.isNull(operatorPositionRelation)) {
            throw new ApplicationException("未找到操作人员信息");
        }
        if (!operatorPositionRelation.getOrganizationId().equals(sfaEmployeeInfoModel.getCompanyCode())
                && !operatorPositionRelation.getOrganizationId().equals(sfaEmployeeInfoModel.getAreaCode())
                && !operatorPositionRelation.getOrganizationId().equals(sfaEmployeeInfoModel.getDepartmentCode())) {
            throw new ApplicationException("请联系合伙人对应的区域经理或区域总监或总督导");
        }
        // 判断当前流程是否允许操作重新试岗
        SfaInterviewProcessModel processModel = sfaInterviewProcessMapper.selectOne(
                new QueryWrapper<SfaInterviewProcessModel>().eq("application_id", request.getApplicationId()));
        if (!(processModel.getProcessType() == ProcessType.DO_RESIGN.getProcessCode()) || !(processModel.getProcessResult() == ProcessResult.PASS.getResultCode())) {
            throw new ApplicationException("非法操作，当前员工非离职状态");
        }

        int oldPositionId = getPositionId(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
        int transactionPositionId = getPositionId(request.getCeoType(), 2, applyMemberPo.getPosition());

        // 更新报名表为兼职
        applyMemberPo.setJobsType(2);
        if (request.getCeoType() == 2) {
            applyMemberPo.setCeoType(2);
        }
        applyMemberMapper.updateById(applyMemberPo);

        // 新增节点试岗节点，刷新流程相关数据(sfa_interview_process、sfa_interview_process_record)
        processModel.setProcessType(ProcessType.PROBATION.getProcessCode());
        processModel.setProcessResult(ProcessResult.PROCESSING.getResultCode());
        String valueByCode = configMapper.getValueByCode(PROBATION_INTERVER);
        if (StringUtils.isNotEmpty(valueByCode)) {
            processModel.setProbationDays(Integer.valueOf(valueByCode));
            processModel.setProbationStartDay(new Date());
            DateTime endDate = DateUtil.offsetDay(new Date(), Integer.valueOf(valueByCode));
            processModel.setProbationEndDay(endDate);
        }
        processModel.setOnboardTime(null);
        processModel.setOffTime(null);
        processModel.setApplyOffTime(null);
        processModel.setApplyOnboardTime(null);
        processModel.setOnboardValidTime(null);
        processModel.setOffValidTime(null);
        processModel.setRecommendOnboardTime(null);
        processModel.setResignId(null);

        //构建试岗记录,获取最新的为模板
        SfaInterviewProcessRecordModel processRecordModel = sfaInterviewProcessRecordMapper.selectById(processModel.getInterviewRecordId());
        SfaInterviewProcessRecordModel originProcessRecordModel = new SfaInterviewProcessRecordModel();
        org.springframework.beans.BeanUtils.copyProperties(processRecordModel, originProcessRecordModel);
        processRecordModel.setProcessType(ProcessType.PROBATION.getProcessCode());
        processRecordModel.setProcessResult(ProcessResult.PROCESSING.getResultCode());
        processRecordModel.setProcessUserId(null);
        processRecordModel.setProcessUserName("系统审核");
        processRecordModel.setOrganizationId(null);
        processRecordModel.setComment(null);
        processRecordModel.setCreateTime(new Date());
        processRecordModel.setProcessDate(null);
        processRecordModel.setResignApplyId(null);
        processRecordModel.setNextProcessId(null);
        processRecordModel.setSalaryStructureId(null);
        processRecordModel.setPrevProcessId(processRecordModel.getId());
        List<String> tempStrList = new ArrayList<>();
        for (Integer code : request.getSmallMarketList()) {
            tempStrList.add(String.valueOf(code));
        }
        processRecordModel.setSmallMarketCodes(String.join(",", tempStrList));
        tempStrList.clear();
        for (Integer code : request.getProductionGroupList()) {
            tempStrList.add(String.valueOf(code));
        }
        processRecordModel.setProductionGroup(String.join(",", tempStrList));
        sfaInterviewProcessRecordMapper.insert(processRecordModel);

        originProcessRecordModel.setNextProcessId(processRecordModel.getId());
        sfaInterviewProcessRecordMapper.updateById(originProcessRecordModel);

        processModel.setInterviewRecordId(processRecordModel.getId());


        // 更新相关表的数据 sfa_employee_info、sfa_customer、ceo_business_organization_position_relation
        SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>().eq("mobile_number", sfaEmployeeInfoModel.getMobile()).eq("is_frozen", 1));
        if (Objects.isNull(sfaCustomer) || sfaCustomer.getIsFrozen() != 1) {
            throw new ApplicationException("未找到合伙人信息或未被汰换");
        }
        sfaCustomer.setIsFrozen(0);
        sfaCustomerMapper.updateById(sfaCustomer);

        // 插入sfa_employee_rehire数据
        employeeInfoProcessService.saveOldEmployeeInfo(sfaEmployeeInfoModel, oldPositionId, transactionPositionId, sfaCustomer.getMemberKey(), null);
        // 插入sfa_employee_rehire数据表会用到流程表的时间
        sfaInterviewProcessMapper.updateById(processModel);


        sfaEmployeeInfoModel.setOnboardTime(null);
        sfaEmployeeInfoModel.setPostType(2);
        if (request.getCeoType() == 2) {
            sfaEmployeeInfoModel.setType(2);
        }
        sfaEmployeeInfoModel.setEmployeeStatus(1);
        sfaEmployeeInfoModel.setUpdateTime(new Date());
        sfaEmployeeInfoModel.setUpdateUserId(request.getEmployeeId());
        sfaEmployeeInfoModel.setJoiningCompany(null);
        sfaEmployeeInfoModel.setContractCompany(null);
        sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);

        //劳动合同
        LaborContractEntity contractEntity = new LaborContractEntity();
        contractEntity.setApplyId(sfaEmployeeInfoModel.getApplicationId());
        contractEntity.setUpdateUserId(request.getEmployeeId());
        contractEntity.setDeleteFlag(1);
        laborContractMapper.updateById(contractEntity);


        CeoBusinessOrganizationPositionRelation partnerPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", sfaEmployeeInfoModel.getBranchCode()));
        partnerPositionRelation.setEmployeeId(null);
        partnerPositionRelation.setEmployeeName(null);
        partnerPositionRelation.setOnboardTime(null);
        partnerPositionRelation.setOffTime(null);
        partnerPositionRelation.setUpdatedTime(LocalDateTime.now());
        ceoBusinessOrganizationPositionRelationMapper.updateById(partnerPositionRelation);

//        判断小标是否被其它人占用,更新同步到旺铺
        List<Integer> productionGroup = request.getProductionGroupList();
        List<Integer> smallMarketCodes = request.getSmallMarketList();
        CeoMarketRelationRequest ceoMarketRelationRequest = new CeoMarketRelationRequest();
        ceoMarketRelationRequest.setSmallMarketList(smallMarketCodes);
        ceoMarketRelationRequest.setProductionGroupList(productionGroup);
        ceoMarketRelationRequest.setEmployeeId(sfaEmployeeInfoModel.getId());
        marketManagementService.updateCeoMarketRelation(ceoMarketRelationRequest);

        //调用旺铺接口包括重启账号、本地数据同步给旺铺
        //1、先关闭是为了清空离职时间，然后再开启
        rootConnectorUtil.closeMember(String.valueOf(sfaCustomer.getMemberKey()), -1, null);
        rootConnectorUtil.openMember(String.valueOf(sfaCustomer.getMemberKey()));


        SaveOrUpdateCeoDto saveOrUpdateCeoDto = new SaveOrUpdateCeoDto();
        saveOrUpdateCeoDto.setMemberKey(sfaCustomer.getMemberKey().toString());
        saveOrUpdateCeoDto.setAreaCode(sfaEmployeeInfoModel.getAreaCode());
        saveOrUpdateCeoDto.setArea(sfaEmployeeInfoModel.getAreaName());
        saveOrUpdateCeoDto.setCompanyCode(sfaEmployeeInfoModel.getCompanyCode());
        saveOrUpdateCeoDto.setCompany(sfaEmployeeInfoModel.getCompanyName());
        saveOrUpdateCeoDto.setBranch(sfaEmployeeInfoModel.getDepartmentName());
        saveOrUpdateCeoDto.setBranchCode(sfaEmployeeInfoModel.getDepartmentCode());
        saveOrUpdateCeoDto.setProvince(sfaEmployeeInfoModel.getProvince());
        saveOrUpdateCeoDto.setCity(sfaEmployeeInfoModel.getCity());
        saveOrUpdateCeoDto.setDistrict(sfaEmployeeInfoModel.getDistrict());
        saveOrUpdateCeoDto.setStreet(StringUtils.EMPTY);

        saveOrUpdateCeoDto.setProcessDate(LocalDateTimeUtils.formatTime(LocalDateTime.now(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
        saveOrUpdateCeoDto.setInductionDate(null);
        saveOrUpdateCeoDto.setSigningCompany(sfaEmployeeInfoModel.getJoiningCompany());
        saveOrUpdateCeoDto.setRealInductionDate(null);

        // 设置岗位属性
        saveOrUpdateCeoDto.setPositionType(applyMemberPo.getCeoType(), applyMemberPo.getPosition(), applyMemberPo.getJobsType())
                // 设置试岗状态
                .setProbationAttribution(sfaEmployeeInfoModel.getEmployeeStatus())
                // 设置合同属性
                .setContractJobInfo(applyMemberPo.getCeoType(), null);

        //2、保存信息
        boolean flag =
                rootConnectorUtil.sendUpdateAddressByKeyToMEMBERForBh(saveOrUpdateCeoDto);


        if (!flag) {
            throw new ApplicationException("旺铺同步地址失败");
        }

        //更新产品组
//        List<String> categoryList = new ArrayList<>();
//        request.getProductionGroupList().forEach(e->{
//            categoryList.add(e.toString());
//        });
//        categoryConnectorUtil.updateCategory(sfaCustomer.getMemberKey().toString(), categoryList);
    }

    @Override
    public CeoInterviewVo getCeoInterviewInfo(Long memberKey) {
        CeoInterviewVo vo = new CeoInterviewVo();
        Integer ceoInterviewInfo = applyMemberMapper.getCeoInterviewInfo(memberKey);

        if (Objects.isNull(ceoInterviewInfo)) {
            vo.setStatus(StringUtils.EMPTY);
            return vo;
        }


        InterviewState interviewState = InterviewState.findById(ceoInterviewInfo);


        vo.setStatus(interviewState.getName());
        return vo;
    }

    @Override
    public void updateLaborContract(LaborContractRequest request) {
        log.info("updateLaborContract update: {}", request);
        //校验逻辑
        String persons = settingServiceImpl.getValue("interview_human_resources");
        if (persons == null || !persons.contains(request.getPerson())) {
            throw new ApplicationException("请配置上传劳动合同的权限");
        }
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = checkCustomerService.getPersonInfo(request.getPerson(), loginInfo);

        laborContractService.saveLaborContract(request.getApplicationId(), request.getLaborContract(), request.getPerson(), ceoBusinessOrganizationPositionRelation.getEmployeeName());
    }

    @Override
    public CertificationVo checkCertification(Integer applyId) {

        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(applyId);
        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("报名信息获取失败");
        }

        CertificationVo vo = new CertificationVo();
        Integer position = applyMemberPo.getPosition();

        if (position == 1) {
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applyId));
            if (Objects.isNull(sfaEmployeeInfoModel)) {
                return null;
            }

            LocalDateTime certificationTime = sfaEmployeeInfoModel.getCertificationTime();
            if (Objects.nonNull(certificationTime)) {
                vo.setCertificationFinish(true);
            }
        }
        // 管理岗位
        else {
            SfaEmployeeInfoAdditional additional = sfaEmployeeInfoAdditionalMapper.selectOne(new QueryWrapper<SfaEmployeeInfoAdditional>().eq("apply_id", applyId).last("limit 1"));
            if (Objects.nonNull(additional)) {
                vo.setCertificationFinish(true);
            }

        }


        String originalUserName = applyMemberPo.getOriginalUserName();
        String originalIdCardNum = applyMemberPo.getOriginalIdCardNum();

        if (StringUtils.isNotBlank(originalUserName) || StringUtils.isNotBlank(originalIdCardNum)) {
            vo.setModify(true);
            if (StringUtils.isNotBlank(originalUserName)) {
                vo.setOriginalUserName(originalUserName);
            }
            if (StringUtils.isNotBlank(originalIdCardNum)) {
                vo.setOriginalIdCard(originalIdCardNum);
            }
        }

        // 检查是否用重复信息
        List<DuplicateModel> duplicateModels = sfaEmployeeInfoMapper.checkDuplicate(applyId, applyMemberPo.getUserName(), applyMemberPo.getIdCardNum());

        if (!CollectionUtils.isEmpty(duplicateModels)) {
            vo.setDuplicate(true);
            List<DuplicateVo> list = new ArrayList<>();
            duplicateModels.forEach(e -> {
                DuplicateVo duplicateVo = new DuplicateVo();
                duplicateVo.setDuplicateApplyId(e.getApplyId());
                duplicateVo.setDuplicateRecordId(e.getInterviewRecordId());
                duplicateVo.setDuplicateUserName(e.getEmployeeName());
                Integer employeeStatus = e.getEmployeeStatus();
                if (Objects.isNull(employeeStatus) || employeeStatus > 2) {
                    duplicateVo.setIsWork(0);
                } else {
                    duplicateVo.setIsWork(1);
                }
                list.add(duplicateVo);
            });

            vo.setDuplicateVoList(list);

        }

        return vo;
    }

    @Override
    @Transactional
    public void synDelayReason(SynDelayReasonRequest request) {
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(request.getApplyId());
        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("报名信息错误");
        }

        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new LambdaQueryWrapper<SfaInterviewProcessModel>().eq(SfaInterviewProcessModel::getApplicationId, request.getApplyId()));
        if (Objects.isNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("applyId错误");
        }


        sfaInterviewProcessModel.setDelayOnBoardDate(request.getDelayDate());
        sfaInterviewProcessModel.setDelayOnBoardReason(request.getDelayReason());
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);


        // 发送短信给直属上级
        Integer position = applyMemberPo.getPosition();
        String organizationId = applyMemberPo.getProvinceOrganizationId();
        if (position == 6) {
            organizationId = applyMemberPo.getVareaOrganizationId();
        } else if (position == 5) {
            organizationId = applyMemberPo.getAreaOrganizationId();
        }


        String curOrgCode = StringUtils.EMPTY;
        switch (position) {
            case 4:
                curOrgCode = applyMemberPo.getBranchOrganizationId();
                break;
            case 2:
                curOrgCode = applyMemberPo.getCompanyOrganizationId();
                break;
            case 6:
                curOrgCode = applyMemberPo.getProvinceOrganizationId();
                break;
            case 5:
                curOrgCode = applyMemberPo.getVareaOrganizationId();
                break;
            case 3:
                curOrgCode = applyMemberPo.getAreaOrganizationId();
                break;
        }
        String positionIdByOrgCode = ceoBusinessOrganizationPositionRelationMapper.getPositionIdByOrgCode(curOrgCode);
        if(StringUtils.isNotBlank(positionIdByOrgCode)){
            // 判断时间是否冲突
            checkInterviewService.checkDateConflict(request.getDelayDate(),positionIdByOrgCode,applyMemberPo.getUserMobile());
        }

        SelectAuditDto dto = new SelectAuditDto();
        dto.setChannel(3);
        dto.setCurrentOrganizationId(organizationId);
        String employeeId = configMapper.getValueByCode(ZW_HR_EMPLOYEE_ID_CODE);
        dto.setStandbyEmployeeId(employeeId);
        dto.setBusinessGroup(applyMemberPo.getBusinessGroup());
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = auditService.chooseAuditPerson(dto);
        if (Objects.nonNull(ceoBusinessOrganizationPositionRelation) && ceoBusinessOrganizationPositionRelation.getPositionTypeId() != 7) {
            String managerEmpId = ceoBusinessOrganizationPositionRelation.getEmployeeId();
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getEmployeeId, managerEmpId).in(SfaEmployeeInfoModel::getEmployeeStatus, 1, 2));
            if (Objects.nonNull(sfaEmployeeInfoModel) && StringUtils.isNotBlank(sfaEmployeeInfoModel.getEmployeeId())) {
                // 发送短信
                String delayOnBoardMsg = configMapper.getValueByCode("delay_on_board_msg_code");
                Map<String, String> smsParam = new HashMap<>();
                smsParam.put("employeeName", applyMemberPo.getUserName());
                smsParam.put("reason", request.getDelayReason());
                smsParam.put("delayDate", request.getDelayDate().toString());
                List<String> recNum = new ArrayList<>();
                recNum.add(sfaEmployeeInfoModel.getMobile());
                try {
                    geTuiService.SmsPushList(delayOnBoardMsg, smsParam, recNum);
                } catch (Exception e) {
                    log.error("【delay on board message send error】ex:{}", e.getMessage());
                }
            }
        }

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public CityManagerProbationVO getCityManagerProbation(String departmentId) {
        CityManagerProbationVO cityManagerProbationVO = new CityManagerProbationVO();
        String requirePerformance = Optional.ofNullable(configMapper.getValueByCode("city_manager_require_performance")).orElse("0");

        String currentYearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        BigDecimal performance = Optional.ofNullable(
                interviewBigTableService.selectDepartmentPerformance(departmentId, currentYearMonth)
        ).orElse(BigDecimal.ZERO);
        cityManagerProbationVO.setPerformance(performance);

        BigDecimal require = new BigDecimal(requirePerformance);
        cityManagerProbationVO.setRequirePerformance(require);

        if (require.compareTo(BigDecimal.ZERO) > 0 && require.compareTo(performance) < 0) {
            cityManagerProbationVO.setSkipProbation(true);
        }


        return cityManagerProbationVO;
    }

    @Override
    public void wantCoinsRetrieve(String organizationId) {
        log.info("【want coins retrieve】orgCode:{}", organizationId);
        // 检查是否有额度
        BigDecimal surplus = Optional.ofNullable(wantWalletLogMapper.selectSurplus(organizationId)).orElse(BigDecimal.ZERO);
        log.info("【want coins retrieve】surplus:{}", surplus);
        if (surplus.compareTo(BigDecimal.ZERO) > 0) {
            // 找到上级有人的组织
            SelectAuditDto dto = new SelectAuditDto();
            dto.setChannel(3);
            dto.setCurrentOrganizationId(organizationMapper.getOrganizationParentId(organizationId));
            String employeeId = configMapper.getValueByCode(ZW_HR_EMPLOYEE_ID_CODE);
            dto.setStandbyEmployeeId(employeeId);
            dto.setBusinessGroup(organizationMapper.getBusinessGroupById(organizationId));
            CeoBusinessOrganizationPositionRelation auditPerson = auditService.chooseAuditPerson(dto);

            RetrieveRequest retrieveRequest = new RetrieveRequest();
            retrieveRequest.setSourceOrganizationId(organizationId);
            retrieveRequest.setTargetOrganizationId(auditPerson.getOrganizationId());
            retrieveRequest.setRemark("人员离职自动回收");
            // 有额度调用回收
            walletApplicationService.retrieve(retrieveRequest);
        }

        // 扣罚转移
        penaltyService.transferLeaveBalance(organizationId);
    }

    @Override
    public List<Integer> getExternalBusinessGroup() {
        String externalBusinessGroup = configMapper.getValueByCode("external_business_group");
        if (StringUtils.isBlank(externalBusinessGroup)) {
            return ListUtils.EMPTY_LIST;
        }

        return Arrays.asList(externalBusinessGroup.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void importApplyMember(MultipartFile file) {


        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);


        List<ImportApplyMemberModel> list = new ArrayList<>();
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), ImportApplyMemberModel.class, params);
        } catch (Exception e) {
            log.error("【gold upload】导入失败", e);
            throw new ApplicationException("导入失败");
        }
        // 过滤空行
        list = list.stream().filter(f -> StringUtils.isNotBlank(f.getUserName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            throw new ApplicationException("导入失败");
        }

        for (int i = 0; i < list.size(); i++) {
            ImportApplyMemberModel importApplyMemberModel = list.get(i);

            try {
                // 组装报名参数
                ApplyMemberRequest applyMemberRequest = applyMemberAssemble(importApplyMemberModel);

                // 调用报名接口
                applyMemberService.signUp(applyMemberRequest);
            } catch (Exception e) {

                throw new ApplicationException(MessageFormat.format(IMPORT_ERROR_MESSAGE, String.valueOf(i + 3), e.getMessage()));
            }
        }
    }

    private ApplyMemberRequest applyMemberAssemble(ImportApplyMemberModel importApplyMemberModel) {
        ApplyMemberRequest applyMemberRequest = new ApplyMemberRequest();
        BeanUtils.copyProperties(importApplyMemberModel, applyMemberRequest);

        String agentProvince = importApplyMemberModel.getAgentProvince();
        String agentCity = importApplyMemberModel.getAgentCity();
        String agentDistrict = importApplyMemberModel.getAgentDistrict();
        if (StringUtils.isBlank(agentProvince) || StringUtils.isBlank(agentCity) || StringUtils.isBlank(agentDistrict)) {
            throw new ApplicationException("经销地区缺失");
        }

        // 默认全职
        applyMemberRequest.setJobsType(1);

        int businessGroupId = RequestUtils.getBusinessGroup();
        applyMemberRequest.setBusinessGroup(RequestUtils.getBusinessGroup());

        String mobile = importApplyMemberModel.getUserMobile();
        if (StringUtils.isBlank(mobile)) {
            throw new ApplicationException("缺少手机号");
        }
        // 检查手机号格式是否正确
        Matcher PhoneMatcher = CommonConstants.PHONE_NUMBER_PATTERN.matcher(mobile);
        if (!PhoneMatcher.matches()) {
            throw new ApplicationException("手机号错误");
        }

        String idCardNum = importApplyMemberModel.getIdCardNum();
        if (StringUtils.isBlank(idCardNum)) {
            throw new ApplicationException("缺少身份证号错误");
        }
        // 检查身份证号
        Matcher idCardNumMatcher = CommonConstants.ID_CARD_PATTERN.matcher(idCardNum);
        if (!idCardNumMatcher.matches()) {
            throw new ApplicationException("身份证号错误");
        }


        String gender = importApplyMemberModel.getGender();
        Integer genderId = ImportGenderEnum.getIdByName(gender);
        applyMemberRequest.setGender(genderId);

        Integer positionId = ImportPositionEnum.getIdByName(importApplyMemberModel.getPosition());
        applyMemberRequest.setPosition(positionId);


        if (businessGroupId != 15 && businessGroupId != 16 && positionId == 8) {
            throw new ApplicationException("岗位错误，不支持客户主任");
        }

        String province = importApplyMemberModel.getProvince();
        String city = importApplyMemberModel.getCity();
        String district = importApplyMemberModel.getDistrict();
        String street = importApplyMemberModel.getStreet();
        if (StringUtils.isBlank(province) || StringUtils.isBlank(city) || StringUtils.isBlank(district) || StringUtils.isBlank(street)) {
            throw new ApplicationException("家庭地址缺失");
        }

        Integer modeId = ImportModeEnum.getIdByName(importApplyMemberModel.getMode());
        if (modeId == 2 && businessGroupId != 13) {
            throw new ApplicationException("不支持夜班");
        }
        applyMemberRequest.setWorkModeType(modeId);


        return applyMemberRequest;
    }

}
