package com.wantwant.sfa.backend.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.util.PoiPublicUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wantwant.commons.cons.StatusCode;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.entity.SfaAbnormalPersonDetail;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.arch.model.AccountModel;
import com.wantwant.sfa.backend.arch.request.SAccountRequest;
import com.wantwant.sfa.backend.businessGroup.service.impl.BusinessGroupService;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.common.OrganizationPositionRelationEnums;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.dict.request.DictCodeRequest;
import com.wantwant.sfa.backend.dict.service.impl.DictCodeServiceImpl;
import com.wantwant.sfa.backend.dict.vo.DictCodeVo;
import com.wantwant.sfa.backend.display.vo.ExportOrganizationSkuVO;
import com.wantwant.sfa.backend.display.vo.ExportSkuVO;
import com.wantwant.sfa.backend.employee.vo.EmployeeInfoVO;
import com.wantwant.sfa.backend.employee.vo.ParentVo;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.enums.RankingListPositionEnums;
import com.wantwant.sfa.backend.info.vo.SkuSpecificationNewVo;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.mainProduct.request.QuarterProductQueryRequest;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.arch.AccountMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.mapper.mainProduct.MainProductMapper;
import com.wantwant.sfa.backend.mapper.metrics.SfaAlterTemplateMapper;
import com.wantwant.sfa.backend.mapper.metrics.SfaMetricsMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.review.SfaReviewReportMapper;
import com.wantwant.sfa.backend.metrics.entity.SfaMetricsEntity;
import com.wantwant.sfa.backend.model.*;
import com.wantwant.sfa.backend.model.employee.EmployeeDTO;
import com.wantwant.sfa.backend.model.organizationGoal.QuarterOrganizationGoalExcelPO;
import com.wantwant.sfa.backend.notify.dto.CompanyManagerDTO;
import com.wantwant.sfa.backend.organization.vo.OrganizationAllChildInfoVo;
import com.wantwant.sfa.backend.organizationGoal.request.EmployeeGoalQueryRequest;
import com.wantwant.sfa.backend.organizationGoal.vo.EmpGoalVO;
import com.wantwant.sfa.backend.organizationGoal.vo.EmployeeGoalLogVO;
import com.wantwant.sfa.backend.po.RealTimeRankingInfoPO;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.realData.anno.Radar;
import com.wantwant.sfa.backend.realData.dto.CeoTrendDTO;
import com.wantwant.sfa.backend.realData.dto.EmployeeGoalDTO;
import com.wantwant.sfa.backend.realData.dto.InventoryDTO;
import com.wantwant.sfa.backend.realData.dto.YearQuarterDTO;
import com.wantwant.sfa.backend.realData.request.*;
import com.wantwant.sfa.backend.realData.vo.*;
import com.wantwant.sfa.backend.review.entity.SfaReviewReportEntity;
import com.wantwant.sfa.backend.salary.request.SalaryControlSearchRequest;
import com.wantwant.sfa.backend.salary.request.SearchAllocatedHistoryRequest;
import com.wantwant.sfa.backend.salary.service.ISalaryMiddlewareService;
import com.wantwant.sfa.backend.salary.vo.SalaryAllocatedVo;
import com.wantwant.sfa.backend.salary.vo.SalaryControlConfigVo;
import com.wantwant.sfa.backend.service.*;
import com.wantwant.sfa.backend.service.filterChain.TargetFilterChainComponent;
import com.wantwant.sfa.backend.service.filterChain.TargetRule;
import com.wantwant.sfa.backend.util.*;
import com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO;
import com.wantwant.sfa.backend.zw.request.RecruitmentProgress1Request;
import com.wantwant.sfa.backend.zw.request.RecruitmentProgressRequest;
import com.wantwant.sfa.backend.zw.vo.*;
import com.wantwant.sfa.common.base.CommonConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 总部、总督导、区域总监实时数据serviceImpl
 *
 * @version 1.0
 * @date 2022-02-16 18:27
 */
@Slf4j
@Service
public class RealtimeDataServiceImpl implements IRealtimeDataService {

    private static final Object NEXT_ORG_KEY = "sfa:next:org:";
    @Resource
    private RealtimeDataMapper mapper;

    @Resource
    private EmployeeGoalMapper employeeGoalMapper;
    @Resource
    private RealTimeUtils realTimeUtils;

    @Resource
    private CeoBusinessOrganizationPositionRelationMapper relationMapper;

    @Resource
    private ApplyMemberMapper applyMemberMapper;

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Resource
    private OrganizationGoalService organizationGoalService;

    @Resource
    private EmployeeMapper employeeMapper;

    @Resource
    private OrganizationProductGoalService organizationProductGoalService;

    @Resource
    private EmployeeGoalLogMapper employeeGoalLogMapper;

    @Resource
    private MainProductMapper mainProductMapper;

    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;

    @Resource
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;

    @Resource
    private RealtimeMapper realtimeMapper;

    @Resource
    private CustomerVisitInfoMapper customerVisitInfoMapper;

    @Resource
    private CustomerManagementCallBackInfoMapper customerManagementCallBackInfoMapper;

    @Resource
    private CommodityMapper commodityMapper;

    @Resource
    private SettingsMapper settingsMapper;

    @Resource
    private RealtimeDataMapper realtimeDataMapper;

    @Resource
    private SfaAlterTemplateMapper sfaAlterTemplateMapper;

    @Resource
    private SfaReviewReportMapper sfaReviewReportMapper;

    @Resource
    private MarkMapper markMapper;

    @Resource
    private SfaAbnormalPersonDetailMapper sfaAbnormalPersonDetailMapper;

    @Resource
    private SfaMetricsMapper sfaMetricsMapper;
    @Resource
    private TargetFilterChainComponent targetFilterChainComponent;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Resource
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Resource
    private ICheckCustomerService checkCustomerService;
    @Resource
    private EmployeeGoalServiceImpl employeeGoalServiceImpl;
    @Resource
    private EmployeeGoalLogServiceImpl employeeGoalLogServiceImpl;

    @Resource
    private DictCodeServiceImpl dictCodeServiceImpl;

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ISalaryMiddlewareService salaryMiddlewareService;
    @Resource
    private ZWSalaryService zWSalaryService;
    @Resource
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    /**
     * 财年业绩描述
     */
    private final static String FISCAL_YEAR_DESC = "年财年业绩";
    /**
     * 数据比对类型:环比chain
     */
    private final static String CONTRAST_TYPE_CHAIN = "chain";
    /**
     * 数据比对类型:同比year
     */
    private final static String CONTRAST_TYPE_YEAR = "year";
    /**
     * 数据比对类型:环比chain
     */
    private final static String CONTRAST_TYPE_CHAIN_STR = "成长业绩";
    /**
     * 数据比对类型:同比year
     */
    private final static String CONTRAST_TYPE_YEAR_STR = "同期成长业绩";

    @Resource
    private CustomerMapper customerMapper;

    @Resource
    private EmployeeGoalDetailMapper employeeGoalDetailMapper;
    @Resource
    private EmployeeGoalDetailServiceImpl employeeGoalDetailServiceImpl;
    @Resource
    private MainProductService mainProductService;

    @Resource
    private BusinessGroupService businessGroupService;

    /**
     * 实时数据
     *
     * @param organizationId 组织ID
     * @param yearMonth      年月
     * @param employeeId     工号
     * @return: com.wantwant.sfa.backend.realData.vo.RealtimeDataVO
     * @date: 2022-02-16 18:31
     */
    @Override
    public RealtimeDataVO queryRealtimeData(String organizationId, String yearMonth, String employeeId) {
        log.info("实时数据入参:{}-{}", organizationId, yearMonth, employeeId);
        RealtimeDataVO res = mapper.queryRealtimeData(organizationId, yearMonth);
        if (Objects.isNull(res)) {
            res = new RealtimeDataVO();
        }
        if (organizationId.contains("ZB_Z")) {
            CeoBusinessOrganizationPositionRelation relation = relationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, employeeId).eq(CeoBusinessOrganizationPositionRelation::getChannel, RequestUtils.getChannel())).get(0);
            if (Objects.nonNull(relation)) {
                res.setEmployeeName(relation.getEmployeeName());
                if (null != relation.getOnboardTime()) {
                    res.setOnboardTime(LocalDateTimeUtils.formatTime(relation.getOnboardTime(), "yyyy-MM-dd HH:mm:ss"));
                    res.setOnboardDays(daysBetween(res.getOnboardTime()));
                }
            }
        }
        return res;
    }

    public String daysBetween(String smdate) {
        String day = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(smdate));
            long time1 = cal.getTimeInMillis();
            long time2 = System.currentTimeMillis();
            long between_days = (time2 - time1) / (1000 * 3600 * 24);
            day = String.valueOf(between_days);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return day;
    }

    /**
     * 根据组织ID获取下级数据
     *
     * @param organizationId   组织ID
     * @param yearMonth        年月
     * @param organizationName 组织名称
     * @return: java.util.List<com.wantwant.sfa.backend.realData.vo.RealtimeDataVO>
     * @date: 2022-02-17 20:13
     */
    @Override
    public List<RealtimeDataVO> queryChildRealtimeData(String organizationId, String yearMonth, String organizationName) {
        log.info("根据组织ID获取下级数据入参:{}-{}", organizationId, yearMonth, organizationName);
        return mapper.queryChildRealtimeData(organizationId, yearMonth, organizationName);
    }

    /**
     * 合伙人目标列表
     *
     * @param query
     * @return: com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.realData.vo.EmployeeGoalVO>
     * @date: 2022-02-18 16:23
     */
    @Override
    public IPage<EmployeeGoalVO> queryEmpGoalByPage(EmployeeGoalRequest query) {
        log.info("合伙人目标列表入参:{}", query);
        Page<EmployeeGoalVO> page = new Page<>(query.getPage(), query.getRows());
        List<EmployeeGoalVO> list = mapper.pageEmpGoal(page, query);
        EmployeeGoalVO hj = new EmployeeGoalVO();
        hj.setArea("合计");
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            List<String> ids = list.stream().map(EmployeeGoalVO::getPositionId).collect(Collectors.toList());
            Map<String, EmployeeGoalPo> map = employeeGoalMapper.selectByIds(ids, query.getYearMonth(), RequestUtils.getChannel());
            hj.setSaleGoal(employeeGoalMapper.getTotalByIds(ids, query.getYearMonth(), RequestUtils.getChannel()));
            list.forEach(e -> {
                EmployeeGoalPo po = map.get(e.getPositionId());
                if (Objects.nonNull(po)) {
                    e.setId(po.getId());
                    e.setEffectiveDate(po.getEffectiveDate());
                    e.setSaleGoal(po.getSaleGoal());
                    e.setUpdatedBy(po.getUpdatedBy());
                    e.setUpdatedName(po.getUpdatedName());
                }
            });
        }
        list.add(hj);
        page.setRecords(list);
        return page;
    }

    //    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public List<EmpGoalVO> auditPartnerGoalList() {
        return realtimeDataMapper.auditPartnerGoalList();
    }

    @Override
    public List<EmpGoalVO> queryNeedSetGoalList(String yearMonth) {
        return realtimeDataMapper.queryNeedSetGoalList(yearMonth);
    }

    /**
     * 新合伙人目标列表
     */
    @Override
    public IPage<EmpGoalVO> pageEmpGoalNew(EmployeeGoalQueryRequest query) {
        log.info("合伙人目标列表入参:{}", query);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        String organizationType = RequestUtils.getLoginInfo().getOrganizationType();
        query.setBusinessGroup(loginInfo.getBusinessGroup());
        query.setOrganizationType(organizationType);
        if (CommonUtil.StringUtils.isBlank(query.getOrganizationId())) {
            if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(organizationType)) {
                List<String> employeeOrganizationIds = organizationMapper.getEmployeeOrganizationId(query.getEmployeeId(), loginInfo);
                if (CollectionUtils.isEmpty(employeeOrganizationIds)) {
                    throw new ApplicationException("操作人组织信息获取失败");
                }
                query.setOrganizationIds(employeeOrganizationIds);
            }
        }
        List<String> employeeIdList = getList();
        List<String> nextOrgCodes = new ArrayList<>();
        List<Integer> goalIds = new ArrayList<>();
        if (query.getType() != null && query.getType() == 1) {
            if (OrganizationTypeEnum.DEPARTMENT.getOrganizationType().equals(organizationType) || OrganizationTypeEnum.COMPANY.getOrganizationType().equals(organizationType) || OrganizationTypeEnum.PROVINCE.getOrganizationType().equals(organizationType) || OrganizationTypeEnum.VARE.getOrganizationType().equals(organizationType)) {

                // 查询登陆人的所有合伙人
                List<ParentVo> childrenVoList = sfaEmployeeInfoMapper.queryChildrenListByEmployeeInfoIds(query.getEmployeeId(), query.getBusinessGroup(), organizationType);
                Map<Integer, String> childrenMap = childrenVoList.stream().collect(Collectors.toMap(ParentVo::getEmployeeInfoId, ParentVo::getOrganizationId));
                // 查询所有合伙人的上级
                List<ParentVo> parentVoList = sfaEmployeeInfoMapper.queryParentListByEmployeeInfoIds1(childrenVoList.stream().map(ParentVo::getEmployeeInfoId).collect(Collectors.toList()), query.getBusinessGroup());
                Map<Integer, List<ParentVo>> parentMap = parentVoList.stream().collect(Collectors.groupingBy(ParentVo::getEmployeeInfoId));
                childrenVoList.stream().forEach(vo -> {
                    // 判断登陆人是否是合伙人的直接上级
                    List<ParentVo> parentVos = parentMap.get(vo.getEmployeeInfoId());
                    if (!CollectionUtils.isEmpty(parentVos)) {
                        if (OrganizationTypeEnum.DEPARTMENT.getOrganizationType().equals(organizationType) && parentVos.get(0).getLevel() == 1) {
                            nextOrgCodes.add(childrenMap.get(vo.getEmployeeInfoId()));
                        } else if (OrganizationTypeEnum.COMPANY.getOrganizationType().equals(organizationType) && parentVos.get(0).getLevel() == 2) {
                            nextOrgCodes.add(childrenMap.get(vo.getEmployeeInfoId()));
                        } else if (OrganizationTypeEnum.PROVINCE.getOrganizationType().equals(organizationType) && parentVos.get(0).getLevel() == 3) {
                            nextOrgCodes.add(childrenMap.get(vo.getEmployeeInfoId()));
                        } else if (OrganizationTypeEnum.VARE.getOrganizationType().equals(organizationType) && parentVos.get(0).getLevel() == 4) {
                            nextOrgCodes.add(childrenMap.get(vo.getEmployeeInfoId()));
                        }
                    }
                });
            }
        } else if (query.getType() != null && query.getType() == 2) {
            if (OrganizationTypeEnum.COMPANY.getOrganizationType().equals(organizationType) || OrganizationTypeEnum.PROVINCE.getOrganizationType().equals(organizationType) || OrganizationTypeEnum.VARE.getOrganizationType().equals(organizationType)) {
                List<EmployeeGoalLogPO> employeeGoalLogPOList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>().eq(EmployeeGoalLogPO::getUpdatedBy, query.getEmployeeId()).eq(EmployeeGoalLogPO::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0).eq(EmployeeGoalLogPO::getIsDelete, 0));
                goalIds = employeeGoalLogPOList.stream().map(EmployeeGoalLogPO::getGoalId).collect(Collectors.toList());
            } else if (OrganizationTypeEnum.ZB.getOrganizationType().equals(organizationType) && employeeIdList.contains(query.getEmployeeId())) {
                List<EmployeeGoalLogPO> employeeGoalLogPOList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>().isNull(EmployeeGoalLogPO::getUpdatedBy).eq(EmployeeGoalLogPO::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0).eq(EmployeeGoalLogPO::getIsDelete, 0));
                goalIds = employeeGoalLogPOList.stream().map(EmployeeGoalLogPO::getGoalId).collect(Collectors.toList());
            }
        }
        query.setYmd(LocalDate.parse(query.getYearMonth() + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        Page<EmpGoalVO> page = new Page<>(query.getPage(), query.getRows());
        List<EmpGoalVO> list = realtimeDataMapper.pageEmpGoalNew(page, query, nextOrgCodes, goalIds, RequestUtils.getBusinessGroup());
        // List 里面的 id为0表示没有设置月度目标，非0的值和 sfa_employee_goal 表的 id 一致
        // 这里不能根据 id 查询，因为申请审批的数据还没有同步到大数据库，得根据pageEmpGoalNew sql 里面这个 sql 语句条件查询
        // LEFT JOIN ods.hp_sfa_employee_goal g on g.position_id = t.position_id and g.`ranges` = 0 and DATE_FORMAT(g.effective_date,'%Y-%m') = t.the_year_mon and g.is_delete = 0
        List<String> positionIdList = list.stream().map(EmpGoalVO::getPositionId).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> yearMonList = list.stream().map(EmpGoalVO::getTheYearMon).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 月度目标
        List<EmployeeGoalPo> employeeMonthGoalPos;
        // 季度目标
        List<EmployeeGoalPo> employeeQuarterGoalPos;
        // 计算季度值
        Integer quarter = QuarterCycle.getQuarter(query.getYmd());
        Integer year = query.getYmd().getYear();
        if (!CollectionUtils.isEmpty(positionIdList) && !CollectionUtils.isEmpty(yearMonList)) {
            // 这个写法查询不出来数据，从日志打印的 sql 单独执行是没有问题就很奇怪
//            String inClause = yearMonList.stream()
//                    .map(ym -> "'" + ym + "'")
//                    .collect(Collectors.joining(","));
//            employeeGoalPos = employeeGoalMapper.selectList(new LambdaQueryWrapper<EmployeeGoalPo>()
//                    .in(EmployeeGoalPo::getPositionId, positionIdList)
//                    // 这种写法可能导致yearMonList 不是被遍历传入，而是作为一个整体对象传入，导入查询失败
////                    .apply("DATE_FORMAT(effective_date, '%Y-%m') IN ({0})", yearMonList)
//                    // 可以用这种拼接的方式 解决，但是还是推荐在 xml 中书写，更直观一些，阅读性好点
//                    .apply("DATE_FORMAT(effective_date, '%Y-%m') IN (" + inClause + ")")
//                    .eq(EmployeeGoalPo::getRange, 0)
//                    .eq(EmployeeGoalPo::getIsDelete, 0));
            employeeMonthGoalPos = employeeGoalMapper.selectMonthByPositionIdsAndYearMonths(positionIdList, yearMonList);
            employeeQuarterGoalPos = employeeGoalMapper.selectQuarterByPositionIdsAndYearMonths(positionIdList, quarter, year);

        } else {
            employeeMonthGoalPos = new ArrayList<>();
            employeeQuarterGoalPos = new ArrayList<>();
        }

        Map<Integer, String> partnerGoalSetStatusMap = dictCodeServiceImpl.getCodeItem(DictCodeRequest.builder().classCd(DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS).build()).stream().collect(Collectors.toMap(model -> Integer.valueOf(model.getItemValue()), DictCodeVo::getItemContent));
        HashMap<Integer, String> titleMap = mainProductService.getTargetTitle(query.getYearMonth());
        BigDecimal saleGoalTotal = BigDecimal.ZERO;
        BigDecimal quarterSaleGoalTotal = BigDecimal.ZERO;
        for (EmpGoalVO vo : list) {
            // 因为大数据同步延迟的问题，vo.getGoalStatus() 可能为null，导致申请之后按钮还会展示，或者修改之后不是最新的值
            // 所以审批状态获取从 sfa 数据库表查询实时数据
            Integer goalStatus = vo.getGoalStatus();
            Optional<EmployeeGoalPo> poOptional = employeeMonthGoalPos.stream().filter(f ->
                    Objects.equals(f.getPositionId(), vo.getPositionId()) && Objects.equals(f.getEffectiveDate().format(DateTimeFormatter.ofPattern("yyyy-MM")), vo.getTheYearMon())).findFirst();
            if (poOptional.isPresent()) {
                goalStatus = poOptional.get().getGoalStatus();
                vo.setId(poOptional.get().getId());
                vo.setSaleGoal(poOptional.get().getSaleGoal());
            }
            if (Objects.nonNull(goalStatus)) {
                vo.setGoalStatus(goalStatus);
                vo.setGoalStatusName(partnerGoalSetStatusMap.get(goalStatus));
            }

            // 设置季度目标返回值
            Optional<EmployeeGoalPo> poQuarterOptional = employeeQuarterGoalPos.stream().filter(f ->
                    Objects.equals(f.getPositionId(), vo.getPositionId())).findFirst();
            poQuarterOptional.ifPresent(employeeGoalPo -> vo.setQuarterSaleGoal(employeeGoalPo.getSaleGoal()));

            if (vo.getSaleGoal() != null) {
                saleGoalTotal = saleGoalTotal.add(vo.getSaleGoal());
            }
            if (vo.getQuarterSaleGoal() != null) {
                quarterSaleGoalTotal = quarterSaleGoalTotal.add(vo.getQuarterSaleGoal());
            }
            if (vo.getId() == null || vo.getId() == 0 ) {
                continue;
            }
            // 新增主推品明细查询(vo 中返回的ID是 月度ID
            List<EmployeeGoalDetailPo> monthDetailPoList = employeeGoalDetailMapper.selectList(new LambdaQueryWrapper<EmployeeGoalDetailPo>().eq(EmployeeGoalDetailPo::getGoalId, vo.getId()).eq(EmployeeGoalDetailPo::getIsDelete, 0));
            List<EmployeeGoalDetailPo> allList = new ArrayList<>(monthDetailPoList);
            // 查询季度
            List<EmployeeGoalDetailPo> detailPoList = employeeGoalDetailMapper.selectList(new LambdaQueryWrapper<EmployeeGoalDetailPo>().eq(EmployeeGoalDetailPo::getPositionId, vo.getPositionId())
                    .eq(EmployeeGoalDetailPo::getYear, query.getYmd().getYear()).eq(EmployeeGoalDetailPo::getQuarter, QuarterCycle.getQuarter(query.getYmd()))
                    .eq(EmployeeGoalDetailPo::getRange, 1).eq(EmployeeGoalDetailPo::getIsDelete, 0)
                    .orderByDesc(EmployeeGoalDetailPo::getId)
            );

            if(!CollectionUtils.isEmpty(detailPoList)){
                Map<Integer, List<EmployeeGoalDetailPo>> detailMap = detailPoList.stream().filter(f -> Objects.nonNull(f.getMainId()))
                        .collect(groupingBy(EmployeeGoalDetailPo::getMainId));

                detailPoList.clear();
                detailMap.forEach((k,v) -> {
                    if (!v.isEmpty()) {
                        detailPoList.add(v.get(0));
                    }
                });
                allList.addAll(detailPoList);
            }

            if (CommonUtil.ListUtils.isEmpty(allList)) {
                continue;
            }
            List<EmployeeGoalDetailVO> mainProductList = new ArrayList<>();
            for (EmployeeGoalDetailPo detailPo : allList) {
                EmployeeGoalDetailVO employeeGoalDetailVO = BeanUtil.copyProperties(detailPo, EmployeeGoalDetailVO.class);
                String title = titleMap.get(detailPo.getMainId());
                employeeGoalDetailVO.setTitle(title);
                mainProductList.add(employeeGoalDetailVO);
            }
            vo.setMainProductList(mainProductList);
        };
        if (CommonUtil.ListUtils.isNotEmpty(list) && query.getBtnFlg()) {
            Integer day = getDeadline(query.getYmd());
            if (query.getYmd().getMonthValue() == 2 && getDeadline(query.getYmd()) == 30) {
                day = day - 2;
            }
            LocalDate commonDeadline = query.getYmd().withDayOfMonth(day);
            Integer days = Integer.valueOf(settingsMapper.getSfaSettingsByCode("partner_goal_set_deadline_days"));
            // 查询所有合伙人的上级
            List<Integer> employeeInfoIds = list.stream().map(EmpGoalVO::getEmployeeInfoId).collect(Collectors.toList());
            List<ParentVo> parentVoList = sfaEmployeeInfoMapper.queryParentListByEmployeeInfoIds1(employeeInfoIds, query.getBusinessGroup());
            Map<Integer, List<ParentVo>> parentMap = parentVoList.stream().collect(Collectors.groupingBy(ParentVo::getEmployeeInfoId));

            // 查询所有审批人
            Map<Integer, EmployeeGoalLogPO> goalPersonMap;
            if (CommonUtil.ListUtils.isNotEmpty(employeeMonthGoalPos)) {
                List<Integer> goalAllIds = employeeMonthGoalPos.stream().map(EmployeeGoalPo::getId).collect(Collectors.toList());
                List<EmployeeGoalLogPO> employeeGoalLogPOList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>().in(EmployeeGoalLogPO::getGoalId, goalAllIds).eq(EmployeeGoalLogPO::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0).eq(EmployeeGoalLogPO::getIsDelete, 0));
                goalPersonMap = employeeGoalLogPOList.stream()
                        .collect(Collectors.toMap(
                                EmployeeGoalLogPO::getGoalId,
                                Function.identity(),
                                (existing, replacement) -> replacement
                        ));
            }else {
                goalPersonMap = new HashMap<>();
            }

            list.forEach(vo -> {
                LocalDate deadline = commonDeadline;
                if (vo.getOnJobTime().plusDays(days).isAfter(deadline)) {
                    deadline = vo.getOnJobTime().plusDays(days);
                }
                if (!LocalDate.now().isAfter(deadline)) {
                    List<ParentVo> parentVos = parentMap.get(vo.getEmployeeInfoId());
                    // 因为大数据同步延迟的问题，vo.getGoalStatus() 可能为null，导致申请之后按钮还会展示，或者修改之后不是最新的值
                    // 所以审批状态获取从 sfa 数据库表查询实时数据
                    Integer goalStatus = vo.getGoalStatus();
                    Optional<EmployeeGoalPo> poOptional = employeeMonthGoalPos.stream().filter(f ->
                            Objects.equals(f.getPositionId(), vo.getPositionId()) && Objects.equals(f.getEffectiveDate().format(DateTimeFormatter.ofPattern("yyyy-MM")), vo.getTheYearMon())).findFirst();
                    if (poOptional.isPresent()) {
                        goalStatus = poOptional.get().getGoalStatus();
                    }
                    if ((goalStatus == null || DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_2.equals(goalStatus))) {
                        // get(0) 表示可以审核的人
                        if (!CollectionUtils.isEmpty(parentVos) && query.getEmployeeId().equals(parentVos.get(0).getParentEmployeeId())) {
                            vo.setBShowBtn1(true);
                        } else if (CollectionUtils.isEmpty(parentVos) && OrganizationTypeEnum.ZB.getOrganizationType().equals(organizationType) && employeeIdList.contains(query.getEmployeeId())) {
                            vo.setBShowBtn1(true);
                        }
                    }
                    if (poOptional.isPresent() && DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0.equals(goalStatus)) {
                        Integer goalId = poOptional.get().getId();
                        // 判断当前登录人和审批人是否相同
                        String auditUser = "";
                        if (goalPersonMap.containsKey(goalId)) {
                            auditUser = goalPersonMap.get(goalId).getUpdatedBy();
                        }
                        if (Objects.equals(auditUser, query.getEmployeeId())) {
                            vo.setBShowBtn2(true);
                            // auditUser 为空说明需要总部人审批
                        }else if (StringUtils.isBlank(auditUser) && employeeIdList.contains(query.getEmployeeId())) {
                            // employeeIdList 表示总部有审批权限的人（对应审核日志表的审核人为空的情况）
                            vo.setBShowBtn2(true);
                        }
                    }

                    // 设置是否可以编辑季度目标值状态
                    Optional<EmployeeGoalPo> poQuarterOptional = employeeQuarterGoalPos.stream().filter(f ->
                            Objects.equals(f.getPositionId(), vo.getPositionId())).findFirst();
                    if (poQuarterOptional.isPresent()) {
                        Integer quarterGoalStatus = poQuarterOptional.get().getGoalStatus();
                        if (DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_1.equals(quarterGoalStatus)) {
                            vo.setCanEdit(false);
                        }
                    }
                }
            });
        }
        if (Objects.isNull(query.getType())) {
            //合计
            EmpGoalVO hj = realtimeDataMapper.selectSumEmpGoal(query, nextOrgCodes, goalIds);
            hj.setSaleGoal(saleGoalTotal);
            hj.setQuarterSaleGoal(quarterSaleGoalTotal);
            list.add(hj);
        }
        page.setRecords(list);
        return page;
    }

    private List<String> getList() {
        SAccountRequest accountRequest = new SAccountRequest();
        accountRequest.setRows(1000);
        accountRequest.setRoleId(30);
        List<AccountModel> accountList = accountMapper.selectList(accountRequest, RequestUtils.getChannel(), RequestUtils.getBusinessGroup());
        return accountList.stream().map(AccountModel::getEmployeeId).collect(Collectors.toList());
    }

    private Integer getDeadline(LocalDate ymd) {
        if (Objects.isNull(ymd)) {
            ymd = LocalDate.now();
        }
        QuarterProductQueryRequest request = new QuarterProductQueryRequest();
        request.setYear(ymd.getYear());
        request.setQuarter(ymd.get(IsoFields.QUARTER_OF_YEAR));
        Integer day = organizationGoalService.goalSetDay(request);
        if (Objects.isNull(day)) {
            day = ymd.lengthOfMonth();
//            throw new ApplicationException("请先设置每月目标截止日期");
        }
        return day;
    }

    /**
     * 设置合伙人目标
     *
     * @param request
     * @return: java.lang.Integer
     * @date: 2022-02-18 18:50
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void setGoal(EmployeeSetGoalRequest request) {
        log.info("设置合伙人目标入参:{}", request);

        String employeeName = employeeMapper.getEmployeeNameByEmployeeIdFor123(request.getUpdatedBy(), RequestUtils.getChannel());
        LocalDateTime now = LocalDateTime.now();
        String organizationType = RequestUtils.getLoginInfo().getOrganizationType();
        boolean isPass = OrganizationTypeEnum.ZB.getOrganizationType().equals(organizationType) || OrganizationTypeEnum.VARE.getOrganizationType().equals(organizationType);
        Integer status = isPass ? DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_1 : DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0;
        List<SfaEmployeeInfoModel> parentList = sfaEmployeeInfoMapper.queryParentList(request.getPositionId());

        // 如果季度全品项业绩目标不为空 或者 季度目标（主推品） 不为空 判断时间是否允许修改
        if (Objects.nonNull(request.getQuarterSaleGoal()) || !CollectionUtils.isEmpty(request.getQuarterList())) {
            QuarterOrganizationGoalExcelPO quarterOrganizationGoalExcelPO = organizationGoalService.getQuarterOrganizationGoalExcelPO();
            if (Objects.isNull(quarterOrganizationGoalExcelPO)) {
                throw new ApplicationException("季度组织目标配置不存在");
            }
//            // 获取当前日期
//            LocalDate currentDate = LocalDate.now();
//            // 当前日期是否在每个季度的第一个月
//            if (!isFirstMonthOfQuarter(currentDate)) {
//                throw new ApplicationException("当前时间段不允许修改季度目标");
//            }
//            // 提取当前日期的“日”并转为 int
//            int dayOfMonth = currentDate.getDayOfMonth();
//            if (dayOfMonth > quarterOrganizationGoalExcelPO.getDay()) {
//                throw new ApplicationException("当前时间段不允许修改季度目标");
//            }

            // 查询季度目标(全品项
            EmployeeGoalPo quarterGoalPo = employeeGoalMapper.selectOne(new LambdaQueryWrapper<EmployeeGoalPo>().eq(EmployeeGoalPo::getPositionId, request.getPositionId()).eq(EmployeeGoalPo::getYear, request.getEffectiveDate().getYear()).eq(EmployeeGoalPo::getQuarter, QuarterCycle.getQuarter(request.getEffectiveDate())).eq(EmployeeGoalPo::getRange, 1).eq(EmployeeGoalPo::getIsDelete, 0));
            Integer oldStatus = null;
            if (Objects.isNull(quarterGoalPo)) {
                quarterGoalPo = saveQuarterGoalPo(request, employeeName, now, status);
                oldStatus = quarterGoalPo.getGoalStatus();
            }else {
                oldStatus = quarterGoalPo.getGoalStatus();
                // 则更新 只有驳回状态才更新
                if (DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_2.equals(quarterGoalPo.getGoalStatus())) {
                    quarterGoalPo.setGoalStatus(status);
                    updateQuarterGoalPo(request, quarterGoalPo, employeeName, now);
                }
            }
            // 新增写入季度 目标设置
            List<EmployeeGoalDetailPo> resList = processGoalDetails(quarterGoalPo.getId(), request.getQuarterList(), status, employeeName, now, true);
            if (!CollectionUtils.isEmpty(resList)) {
                for (EmployeeGoalDetailPo detailPo : resList) {
                    if (detailPo.getId() == null) {
                        employeeGoalDetailMapper.insert(detailPo);
                        EmployeeGoalLogPO logDetailPo = createLogDetailPo(detailPo, status, employeeName, parentList, now, isPass, true);
                        employeeGoalLogMapper.insert(logDetailPo);
                    } else {
                        if (DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_2.equals(oldStatus)) {
                            employeeGoalDetailMapper.updateById(detailPo);
                            EmployeeGoalLogPO logDetailPo = createLogDetailPo(detailPo, status, employeeName, parentList, now, isPass, true);
                            employeeGoalLogMapper.insert(logDetailPo);
                        }
                    }
                }
            }
        }

        // 月度目标(全品项
        EmployeeGoalPo employeeGoalPo = employeeGoalMapper.selectOne(new LambdaQueryWrapper<EmployeeGoalPo>().eq(EmployeeGoalPo::getPositionId, request.getPositionId()).eq(EmployeeGoalPo::getEffectiveDate, request.getEffectiveDate()).eq(EmployeeGoalPo::getRange, 0).eq(EmployeeGoalPo::getIsDelete, 0));
        if (Objects.isNull(employeeGoalPo) || (Objects.isNull(employeeGoalPo.getGoalStatus()) || DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_2.equals(employeeGoalPo.getGoalStatus()))) {

            if (Objects.nonNull(employeeGoalPo)) {
                employeeGoalPo.setSaleGoal(request.getSaleGoal());
                employeeGoalPo.setGoalStatus(status);
                employeeGoalPo.setUpdatedBy(request.getUpdatedBy());
                employeeGoalPo.setUpdatedName(employeeName);
                employeeGoalPo.setUpdatedTime(now);
                employeeGoalMapper.updateById(employeeGoalPo);
            } else {
                employeeGoalPo = new EmployeeGoalPo();
                employeeGoalPo.setPositionId(request.getPositionId());
                employeeGoalPo.setRange(0);
                employeeGoalPo.setEffectiveDate(request.getEffectiveDate());
                employeeGoalPo.setSaleGoal(request.getSaleGoal());
                employeeGoalPo.setGoalStatus(status);
                employeeGoalPo.setUpdatedBy(request.getUpdatedBy());
                employeeGoalPo.setUpdatedName(employeeName);
                employeeGoalPo.setUpdatedTime(now);
                employeeGoalPo.setCreatedBy(request.getUpdatedBy());
                employeeGoalPo.setCreatedName(employeeName);
                employeeGoalPo.setCreatedTime(now);
                employeeGoalMapper.insert(employeeGoalPo);
            }
            // 审核人（为空：总部营运岗审核）
            EmployeeGoalLogPO logPo = new EmployeeGoalLogPO();
            logPo.setGoalId(employeeGoalPo.getId());
            logPo.setEffectiveDate(request.getEffectiveDate());
            logPo.setModifyAttribute("本月全品项业绩目标");
            logPo.setModifyValue(String.valueOf(request.getSaleGoal()));
            logPo.setGoalStatus(status);
            logPo.setCreatedBy(request.getUpdatedBy());
            logPo.setCreatedName(employeeName);
            if (!CollectionUtils.isEmpty(parentList) && parentList.size() > 1) {
                logPo.setUpdatedBy(parentList.get(1).getEmployeeId());
                logPo.setUpdatedName(parentList.get(1).getEmployeeName());
            }
            logPo.setCreatedTime(now);
            if (isPass) {
                logPo.setUpdatedBy(request.getUpdatedBy());
                logPo.setUpdatedName(employeeName);
                logPo.setUpdatedTime(now);
            }
            employeeGoalLogMapper.insert(logPo);
        }
        // 新增写入月度 目标设置
        List<EmployeeGoalDetailPo> resMonthList = processGoalDetails(employeeGoalPo.getId(), request.getMonthList(), status, employeeName, now, false);
        if (!CollectionUtils.isEmpty(resMonthList)) {
            for (EmployeeGoalDetailPo detailPo : resMonthList) {
                if (detailPo.getId() == null) {
                    employeeGoalDetailMapper.insert(detailPo);
                } else {
                    employeeGoalDetailMapper.updateById(detailPo);
                }
                EmployeeGoalLogPO logDetailPo = createLogDetailPo(detailPo, status, employeeName, parentList, now, isPass, false);
                employeeGoalLogMapper.insert(logDetailPo);
            }
        }
    }

    /**
     * 判断给定日期是否在某个季度的第一个月
     *
     * @param date 给定的日期
     * @return 如果是季度的第一个月返回 true，否则返回 false
     */
    private static boolean isFirstMonthOfQuarter(LocalDate date) {
        int monthValue = date.getMonthValue();
        // 季度的第一个月分别是：1月、4月、7月、10月
        return monthValue == 1 || monthValue == 4 || monthValue == 7 || monthValue == 10;
    }

    private void updateQuarterGoalPo(EmployeeSetGoalRequest request, EmployeeGoalPo quarterGoalPo, String employeeName, LocalDateTime now) {
        quarterGoalPo.setUpdatedBy(request.getUpdatedBy());
        quarterGoalPo.setUpdatedName(employeeName);
        quarterGoalPo.setUpdatedTime(now);
        quarterGoalPo.setSaleGoal(request.getQuarterSaleGoal());
        employeeGoalMapper.updateById(quarterGoalPo);
        saveQuarterGoalLogPO(request, employeeName, now, quarterGoalPo);
    }

    private EmployeeGoalPo saveQuarterGoalPo(EmployeeSetGoalRequest request, String employeeName, LocalDateTime now, Integer status) {
        EmployeeGoalPo quarterGoalPo = new EmployeeGoalPo();
        quarterGoalPo.setSaleGoal(request.getQuarterSaleGoal());
        quarterGoalPo.setGoalStatus(status);
        quarterGoalPo.setPositionId(request.getPositionId());
        quarterGoalPo.setUpdatedBy(request.getUpdatedBy());
        quarterGoalPo.setUpdatedName(employeeName);
        quarterGoalPo.setUpdatedTime(now);
        quarterGoalPo.setCreatedBy(request.getUpdatedBy());
        quarterGoalPo.setCreatedName(employeeName);
        quarterGoalPo.setCreatedTime(now);
        quarterGoalPo.setYear(request.getEffectiveDate().getYear());
        quarterGoalPo.setQuarter(QuarterCycle.getQuarter(request.getEffectiveDate()));
        quarterGoalPo.setRange(1);
        employeeGoalMapper.insert(quarterGoalPo);
        saveQuarterGoalLogPO(request, employeeName, now, quarterGoalPo);
        return quarterGoalPo;
    }

    private void saveQuarterGoalLogPO(EmployeeSetGoalRequest request, String employeeName, LocalDateTime now, EmployeeGoalPo quarterGoalPo) {
        EmployeeGoalLogPO quarterLogPo = new EmployeeGoalLogPO();
        quarterLogPo.setGoalId(quarterGoalPo.getId());
        quarterLogPo.setModifyAttribute("季度全品项业绩目标");
        quarterLogPo.setModifyValue(String.valueOf(request.getQuarterSaleGoal()));
        quarterLogPo.setGoalStatus(DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_1);
        quarterLogPo.setEffectiveDate(request.getEffectiveDate());
        quarterLogPo.setCreatedBy(request.getUpdatedBy());
        quarterLogPo.setCreatedName(employeeName);
        quarterLogPo.setCreatedTime(now);
        quarterLogPo.setUpdatedBy(request.getUpdatedBy());
        quarterLogPo.setUpdatedName(employeeName);
        quarterLogPo.setUpdatedTime(now);
        employeeGoalLogMapper.insert(quarterLogPo);
    }

    private List<EmployeeGoalDetailPo> processGoalDetails(Integer goalId, List<EmployeeSetGoalDetailRequest> goalList, Integer status, String employeeName, LocalDateTime now, boolean isQuarter) {
        if (!CollectionUtils.isEmpty(goalList)) {
            List<EmployeeGoalDetailPo> res = new ArrayList<>();
            for (EmployeeSetGoalDetailRequest item : goalList) {
                EmployeeGoalDetailPo detailPo = null;
                if (item.getId() != null) {
                    detailPo = employeeGoalDetailMapper.selectById(item.getId());
                }
//                EmployeeGoalDetailPo detailPo = employeeGoalDetailMapper.selectOne(new LambdaQueryWrapper<EmployeeGoalDetailPo>().eq(EmployeeGoalDetailPo::getPositionId, item.getPositionId())
//                        .eq(isQuarter ? EmployeeGoalDetailPo::getYear : EmployeeGoalDetailPo::getEffectiveDate, isQuarter ? item.getEffectiveDate().getYear() : item.getEffectiveDate())
//                        .eq(isQuarter ? EmployeeGoalDetailPo::getQuarter : EmployeeGoalDetailPo::getRange, isQuarter ? QuarterCycle.getQuarter(item.getEffectiveDate()) : 0)
//                        .eq(EmployeeGoalDetailPo::getRange, isQuarter ? 1 : 0).eq(EmployeeGoalDetailPo::getIsDelete, 0));

                if (Objects.isNull(detailPo) || (Objects.isNull(detailPo.getGoalStatus()) || DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_2.equals(detailPo.getGoalStatus()))) {
                    if (Objects.nonNull(detailPo)) {
                        updateDetailPo(detailPo, item, status, employeeName, now);
                    } else {
                        detailPo = createDetailPo(goalId, item, status, employeeName, now, isQuarter);
                    }
                    res.add(detailPo);
                }
            }
            return res;
        }
        return Lists.newArrayList();
    }

    private void updateDetailPo(EmployeeGoalDetailPo detailPo, EmployeeSetGoalDetailRequest item, Integer status, String employeeName, LocalDateTime now) {
        detailPo.setSaleGoal(item.getSaleGoal());
        detailPo.setGoalStatus(status);
        detailPo.setUpdatedBy(item.getUpdatedBy());
        detailPo.setUpdatedName(employeeName);
        detailPo.setUpdatedTime(now);
    }

    private EmployeeGoalDetailPo createDetailPo(Integer goalId, EmployeeSetGoalDetailRequest item, Integer status, String employeeName, LocalDateTime now, boolean isQuarter) {
        EmployeeGoalDetailPo detailPo = BeanUtil.copyProperties(item, EmployeeGoalDetailPo.class);
        detailPo.setRange(isQuarter ? 1 : 0);
        detailPo.setYear(item.getEffectiveDate().getYear());
        if (isQuarter) {
            detailPo.setQuarter(QuarterCycle.getQuarter(item.getEffectiveDate()));
        } else {
            detailPo.setQuarter(item.getEffectiveDate().getMonthValue());
        }
        detailPo.setGoalStatus(status);
        detailPo.setUpdatedBy(item.getUpdatedBy());
        detailPo.setUpdatedName(employeeName);
        detailPo.setUpdatedTime(now);
        detailPo.setCreatedBy(item.getUpdatedBy());
        detailPo.setCreatedName(employeeName);
        detailPo.setCreatedTime(now);
        detailPo.setGoalId(goalId);
        return detailPo;
    }

    private EmployeeGoalLogPO createLogDetailPo(EmployeeGoalDetailPo detailPo, Integer status, String employeeName, List<SfaEmployeeInfoModel> parentList, LocalDateTime now, boolean isPass, boolean isQuarter) {
        EmployeeGoalLogPO logDetailPo = new EmployeeGoalLogPO();
        logDetailPo.setGoalId(detailPo.getId());
        logDetailPo.setEffectiveDate(detailPo.getEffectiveDate());
        logDetailPo.setModifyAttribute(detailPo.getProductName() + (isQuarter ? "季度业绩目标" : "月度业绩目标"));
        logDetailPo.setModifyValue(String.valueOf(detailPo.getSaleGoal()));
        logDetailPo.setGoalStatus(status);
        logDetailPo.setCreatedBy(detailPo.getUpdatedBy());
        logDetailPo.setCreatedName(employeeName);
        if (!CollectionUtils.isEmpty(parentList) && parentList.size() > 1) {
            logDetailPo.setUpdatedBy(parentList.get(1).getEmployeeId());
            logDetailPo.setUpdatedName(parentList.get(1).getEmployeeName());
        }
        logDetailPo.setCreatedTime(now);
        if (isPass) {
            logDetailPo.setUpdatedBy(detailPo.getUpdatedBy());
            logDetailPo.setUpdatedName(employeeName);
            logDetailPo.setUpdatedTime(now);
        }
        return logDetailPo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditGoal(EmployeeAuditGoalRequest request) {
        log.info("审核合伙人目标入参:{}", request);

        if (Objects.isNull(request.getId()) && CollectionUtils.isEmpty(request.getIds())) {
            throw new ApplicationException("id不能为空");
        }
        if (Objects.nonNull(request.getId())) {
            request.setIds(Arrays.asList(request.getId()));
        }

        String employeeName = employeeMapper.getEmployeeNameByEmployeeIdFor123(request.getUpdatedBy(), RequestUtils.getChannel());
        Integer goalStatus = request.getOperatorType() == 1 ? DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_1 : DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_2;
        LocalDateTime nowLocalDateTime = LocalDateTime.now();

        //月度 和 季度目标
        List<EmployeeGoalPo> employeeGoalPoList = employeeGoalMapper.selectList(new LambdaQueryWrapper<EmployeeGoalPo>().in(EmployeeGoalPo::getId, request.getIds()).eq(EmployeeGoalPo::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0).eq(EmployeeGoalPo::getRange, 0).eq(EmployeeGoalPo::getIsDelete, 0));
        if (CollectionUtils.isEmpty(employeeGoalPoList) || employeeGoalPoList.size() != request.getIds().size()) {
            throw new ApplicationException("待审核记录不存在");
        }
        List<String> positionIdList = employeeGoalPoList.stream().map(EmployeeGoalPo::getPositionId).distinct().collect(Collectors.toList());
        LocalDate effectiveDate=employeeGoalPoList.get(0).getEffectiveDate();
        List<EmployeeGoalPo> quarterGoalPoList = employeeGoalMapper.selectList(new LambdaQueryWrapper<EmployeeGoalPo>().in(EmployeeGoalPo::getPositionId, positionIdList).eq(EmployeeGoalPo::getYear, effectiveDate.getYear()).eq(EmployeeGoalPo::getQuarter, QuarterCycle.getQuarter(effectiveDate))
                .eq(EmployeeGoalPo::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0)
                .eq(EmployeeGoalPo::getRange, 1).eq(EmployeeGoalPo::getIsDelete, 0));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(quarterGoalPoList)) {
            employeeGoalPoList.addAll(quarterGoalPoList);
        }

        List<EmployeeGoalLogPO> employeeGoalLogPOList;

        List<String> employeeIdList = getList();
        String organizationType = RequestUtils.getLoginInfo().getOrganizationType();

        if (OrganizationTypeEnum.ZB.getOrganizationType().equals(organizationType) && employeeIdList.contains(request.getUpdatedBy())) {
            employeeGoalLogPOList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>().in(EmployeeGoalLogPO::getGoalId, request.getIds()).eq(EmployeeGoalLogPO::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0).isNull(EmployeeGoalLogPO::getUpdatedBy).eq(EmployeeGoalLogPO::getIsDelete, 0));
        } else {
            employeeGoalLogPOList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>().in(EmployeeGoalLogPO::getGoalId, request.getIds()).eq(EmployeeGoalLogPO::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0).eq(EmployeeGoalLogPO::getUpdatedBy, request.getUpdatedBy()).eq(EmployeeGoalLogPO::getIsDelete, 0));
        }
        if (CollectionUtils.isEmpty(employeeGoalLogPOList) || employeeGoalLogPOList.size() != request.getIds().size()) {
            throw new ApplicationException("待审核履历不存在");
        }

        employeeGoalPoList.stream().forEach(employeeGoalPo -> {
            employeeGoalPo.setGoalStatus(goalStatus);
            employeeGoalPo.setUpdatedBy(request.getUpdatedBy());
            employeeGoalPo.setUpdatedName(employeeName);
            employeeGoalPo.setUpdatedTime(nowLocalDateTime);
        });
        
        employeeGoalLogPOList.stream().forEach(employeeGoalLogPO -> {
            employeeGoalLogPO.setGoalStatus(goalStatus);
            employeeGoalLogPO.setReason(request.getReason());
            employeeGoalLogPO.setUpdatedBy(request.getUpdatedBy());
            employeeGoalLogPO.setUpdatedName(employeeName);
            employeeGoalLogPO.setUpdatedTime(nowLocalDateTime);
        });

        employeeGoalServiceImpl.updateBatchById(employeeGoalPoList);
        employeeGoalLogServiceImpl.updateBatchById(employeeGoalLogPOList);

        // 本次新增处理明细。
        List<EmployeeGoalDetailPo> allList = new ArrayList<>();
        // ID只能查询月度
        List<EmployeeGoalDetailPo> monthDetailPoList = employeeGoalDetailMapper.selectList(new LambdaQueryWrapper<EmployeeGoalDetailPo>().in(EmployeeGoalDetailPo::getGoalId, request.getIds()).eq(EmployeeGoalDetailPo::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0).eq(EmployeeGoalDetailPo::getIsDelete, 0));

        if (!CollectionUtils.isEmpty(monthDetailPoList)){
            allList.addAll(monthDetailPoList);
        }
        // 单独处理季度的
        List<EmployeeGoalDetailPo> detailPoList = employeeGoalDetailMapper.selectList(new LambdaQueryWrapper<EmployeeGoalDetailPo>().in(EmployeeGoalDetailPo::getPositionId,positionIdList)
                .eq(EmployeeGoalDetailPo::getYear, effectiveDate.getYear()).eq(EmployeeGoalDetailPo::getQuarter, QuarterCycle.getQuarter(effectiveDate))
                .eq(EmployeeGoalDetailPo::getRange, 1).eq(EmployeeGoalDetailPo::getGoalStatus,DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0)
                .eq(EmployeeGoalDetailPo::getIsDelete, 0));
        if (!CollectionUtils.isEmpty(detailPoList)){

            allList.addAll(detailPoList);
        }
        if (!CollectionUtils.isEmpty(allList)) {
            for (EmployeeGoalDetailPo detailPo : allList) {
                detailPo.setGoalStatus(goalStatus);
                detailPo.setUpdatedBy(request.getUpdatedBy());
                detailPo.setUpdatedName(employeeName);
                detailPo.setUpdatedTime(nowLocalDateTime);
            }
            List<List<EmployeeGoalDetailPo>> updateList = Lists.partition(allList, 1000);
            for (List<EmployeeGoalDetailPo> employeeGoalDetailPos : updateList) {
                employeeGoalDetailServiceImpl.updateBatchById(employeeGoalDetailPos);
            }
            List<Integer> logGoalIds = allList.stream().map(EmployeeGoalDetailPo::getId).collect(Collectors.toList());
            List<EmployeeGoalLogPO> detailLog;
            if (OrganizationTypeEnum.ZB.getOrganizationType().equals(organizationType) && employeeIdList.contains(request.getUpdatedBy())) {
                detailLog = getEmployeeGoalLogPOList(logGoalIds, request.getUpdatedBy(), true);
            } else {
                detailLog = getEmployeeGoalLogPOList(logGoalIds, request.getUpdatedBy(), false);
            }
            if (!CollectionUtils.isEmpty(detailLog)) {
                for (EmployeeGoalLogPO item : detailLog) {
                    item.setGoalStatus(goalStatus);
                    item.setReason(request.getReason());
                    item.setUpdatedBy(request.getUpdatedBy());
                    item.setUpdatedName(employeeName);
                    item.setUpdatedTime(nowLocalDateTime);
                }
                List<List<EmployeeGoalLogPO>> updateLogList = Lists.partition(detailLog, 1000);
                for (List<EmployeeGoalLogPO> logList : updateLogList) {
                    employeeGoalLogServiceImpl.updateBatchById(logList);
                }
            }
        }

        // 定时任务mainProductGoalPushTask推送旺铺已审核数据
    }

    private List<EmployeeGoalLogPO> getEmployeeGoalLogPOList(List<Integer> ids, String updatedBy, boolean isNullUpdatedBy) {
        LambdaQueryWrapper<EmployeeGoalLogPO> queryWrapper = new LambdaQueryWrapper<EmployeeGoalLogPO>().in(EmployeeGoalLogPO::getGoalId, ids).eq(EmployeeGoalLogPO::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0).eq(EmployeeGoalLogPO::getIsDelete, 0);

        if (isNullUpdatedBy) {
            queryWrapper.isNull(EmployeeGoalLogPO::getUpdatedBy);
        } else {
            queryWrapper.eq(EmployeeGoalLogPO::getUpdatedBy, updatedBy);
        }

        return employeeGoalLogMapper.selectList(queryWrapper);
    }


    /**
     * 合伙人目标分配历史
     */
    @Override
    public List<EmployeeGoalLogVO> listEmpGoalLog(Integer id) {
        List<EmployeeGoalLogVO> list = employeeGoalMapper.selectListEmpGoalLog(id);
        List<EmployeeGoalLogVO> resList = new ArrayList<>(list);
        // 新增查询明细日志-月度
        List<EmployeeGoalDetailPo> monthList = employeeGoalDetailMapper.selectList(new LambdaQueryWrapper<EmployeeGoalDetailPo>().eq(EmployeeGoalDetailPo::getGoalId, id).eq(EmployeeGoalDetailPo::getIsDelete, 0));
        if (!CollectionUtils.isEmpty(monthList)) {
            List<Integer> ids = monthList.stream().map(EmployeeGoalDetailPo::getId).collect(Collectors.toList());
            List<EmployeeGoalLogPO> dList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>().in(EmployeeGoalLogPO::getGoalId, ids).eq(EmployeeGoalLogPO::getIsDelete, 0));
            List<EmployeeGoalLogVO> logVOList = BeanUtil.copyToList(dList, EmployeeGoalLogVO.class);
            resList.addAll(logVOList);
            EmployeeGoalDetailPo detailPo = monthList.get(0);
            String positionId = detailPo.getPositionId();
            int quarter = QuarterCycle.getQuarter(detailPo.getEffectiveDate());
            int year = detailPo.getYear();

            List<EmployeeGoalDetailPo> detailPoList = employeeGoalDetailMapper.selectList(new LambdaQueryWrapper<EmployeeGoalDetailPo>().eq(EmployeeGoalDetailPo::getPositionId, positionId)
                    .eq(EmployeeGoalDetailPo::getYear, year).eq(EmployeeGoalDetailPo::getQuarter, quarter)
                    .eq(EmployeeGoalDetailPo::getRange, 1).eq(EmployeeGoalDetailPo::getIsDelete, 0));
            if (!CollectionUtils.isEmpty(detailPoList)) {
                List<Integer> quarterIds = detailPoList.stream().map(EmployeeGoalDetailPo::getId).collect(Collectors.toList());
                List<EmployeeGoalLogPO> quarterList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>().in(EmployeeGoalLogPO::getGoalId, quarterIds).eq(EmployeeGoalLogPO::getIsDelete, 0));
                List<EmployeeGoalLogVO> quarterLogVOList = BeanUtil.copyToList(quarterList, EmployeeGoalLogVO.class);
                resList.addAll(quarterLogVOList);
            }
        }
        return resList;
    }

    /**
     * 合伙人目标导入模板
     */
    @Override
    public void template(String organizationId, HttpServletResponse response) {
        List<EmployeeGoalDTO> list = mapper.selectTemplate(organizationId);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), EmployeeGoalDTO.class, list);
        EasyPoiUtil.downLoadExcel("合伙人目标导入模板.xls", response, workbook);
    }

    /**
     * 导入合伙人目标
     */
    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public synchronized Response<ImportMsgVO> importEmployeeGoal(MultipartFile file, LocalDate parse, String updatedBy) {
        log.info("导入合伙人目标入参,effectiveDate:{},updatedBy:{}", parse, updatedBy);
        ImportMsgVO importMsgVO = new ImportMsgVO();
        List<String> res = Lists.newArrayList();
        String employeeName = employeeMapper.getEmployeeNameByEmployeeIdFor123(updatedBy, RequestUtils.getChannel());
        ImportParams params = new ImportParams();
        try {
            List<EmployeeGoalDTO> list = new ArrayList<>();
            ExcelImportResult<EmployeeGoalDTO> result = ExcelImportUtil.importExcelMore(file.getInputStream(), EmployeeGoalDTO.class, params);
            if (CommonUtil.ListUtils.isNotEmpty(result.getFailList())) {
                for (EmployeeGoalDTO c : result.getFailList()) {
                    res.add("导入失败,错误信息:第" + (c.getRowNum() + 1) + "行数据," + c.getErrorMsg());
                }
            } else {
                list = result.getList();
            }
            for (EmployeeGoalDTO e : list) {
                log.info("employeGoalItem:{}", e);
                SfaEmployeeInfoModel employee = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMobile, e.getMobile()));
                if (Objects.isNull(employee) || StringUtils.isBlank(employee.getPositionId())) {
                    res.add("导入失败,错误信息:第" + (e.getRowNum() + 1) + "行数据,合伙人手机号有误！");
                    continue;
                }
                EmployeeGoalPo goalPo = employeeGoalMapper.selectOne(new QueryWrapper<EmployeeGoalPo>().eq("position_id", employee.getPositionId()).eq("date_format(effective_date,'%Y-%m')", parse.format(DateTimeFormatter.ofPattern("yyyy-MM"))).eq("is_delete", 0));
                EmployeeGoalPo po = new EmployeeGoalPo();
                po.setSaleGoal(e.getSaleGoal());
                po.setUpdatedBy(updatedBy);
                po.setUpdatedName(employeeName);
                po.setPositionId(employee.getPositionId());
                po.setUpdatedTime(LocalDateTime.now());
                if (Objects.nonNull(goalPo)) {
                    po.setId(goalPo.getId());
                    employeeGoalMapper.updateById(po);
                } else {
                    po.setCreatedBy(updatedBy);
                    po.setCreatedName(employeeName);
                    po.setCreatedTime(LocalDateTime.now());
                    po.setEffectiveDate(parse);
                    employeeGoalMapper.insert(po);
                }
                EmployeeGoalLogPO logPo = new EmployeeGoalLogPO();
                logPo.setGoalId(po.getId());
                logPo.setEffectiveDate(parse);
                logPo.setModifyAttribute("全品项业绩目标");
                if (Objects.isNull(e.getSaleGoal())) {
                    logPo.setModifyValue("0");
                } else {
                    logPo.setModifyValue(String.valueOf(e.getSaleGoal()));
                }
                logPo.setCreatedBy(updatedBy);
                logPo.setCreatedName(employeeName);
                logPo.setUpdatedBy(updatedBy);
                logPo.setUpdatedName(employeeName);
                logPo.setCreatedTime(LocalDateTime.now());
                logPo.setUpdatedTime(LocalDateTime.now());
                employeeGoalLogMapper.insert(logPo);
            }
        } catch (Exception e) {
            throw new ApplicationException("导入失败，请检查模版！");
        }
        if (CommonUtil.ListUtils.isNotEmpty(res)) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            importMsgVO.setImportId(null);
            importMsgVO.setMsg(res);
            return Response.body(StatusCode.FAILED).build(importMsgVO);
        } else {
            importMsgVO.setImportId(1);
            importMsgVO.setMsg(null);
            return Response.success(importMsgVO);
        }
    }

    /**
     * 合伙人目标列表导出
     */
    @Override
    public void exportEmployeeGoal(EmployeeGoalQueryRequest request, HttpServletResponse response) {
        request.setBtnFlg(false);
        request.setRows(10000);
        List<EmpGoalVO> list = pageEmpGoalNew(request).getRecords();
//        LoginModel loginInfo = RequestUtils.getLoginInfo();
//        request.setBusinessGroup(loginInfo.getBusinessGroup());
//        if (CommonUtil.StringUtils.isBlank(request.getOrganizationType())) {
//            request.setOrganizationType(loginInfo.getOrganizationType());
//        }
//        if (CommonUtil.StringUtils.isBlank(request.getOrganizationId())) {
//            List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(), loginInfo);
//            if (RequestUtils.getLoginInfo().getOrganizationType().equals("zb")) {
//                request.setOrganizationId(employeeOrganizationId.get(0));
//            } else {
//                request.setOrganizationIds(employeeOrganizationId);
//            }
//        }
//        List<EmpGoalVO> list = realtimeDataMapper.listEmpGoal(request);
//        EmpGoalVO hj = realtimeDataMapper.selectSumEmpGoal(request,new ArrayList<>(),new ArrayList<>());
//        list.add(hj);
        HashMap<Integer, String> titleMap=mainProductService.getTargetTitle(request.getYearMonth());
        int i=1;

        List<ExcelExportEntity> exportEntities = new ArrayList<>();
        Field[] fields = PoiPublicUtil.getClassFields(EmpGoalVO.class);
        for (Field field : fields) {
            if (field.getAnnotation(Excel.class) != null) {
                Excel excel = field.getAnnotation(Excel.class);
                // 是否主推list
                if (!(field.getType() == java.util.List.class)) {
                    ExcelExportEntity excelEntity = new ExcelExportEntity(excel.name(), field.getName());
                    excelEntity.setOrderNum(Integer.parseInt(excel.orderNum()));
                    if (field.getType() == java.util.Date.class) {
                        excelEntity.setFormat("yyyy-MM-dd");
                    }
                    exportEntities.add(excelEntity);
                    i=Integer.parseInt(excel.orderNum());
                }
            }
        }
        if (MapUtils.isNotEmpty(titleMap)){
            for (Integer key : titleMap.keySet()) {
                i++;
                String v=titleMap.get(key);
                ExcelExportEntity excelEntity = new ExcelExportEntity(v, v);
                excelEntity.setOrderNum(i);
                exportEntities.add(excelEntity);
            }
        }
//        List<Map<String, Object>> dataMap = new ArrayList<Map<String, Object>>();
//        list.forEach(o -> {
//            Map<String, Object> valMap = com.wantwant.sfa.backend.util.BeanUtils.objToMap(o);
//            dataMap.add(valMap);
//        });

        List<Map<String, Object>> voMapList = list.stream().map(vo -> {
            if (CollectionUtils.isEmpty(vo.getMainProductList())) {
                return BeanUtils.beanToMap(vo);
            }
            Map<String, Object> voMap = BeanUtils.beanToMap(vo);
            Map<String, BigDecimal> collect = vo.getMainProductList().stream().collect(Collectors.toMap(EmployeeGoalDetailVO::getTitle, EmployeeGoalDetailVO::getSaleGoal, (k1, k2) -> k1));
            voMap.putAll(collect);
            return voMap;
        }).collect(Collectors.toList());
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), exportEntities, voMapList);
        EasyPoiUtil.downLoadExcel("合伙人目标设置导出.xls", response, workbook);
    }

    /**
     * 查询下月已完成合伙人目标设置的分公司
     *
     * @param
     * @return: java.util.List<java.lang.String>
     * @date: 3/8/22 11:23 AM
     */
    @Override
    public List<String> getCompletedCompany() {
        return employeeGoalMapper.getCompletedCompany();
    }

    @Override
    public List<String> getCompletedEmployee() {
        return employeeGoalMapper.getCompletedEmployee();
    }

    @Override
    public List<String> getCompletedEmployeeOrganization(String examineQuarter) {
        return employeeGoalMapper.getCompletedEmployeeOrganization(examineQuarter);
    }


    /**
     * 查询造旺所有分公司负责人(分公司没有取大区)
     *
     * @param
     * @return: java.util.List<com.wantwant.sfa.backend.notify.dto.CompanyManagerDTO>
     * @date: 3/8/22 2:39 PM
     */
    @Override
    public List<CompanyManagerDTO> getAllCompany() {
        return employeeGoalMapper.getAllCompany();
    }

    @Override
    public RrecruitmentManagementDataVo queryRrecruitmentManagementData(TradeGoodReq request) {
        log.info("根据组织ID获取招聘与管理与商品线占比分析入参request:{}", request);
        if (null == request.getOrganizationId() || request.getYearMonth() == "") {
            throw new ApplicationException("组织id不能为空 ");
        }
        if (null == request.getYearMonth() || request.getYearMonth() == "") {
            throw new ApplicationException("月份不能为空 ");
        }
        RrecruitmentManagementDataVo rrecruitmentManagementData = mapper.getRrecruitmentManagementData(request);
        if (null == rrecruitmentManagementData) {
            rrecruitmentManagementData = new RrecruitmentManagementDataVo();
        }
        if (request.getOrganizationId().contains("ZB_Z")) {
            rrecruitmentManagementData = publicZBInformation(request, rrecruitmentManagementData);
        }

        if (request.getOrganizationType() == 4) {
            CeoBusinessOrganizationPositionRelation organization = relationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", request.getOrganizationId()));
            if (Objects.nonNull(organization)) {
                SfaEmployeeInfoModel employeeInfo = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("position_id", organization.getPositionId()).eq("employee_status", 2));
                if (Objects.nonNull(employeeInfo)) {
                    if (employeeInfo.getType() == 1) {
                        rrecruitmentManagementData.setOrganizationName("业务合伙人");
                    } else {
                        rrecruitmentManagementData.setOrganizationName("企业合伙人");
                    }
                }
            }
        }
        return rrecruitmentManagementData;
    }

    @Override
    public com.wantwant.commons.pagination.Page<RecruitmentDate> queryRrecruitmentData(TradeGoodReq request) {
        log.info("start RealtimeDataServiceImpl queryRrecruitmentData request:{}", request);
        // Page<RecruitmentDate> result = new Page<>();
        com.wantwant.commons.pagination.Page<RecruitmentDate> result = new com.wantwant.commons.pagination.Page<>();
        if (request.getOrganizationType() == 1) {
            List<RecruitmentDate> recruitmentDate = mapper.getRecruitmentDateZB(request);
            Integer recruitmentCount = mapper.getRecruitmentDateZBCount(request);
            result.setList(recruitmentDate);
            result.setTotalItem(recruitmentCount);
        } else {
            List<RecruitmentDate> recruitmentDate = mapper.getRecruitmentDate(request);
            Integer count = mapper.getRecruitmentDateCount(request);
            result.setList(recruitmentDate);
            result.setTotalItem(count);
        }

        return result;
    }


    @Override
    public List<TrendAnalysisDataVo> queryTrendAnalysisData(TradeGoodReq request) {
        log.info("获取趋势分析数据入参request:{}", request);
        if (null == request.getOrganizationId() || request.getYearMonth() == "") {
            throw new ApplicationException("组织id不能为空 ");
        }
        if (null == request.getYearMonth() || request.getYearMonth() == "") {
            throw new ApplicationException("月份不能为空 ");
        }
        String month = request.getYearMonth().substring(0, 4) + "01";
        request.setYearMonth(request.getYearMonth().replace("-", ""));
        List<TrendAnalysisDataVo> trendAnalysisData = mapper.getTrendAnalysisData(request, month);
        return trendAnalysisData;
    }


    @Override
    public IPage<RealTimeRankingListDetailVo> queryRankingListDetail(RealTimeRankingListDetailReq request) {

        IPage<RealTimeRankingListDetailVo> page = new Page<>(request.getPage(), request.getRows());

        Integer businessGroup = RequestUtils.getBusinessGroup();
        //获取财年和季度最新的一个月：dim_td_date表
        String theDate = realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth());
        String organizationId;
        String organizationType;
        if (request.getMemberKey() != null) {
            //合伙人每个人一个岗位 不存在一个岗位多个人
            organizationType = RankingListPositionEnums.BRANCH.getOrganizationType();
            //通过memberKey查询组织id
            organizationId = organizationMapper.getOrgCodeByMemberKey(businessGroup, request.getMemberKey(), theDate);
        } else {
            organizationId = request.getOrganizationId();
            organizationType = organizationMapper.getOrganizationType(organizationId);
        }

        if (StringUtils.isBlank(organizationType)) {
            log.warn("排行榜请求参数[{}]查询不到organizationType", request);
            return page;
        }
        String queryType = request.getQueryType();
        Integer positionTypeId = RankingListPositionEnums.getPositionTypeId(queryType);
        List<String> sameFatherAndSameLevels;
        List<Long> partnerSameFatherAndSameLevels = null;
        //总部-->直接根据产品组businessGroup和岗位queryType筛选所有人
        if (RankingListPositionEnums.ZB.getOrganizationType().equals(organizationType)) {
            sameFatherAndSameLevels = organizationMapper.getOrganizationIdByOrganizationTypeAndGroup(businessGroup, queryType);
        } else if (organizationType.equals(queryType)) {
            //查询自己同岗位的
            //查询所有父级下所有同级 organizationIds 总部无父级数据 战区无父级数据
            if (RankingListPositionEnums.AREA.getOrganizationType().equals(organizationType)) {
                sameFatherAndSameLevels = organizationMapper.getOrganizationIdByOrganizationTypeAndGroup(businessGroup, organizationType);
            } else {
                sameFatherAndSameLevels = organizationMapper.getSameFatherAndSameLevel(organizationId);
            }
        } else {
            //查询下级
            //查询所有下级
            List<OrganizationAllChildInfoVo> allChildren = organizationMapper.getAllChildrenTimeZones(organizationId, theDate);
            if (CollectionUtils.isEmpty(allChildren)) {
                log.error("排行榜请求参数[{}]查询不到下级数据", request);
                return page;
            }
            Map<String, List<OrganizationAllChildInfoVo>> allChildrenMap = allChildren.stream().collect(Collectors.toMap(children -> String.valueOf(children.getLevel()), children -> {
                List<OrganizationAllChildInfoVo> list = new ArrayList<>();
                list.add(children);
                return list;
            }, (v1, v2) -> {
                v1.addAll(v2);
                return v1;
            }));
            //判断是那个下级
            int i = RankingListPositionEnums.compareAndGetInterval(request.getQueryType(), organizationType);
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos = allChildrenMap.get(String.valueOf(i));
            if (CollectionUtils.isEmpty(organizationAllChildInfoVos)) {
                log.error("排行榜请求参数[{}]查询不到下级数据", request);
                return page;
            }
            sameFatherAndSameLevels = organizationAllChildInfoVos.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
        }

        //分页查询数据
        List<RealTimeRankingListDetailVo> realTimeRankingListDetailVos = null;
        if (RankingListPositionEnums.BRANCH.getOrganizationType().equals(queryType)) {
            //全组合伙人无数据
            if (99 == businessGroup) {
                log.warn("排行榜请求参数[{}]全组合伙人无数据", request);
                return page;
            }
            //sameFatherAndSameLevels和queryType查询数据
            //合伙人可能有空的数据
            sameFatherAndSameLevels = sameFatherAndSameLevels.stream().filter(p -> StringUtils.isNotEmpty(p)).collect(Collectors.toList());
            partnerSameFatherAndSameLevels = organizationMapper.getMemberKeyByOrgCodes(businessGroup, sameFatherAndSameLevels, theDate);
            realTimeRankingListDetailVos = realtimeMapper.queryRankingListPartnerDetail(page, request.getDateTypeId(), request.getYearMonth(), businessGroup, theDate, partnerSameFatherAndSameLevels, request.getOrderField(), request.getOrderType());

        } else {
            realTimeRankingListDetailVos = realtimeMapper.queryRankingListDetail(page, request.getQueryDataType(), request.getDateTypeId(), request.getYearMonth(), businessGroup, theDate, positionTypeId, sameFatherAndSameLevels, request.getOrderField(), request.getOrderType());

        }
        if (CollectionUtils.isEmpty(realTimeRankingListDetailVos)) {
            log.warn("排行榜请求参数[{}]无数据", request);
            return page;
        }
        page.setRecords(realTimeRankingListDetailVos);
        //全国排名：岗位类型+产品组--->直接查询实时数据
        //个人排名：岗位类型+产品组+同组织 下所有人
        List<RealTimeRankingInfoPO> nationalRankingInfos;
        List<RealTimeRankingInfoPO> regionRankingInfos;
        //合伙人表只有合伙人信息 且全组不需要处理合伙人排名
        if (RankingListPositionEnums.BRANCH.getOrganizationType().equals(request.getQueryType())) {
            //合伙人使用memberKey
            nationalRankingInfos = realtimeMapper.getPartnerNationalRanking(request.getDateTypeId(), request.getYearMonth(), businessGroup, request.getOrderField(), request.getOrderType());

            regionRankingInfos = realtimeMapper.getPartnerRegionRanking(request.getDateTypeId(), request.getYearMonth(), partnerSameFatherAndSameLevels, businessGroup, request.getOrderField(), request.getOrderType());

            if (!CollectionUtils.isEmpty(nationalRankingInfos)) {
                List<Long> orderList = nationalRankingInfos.stream().map(RealTimeRankingInfoPO::getMemberKey).collect(Collectors.toList());
                realTimeRankingListDetailVos.forEach(p -> {
                    p.setPerformanceNationalRanking(orderList.indexOf(p.getMemberKey()) + 1);
                });
            }

            if (!CollectionUtils.isEmpty(regionRankingInfos)) {
                List<Long> orderList = regionRankingInfos.stream().map(RealTimeRankingInfoPO::getMemberKey).collect(Collectors.toList());
                realTimeRankingListDetailVos.forEach(p -> {
                    p.setPerformanceRegionRanking(orderList.indexOf(p.getMemberKey()) + 1);
                });
            }

        } else {
            //其他 使用organizationId
            nationalRankingInfos = realtimeMapper.getNationalRanking(request.getDateTypeId(), request.getQueryDataType(), request.getYearMonth(), businessGroup, positionTypeId, request.getOrderField(), request.getOrderType());

            regionRankingInfos = realtimeMapper.getRegionRanking(request.getDateTypeId(), request.getQueryDataType(), request.getYearMonth(), sameFatherAndSameLevels, businessGroup, positionTypeId, request.getOrderField(), request.getOrderType());

            if (!CollectionUtils.isEmpty(nationalRankingInfos)) {
                List<String> orderList = nationalRankingInfos.stream().map(RealTimeRankingInfoPO::getOrganizationId).collect(Collectors.toList());
                realTimeRankingListDetailVos.forEach(p -> {
                    p.setPerformanceNationalRanking(orderList.indexOf(p.getOrganizationId()) + 1);
                });
            }

            if (!CollectionUtils.isEmpty(regionRankingInfos)) {
                List<String> orderList = regionRankingInfos.stream().map(RealTimeRankingInfoPO::getOrganizationId).collect(Collectors.toList());
                realTimeRankingListDetailVos.forEach(p -> {
                    p.setPerformanceRegionRanking(orderList.indexOf(p.getOrganizationId()) + 1);
                });
            }
        }

        page.setRecords(realTimeRankingListDetailVos);
        return page;
    }


    /**
     * @param request
     * @return 人员信息取数逻辑：
     * 合伙人使用dim_emp_pos_role_org_mon表，新表不会放合伙人
     * 管理人员数据用新表ads_member_personal_month，月度数据用月份关联去取;
     * 季度/财年数据放季度/财年最新一个月的数据，比如现在看23财年，就放2024-03的数据，看24财年，就放2024-05的数据
     */
    @Override
    public RealTimeRankingListVo queryRankingList(RealTimeRankingListReq request) {
        //判断organizationId属于哪一层级
        RealTimeRankingListVo realTimeRankingListVo = new RealTimeRankingListVo();
        //设置默认值 -->前端好处理
        realTimeRankingListVo.initEmptyProperty();

        Integer businessGroup = RequestUtils.getBusinessGroup();
        //获取财年和季度最新的一个月：dim_td_date表
        String theDate = realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth());

        String organizationId;
        String organizationType;
        //如果合伙人不为空
        if (request.getMemberKey() != null) {
            //合伙人每个人一个岗位 不存在一个岗位多个人
            organizationType = RankingListPositionEnums.BRANCH.getOrganizationType();
            //通过memberKey查询组织id
            organizationId = organizationMapper.getOrgCodeByMemberKey(businessGroup, request.getMemberKey(), theDate);
            if (StringUtils.isBlank(organizationId)) {
                log.error("排行榜请求参数[{}]查询不到organizationId", request);
                return realTimeRankingListVo;
            }
        } else {
            organizationId = request.getOrganizationId();
            organizationType = organizationMapper.getOrganizationType(organizationId);
        }
        if (StringUtils.isBlank(organizationType)) {
            log.warn("排行榜请求参数[{}]查询不到organizationType", request);
            return realTimeRankingListVo;
        }


        List<String> zbOrganizationIds = new ArrayList<>();
        List<String> areaOrganizationIds = new ArrayList<>();
        List<String> virtualAreaOrganizationIds = new ArrayList<>();
        List<String> provinceOrganizationIds = new ArrayList<>();
        List<String> companyOrganizationIds = new ArrayList<>();
        List<String> departmentOrganizationIds = new ArrayList<>();
        List<Long> branchOrganizationIds = new ArrayList<>();


        //查询所有下级
        List<OrganizationAllChildInfoVo> allChildren;
        if (RankingListPositionEnums.ZB.getOrganizationType().equals(organizationType)) {
            //总部默认已实现
            allChildren = new ArrayList<>();
        } else {
            allChildren = organizationMapper.getAllChildrenTimeZones(organizationId, theDate);

            if (CollectionUtils.isEmpty(allChildren)) {
                log.error("排行榜请求参数[{}]查询不到下级数据", request);
                return realTimeRankingListVo;
            }
        }

        Map<String, List<OrganizationAllChildInfoVo>> allChildrenMap = allChildren.stream().collect(Collectors.toMap(children -> String.valueOf(children.getLevel()), children -> {
            List<OrganizationAllChildInfoVo> list = new ArrayList<>();
            list.add(children);
            return list;
        }, (v1, v2) -> {
            v1.addAll(v2);
            return v1;
        }));
        //查询所有父级下所有同级 organizationIds 总部无父级数据 战区无父级数据
        List<String> sameFatherAndSameLevels = organizationMapper.getSameFatherAndSameLevel(organizationId);
        //以总部视角查看都有全组
        if (RankingListPositionEnums.AREA.getOrganizationType().equals(organizationType)) {
            //查询所有战区-->根据area+businessgroup
//            areaOrganizationIds.addAll(sameFatherAndSameLevels);
            areaOrganizationIds = organizationMapper.getOrganizationIdByOrganizationTypeAndGroup(businessGroup, organizationType);
            //大区
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos1)) {
                virtualAreaOrganizationIds = organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
            }
            //省区
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos2 = allChildrenMap.get("2");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos2)) {
                provinceOrganizationIds = organizationAllChildInfoVos2.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
            }
            //分公司
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos3 = allChildrenMap.get("3");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos3)) {
                companyOrganizationIds = organizationAllChildInfoVos3.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
            }
            //区域经理
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos4 = allChildrenMap.get("4");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos4)) {
                departmentOrganizationIds = organizationAllChildInfoVos4.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
            }
            //合伙人
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos5 = allChildrenMap.get("5");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos5)) {
                //合伙人使用memberKey
                branchOrganizationIds = organizationAllChildInfoVos5.stream().filter(p -> Objects.nonNull(p.getMemberKey())).map(OrganizationAllChildInfoVo::getMemberKey).collect(Collectors.toList());
            }

        }

        //大区总监
        if (RankingListPositionEnums.V_AREA.getOrganizationType().equals(organizationType)) {
            //查询父战区下所有的虚拟大区-->varea+businessgroup
            virtualAreaOrganizationIds.addAll(sameFatherAndSameLevels);
            //省区
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos1)) {
                provinceOrganizationIds = organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
            }
            //分公司
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos2 = allChildrenMap.get("2");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos2)) {
                companyOrganizationIds = organizationAllChildInfoVos2.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
            }
            //区域经理
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos3 = allChildrenMap.get("3");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos3)) {
                departmentOrganizationIds = organizationAllChildInfoVos3.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
            }
            //合伙人
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos4 = allChildrenMap.get("4");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos4)) {
                //合伙人使用memberKey
                branchOrganizationIds = organizationAllChildInfoVos4.stream().filter(p -> Objects.nonNull(p.getMemberKey())).map(OrganizationAllChildInfoVo::getMemberKey).collect(Collectors.toList());
            }

        }
        if (RankingListPositionEnums.PROVINCE.getOrganizationType().equals(organizationType)) {
            //查询父虚拟大区下的所有省区-->province+businessgroup
            provinceOrganizationIds.addAll(sameFatherAndSameLevels);
            //分公司
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos1)) {
                companyOrganizationIds = organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
            }
            //区域经理
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos2 = allChildrenMap.get("2");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos2)) {
                departmentOrganizationIds = organizationAllChildInfoVos2.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
            }
            //合伙人
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos3 = allChildrenMap.get("3");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos3)) {
                //合伙人使用memberKey
                branchOrganizationIds = organizationAllChildInfoVos3.stream().filter(p -> Objects.nonNull(p.getMemberKey())).map(OrganizationAllChildInfoVo::getMemberKey).collect(Collectors.toList());
            }
        }
        if (RankingListPositionEnums.COMPANY.getOrganizationType().equals(organizationType)) {
            //查询父省区下的所有分公司-->company+businessgroup
            companyOrganizationIds.addAll(sameFatherAndSameLevels);
            //区域经理
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos1)) {
                departmentOrganizationIds = organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList());
            }
            //合伙人
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos2 = allChildrenMap.get("2");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos2)) {
                //合伙人使用memberKey
                branchOrganizationIds = organizationAllChildInfoVos2.stream().filter(p -> Objects.nonNull(p.getMemberKey())).map(OrganizationAllChildInfoVo::getMemberKey).collect(Collectors.toList());
            }

        }
        if (RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(organizationType)) {
            //查询父分公司下的区域经理-->department+businessgroup
            departmentOrganizationIds.addAll(sameFatherAndSameLevels);
            //查询下面所有合伙人
            List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
            if (!CollectionUtils.isEmpty(organizationAllChildInfoVos1)) {
                //合伙人使用memberKey
                branchOrganizationIds = organizationAllChildInfoVos1.stream().filter(p -> Objects.nonNull(p.getMemberKey())).map(OrganizationAllChildInfoVo::getMemberKey).collect(Collectors.toList());
            }
        }
        //合伙人没有全组的概念
        if (RankingListPositionEnums.BRANCH.getOrganizationType().equals(organizationType)) {

            //根据合伙人组织id查询memberKey
            branchOrganizationIds = organizationMapper.getMemberKeyByOrgCodes(businessGroup, sameFatherAndSameLevels, theDate);

        }

        //查询
        List<RealTimeRankingVo> rankingList;
        if (99 == businessGroup) {
            rankingList = realtimeMapper.getRankingListGroup99(organizationType, request.getDateTypeId(), request.getYearMonth(), businessGroup, request.getQueryDataType(), theDate, areaOrganizationIds, virtualAreaOrganizationIds, provinceOrganizationIds, companyOrganizationIds, departmentOrganizationIds);
        } else {
            //不是总部的话 branchOrganizationIds 补0避免db判断
            if (branchOrganizationIds.isEmpty() && !RankingListPositionEnums.ZB.getOrganizationType().equals(organizationType)) {
                branchOrganizationIds.add(0L);
            }

            rankingList = realtimeMapper.getRankingList(organizationType, request.getDateTypeId(), request.getYearMonth(), businessGroup, request.getQueryDataType(), theDate, areaOrganizationIds, virtualAreaOrganizationIds, provinceOrganizationIds, companyOrganizationIds, departmentOrganizationIds, branchOrganizationIds);
        }

        if (!CollectionUtils.isEmpty(rankingList)) {
            Map<String, List<RealTimeRankingVo>> rankMap = rankingList.stream().collect(groupingBy(RealTimeRankingVo::getOrganizationType));
            Arrays.stream(RankingListPositionEnums.values()).forEach(enums -> {
                List<RealTimeRankingVo> realTimeRankingVos = rankMap.get(enums.getOrganizationType());
                if (CollectionUtils.isEmpty(realTimeRankingVos)) {
                    return;
                }
                if (request.getQueryDataType() == 1) {
                    //根据OrganizationType 判断
                    if (enums.equals(RankingListPositionEnums.BRANCH)) {
                        //按照管理岗人均业绩排序
//                        realTimeRankingVos = realTimeRankingVos.stream().sorted(Comparator.comparing(RealTimeRankingVo::getManagementPerPerformance).reversed().thenComparing(RealTimeRankingVo::getMemberKey)).collect(Collectors.toList());
//                        realTimeRankingListVo.setBranchVo(realTimeRankingVos);
                    } else {
                        //按照管理岗人均业绩排序
                        realTimeRankingVos = realTimeRankingVos.stream().sorted(Comparator.comparing(RealTimeRankingVo::getManagementPerPerformance).reversed().thenComparing(RealTimeRankingVo::getOrganizationId)).collect(Collectors.toList());
                        if (enums.equals(RankingListPositionEnums.DEPARTMENT)) {
                            realTimeRankingListVo.setDepartmentVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.COMPANY)) {
                            realTimeRankingListVo.setCompanyVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.PROVINCE)) {
                            realTimeRankingListVo.setProvinceVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.V_AREA)) {
                            realTimeRankingListVo.setVirtualAreaVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.AREA)) {
                            realTimeRankingListVo.setAreaVo(realTimeRankingVos);
                        }
                    }
                } else if (request.getQueryDataType() == 2) {
                    //根据OrganizationType 判断
                    if (enums.equals(RankingListPositionEnums.BRANCH)) {
                        //按照客单价排序
//                        realTimeRankingVos = realTimeRankingVos.stream().sorted(Comparator.comparing(RealTimeRankingVo::getManagementPositionCustomerNum).reversed().thenComparing(RealTimeRankingVo::getMemberKey)).collect(Collectors.toList());
//                        realTimeRankingListVo.setBranchVo(realTimeRankingVos);
                    } else {
                        //按照客单价排序
                        realTimeRankingVos = realTimeRankingVos.stream().sorted(Comparator.comparing(RealTimeRankingVo::getPerCustomer).reversed().thenComparing(RealTimeRankingVo::getOrganizationId)).collect(Collectors.toList());
                        if (enums.equals(RankingListPositionEnums.DEPARTMENT)) {
                            realTimeRankingListVo.setDepartmentVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.COMPANY)) {
                            realTimeRankingListVo.setCompanyVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.PROVINCE)) {
                            realTimeRankingListVo.setProvinceVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.V_AREA)) {
                            realTimeRankingListVo.setVirtualAreaVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.AREA)) {
                            realTimeRankingListVo.setAreaVo(realTimeRankingVos);
                        }
                    }
                } else {
                    //queryType = 0
                    //根据OrganizationType 判断
                    if (enums.equals(RankingListPositionEnums.BRANCH)) {
                        //按照业绩排序
                        realTimeRankingVos = realTimeRankingVos.stream().sorted(Comparator.comparing(RealTimeRankingVo::getZwProductPerformance).reversed().thenComparing(RealTimeRankingVo::getMemberKey)).collect(Collectors.toList());
                        realTimeRankingListVo.setBranchVo(realTimeRankingVos);
                    } else {
                        //按照业绩排序
                        realTimeRankingVos = realTimeRankingVos.stream().sorted(Comparator.comparing(RealTimeRankingVo::getZwProductPerformance).reversed().thenComparing(RealTimeRankingVo::getOrganizationId)).collect(Collectors.toList());
                        if (enums.equals(RankingListPositionEnums.DEPARTMENT)) {
                            realTimeRankingListVo.setDepartmentVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.COMPANY)) {
                            realTimeRankingListVo.setCompanyVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.PROVINCE)) {
                            realTimeRankingListVo.setProvinceVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.V_AREA)) {
                            realTimeRankingListVo.setVirtualAreaVo(realTimeRankingVos);
                        } else if (enums.equals(RankingListPositionEnums.AREA)) {
                            realTimeRankingListVo.setAreaVo(realTimeRankingVos);
                        }
                    }
                }

            });

        }
        //获取个人信息
        RealTimeRankingPersonalInfoVo performanceInfo = new RealTimeRankingPersonalInfoVo();

        //总部人员查询-->使用原始组织类型
        if (RankingListPositionEnums.ZB.getOrganizationType().equals(organizationType)) {
            List<CeoBusinessOrganizationEntity> partJobOrganization = organizationMapper.getPartJobOrganization(request.getEmployeeId(), businessGroup, RequestUtils.getChannel());
            if (!CollectionUtils.isEmpty(partJobOrganization)) {
                if (request.getOrganizationId().contains("ZB_Z")) {
                    TradeGoodReq tradeGoodReq = new TradeGoodReq();
                    tradeGoodReq.setEmployeeId(request.getEmployeeId());
                    RrecruitmentManagementDataVo rrecruitmentManagement = new RrecruitmentManagementDataVo();
                    RrecruitmentManagementDataVo rrecruitmentManagementDataVo = publicZBInformation(tradeGoodReq, rrecruitmentManagement);
                    RealTimeRankingPersonalInfoVo personalInfoVo = new RealTimeRankingPersonalInfoVo();
                    personalInfoVo.setEmployeeName(rrecruitmentManagementDataVo.getEmployeeName());
                    personalInfoVo.setOnboardTime(LocalDate.parse(rrecruitmentManagementDataVo.getOnboardTime().substring(0, 10)).toString());
                    personalInfoVo.setOnboardDays(Integer.valueOf(rrecruitmentManagementDataVo.getOnboardDays()));
                    personalInfoVo.setOrganizationName("总部");
                    realTimeRankingListVo.setPersonalInfoVo(personalInfoVo);
                }
            }
        } else {
            //其他人员信息查询
            //全组不需要查询其他的人员信息
            if (businessGroup != 99) {
                RealTimeRankingPersonalInfoVo info = realtimeMapper.getPersonalPerformanceInfo(request.getDateTypeId(), request.getYearMonth(), organizationType, RankingListPositionEnums.getPositionTypeId(organizationType), businessGroup, RankingListPositionEnums.BRANCH.getOrganizationType().equals(organizationType) ? organizationMapper.getMemberKeyByOrgCode(businessGroup, organizationId, theDate) : organizationId, theDate);
                if (Objects.nonNull(info)) {
                    performanceInfo = info;
                }
            }
            if (StringUtils.isNotBlank(performanceInfo.getPositionId())) {
                performanceInfo.setResumeUrl(applyMemberMapper.getResumeUrl(performanceInfo.getPositionId()));
            }
            //只有业绩时才展示具体个人排名信息
            if (request.getQueryDataType() == 0) {
                //todo 总部全组点进去具体的人-->是否是全组 那个人的信息如何展示？

                Integer positionTypeId = RankingListPositionEnums.getPositionTypeId(organizationType);
                //全国排名：岗位类型+产品组--->直接查询实时数据
                //个人排名：岗位类型+产品组+同组织 下所有人
                List<String> regionRankings;
                if (RankingListPositionEnums.AREA.getOrganizationType().equals(organizationType)) {
                    regionRankings = areaOrganizationIds;
                } else {
                    regionRankings = sameFatherAndSameLevels;
                }

                //全国排名：岗位类型+产品组--->直接查询实时数据
                //个人排名：岗位类型+产品组+同组织 下所有人
                List<RealTimeRankingInfoPO> nationalRankingInfos;
                List<RealTimeRankingInfoPO> regionRankingInfos;
                //合伙人表只有合伙人信息 且全组不需要处理合伙人排名
                if (RankingListPositionEnums.BRANCH.getOrganizationType().equals(organizationType)) {
                    //管理岗无合伙人信息
                    if (businessGroup == 99 || 0 != request.getQueryDataType()) {
                        return realTimeRankingListVo;
                    }
                    nationalRankingInfos = realtimeMapper.getPartnerNationalRanking(request.getDateTypeId(), request.getYearMonth(), businessGroup, null, null);

                    regionRankingInfos = realtimeMapper.getPartnerRegionRanking(request.getDateTypeId(), request.getYearMonth(), branchOrganizationIds, businessGroup, null, null);

                    //全国排名
                    if (!CollectionUtils.isEmpty(nationalRankingInfos)) {
                        int i = nationalRankingInfos.stream().sorted(Comparator.comparing(RealTimeRankingInfoPO::getZwProductPerformance).reversed().thenComparing(RealTimeRankingInfoPO::getMemberKey)).map(RealTimeRankingInfoPO::getMemberKey).collect(Collectors.toList()).indexOf(request.getMemberKey());
                        performanceInfo.setPerformanceNationalRanking(i + 1);
                        int j = nationalRankingInfos.stream().sorted(Comparator.comparing(RealTimeRankingInfoPO::getZwProductPerformanceYearRatio).reversed().thenComparing(RealTimeRankingInfoPO::getMemberKey)).map(RealTimeRankingInfoPO::getMemberKey).collect(Collectors.toList()).indexOf(request.getMemberKey());
                        performanceInfo.setYearNationalRanking(j + 1);
                    }
                    //区域排名
                    if (!CollectionUtils.isEmpty(regionRankingInfos)) {
                        int i = regionRankingInfos.stream().sorted(Comparator.comparing(RealTimeRankingInfoPO::getZwProductPerformance).reversed().thenComparing(RealTimeRankingInfoPO::getMemberKey)).map(RealTimeRankingInfoPO::getMemberKey).collect(Collectors.toList()).indexOf(request.getMemberKey());
                        performanceInfo.setPerformanceRegionRanking(i + 1);
                        int j = regionRankingInfos.stream().sorted(Comparator.comparing(RealTimeRankingInfoPO::getZwProductPerformanceYearRatio).reversed().thenComparing(RealTimeRankingInfoPO::getMemberKey)).map(RealTimeRankingInfoPO::getMemberKey).collect(Collectors.toList()).indexOf(request.getMemberKey());
                        performanceInfo.setYearRegionRanking(j + 1);
                    }

                } else {
                    //其他
                    nationalRankingInfos = realtimeMapper.getNationalRanking(request.getDateTypeId(), request.getQueryDataType(), request.getYearMonth(), businessGroup, positionTypeId, null, null);

                    regionRankingInfos = realtimeMapper.getRegionRanking(request.getDateTypeId(), request.getQueryDataType(), request.getYearMonth(), regionRankings, businessGroup, positionTypeId, null, null);

                    //全国排名
                    if (!CollectionUtils.isEmpty(nationalRankingInfos)) {
                        int i = nationalRankingInfos.stream().sorted(Comparator.comparing(RealTimeRankingInfoPO::getZwProductPerformance).reversed().thenComparing(RealTimeRankingInfoPO::getOrganizationId)).map(RealTimeRankingInfoPO::getOrganizationId).collect(Collectors.toList()).indexOf(organizationId);
                        performanceInfo.setPerformanceNationalRanking(i + 1);
                        int j = nationalRankingInfos.stream().sorted(Comparator.comparing(RealTimeRankingInfoPO::getZwProductPerformanceYearRatio).reversed().thenComparing(RealTimeRankingInfoPO::getOrganizationId)).map(RealTimeRankingInfoPO::getOrganizationId).collect(Collectors.toList()).indexOf(organizationId);
                        performanceInfo.setYearNationalRanking(j + 1);
                    }
                    //区域排名
                    if (!CollectionUtils.isEmpty(regionRankingInfos)) {
                        int i = regionRankingInfos.stream().sorted(Comparator.comparing(RealTimeRankingInfoPO::getZwProductPerformance).reversed().thenComparing(RealTimeRankingInfoPO::getOrganizationId)).map(RealTimeRankingInfoPO::getOrganizationId).collect(Collectors.toList()).indexOf(organizationId);
                        performanceInfo.setPerformanceRegionRanking(i + 1);
                        int j = regionRankingInfos.stream().sorted(Comparator.comparing(RealTimeRankingInfoPO::getZwProductPerformanceYearRatio).reversed().thenComparing(RealTimeRankingInfoPO::getOrganizationId)).map(RealTimeRankingInfoPO::getOrganizationId).collect(Collectors.toList()).indexOf(organizationId);
                        performanceInfo.setYearRegionRanking(j + 1);
                    }
                }
            }


            realTimeRankingListVo.setPersonalInfoVo(performanceInfo);

        }


        return realTimeRankingListVo;
    }


    @Override
    public com.wantwant.commons.pagination.Page<NextRealTimeDataVo> queryNextRealTimeData(TradeGoodReq request) {
        log.info("start RealtimeDataServiceImpl queryNextRealTimeData request:{}", request);
        com.wantwant.commons.pagination.Page<NextRealTimeDataVo> result = new com.wantwant.commons.pagination.Page<>();
        log.info("根据上级获取下级数据入参:request:{}", request);
        if (null == request.getOrganizationId() || request.getYearMonth() == "") {
            throw new ApplicationException("组织id不能为空 ");
        }
        if (null == request.getYearMonth() || request.getYearMonth() == "") {
            throw new ApplicationException("月份不能为空 ");
        }
        result.setList(mapper.getNextRealTimeData(request));
        result.setTotalItem(mapper.getNextRealTimeCount(request));
        return result;
    }

    /**
     * 招聘进度
     *
     * @param request
     * @return: com.wantwant.sfa.backend.zw.vo.RecruitmentProgressVO
     * @date: 3/22/22 10:03 AM
     */
    @Override
    public RecruitmentProgressVO queryProgress(RecruitmentProgressRequest request) {

        // 处理大区、分公司code
        splitIdsForAreaAndCompany(request);
        log.info("\narea:{} \ncompany:{}", request.getAreaOrganizationIdArr(), request.getCompanyOrganizationIdArr());

        RecruitmentProgressVO vo = new RecruitmentProgressVO();
        String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
        log.info("\norganizationType:{}\n", organizationType);

        // 分公司只显示合伙人
        if ("company".equals(organizationType)) {
            List<RecruitmentPartnerProgressVO> partner = mapper.listPartnerProgressByIds(request);
            // 按分公司汇总
            List<RecruitmentPartnerProgressVO> companyList = new ArrayList<>();
            Map<String, List<RecruitmentPartnerProgressVO>> companyMap = partner.stream().collect(groupingBy(p -> p.getArea()));
            for (Map.Entry<String, List<RecruitmentPartnerProgressVO>> entry : companyMap.entrySet()) {
                RecruitmentPartnerProgressVO company = new RecruitmentPartnerProgressVO();
                company.setCompany(entry.getKey());
                List<RecruitmentPartnerProgressVO> v = entry.getValue();
                company.setArea(v.get(0).getArea());
                company.setEmployeeName(v.get(0).getEmployeeName());
                collectPartner(company, v);
                company.setChildren(v);
                companyList.add(company);
            }
            RecruitmentPartnerProgressVO hj = new RecruitmentPartnerProgressVO();
            hj.setArea("合计");
            collectPartner(hj, companyList);
            companyList.add(hj);
            vo.setPartner(companyList);
        } else {
            if (1 == request.getPosition()) {

                List<RecruitmentPartnerProgressVO> partner = mapper.listPartnerProgressByIds(request);

                // 按大区汇总
                List<RecruitmentPartnerProgressVO> list = new ArrayList<>();
                Map<String, List<RecruitmentPartnerProgressVO>> areaMap = partner.stream().collect(groupingBy(p -> p.getArea()));
                log.info("\nmap:{}\n", areaMap);

                for (Map.Entry<String, List<RecruitmentPartnerProgressVO>> entry : areaMap.entrySet()) {

                    RecruitmentPartnerProgressVO area = new RecruitmentPartnerProgressVO();
                    area.setArea(entry.getKey());
                    List<RecruitmentPartnerProgressVO> v = entry.getValue();
                    // 负责人
                    area.setEmployeeName(v.get(0).getAreaEmployeeName());
                    collectPartner(area, v);
                    area.setChildren(v);
                    list.add(area);
                }
                RecruitmentPartnerProgressVO hj = new RecruitmentPartnerProgressVO();
                hj.setArea("合计");
                collectPartner(hj, list);
                list.add(hj);
                vo.setPartner(list);
            } else {
                List<RecruitmentDirectorProgressVO> director = mapper.listDirectorProgressByIds(request);
                // 按大区汇总
                List<RecruitmentDirectorProgressVO> areaList = new ArrayList<>();
                Map<String, List<RecruitmentDirectorProgressVO>> areaMap = director.stream().collect(groupingBy(d -> d.getArea()));
                for (Map.Entry<String, List<RecruitmentDirectorProgressVO>> entry : areaMap.entrySet()) {
                    RecruitmentDirectorProgressVO area = new RecruitmentDirectorProgressVO();
                    area.setArea(entry.getKey());
                    List<RecruitmentDirectorProgressVO> r = entry.getValue();
                    collectDirector(area, r);
                    area.setChildren(r);
                    areaList.add(area);
                }

                vo.setDirector(areaList);
            }

            // 只有总部，且显示所有数据是，展示招聘渠道数据
            ArrayList areaIds = request.getAreaOrganizationIdArr();
            ArrayList companyIds = request.getCompanyOrganizationIdArr();
            if ("zb".equals(organizationType) && (companyIds == null || companyIds.size() <= 0) && (areaIds == null || areaIds.size() <= 0)) {
                vo.setCount(mapper.listCountProgress(request));
            }
        }
        return vo;
    }

    /**
     * 招聘进度导出
     *
     * @param request
     * @return: void
     * @date: 5/5/22 2:49 PM
     */
    @Override
    public void exportProgress(RecruitmentProgress1Request request) {
        log.info("招聘进度导出入参:{}", request);

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName = LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss");

        // 查询条件
        RecruitmentProgressRequest progressRequest = new RecruitmentProgressRequest();
        BeanUtils.copyProperties(request, progressRequest);

        // 处理大区、分公司code
        splitIdsForAreaAndCompany(progressRequest);
        log.info("\narea:{} \ncompany:{}", progressRequest.getAreaOrganizationIdArr(), progressRequest.getCompanyOrganizationIdArr());

        // 查询组织类型
        String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
        // 导出类型(1:招聘进度,2.招聘渠道)
        if (1 == request.getType()) {
            // 导出招聘进度
            if ("company".equals(organizationType)) {
                // 分公司只显示合伙人
                List<RecruitmentPartnerProgressVO> partner = mapper.listPartnerProgressByIds(progressRequest);
                List<RecruitmentPartnerProgressVO> companyList = new ArrayList<>();
                if (CommonUtil.ListUtils.isNotEmpty(partner)) {
                    // 分公司维度组装数据
                    Map<String, List<RecruitmentPartnerProgressVO>> companyMap = partner.stream().collect(groupingBy(p -> p.getArea()));
                    for (Map.Entry<String, List<RecruitmentPartnerProgressVO>> entry : companyMap.entrySet()) {
                        RecruitmentPartnerProgressVO company = new RecruitmentPartnerProgressVO();
                        company.setCompany(entry.getKey());
                        List<RecruitmentPartnerProgressVO> v = entry.getValue();
                        company.setArea(v.get(0).getArea());
                        company.setEmployeeName(v.get(0).getEmployeeName());
                        collectPartner(company, v);
                        companyList.add(company);
                        companyList.addAll(v);
                    }
                    RecruitmentPartnerProgressVO hj = new RecruitmentPartnerProgressVO();
                    hj.setArea("合计");
                    collectPartner(hj, companyList);
                    companyList.add(hj);
                }
                Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), RecruitmentPartnerProgressVO.class, companyList);
                writeExcel(servletRequestAttributes, response, wb, "合伙人招聘进度导出");
            } else {
                if (1 == request.getPosition()) {
                    // 合伙人招聘进度
                    List<RecruitmentPartnerProgressVO> partner = mapper.listPartnerProgressByIds(progressRequest);
                    List<RecruitmentPartnerProgressVO> list = new ArrayList<>();
                    if (CommonUtil.ListUtils.isNotEmpty(partner)) {
                        // 大区维度组装数据
                        Map<String, List<RecruitmentPartnerProgressVO>> areaMap = partner.stream().collect(groupingBy(p -> p.getArea()));
                        for (Map.Entry<String, List<RecruitmentPartnerProgressVO>> entry : areaMap.entrySet()) {
                            RecruitmentPartnerProgressVO area = new RecruitmentPartnerProgressVO();
                            area.setArea(entry.getKey());
                            List<RecruitmentPartnerProgressVO> v = entry.getValue();
                            // 负责人
                            area.setEmployeeName(v.get(0).getAreaEmployeeName());
                            collectPartner(area, v);
                            list.add(area);
                            list.addAll(v);
                        }
                        RecruitmentPartnerProgressVO hj = new RecruitmentPartnerProgressVO();
                        hj.setArea("合计");
                        collectPartner(hj, list);
                        list.add(hj);
                    }
                    Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), RecruitmentPartnerProgressVO.class, list);
                    writeExcel(servletRequestAttributes, response, wb, "合伙人招聘进度导出");
                } else {
                    // 总监招聘进度
                    List<RecruitmentDirectorProgressVO> director = mapper.listDirectorProgressByIds(progressRequest);
                    List<RecruitmentDirectorProgressVO> areaList = new ArrayList<>();
                    if (CommonUtil.ListUtils.isNotEmpty(director)) {
                        Map<String, List<RecruitmentDirectorProgressVO>> areaMap = director.stream().collect(groupingBy(d -> d.getArea()));
                        for (Map.Entry<String, List<RecruitmentDirectorProgressVO>> entry : areaMap.entrySet()) {
                            RecruitmentDirectorProgressVO area = new RecruitmentDirectorProgressVO();
                            area.setArea(entry.getKey());
                            List<RecruitmentDirectorProgressVO> r = entry.getValue();
                            collectDirector(area, r);
                            areaList.add(area);
                            areaList.addAll(r);
                        }
                    }
                    Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), RecruitmentDirectorProgressVO.class, areaList);
                    writeExcel(servletRequestAttributes, response, wb, "总监招聘进度导出");
                }
            }
        } else if (2 == request.getType()) {
            // 招聘渠道

            // 只有总部，且显示所有数据是，展示招聘渠道数据
            ArrayList areaIds = request.getAreaOrganizationIdArr();
            ArrayList companyIds = request.getCompanyOrganizationIdArr();
            if ("zb".equals(organizationType) && (companyIds == null || companyIds.size() <= 0) && (areaIds == null || areaIds.size() <= 0)) {

                List<RecruitmentProgressCountVO> list = mapper.listCountProgress(progressRequest);
                RecruitmentProgressCountVO hj = mapper.listCountProgresshj(progressRequest);
                list.add(hj);
                if (1 == request.getPosition()) {
                    Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), RecruitmentProgressCountVO.class, list);
                    writeExcel(servletRequestAttributes, response, wb, "合伙人招聘渠道导出");
                } else {
                    List<RecruitmentProgressCount1VO> list1 = new ArrayList<>();
                    BeanUtils.copyProperties(list, list1, RecruitmentProgressCountVO.class, RecruitmentProgressCount1VO.class);

                    Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), RecruitmentProgressCount1VO.class, list1);
                    writeExcel(servletRequestAttributes, response, wb, "总监招聘渠道导出");
                }
            }
        }
    }


    public RrecruitmentManagementDataVo publicZBInformation(TradeGoodReq request, RrecruitmentManagementDataVo rrecruitmentManagementData) {
        log.info("start RealtimeDataServiceImpl publicZBInformation request:{},rrecruitmentManagementData:{}", request, rrecruitmentManagementData);
        CeoBusinessOrganizationPositionRelation relation = relationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, request.getEmployeeId()).eq(CeoBusinessOrganizationPositionRelation::getChannel, RequestUtils.getChannel())).get(0);
        if (Objects.nonNull(relation)) {
            rrecruitmentManagementData.setEmployeeName(relation.getEmployeeName());
            if (null != relation.getOnboardTime()) {
                rrecruitmentManagementData.setOnboardTime(LocalDateTimeUtils.formatTime(relation.getOnboardTime(), "yyyy-MM-dd HH:mm:ss"));
                rrecruitmentManagementData.setOnboardDays(daysBetween(rrecruitmentManagementData.getOnboardTime()));
            }
        }
        return rrecruitmentManagementData;
    }

    private BigDecimal convertPercent(Integer val) {
        return CalculateUtils.ratio(new BigDecimal(val), new BigDecimal(100), 2);
    }

    private BigDecimal convertPercent(Double val) {
        return CalculateUtils.ratio(new BigDecimal(val), new BigDecimal(100), 2);
    }

    private BigDecimal convertPercent(BigDecimal val) {
        return CalculateUtils.ratio(val, new BigDecimal(100), 2);
    }

    public List<MainProductsDataVo> mainProductsData(List<MainProductsDataVo> mainProductsName, Map.Entry<String, List<NextResultsDataVo>> next) {
        ArrayList<MainProductsDataVo> mainProductsDataTwoList = new ArrayList<>();
        for (MainProductsDataVo mainProducts : mainProductsName) {
            MainProductsDataVo mainProductsTwo = new MainProductsDataVo();
            List<NextResultsDataVo> data = next.getValue().stream().filter(v -> CommonUtil.StringUtils.isNotBlank(v.getMainProductsName()) && v.getMainProductsName().equals(mainProducts.getMainProductsName())).collect(Collectors.toList());
            if (CommonUtil.ListUtils.isNotEmpty(data)) {
                NextResultsDataVo nextResultsDataVo1 = data.get(0);
                BeanUtils.copyProperties(nextResultsDataVo1, mainProductsTwo);
            }
            mainProductsDataTwoList.add(mainProductsTwo);
        }
        return mainProductsDataTwoList;
    }

    /*获取下级业绩排序处理*/
    List<NextResultsDataVo> sortedNextResultsData(TradeGoodReq request, ArrayList<NextResultsDataVo> nextResultsDataVos) {
        log.info("start RealtimeDataServiceImpl sortedNextResultsData request:{},nextResultsDataVos:{}", request, nextResultsDataVos);
        List<NextResultsDataVo> collectSort = null;
        if (CommonUtil.ListUtils.isEmpty(nextResultsDataVos)) {
            collectSort = new ArrayList<>();
        } else {
            if (CommonUtil.StringUtils.isNotBlank(request.getSortName()) && CommonUtil.StringUtils.isNotBlank(request.getSortOrder())) {
                if (request.getSortName().equals("itemsSupplyTotalCm") && request.getSortOrder().equals("ASC")) {
                    collectSort = nextResultsDataVos.stream().sorted(Comparator.comparingDouble(NextResultsDataVo::getItemsSupplyTotalCm)).collect(Collectors.toList());
                    for (NextResultsDataVo col : collectSort) {
                        if (col.getArea().equals("合计")) {
                            continue;
                        }
                        if (null != col.getNextResultsDataVo()) {
                            List<NextResultsDataVo> collect2 = col.getNextResultsDataVo().stream().sorted(Comparator.comparingDouble(NextResultsDataVo::getItemsSupplyTotalCm)).collect(Collectors.toList());
                            col.setNextResultsDataVo(collect2);
                        }
                    }
                }
                if (request.getSortName().equals("itemsSupplyTotalCm") && request.getSortOrder().equals("DESC")) {
                    collectSort = nextResultsDataVos.stream().sorted(Comparator.comparingDouble(NextResultsDataVo::getItemsSupplyTotalCm).reversed()).collect(Collectors.toList());
                    for (NextResultsDataVo col : collectSort) {
                        if (col.getArea().equals("合计")) {
                            continue;
                        }
                        if (null != col.getNextResultsDataVo()) {
                            List<NextResultsDataVo> collect2 = col.getNextResultsDataVo().stream().sorted(Comparator.comparingDouble(NextResultsDataVo::getItemsSupplyTotalCm).reversed()).collect(Collectors.toList());
                            col.setNextResultsDataVo(collect2);
                        }
                    }
                }
                if (request.getSortName().equals("saleGoalAchievementRate") && request.getSortOrder().equals("ASC")) {
                    collectSort = nextResultsDataVos.stream().sorted(Comparator.comparingDouble(NextResultsDataVo::getSaleGoalAchievementRate)).collect(Collectors.toList());
                    for (NextResultsDataVo col : collectSort) {
                        if (col.getArea().equals("合计")) {
                            continue;
                        }
                        if (null != col.getNextResultsDataVo()) {
                            List<NextResultsDataVo> collect2 = col.getNextResultsDataVo().stream().sorted(Comparator.comparingDouble(NextResultsDataVo::getSaleGoalAchievementRate)).collect(Collectors.toList());
                            col.setNextResultsDataVo(collect2);
                        }
                    }
                }
                if (request.getSortName().equals("saleGoalAchievementRate") && request.getSortOrder().equals("DESC")) {
                    collectSort = nextResultsDataVos.stream().sorted(Comparator.comparingDouble(NextResultsDataVo::getSaleGoalAchievementRate).reversed()).collect(Collectors.toList());
                    for (NextResultsDataVo col : collectSort) {
                        if (col.getArea().equals("合计")) {
                            continue;
                        }
                        if (null != col.getNextResultsDataVo()) {
                            List<NextResultsDataVo> collect2 = col.getNextResultsDataVo().stream().sorted(Comparator.comparingDouble(NextResultsDataVo::getSaleGoalAchievementRate).reversed()).collect(Collectors.toList());
                            col.setNextResultsDataVo(collect2);
                        }
                    }
                }
            } else {
                collectSort = nextResultsDataVos.stream().sorted(Comparator.comparingDouble(NextResultsDataVo::getItemsSupplyTotalCm).reversed()).collect(Collectors.toList());
                for (NextResultsDataVo col : collectSort) {
                    if (StringUtils.isNotBlank(col.getArea()) && col.getArea().equals("合计")) {
                        continue;
                    }
                    if (null != col.getNextResultsDataVo()) {
                        List<NextResultsDataVo> collect2 = col.getNextResultsDataVo().stream().sorted(Comparator.comparingDouble(NextResultsDataVo::getItemsSupplyTotalCm).reversed()).collect(Collectors.toList());
                        col.setNextResultsDataVo(collect2);
                    }
                }
            }
        }
        return collectSort;
    }


    /**
     * 根据员工id获取组织id
     *
     * @param employeeId
     * @return
     */
    public String getOrganizationIdZB(String employeeId) {
        if (RequestUtils.getLoginInfo().getOrganizationType().equals("zb")) {
            List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(employeeId, RequestUtils.getLoginInfo());
            if (CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)) {
                return employeeOrganizationId.get(0);
            }
        }
        return null;
    }


    @Override
    public List<PerformanceTrendsAllVo> getPerformanceTrends(PerformanceTrendsRequest request) {
        log.info("start RealtimeDataServiceImpl getPerformanceTrends request:{}", request);
        request.setBusinessGroup(RequestUtils.getLoginInfo().getBusinessGroup());
        /**
         * 月度：本月和上月的日业绩
         * 季度：一个季度+多一个月
         * 财年：月维度数据
         * 默认每个月31天
         * queryType 默认都是线别
         */
        if ("10".equals(request.getDateTypeId())) {
            return getPerformanceTrendsForMonth(request);
        } else if ("11".equals(request.getDateTypeId())) {
            return getPerformanceTrendsForQuarter(request);
        } else if ("2".equals(request.getDateTypeId())) {
            return getPerformanceTrendsForFiscalYear(request);
        }
        return CollectionUtil.newArrayList();
    }

    private List<PerformanceTrendsAllVo> getPerformanceTrendsForMonth(PerformanceTrendsRequest request) {
        List<PerformanceTrendsAllVo> performanceTrendsAllVos = new ArrayList<>();
        //大于今天直接过滤
        String nowDate = LocalDate.now().toString();
        //不加具体筛选条件查所有
        if (CollectionUtils.isEmpty(request.getQueryNames())) {
            //查询本月和上月的每日业绩信息 2024-01
            Integer year = Integer.valueOf(request.getYearMonth().substring(0, 4));
            Integer month = Integer.valueOf(request.getYearMonth().substring(5));
            LocalDate localDate = LocalDate.of(year, month, 1);
            //上一个月的时间计算
            String lastMonth = localDate.minusMonths(1).toString().substring(0, 7);
            List<String> yearMonthList = new ArrayList<>();
            yearMonthList.add(request.getYearMonth());
            yearMonthList.add(lastMonth);
            request.setYearMonthList(yearMonthList);
            //时间内的汇总业绩
            List<PerformanceTrendsPerformanceVo> performanceTrendsMonths = realtimeMapper.getTotalPerformanceTrendsMonth(request);
            if (CollectionUtil.isEmpty(performanceTrendsMonths)) {
                return performanceTrendsAllVos;
            }
            Map<String, PerformanceTrendsPerformanceVo> performanceTrendsMonthsMap = performanceTrendsMonths.stream().collect(Collectors.toMap(PerformanceTrendsPerformanceVo::getTheYearDate, Function.identity()));
            //本月天数
            String monthName = "当月业绩";
            List<String> monthDays = realtimeMapper.getMonthDays(request.getYearMonth());
            List<PerformanceTrendsPerformanceVo> monthVo = new ArrayList<>(monthDays.size());
            for (String monthDay : monthDays) {
                //判断日期是否大于今天 大于则不补0
                if (monthDay.compareTo(nowDate) > 0) {
                    continue;
                }
                PerformanceTrendsPerformanceVo performanceVo = performanceTrendsMonthsMap.get(monthDay);
                if (Objects.isNull(performanceVo)) {
                    performanceVo = new PerformanceTrendsPerformanceVo();
                    performanceVo.setZwPerformance("0");
                    performanceVo.setTheYearDate(monthDay);
                }
                performanceVo.setName(monthName);
                monthVo.add(performanceVo);
            }
            PerformanceTrendsAllVo performanceTrendsAllVo = new PerformanceTrendsAllVo();
            performanceTrendsAllVo.setList(monthVo);
            performanceTrendsAllVo.setName(monthName);
            performanceTrendsAllVos.add(performanceTrendsAllVo);

            //上月天数
            String lastMonthName = "上月业绩";
            List<String> lastMonthDays = realtimeMapper.getMonthDays(lastMonth);
            List<PerformanceTrendsPerformanceVo> lastMonthVo = new ArrayList<>(lastMonthDays.size());
            for (String monthDay : lastMonthDays) {
                PerformanceTrendsPerformanceVo performanceVo = performanceTrendsMonthsMap.get(monthDay);
                if (Objects.isNull(performanceVo)) {
                    performanceVo = new PerformanceTrendsPerformanceVo();
                    performanceVo.setZwPerformance("0");
                    performanceVo.setTheYearDate(monthDay);
                }
                performanceVo.setName(lastMonthName);
                lastMonthVo.add(performanceVo);
            }
            PerformanceTrendsAllVo lastPerformanceTrendsAllVo = new PerformanceTrendsAllVo();
            lastPerformanceTrendsAllVo.setList(lastMonthVo);
            lastPerformanceTrendsAllVo.setName(lastMonthName);
            performanceTrendsAllVos.add(lastPerformanceTrendsAllVo);

            //查询本月不同线别的业绩信息
            yearMonthList.clear();
            yearMonthList.add(request.getYearMonth());
            request.setYearMonthList(yearMonthList);
            List<PerformanceTrendsPerformanceVo> linePerformanceTrendsMonth = realtimeMapper.getLinePerformanceTrendsMonth(request);
            if (CollectionUtil.isEmpty(linePerformanceTrendsMonth)) {
                return performanceTrendsAllVos;
            }
            Map<String, List<PerformanceTrendsPerformanceVo>> linePerformanceTrendsMonthMap = linePerformanceTrendsMonth.stream().collect(groupingBy(PerformanceTrendsPerformanceVo::getName));
            linePerformanceTrendsMonthMap.forEach((key, value) -> {
                String lineName = key;
                Map<String, PerformanceTrendsPerformanceVo> lineTrendsPerformanceVoMap = value.stream().collect(Collectors.toMap(PerformanceTrendsPerformanceVo::getTheYearDate, Function.identity()));
                List<PerformanceTrendsPerformanceVo> lineVo = new ArrayList<>();
                for (String monthDay : monthDays) {
                    //判断日期是否大于今天 大于则不补0
                    if (monthDay.compareTo(nowDate) > 0) {
                        continue;
                    }
                    PerformanceTrendsPerformanceVo performanceVo = lineTrendsPerformanceVoMap.get(monthDay);
                    if (Objects.isNull(performanceVo)) {
                        performanceVo = new PerformanceTrendsPerformanceVo();
                        performanceVo.setZwPerformance("0");
                        performanceVo.setTheYearDate(monthDay);
                    }
                    performanceVo.setName(lineName);
                    lineVo.add(performanceVo);
                }
                PerformanceTrendsAllVo linePerformanceTrendsAllVo = new PerformanceTrendsAllVo();
                linePerformanceTrendsAllVo.setList(lineVo);
                linePerformanceTrendsAllVo.setName(lineName);
                performanceTrendsAllVos.add(linePerformanceTrendsAllVo);

            });

        } else {
            //只有线别数据 线别只查当月
            List<String> yearMonthList = new ArrayList<>();
            yearMonthList.add(request.getYearMonth());
            request.setYearMonthList(yearMonthList);
            List<PerformanceTrendsPerformanceVo> performanceTrendsMonths = realtimeMapper.getLinePerformanceTrendsMonth(request);
            if (CollectionUtil.isEmpty(performanceTrendsMonths)) {
                return performanceTrendsAllVos;
            }
            List<String> monthDays = realtimeMapper.getMonthDays(request.getYearMonth());
            Map<String, List<PerformanceTrendsPerformanceVo>> performanceTrendsMonthMap = performanceTrendsMonths.stream().collect(groupingBy(PerformanceTrendsPerformanceVo::getName));
            performanceTrendsMonthMap.forEach((key, value) -> {
                String lineName = key;
                Map<String, PerformanceTrendsPerformanceVo> lineTrendsPerformanceVoMap = value.stream().collect(Collectors.toMap(PerformanceTrendsPerformanceVo::getTheYearDate, Function.identity()));
                List<PerformanceTrendsPerformanceVo> lineVo = new ArrayList<>();
                for (String monthDay : monthDays) {
                    //判断日期是否大于今天 大于则不补0
                    if (monthDay.compareTo(nowDate) > 0) {
                        continue;
                    }
                    PerformanceTrendsPerformanceVo performanceVo = lineTrendsPerformanceVoMap.get(monthDay);
                    if (Objects.isNull(performanceVo)) {
                        performanceVo = new PerformanceTrendsPerformanceVo();
                        performanceVo.setZwPerformance("0");
                        performanceVo.setTheYearDate(monthDay);
                    }
                    performanceVo.setName(lineName);
                    lineVo.add(performanceVo);
                }
                PerformanceTrendsAllVo linePerformanceTrendsAllVo = new PerformanceTrendsAllVo();
                linePerformanceTrendsAllVo.setList(lineVo);
                linePerformanceTrendsAllVo.setName(lineName);
                performanceTrendsAllVos.add(linePerformanceTrendsAllVo);
            });

        }

        return performanceTrendsAllVos;
    }

    private List<PerformanceTrendsAllVo> getPerformanceTrendsForQuarter(PerformanceTrendsRequest request) {
        List<PerformanceTrendsAllVo> performanceTrendsAllVos = new ArrayList<>();
        //大于今天直接过滤
        String nowDate = LocalDate.now().toString();
        //查询本季度和上季度的每日业绩信息
        List<String> quarterMonths = realtimeMapper.getQuarterMonths(request.getYearMonth());
        List<String> lastQuarterMonths = realtimeMapper.getLastQuarterMonths(request.getYearMonth()).stream().map(YearQuarterDTO::getTheYearMonth).collect(Collectors.toList());
        List<String> yearMonthList = new ArrayList<>();
        yearMonthList.addAll(quarterMonths);
        yearMonthList.addAll(lastQuarterMonths);
        request.setYearMonthList(yearMonthList);
        //时间内的汇总业绩
        List<PerformanceTrendsPerformanceVo> performanceTrendsMonths = realtimeMapper.getTotalPerformanceTrendsMonth(request);
        if (CollectionUtil.isEmpty(performanceTrendsMonths)) {
            return performanceTrendsAllVos;
        }
        Map<String, PerformanceTrendsPerformanceVo> performanceTrendsMonthsMap = performanceTrendsMonths.stream().collect(Collectors.toMap(PerformanceTrendsPerformanceVo::getTheYearDate, Function.identity()));
        String quarterName = "当季业绩";
        List<PerformanceTrendsPerformanceVo> quarterVo = new ArrayList<>();
        for (String month : quarterMonths) {
            List<String> monthDays = realtimeMapper.getMonthDays(month);
            for (String monthDay : monthDays) {
                //判断日期是否大于今天 大于则不补0
                if (monthDay.compareTo(nowDate) > 0) {
                    continue;
                }
                PerformanceTrendsPerformanceVo performanceVo = performanceTrendsMonthsMap.get(monthDay);
                if (Objects.isNull(performanceVo)) {
                    performanceVo = new PerformanceTrendsPerformanceVo();
                    performanceVo.setZwPerformance("0");
                    performanceVo.setTheYearDate(monthDay);
                }
                performanceVo.setName(quarterName);
                quarterVo.add(performanceVo);
            }
        }
        PerformanceTrendsAllVo quarterAllVo = new PerformanceTrendsAllVo();
        quarterAllVo.setName(quarterName);
        quarterAllVo.setList(quarterVo);
        performanceTrendsAllVos.add(quarterAllVo);

        String lastQuarterName = "上季度业绩";
        List<PerformanceTrendsPerformanceVo> lastQuarterVo = new ArrayList<>();
        for (String month : lastQuarterMonths) {
            List<String> monthDays = realtimeMapper.getMonthDays(month);
            for (String monthDay : monthDays) {
                PerformanceTrendsPerformanceVo performanceVo = performanceTrendsMonthsMap.get(monthDay);
                if (Objects.isNull(performanceVo)) {
                    performanceVo = new PerformanceTrendsPerformanceVo();
                    performanceVo.setZwPerformance("0");
                    performanceVo.setTheYearDate(monthDay);
                }
                performanceVo.setName(quarterName);
                lastQuarterVo.add(performanceVo);
            }
        }

        PerformanceTrendsAllVo lastQuarterAllVo = new PerformanceTrendsAllVo();
        lastQuarterAllVo.setName(lastQuarterName);
        lastQuarterAllVo.setList(lastQuarterVo);
        performanceTrendsAllVos.add(lastQuarterAllVo);

        //查询本季度不同线别的业绩信息
        yearMonthList.clear();
        yearMonthList.addAll(quarterMonths);
        request.setYearMonthList(yearMonthList);
        List<PerformanceTrendsPerformanceVo> linePerformanceTrendsMonth = realtimeMapper.getLinePerformanceTrendsMonth(request);
        Map<String, List<PerformanceTrendsPerformanceVo>> linePerformanceTrendsMonthMap = linePerformanceTrendsMonth.stream().collect(groupingBy(PerformanceTrendsPerformanceVo::getName));
        linePerformanceTrendsMonthMap.forEach((key, value) -> {
            String lineName = key;
            Map<String, PerformanceTrendsPerformanceVo> lineTrendsPerformanceVoMap = value.stream().collect(Collectors.toMap(PerformanceTrendsPerformanceVo::getTheYearDate, Function.identity()));

            List<PerformanceTrendsPerformanceVo> lineVo = new ArrayList<>();
            for (String month : quarterMonths) {
                List<String> monthDays = realtimeMapper.getMonthDays(month);
                for (String monthDay : monthDays) {
                    //判断日期是否大于今天 大于则不补0
                    if (monthDay.compareTo(nowDate) > 0) {
                        continue;
                    }
                    PerformanceTrendsPerformanceVo performanceVo = lineTrendsPerformanceVoMap.get(monthDay);
                    if (Objects.isNull(performanceVo)) {
                        performanceVo = new PerformanceTrendsPerformanceVo();
                        performanceVo.setZwPerformance("0");
                        performanceVo.setTheYearDate(monthDay);
                    }
                    performanceVo.setName(lineName);
                    lineVo.add(performanceVo);
                }
            }
            PerformanceTrendsAllVo linePerformanceTrendsAllVo = new PerformanceTrendsAllVo();
            linePerformanceTrendsAllVo.setList(lineVo);
            linePerformanceTrendsAllVo.setName(lineName);
            performanceTrendsAllVos.add(linePerformanceTrendsAllVo);
        });

        return performanceTrendsAllVos;
    }

    private List<PerformanceTrendsAllVo> getPerformanceTrendsForFiscalYear(PerformanceTrendsRequest request) {

        List<PerformanceTrendsAllVo> performanceTrendsAllVos = new ArrayList<>();
        //大于本月直接过滤 yyyy-MM-dd
        String nowDate = LocalDate.now().toString().substring(0, 7);
        //财年只有月业绩汇总和上一财年业绩汇总 无线别数据
        //dim_td_date匹配财年月份  获取每个自然月数据ads_bigtable_organization
        request.setLastYearMonth(String.valueOf(Integer.valueOf(request.getYearMonth()) - 1));
        List<PerformanceTrendsPerformanceVo> fiscalYearPerformanceTrends = realtimeMapper.getFiscalYearPerformanceTrends(request);
        if (CollectionUtils.isEmpty(fiscalYearPerformanceTrends)) {
            return performanceTrendsAllVos;
        }
        //name=1-->YearMonth name=2-->lastYearMonth
        Map<String, List<PerformanceTrendsPerformanceVo>> collect = fiscalYearPerformanceTrends.stream().collect(groupingBy(PerformanceTrendsPerformanceVo::getName));
        collect.entrySet().stream().forEach(k -> {
            StringBuilder stringBuilder = new StringBuilder();
            List<String> fiscalYearMonths;
            if ("1".equals(k.getKey())) {
                stringBuilder.append(request.getYearMonth());
                fiscalYearMonths = realtimeMapper.getFiscalYearMonths(request.getYearMonth());
            } else {
                stringBuilder.append(request.getLastYearMonth());
                fiscalYearMonths = realtimeMapper.getFiscalYearMonths((String.valueOf(Integer.valueOf(request.getYearMonth()) - 1)));
            }
            stringBuilder.append(FISCAL_YEAR_DESC);
            String name = stringBuilder.toString();
            List<PerformanceTrendsPerformanceVo> values = k.getValue();
            Map<String, PerformanceTrendsPerformanceVo> performanceVoMap = values.stream().collect(Collectors.toMap(PerformanceTrendsPerformanceVo::getTheYearDate, Function.identity()));
            List<PerformanceTrendsPerformanceVo> finialVo = new ArrayList<>(fiscalYearMonths.size());
            for (String month : fiscalYearMonths) {
                if (month.compareTo(nowDate) > 0) {
                    continue;
                }
                PerformanceTrendsPerformanceVo performanceVo = performanceVoMap.get(month);
                if (Objects.isNull(performanceVo)) {
                    performanceVo = new PerformanceTrendsPerformanceVo();
                    performanceVo.setZwPerformance("0");
                    performanceVo.setTheYearDate(month);
                }
                performanceVo.setName(name);
                finialVo.add(performanceVo);
            }
            PerformanceTrendsAllVo vo = new PerformanceTrendsAllVo();
            vo.setName(name);
            vo.setList(finialVo);
            performanceTrendsAllVos.add(vo);
        });

        return performanceTrendsAllVos;
    }


    @Override
    public List<PerformanceTrendsAllVo> getPerformanceTrendsContrast(PerformanceTrendsRequest request) {
        log.info("start RealtimeDataServiceImpl getPerformanceTrendsDefault request:{}", request);
        //时间类型 10:自然月,11:自然季,2:财务年
        /**
         * 月环比：成长业绩 当月业绩 上月业绩
         * 月同比：同期成长业绩 当月业绩 同期业绩
         * 季环比：2024-Q1 2023-Q3 成长业绩
         * 季同比：2024-Q1 2023-Q1 同期成长业绩
         * 财年：2024 2023 成长业绩
         */
        if ("2".equals(request.getDateTypeId())) {
            return getPerformanceTrendsContrastForFiscalYear(request);
        } else if ("11".equals(request.getDateTypeId())) {
            //11: 自然季(2024-Q2)
            return getPerformanceTrendsContrastForQuarter(request);
        } else if ("10".equals(request.getDateTypeId())) {
            return getPerformanceTrendsContrastForMonth(request);
        }
        return CollectionUtil.newArrayList();
    }

    private List<PerformanceTrendsAllVo> getPerformanceTrendsContrastForMonth(PerformanceTrendsRequest request) {
        //大于今天直接过滤
        String nowDate = LocalDate.now().toString();
        List<PerformanceTrendsAllVo> performanceTrendsAllVos = new ArrayList<>();
        String growUpTitle;
        String otherMonth;
        String otherTitle;
        Integer year = Integer.valueOf(request.getYearMonth().substring(0, 4));
        Integer month = Integer.valueOf(request.getYearMonth().substring(5));
        LocalDate localDate = LocalDate.of(year, month, 1);
        if (CONTRAST_TYPE_CHAIN.equals(request.getContrastType())) {
            growUpTitle = CONTRAST_TYPE_CHAIN_STR;
            otherTitle = "上月业绩";
            //上一个月的时间计算
            otherMonth = localDate.minusMonths(1).toString().substring(0, 7);
        } else {
            growUpTitle = CONTRAST_TYPE_YEAR_STR;
            otherTitle = "同期业绩";
            //去年同一个的时间
            otherMonth = localDate.minusYears(1).toString().substring(0, 7);
        }
        List<String> yearMonthList = new ArrayList<>();
        yearMonthList.add(request.getYearMonth());
        yearMonthList.add(otherMonth);
        request.setYearMonthList(yearMonthList);
        List<PerformanceTrendsPerformanceVo> performanceTrendsMonths = realtimeMapper.getTotalPerformanceTrendsMonth(request);
        if (CollectionUtil.isEmpty(performanceTrendsMonths)) {
            return performanceTrendsAllVos;
        }
        //业绩信息 -->累加
        Map<String, PerformanceTrendsPerformanceVo> performanceTrendsMonthsMap = performanceTrendsMonths.stream().collect(Collectors.toMap(PerformanceTrendsPerformanceVo::getTheYearDate, Function.identity(), (x1, x2) -> x1));

        //当月业绩
        List<PerformanceTrendsPerformanceVo> yearMonthVos = new ArrayList<>();
        List<String> monthDays = realtimeMapper.getMonthDays(request.getYearMonth());
        BigDecimal yearMomthTotal = BigDecimal.ZERO;
        for (String day : monthDays) {
            //判断日期是否大于今天 大于则不补0
            if (day.compareTo(nowDate) > 0) {
                continue;
            }
            PerformanceTrendsPerformanceVo performanceVo = performanceTrendsMonthsMap.get(day);
            if (Objects.isNull(performanceVo)) {
                performanceVo = new PerformanceTrendsPerformanceVo();
                performanceVo.setZwPerformance("0");
                performanceVo.setTheYearDate(day);
            }
            performanceVo.setName("当月业绩");
            yearMomthTotal = yearMomthTotal.add(new BigDecimal(performanceVo.getZwPerformance())).setScale(1, BigDecimal.ROUND_HALF_UP);
            performanceVo.setZwPerformance(yearMomthTotal.toPlainString());
            yearMonthVos.add(performanceVo);
        }

        PerformanceTrendsAllVo monthAllVo = new PerformanceTrendsAllVo();
        monthAllVo.setName("当月业绩");
        monthAllVo.setList(yearMonthVos);
        performanceTrendsAllVos.add(monthAllVo);

        //同期业绩 or 上月业绩
        List<PerformanceTrendsPerformanceVo> otherMonthVos = new ArrayList<>();
        List<String> otherMonthDays = realtimeMapper.getMonthDays(otherMonth);
        BigDecimal otherMonthTotal = BigDecimal.ZERO;
        for (String day : otherMonthDays) {
            PerformanceTrendsPerformanceVo performanceVo = performanceTrendsMonthsMap.get(day);
            if (Objects.isNull(performanceVo)) {
                performanceVo = new PerformanceTrendsPerformanceVo();
                performanceVo.setZwPerformance("0");
                performanceVo.setTheYearDate(day);
            }
            performanceVo.setName(otherTitle);
            otherMonthTotal = otherMonthTotal.add(new BigDecimal(performanceVo.getZwPerformance())).setScale(1, BigDecimal.ROUND_HALF_UP);
            performanceVo.setZwPerformance(otherMonthTotal.toPlainString());
            otherMonthVos.add(performanceVo);
        }
        PerformanceTrendsAllVo otherMonthAllVo = new PerformanceTrendsAllVo();
        otherMonthAllVo.setName(otherTitle);
        otherMonthAllVo.setList(otherMonthVos);
        performanceTrendsAllVos.add(otherMonthAllVo);


        //同期成长业绩 or 成长业绩
        //使用最小的月份比较
        List<PerformanceTrendsPerformanceVo> growUpVos = new ArrayList<>();
        for (int i = 0; i < NumberUtil.min(otherMonthVos.size(), yearMonthVos.size()); i++) {
            PerformanceTrendsPerformanceVo performanceVo = new PerformanceTrendsPerformanceVo();
            performanceVo.setName(growUpTitle);
            performanceVo.setTheYearDate("D" + (i + 1));
            performanceVo.setZwPerformance(new BigDecimal(yearMonthVos.get(i).getZwPerformance()).subtract(new BigDecimal(otherMonthVos.get(i).getZwPerformance())).setScale(1, BigDecimal.ROUND_HALF_UP).toPlainString());
            growUpVos.add(performanceVo);
        }
        PerformanceTrendsAllVo growUpAllVo = new PerformanceTrendsAllVo();
        growUpAllVo.setName(growUpTitle);
        growUpAllVo.setList(growUpVos);
        performanceTrendsAllVos.add(growUpAllVo);

        return performanceTrendsAllVos;
    }

    private List<PerformanceTrendsAllVo> getPerformanceTrendsContrastForFiscalYear(PerformanceTrendsRequest request) {
        List<PerformanceTrendsAllVo> list = new ArrayList<>();
        //查询财务年每月的数据
        //dim_td_date匹配财年月份  获取每个自然月数据ads_bigtable_organization
        request.setLastYearMonth(String.valueOf(Integer.valueOf(request.getYearMonth()) - 1));
        List<PerformanceTrendsPerformanceVo> fiscalYearPerformanceTrends = realtimeMapper.getFiscalYearPerformanceTrends(request);
        if (CollectionUtils.isEmpty(fiscalYearPerformanceTrends)) {
            return list;
        }
        //name=1-->YearMonth name=2-->lastYearMonth
        Map<String, List<PerformanceTrendsPerformanceVo>> collect = fiscalYearPerformanceTrends.stream().collect(groupingBy(PerformanceTrendsPerformanceVo::getName));
        //今年业绩
        List<PerformanceTrendsPerformanceVo> performanceTrendsPerformanceVos1 = collect.get("1");
        if (CollectionUtils.isEmpty(performanceTrendsPerformanceVos1)) {
            performanceTrendsPerformanceVos1 = new ArrayList<>();
        } else {
            performanceTrendsPerformanceVos1 = collect.get("1").stream().sorted(Comparator.comparing(PerformanceTrendsPerformanceVo::getTheYearDate)).collect(Collectors.toList());
        }
        //去年
        List<PerformanceTrendsPerformanceVo> performanceTrendsPerformanceVos2 = collect.get("2");
        if (CollectionUtils.isEmpty(performanceTrendsPerformanceVos2)) {
            performanceTrendsPerformanceVos2 = new ArrayList<>();
        } else {
            performanceTrendsPerformanceVos2 = collect.get("2").stream().sorted(Comparator.comparing(PerformanceTrendsPerformanceVo::getTheYearDate)).collect(Collectors.toList());

        }
        LocalDate now = LocalDate.now();
        String nowMonth = now.toString().substring(0, 7);
        // 获取所有财年
        List<String> fiscalYearMonths = realtimeMapper.getFiscalYearMonths(request.getYearMonth());
        if (CollectionUtils.isEmpty(performanceTrendsPerformanceVos1) || performanceTrendsPerformanceVos1.size() < fiscalYearMonths.size()) {
            if (CollectionUtils.isEmpty(performanceTrendsPerformanceVos1)) {
                performanceTrendsPerformanceVos1 = new ArrayList<>(12);
                for (String yearMonth : fiscalYearMonths) {
                    //大于本月数据不展示
                    if (yearMonth.compareTo(nowMonth) > 0) {
                        continue;
                    }
                    PerformanceTrendsPerformanceVo v = new PerformanceTrendsPerformanceVo();
                    v.setZwPerformance("0");
                    v.setTheYearDate(yearMonth);
                    performanceTrendsPerformanceVos1.add(v);
                }
            } else {
                Map<String, PerformanceTrendsPerformanceVo> voMap = performanceTrendsPerformanceVos1.stream().collect(Collectors.toMap(PerformanceTrendsPerformanceVo::getTheYearDate, Function.identity()));
                performanceTrendsPerformanceVos1 = new ArrayList<>(12);
                for (String yearMonth : fiscalYearMonths) {
                    //大于本月数据不展示
                    if (yearMonth.compareTo(nowMonth) > 0) {
                        continue;
                    }
                    PerformanceTrendsPerformanceVo v = voMap.get(yearMonth);
                    if (Objects.isNull(v)) {
                        v = new PerformanceTrendsPerformanceVo();
                        v.setZwPerformance("0");
                        v.setTheYearDate(yearMonth);
                    }
                    performanceTrendsPerformanceVos1.add(v);

                }
            }
            collect.put("1", performanceTrendsPerformanceVos1);

        }
        List<String> lastFiscalYearMonths = realtimeMapper.getFiscalYearMonths(request.getLastYearMonth());
        if (CollectionUtils.isEmpty(performanceTrendsPerformanceVos2) || performanceTrendsPerformanceVos2.size() < fiscalYearMonths.size()) {
            if (CollectionUtils.isEmpty(performanceTrendsPerformanceVos2)) {
                performanceTrendsPerformanceVos2 = new ArrayList<>(12);
                for (String yearMonth : lastFiscalYearMonths) {
                    PerformanceTrendsPerformanceVo v = new PerformanceTrendsPerformanceVo();
                    v.setZwPerformance("0");
                    v.setTheYearDate(yearMonth);
                    performanceTrendsPerformanceVos2.add(v);
                }
                collect.put("1", performanceTrendsPerformanceVos2);
            } else {
                Map<String, PerformanceTrendsPerformanceVo> voMap = performanceTrendsPerformanceVos2.stream().collect(Collectors.toMap(PerformanceTrendsPerformanceVo::getTheYearDate, Function.identity()));
                performanceTrendsPerformanceVos2 = new ArrayList<>(12);
                for (String yearMonth : lastFiscalYearMonths) {
                    PerformanceTrendsPerformanceVo v = voMap.get(yearMonth);
                    if (Objects.isNull(v)) {
                        v = new PerformanceTrendsPerformanceVo();
                        v.setZwPerformance("0");
                        v.setTheYearDate(yearMonth);
                    }
                    performanceTrendsPerformanceVos2.add(v);

                }
            }
            collect.put("2", performanceTrendsPerformanceVos2);

        }

        //顺序累加
        //先处理lastYearMonth
        collect.entrySet().forEach(data -> {
            String key = data.getKey();
            StringBuilder stringBuilder = new StringBuilder();
            if ("1".equals(key)) {
                stringBuilder.append(request.getYearMonth());
            } else {
                stringBuilder.append(request.getLastYearMonth());
            }
            stringBuilder.append(FISCAL_YEAR_DESC);
            String name = stringBuilder.toString();
            List<PerformanceTrendsPerformanceVo> performanceVosResult = new ArrayList<>(data.getValue().size());
            List<PerformanceTrendsPerformanceVo> performanceVos = data.getValue().stream().sorted(Comparator.comparing(PerformanceTrendsPerformanceVo::getTheYearDate)).collect(Collectors.toList());
            BigDecimal zwPerformanceSum = new BigDecimal(0);
            //初始化年月
            Calendar calendar = Calendar.getInstance();
            for (int i = 0; i < 12; i++) {
                PerformanceTrendsPerformanceVo original = null;
                if (performanceVos.size() >= i + 1) {
                    original = performanceVos.get(i);
                }
                if (i == 0) {
                    if ("1".equals(key)) {
                        calendar.set(Calendar.YEAR, Integer.valueOf(request.getYearMonth()));
                    } else {
                        calendar.set(Calendar.YEAR, Integer.valueOf(request.getLastYearMonth()));
                    }
                    calendar.set(Calendar.MONTH, Integer.valueOf(original.getTheYearDate().substring(6)));
                } else {
                    calendar.add(Calendar.MONTH, 1);
                }

                PerformanceTrendsPerformanceVo vo = new PerformanceTrendsPerformanceVo();
                vo.setName(name);
                //为null补充数据
                if (Objects.isNull(original)) {
                    vo.setZwPerformance(zwPerformanceSum.toPlainString());
                    vo.setTheYearDate(calendar.get(Calendar.YEAR) + "-" + String.format("%02d", calendar.get(Calendar.MONTH) + 1));
                } else {
                    vo.setTheYearDate(original.getTheYearDate());
                    if (StringUtils.isNotBlank(original.getName())) {
                        zwPerformanceSum = zwPerformanceSum.add(new BigDecimal(original.getZwPerformance()));

                        vo.setZwPerformance(zwPerformanceSum.toPlainString());
                    } else {
                        vo.setZwPerformance(BigDecimal.ZERO.toPlainString());
                    }

                }
                //大于本月不展示
                if (vo.getTheYearDate().compareTo(nowMonth) <= 0) {
                    performanceVosResult.add(vo);
                }
            }
            PerformanceTrendsAllVo vo = new PerformanceTrendsAllVo();
            vo.setName(name);
            vo.setList(performanceVosResult);
            list.add(vo);
        });
        //成长业绩
        String name = "成长业绩";
        List<PerformanceTrendsPerformanceVo> vos = new ArrayList<>();
        //初始化年月
        Calendar calendar = Calendar.getInstance();
        for (int i = 0; i < 12; i++) {
            PerformanceTrendsPerformanceVo vo1 = null;
            if (performanceTrendsPerformanceVos1.size() >= i + 1) {
                vo1 = performanceTrendsPerformanceVos1.get(i);
            }
            if (i == 0) {
                calendar.set(Calendar.YEAR, Integer.valueOf(request.getYearMonth()));
                calendar.set(Calendar.MONTH, Integer.valueOf(vo1.getTheYearDate().substring(6)));
            } else {
                calendar.add(Calendar.MONTH, 1);
            }
            PerformanceTrendsPerformanceVo vo2 = performanceTrendsPerformanceVos2.get(i);
            PerformanceTrendsPerformanceVo vo = new PerformanceTrendsPerformanceVo();
            vo.setName(name);
            if (Objects.nonNull(vo1)) {
                vo.setTheYearDate(vo1.getTheYearDate());
                vo.setZwPerformance(new BigDecimal(vo1.getZwPerformance()).subtract(new BigDecimal(vo2.getZwPerformance())).toPlainString());
                vos.add(vo);
            }
        }
        PerformanceTrendsAllVo vo = new PerformanceTrendsAllVo();
        vo.setName(name);
        vo.setList(vos);
        list.add(vo);

        return list;
    }

    private List<PerformanceTrendsAllVo> getPerformanceTrendsContrastForQuarter(PerformanceTrendsRequest request) {
        /**
         * 季环比：2024-Q1 2023-Q3 成长业绩
         * 季环比：2024-Q1 2023-Q1 同期成长业绩
         */
        //大于今天直接过滤
        String nowDate = LocalDate.now().toString();

        List<PerformanceTrendsAllVo> performanceTrendsAllVos = new ArrayList<>();
        //查询本季度和上季度的每日业绩信息
        List<String> quarterMonths = realtimeMapper.getQuarterMonths(request.getYearMonth());
        List<String> otherQuarterMonths;
        String otherQuarter;
        String growUpTitle;
        if (CONTRAST_TYPE_CHAIN.equals(request.getContrastType())) {
            List<YearQuarterDTO> lastQuarterMonths = realtimeMapper.getLastQuarterMonths(request.getYearMonth());
            otherQuarterMonths = lastQuarterMonths.stream().map(YearQuarterDTO::getTheYearMonth).collect(Collectors.toList());
            //2023-04  ---> 2023-Q4
            otherQuarter = lastQuarterMonths.get(0).getTheYearQuarter().substring(0, 5) + "Q" + lastQuarterMonths.get(0).getTheYearQuarter().substring(6);
            growUpTitle = CONTRAST_TYPE_CHAIN_STR;
        } else {
            //"2024-Q1" -->2023-Q1
            otherQuarter = (Integer.valueOf(request.getYearMonth().substring(0, 4)) - 1) + request.getYearMonth().substring(4);
            otherQuarterMonths = realtimeMapper.getQuarterMonths(otherQuarter);
            growUpTitle = CONTRAST_TYPE_YEAR_STR;
        }

        List<String> yearMonthList = new ArrayList<>();
        yearMonthList.addAll(quarterMonths);
        yearMonthList.addAll(otherQuarterMonths);
        request.setYearMonthList(yearMonthList);
        //时间内的汇总业绩
        List<PerformanceTrendsPerformanceVo> performanceTrendsMonths = realtimeMapper.getTotalPerformanceTrendsMonth(request);
        if (CollectionUtil.isEmpty(performanceTrendsMonths)) {
            return performanceTrendsAllVos;
        }
        //业绩信息和季度信息 -->累加
        Map<String, PerformanceTrendsPerformanceVo> performanceTrendsMonthsMap = performanceTrendsMonths.stream().collect(Collectors.toMap(PerformanceTrendsPerformanceVo::getTheYearDate, Function.identity(), (x1, x2) -> x1));
        //当前季度
        BigDecimal quarterTotal = BigDecimal.ZERO;
        List<PerformanceTrendsPerformanceVo> quarterTotalVos = new ArrayList<>(quarterMonths.size());
        for (String month : quarterMonths) {
            List<String> monthDays = realtimeMapper.getMonthDays(month);
            for (String day : monthDays) {
                //判断日期是否大于今天 大于则不补0
                if (day.compareTo(nowDate) > 0) {
                    continue;
                }
                PerformanceTrendsPerformanceVo performanceVo = performanceTrendsMonthsMap.get(day);
                if (Objects.isNull(performanceVo)) {
                    performanceVo = new PerformanceTrendsPerformanceVo();
                    performanceVo.setTheYearDate(day);
                    performanceVo.setZwPerformance("0");
                }
                performanceVo.setName(request.getYearMonth());
                //累加
                quarterTotal = quarterTotal.add(new BigDecimal(performanceVo.getZwPerformance())).setScale(1, BigDecimal.ROUND_HALF_UP);
                performanceVo.setZwPerformance(quarterTotal.toPlainString());
                quarterTotalVos.add(performanceVo);
            }

        }
        PerformanceTrendsAllVo quarterAllVo = new PerformanceTrendsAllVo();
        quarterAllVo.setList(quarterTotalVos);
        quarterAllVo.setName(request.getYearMonth());
        performanceTrendsAllVos.add(quarterAllVo);

        //其他季度
        BigDecimal otherQuarterTotal = BigDecimal.ZERO;
        List<PerformanceTrendsPerformanceVo> otherQuarterTotalVos = new ArrayList<>(quarterMonths.size());
        for (String month : otherQuarterMonths) {
            List<String> monthDays = realtimeMapper.getMonthDays(month);
            for (String day : monthDays) {
                PerformanceTrendsPerformanceVo performanceVo = performanceTrendsMonthsMap.get(day);
                if (Objects.isNull(performanceVo)) {
                    performanceVo = new PerformanceTrendsPerformanceVo();
                    performanceVo.setTheYearDate(day);
                    performanceVo.setZwPerformance("0");
                }
                performanceVo.setName(otherQuarter);
                //累加
                otherQuarterTotal = otherQuarterTotal.add(new BigDecimal(performanceVo.getZwPerformance())).setScale(1, BigDecimal.ROUND_HALF_UP);
                performanceVo.setZwPerformance(otherQuarterTotal.toPlainString());
                otherQuarterTotalVos.add(performanceVo);
            }

        }
        PerformanceTrendsAllVo otherQuarterAllVo = new PerformanceTrendsAllVo();
        otherQuarterAllVo.setList(otherQuarterTotalVos);
        otherQuarterAllVo.setName(otherQuarter);
        performanceTrendsAllVos.add(otherQuarterAllVo);


        //两个累加数据 相减 -->同期成长业绩/成长业绩 = 当前-之前
        //按照最短时长计算
        List<PerformanceTrendsPerformanceVo> growUpVos = new ArrayList<>(quarterMonths.size());
        for (int i = 0; i < NumberUtil.min(otherQuarterTotalVos.size(), quarterTotalVos.size()); i++) {
            PerformanceTrendsPerformanceVo performanceVo = new PerformanceTrendsPerformanceVo();
            performanceVo.setName(growUpTitle);
            performanceVo.setTheYearDate("D" + (i + 1));
            performanceVo.setZwPerformance(new BigDecimal(quarterTotalVos.get(i).getZwPerformance()).subtract(new BigDecimal(otherQuarterTotalVos.get(i).getZwPerformance())).setScale(1, BigDecimal.ROUND_HALF_UP).toPlainString());
            growUpVos.add(performanceVo);
        }
        PerformanceTrendsAllVo growUpAllVo = new PerformanceTrendsAllVo();
        growUpAllVo.setList(growUpVos);
        growUpAllVo.setName(growUpTitle);
        performanceTrendsAllVos.add(growUpAllVo);

        return performanceTrendsAllVos;
    }


    @Override
    public List<String> getLineName(LineRequest request) {
        request.setBusinessGroup(RequestUtils.getLoginInfo().getBusinessGroup());
        return realtimeMapper.getLineName(request);
    }

    @Override
    public List<PerformanceInfluenceVO> performanceInfluenceFactors(String organizationId, String areaOrganizationId, Integer partnerType, String person) {
        List<PerformanceInfluenceVO> list = new ArrayList<>();
        String organizationType = organizationMapper.getOrganizationTypeByChannel(organizationId, RequestUtils.getChannel());
        int businessGroup = RequestUtils.getBusinessGroup();
        if ("zb".equals(organizationType) || "area".equals(organizationType)) {
            list = realtimeMapper.selectPerformanceInfluenceFactors(organizationId, null, null, null, null, partnerType, businessGroup);
        } else if ("varea".equals(organizationType)) {
            list = realtimeMapper.selectPerformanceInfluenceFactors(areaOrganizationId, organizationId, null, null, null, partnerType, businessGroup);
        } else if ("province".equals(organizationType)) {
            list = realtimeMapper.selectPerformanceInfluenceFactors(areaOrganizationId, null, organizationId, null, null, partnerType, businessGroup);
        } else if ("company".equals(organizationType)) {
            list = realtimeMapper.selectPerformanceInfluenceFactors(areaOrganizationId, null, null, organizationId, null, partnerType, businessGroup);
        } else if ("department".equals(organizationType)) {
            list = realtimeMapper.selectPerformanceInfluenceFactors(areaOrganizationId, null, null, null, organizationId, partnerType, businessGroup);
        }
        return list;
    }

    @Override
    public List<PerformanceInfluenceVO> performanceInfluenceFactorsYear(String organizationId, String areaOrganizationId, Integer partnerType, String person) {
        List<PerformanceInfluenceVO> list = new ArrayList<>();
        String organizationType = organizationMapper.getOrganizationTypeByChannel(organizationId, RequestUtils.getChannel());

        if ("zb".equals(organizationType) || "area".equals(organizationType)) {
            list = realtimeMapper.selectPerformanceInfluenceFactorsYear(organizationId, null, null, null, null, partnerType);
        } else if ("varea".equals(organizationType)) {
            list = realtimeMapper.selectPerformanceInfluenceFactorsYear(areaOrganizationId, organizationId, null, null, null, partnerType);
        } else if ("province".equals(organizationType)) {
            list = realtimeMapper.selectPerformanceInfluenceFactorsYear(areaOrganizationId, null, organizationId, null, null, partnerType);
        } else if ("company".equals(organizationType)) {
            list = realtimeMapper.selectPerformanceInfluenceFactorsYear(areaOrganizationId, null, null, organizationId, null, partnerType);
        } else if ("department".equals(organizationType)) {
            list = realtimeMapper.selectPerformanceInfluenceFactorsYear(areaOrganizationId, null, null, null, organizationId, partnerType);
        }
        return list;
    }

    @Override
    public List<PerformanceInfluenceVO> performanceAvg() {
        return realtimeMapper.selectPerformanceInfluenceFactors("ZB_Z", null, null, null, null, 0, RequestUtils.getBusinessGroup());
    }

    @Override
    public IPage<RecruitmentProcessVO> recruitmentProcess(RecruitmentProcessRequest request) {
        log.info("start RealtimeDataServiceImpl recruitmentProcess request:{}", request);
        Page<RecruitmentProcessVO> page = new Page<>(request.getPage(), request.getRows());
        String organizationType = organizationMapper.getOrganizationTypeByChannel(request.getOrganizationId(), RequestUtils.getChannel());
        if ("zb".equals(organizationType)) {
            request.setPosition("大区");
            List<RecruitmentProcessVO> list = mapper.selectRecruitmentProcess(page, request);
            list.forEach(r -> {
                request.setPosition("分公司");
                request.setOrganizationId(r.getRegionOrganizationId());
                List<RecruitmentProcessVO> recruitmentProcessVOS = mapper.selectRecruitmentProcess(request);
                if (CommonUtil.ListUtils.isNotEmpty(recruitmentProcessVOS)) {
                    for (RecruitmentProcessVO d : recruitmentProcessVOS) {
                        request.setPosition("营业所");
                        request.setOrganizationId(d.getBranchOrganizationId());
                        d.setChild(mapper.selectRecruitmentProcess(request));
                    }
                    r.setChild(recruitmentProcessVOS);
                }
            });
            request.setPosition("总部");
            request.setOrganizationId("ZB");
            RecruitmentProcessVO hj = mapper.selectSumRecruitmentProcess(request);
            if (Objects.nonNull(hj)) {
                hj.setRegionName("总计");
                hj.setPositionName("");
                hj.setEmployeeName("");
                hj.setOnboardTime(null);
                hj.setBranchCoName("");
                list.add(hj);
            }
            page.setRecords(list);
        } else if ("area".equals(organizationType)) {
            request.setPosition("大区");
            List<RecruitmentProcessVO> list = mapper.selectRecruitmentProcess(page, request);
            RecruitmentProcessVO hj = mapper.selectSumRecruitmentProcess(request);
            list.forEach(r -> {
                request.setPosition("分公司");
                request.setOrganizationId(r.getRegionOrganizationId());
                List<RecruitmentProcessVO> recruitmentProcessVOS = mapper.selectRecruitmentProcess(request);
                if (CommonUtil.ListUtils.isNotEmpty(recruitmentProcessVOS)) {
                    for (RecruitmentProcessVO d : recruitmentProcessVOS) {
                        request.setPosition("营业所");
                        request.setOrganizationId(d.getBranchOrganizationId());
                        d.setChild(mapper.selectRecruitmentProcess(request));
                    }
                    r.setChild(recruitmentProcessVOS);
                }
                r.setChild(recruitmentProcessVOS);
            });
            if (Objects.nonNull(hj)) {
                hj.setRegionName("总计");
                hj.setPositionName("");
                hj.setEmployeeName("");
                hj.setOnboardTime(null);
                hj.setBranchCoName("");
                list.add(hj);
            }
            page.setRecords(list);
        } else if ("company".equals(organizationType)) {
            request.setPosition("分公司");
            List<RecruitmentProcessVO> list = mapper.selectRecruitmentProcess(page, request);
            RecruitmentProcessVO hj = mapper.selectSumRecruitmentProcess(request);

            if (CommonUtil.ListUtils.isNotEmpty(list)) {
                for (RecruitmentProcessVO d : list) {
                    request.setPosition("营业所");
                    request.setOrganizationId(d.getBranchOrganizationId());
                    d.setChild(mapper.selectRecruitmentProcess(request));
                }
            }
            if (Objects.nonNull(hj)) {
                hj.setRegionName("总计");
                hj.setPositionName("");
                hj.setEmployeeName("");
                hj.setOnboardTime(null);
                hj.setBranchCoName("");
                list.add(hj);
            }
            page.setRecords(list);
        } else if ("department".equals(organizationType)) {
            request.setPosition("营业所");
            List<RecruitmentProcessVO> list = mapper.selectRecruitmentProcess(page, request);
            RecruitmentProcessVO hj = mapper.selectSumRecruitmentProcess(request);
            if (Objects.nonNull(hj)) {
                hj.setRegionName("总计");
                hj.setPositionName("");
                hj.setEmployeeName("");
                hj.setOnboardTime(null);
                hj.setBranchCoName("");
                list.add(hj);
            }
            page.setRecords(list);
        }
        return page;
    }

    @Override
    public List<RecruitmentProcessVO> recruitmentProcessList(RecruitmentProcessRequest request) {
        log.info("start RealtimeDataServiceImpl recruitmentProcessList request:{}", request);
        List<RecruitmentProcessVO> list = new ArrayList<>();
        String organizationType = organizationMapper.getOrganizationTypeByChannel(request.getOrganizationId(), RequestUtils.getChannel());
        if ("zb".equals(organizationType)) {
            request.setPosition("大区");
            List<RecruitmentProcessVO> zbList = mapper.selectRecruitmentProcess(request);
            for (RecruitmentProcessVO r : zbList) {
                list.add(r);
                request.setPosition("分公司");
                request.setOrganizationId(r.getRegionOrganizationId());
                List<RecruitmentProcessVO> recruitmentProcessVOS = mapper.selectRecruitmentProcess(request);
                if (CommonUtil.ListUtils.isNotEmpty(recruitmentProcessVOS)) {
                    for (RecruitmentProcessVO d : recruitmentProcessVOS) {
                        list.add(d);
                        request.setPosition("营业所");
                        request.setOrganizationId(d.getBranchOrganizationId());
                        list.addAll(mapper.selectRecruitmentProcess(request));
                    }
                }
            }
            request.setPosition("总部");
            request.setOrganizationId("ZB");
            RecruitmentProcessVO hj = mapper.selectSumRecruitmentProcess(request);
            hj.setRegionName("总计");
            hj.setPositionName("");
            hj.setEmployeeName("");
            hj.setOnboardTime(null);
            hj.setBranchCoName("");
            list.add(hj);
        } else if ("area".equals(organizationType)) {
            request.setPosition("大区");
            List<RecruitmentProcessVO> areaList = mapper.selectRecruitmentProcess(request);
            RecruitmentProcessVO hj = mapper.selectSumRecruitmentProcess(request);
            for (RecruitmentProcessVO r : areaList) {
                list.add(r);
                request.setPosition("分公司");
                request.setOrganizationId(r.getRegionOrganizationId());
                List<RecruitmentProcessVO> recruitmentProcessVOS = mapper.selectRecruitmentProcess(request);
                if (CommonUtil.ListUtils.isNotEmpty(recruitmentProcessVOS)) {
                    for (RecruitmentProcessVO d : recruitmentProcessVOS) {
                        list.add(d);
                        request.setPosition("营业所");
                        request.setOrganizationId(d.getBranchOrganizationId());
                        list.addAll(mapper.selectRecruitmentProcess(request));
                    }
                }
            }
            hj.setRegionName("总计");
            hj.setPositionName("");
            hj.setEmployeeName("");
            hj.setOnboardTime(null);
            hj.setBranchCoName("");
            list.add(hj);
        } else if ("company".equals(organizationType)) {
            request.setPosition("分公司");
            List<RecruitmentProcessVO> companyList = mapper.selectRecruitmentProcess(request);
            if (CommonUtil.ListUtils.isNotEmpty(companyList)) {
                for (RecruitmentProcessVO d : companyList) {
                    list.add(d);
                    request.setPosition("营业所");
                    request.setOrganizationId(d.getBranchOrganizationId());
                    list.addAll(mapper.selectRecruitmentProcess(request));
                }
                request.setPosition("分公司");
            }
            RecruitmentProcessVO hj = mapper.selectSumRecruitmentProcess(request);
            hj.setRegionName("总计");
            hj.setPositionName("");
            hj.setEmployeeName("");
            hj.setOnboardTime(null);
            hj.setBranchCoName("");
            list.add(hj);
        }
        return list;
    }

    @Override
    public IPage<IndicatorsVO> indicators(RecruitmentProcessRequest request) {
        Page<IndicatorsVO> page = new Page<>(request.getPage(), request.getRows());
        String organizationType = organizationMapper.getOrganizationTypeByChannel(request.getOrganizationId(), RequestUtils.getChannel());
        // 查询当月主推品
        List<ExportSkuVO> skuTemplate = mapper.selectZSku(request);
        if ("zb".equals(organizationType) || "area".equals(organizationType)) {
            List<IndicatorsVO> list = mapper.selectAreaIndicators(page, request);
            //批量获取主推品目标
            Set<String> regionIdSet = list.stream().map(i -> i.getRegionId()).collect(Collectors.toSet());
            List<ExportOrganizationSkuVO> skuList = mapper.selectSkuByRegionIdSet(regionIdSet, request.getYearMonth());
            Map<String, List<ExportSkuVO>> skuMap = new HashMap<>();
            if (CommonUtil.ListUtils.isNotEmpty(skuList)) {
                skuList.forEach(s -> skuMap.put(s.getOrganizationId(), s.getSkuList()));
            }
            //循环设置目标
            for (IndicatorsVO vo : list) {
                //获取当前组织主推品
                List<ExportSkuVO> itemSkuList = skuMap.get(vo.getRegionId());
                Map<String, ExportSkuVO> iskuMap = new HashMap<>();
                if (CommonUtil.ListUtils.isNotEmpty(itemSkuList)) {
                    itemSkuList.forEach(s -> iskuMap.put(s.getSku(), s));
                }
                vo.setSkuList(fixedSku(skuTemplate, iskuMap));
                //vo.setChildren(getCompanyIndicators(skuTemplate, vo.getRegionId(), null, request.getYearMonth()));
            }
            IndicatorsVO hj = mapper.selectSumIndicators(request);
            if (Objects.nonNull(hj)) {
                List<ExportSkuVO> hjSkuList = mapper.selectSkuByRegionId(hj.getRegionId(), request.getYearMonth());
                Map<String, ExportSkuVO> hjSkuMap = new HashMap<>();
                if (CommonUtil.ListUtils.isNotEmpty(hjSkuList)) {
                    hjSkuList.forEach(s -> hjSkuMap.put(s.getSku(), s));
                }
                hj.setSkuList(fixedSku(skuTemplate, hjSkuMap));
                hj.setRegionName("总计");
                list.add(hj);
            }
            page.setRecords(list);
        } else if ("company".equals(organizationType)) {
            List<IndicatorsVO> list = mapper.selectCompanyIndicators(page, null, request.getOrganizationId(), request.getYearMonth());
            IndicatorsVO hj = mapper.selectSumCompanyIndicators(request);
            if (Objects.nonNull(hj)) {
                hj.setRegionName("总计");
                list.add(hj);
            }

            Set<String> branchIdSet = list.stream().map(i -> i.getBranchId()).collect(Collectors.toSet());
            List<ExportOrganizationSkuVO> branchSkuList = mapper.selectSkuByBranchIdSet(branchIdSet, request.getYearMonth());
            Map<String, List<ExportSkuVO>> branchSkuMap = new HashMap<>();
            if (CommonUtil.ListUtils.isNotEmpty(branchSkuList)) {
                branchSkuList.forEach(s -> branchSkuMap.put(s.getOrganizationId(), s.getSkuList()));
            }

            for (IndicatorsVO vo : list) {
                List<ExportSkuVO> itemSkuList = branchSkuMap.get(vo.getBranchId());
                Map<String, ExportSkuVO> iskuMap = new HashMap<>();
                if (CommonUtil.ListUtils.isNotEmpty(itemSkuList)) {
                    itemSkuList.forEach(s -> iskuMap.put(s.getSku(), s));
                }
                vo.setSkuList(fixedSku(skuTemplate, iskuMap));
            }
            page.setRecords(list);
        } else if ("department".equals(organizationType)) {
            List<IndicatorsVO> list = mapper.selectDepartmentIndicators(page, null, null, request.getOrganizationId(), request.getYearMonth());
            IndicatorsVO hj = mapper.selectSumDepartmentIndicators(request);
            if (Objects.nonNull(hj)) {
                hj.setRegionName("总计");
                list.add(hj);
            }
            for (IndicatorsVO vo : list) {
                List<ExportSkuVO> skuList = mapper.selectSkuByDepartmentId(vo.getBranchId(), request.getYearMonth());
                Map<String, ExportSkuVO> skuMap = new HashMap<>();
                if (CommonUtil.ListUtils.isNotEmpty(skuList)) {
                    skuList.forEach(s -> skuMap.put(s.getSku(), s));
                }
                vo.setSkuList(fixedSku(skuTemplate, skuMap));
            }
            page.setRecords(list);
        }
        return page;
    }

    @Override
    public List<IndicatorsVO> indicatorsChild(RecruitmentProcessRequest request) {
        String organizationType = organizationMapper.getOrganizationTypeByChannel(request.getOrganizationId(), RequestUtils.getChannel());
        // 查询当月主推品
        List<ExportSkuVO> skuTemplate = mapper.selectZSku(request);
        if ("area".equals(organizationType)) {
            List<IndicatorsVO> list = mapper.selectCompanyIndicators(request.getOrganizationId(), null, request.getYearMonth());
            Set<String> branchIdSet = list.stream().map(i -> i.getBranchId()).collect(Collectors.toSet());
            List<ExportOrganizationSkuVO> branchSkuList = mapper.selectSkuByBranchIdSet(branchIdSet, request.getYearMonth());
            Map<String, List<ExportSkuVO>> branchSkuMap = new HashMap<>();
            if (CommonUtil.ListUtils.isNotEmpty(branchSkuList)) {
                branchSkuList.forEach(s -> branchSkuMap.put(s.getOrganizationId(), s.getSkuList()));
            }

            for (IndicatorsVO vo : list) {
                List<ExportSkuVO> itemSkuList = branchSkuMap.get(vo.getBranchId());
                Map<String, ExportSkuVO> iskuMap = new HashMap<>();
                if (CommonUtil.ListUtils.isNotEmpty(itemSkuList)) {
                    itemSkuList.forEach(s -> iskuMap.put(s.getSku(), s));
                }
                vo.setSkuList(fixedSku(skuTemplate, iskuMap));
            }
            return list;
        } else if ("company".equals(organizationType)) {
            List<IndicatorsVO> list = mapper.selectDepartmentIndicators(null, request.getOrganizationId(), null, request.getYearMonth());
            for (IndicatorsVO vo : list) {
                List<ExportSkuVO> skuList = mapper.selectSkuByDepartmentId(vo.getDepartmentId(), request.getYearMonth());
                Map<String, ExportSkuVO> skuMap = new HashMap<>();
                if (CommonUtil.ListUtils.isNotEmpty(skuList)) {
                    skuList.forEach(s -> skuMap.put(s.getSku(), s));
                }
                vo.setSkuList(fixedSku(skuTemplate, skuMap));
            }
            return list;
        }
        return null;
    }

    @Override
    public void exportIndicators(RecruitmentProcessRequest request, HttpServletResponse response) {
        log.info("start RealtimeDataServiceImpl exportIndicators request:{}", request);
        String organizationType = organizationMapper.getOrganizationTypeByChannel(request.getOrganizationId(), RequestUtils.getChannel());
        List<IndicatorsVO> data = new ArrayList<>();
        // 查询当月主推品
        List<ExportSkuVO> skuTemplate = mapper.selectZSku(request);
        if ("zb".equals(organizationType) || "area".equals(organizationType)) {
            List<IndicatorsVO> areaData = mapper.selectAreaIndicators(request);

            Set<String> regionIdSet = areaData.stream().map(i -> i.getRegionId()).collect(Collectors.toSet());
            List<ExportOrganizationSkuVO> skuList1 = mapper.selectSkuByRegionIdSet(regionIdSet, request.getYearMonth());
            Map<String, List<ExportSkuVO>> skuMap = new HashMap<>();
            if (CommonUtil.ListUtils.isNotEmpty(skuList1)) {
                skuList1.forEach(s -> skuMap.put(s.getOrganizationId(), s.getSkuList()));
            }
            Iterator<IndicatorsVO> it = areaData.iterator();
            while (it.hasNext()) {
                IndicatorsVO vo = it.next();
                List<ExportSkuVO> itemSkuList = skuMap.get(vo.getRegionId());
                Map<String, ExportSkuVO> iskuMap = new HashMap<>();
                if (CommonUtil.ListUtils.isNotEmpty(itemSkuList)) {
                    itemSkuList.forEach(s -> iskuMap.put(s.getSku(), s));
                }
                vo.setSkuList(fixedSku(skuTemplate, iskuMap));
                data.add(vo);
                List<IndicatorsVO> companyIndicators = getCompanyIndicators(skuTemplate, vo.getRegionId(), null, request.getYearMonth());
                data.addAll(companyIndicators);
                for (IndicatorsVO c : companyIndicators) {
                    data.addAll(c.getChild());
                }
            }
            IndicatorsVO hj = mapper.selectSumIndicators(request);
            if (Objects.nonNull(hj)) {
                List<ExportSkuVO> hjSkuList = mapper.selectSkuByRegionId(hj.getRegionId(), request.getYearMonth());
                Map<String, ExportSkuVO> hjSkuMap = new HashMap<>();
                if (CommonUtil.ListUtils.isNotEmpty(hjSkuList)) {
                    hjSkuList.forEach(s -> hjSkuMap.put(s.getSku(), s));
                }
                hj.setSkuList(fixedSku(skuTemplate, hjSkuMap));
                hj.setRegionName("总计");
                hj.setBranchName("");
                hj.setPositionTypeName("");
                hj.setEmployeeName("");
                hj.setOnboardDate(null);
                data.add(hj);
            }
        } else if ("company".equals(organizationType)) {
            List<IndicatorsVO> companyData = mapper.selectCompanyIndicators(null, request.getOrganizationId(), request.getYearMonth());
            if (CommonUtil.ListUtils.isNotEmpty(companyData)) {

                Set<String> branchIdSet = companyData.stream().map(i -> i.getBranchId()).collect(Collectors.toSet());
                List<ExportOrganizationSkuVO> branchSkuList = mapper.selectSkuByBranchIdSet(branchIdSet, request.getYearMonth());
                Map<String, List<ExportSkuVO>> branchSkuMap = new HashMap<>();
                if (CommonUtil.ListUtils.isNotEmpty(branchSkuList)) {
                    branchSkuList.forEach(s -> branchSkuMap.put(s.getOrganizationId(), s.getSkuList()));
                }

                Iterator<IndicatorsVO> it = companyData.iterator();
                while (it.hasNext()) {
                    IndicatorsVO next = it.next();
                    List<ExportSkuVO> itemSkuList = branchSkuMap.get(next.getBranchId());
                    Map<String, ExportSkuVO> iskuMap = new HashMap<>();
                    if (CommonUtil.ListUtils.isNotEmpty(itemSkuList)) {
                        itemSkuList.forEach(s -> iskuMap.put(s.getSku(), s));
                    }
                    next.setSkuList(fixedSku(skuTemplate, iskuMap));
                    data.add(next);
                    data.addAll(getDepartmentIndicators(skuTemplate, next.getRegionId(), next.getBranchId(), null, request.getYearMonth()));
                }
                IndicatorsVO hj = mapper.selectSumCompanyIndicators(request);
                if (Objects.nonNull(hj)) {
                    hj.setRegionName("总计");
                    hj.setBranchName("");
                    hj.setPositionTypeName("");
                    hj.setEmployeeName("");
                    hj.setOnboardDate(null);
                    data.add(hj);
                }
            }
        } else if ("department".equals(organizationType)) {
            data = mapper.selectDepartmentIndicators(null, null, request.getOrganizationId(), request.getYearMonth());
            IndicatorsVO hj = mapper.selectSumDepartmentIndicators(request);
            if (Objects.nonNull(hj)) {
                hj.setRegionName("总计");
                hj.setBranchName("");
                hj.setDepartmentName("");
                hj.setPositionTypeName("");
                hj.setEmployeeName("");
                hj.setOnboardDate(null);
                data.add(hj);
            }
            for (IndicatorsVO vo : data) {
                List<ExportSkuVO> skuList = mapper.selectSkuByDepartmentId(vo.getDepartmentId(), request.getYearMonth());
                Map<String, ExportSkuVO> skuMap = new HashMap<>();
                if (CommonUtil.ListUtils.isNotEmpty(skuList)) {
                    skuList.forEach(s -> skuMap.put(s.getSku(), s));
                }
                vo.setSkuList(fixedSku(skuTemplate, skuMap));
            }
        }

        List<ExcelExportEntity> exportEntities = new ArrayList<>();
        Field[] fields = PoiPublicUtil.getClassFields(IndicatorsVO.class);
        for (Field field : fields) {
            if (field.getAnnotation(Excel.class) != null) {
                Excel excel = field.getAnnotation(Excel.class);
                if (StringUtils.isNotBlank(excel.name())) {
                    // 是否主推list
                    if (!(field.getType() == java.util.List.class)) {
                        ExcelExportEntity excelEntity = new ExcelExportEntity(excel.name(), field.getName());
                        excelEntity.setOrderNum(Integer.parseInt(excel.orderNum()));
                        if (StringUtils.isNotEmpty(excel.groupName())) {
                            excelEntity.setGroupName(excel.groupName());
                        }
                        exportEntities.add(excelEntity);
                    } else {
                        for (int i = 0; i < skuTemplate.size(); i++) {
                            ExportSkuVO exportSku = skuTemplate.get(i);
                            ExcelExportEntity yj = new ExcelExportEntity("业绩", exportSku.getSku() + "yj");
                            yj.setGroupName(exportSku.getName());
                            yj.setOrderNum(Integer.parseInt(excel.orderNum()) + i * 3); // 导出顺序 13,16
                            exportEntities.add(yj);
                            ExcelExportEntity mb = new ExcelExportEntity("目标", exportSku.getSku() + "mb");
                            mb.setGroupName(exportSku.getName());
                            mb.setOrderNum(Integer.parseInt(excel.orderNum()) + i * 3 + 1); // 14,17
                            exportEntities.add(mb);
                            ExcelExportEntity dc = new ExcelExportEntity("业绩达成率", exportSku.getSku() + "dc");
                            dc.setGroupName(exportSku.getName());
                            dc.setOrderNum(Integer.parseInt(excel.orderNum()) + i * 3 + 2); // 15,18
                            exportEntities.add(dc);
                        }
                    }
                }
            }
        }
        List<Map<String, Object>> dataMap = new ArrayList<Map<String, Object>>();
        data.forEach(d -> {
            Map<String, Object> valMap = BeanUtils.objToMap(d);
            List<ExportSkuVO> skuList = d.getSkuList();
            if (CommonUtil.ListUtils.isNotEmpty(skuList)) {
                skuList.forEach(k -> {
                    valMap.put(k.getSku() + "yj", k.getAmount());
                    valMap.put(k.getSku() + "mb", k.getTarget());
                    valMap.put(k.getSku() + "dc", k.getRate());
                });
            }
            dataMap.add(valMap);
        });

        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), exportEntities, dataMap);
        EasyPoiUtil.downLoadExcel("业务核心指标报表.xls", response, workbook);
    }


    /**
     * 获取分公司
     *
     * @param skuTemplate
     * @param regionId    大区
     * @param branchId    分公司
     * @param yearMonth   年月
     * @return: java.util.List<com.wantwant.sfa.backend.realData.vo.IndicatorsVO>
     * @date: 8/11/22 10:59 AM
     */
    private List<IndicatorsVO> getCompanyIndicators(List<ExportSkuVO> skuTemplate, String regionId, String branchId, String yearMonth) {
        List<IndicatorsVO> list = mapper.selectCompanyIndicators(regionId, branchId, yearMonth);

        Set<String> branchIdSet = list.stream().map(i -> i.getBranchId()).collect(Collectors.toSet());
        List<ExportOrganizationSkuVO> branchSkuList = mapper.selectSkuByBranchIdSet(branchIdSet, yearMonth);
        Map<String, List<ExportSkuVO>> branchSkuMap = new HashMap<>();
        if (CommonUtil.ListUtils.isNotEmpty(branchSkuList)) {
            branchSkuList.forEach(s -> branchSkuMap.put(s.getOrganizationId(), s.getSkuList()));
        }

        for (IndicatorsVO vo : list) {
            List<ExportSkuVO> itemSkuList = branchSkuMap.get(vo.getBranchId());
            Map<String, ExportSkuVO> iskuMap = new HashMap<>();
            if (CommonUtil.ListUtils.isNotEmpty(itemSkuList)) {
                itemSkuList.forEach(s -> iskuMap.put(s.getSku(), s));
            }
            vo.setSkuList(fixedSku(skuTemplate, iskuMap));
            vo.setChild(getDepartmentIndicators(skuTemplate, vo.getRegionId(), vo.getBranchId(), null, yearMonth));
        }
        return list;
    }

    /**
     * 获取区域经理机构
     *
     * @param skuTemplate
     * @param regionId     大区
     * @param branchId     分公司
     * @param departmentId 区域经理机构id
     * @param yearMonth    年月
     * @return: java.util.List<com.wantwant.sfa.backend.realData.vo.IndicatorsVO>
     * @date: 8/11/22 10:59 AM
     */
    private List<IndicatorsVO> getDepartmentIndicators(List<ExportSkuVO> skuTemplate, String regionId, String branchId, String departmentId, String yearMonth) {
        List<IndicatorsVO> list = mapper.selectDepartmentIndicators(regionId, branchId, departmentId, yearMonth);

        Set<String> departmentIdSet = list.stream().map(i -> i.getDepartmentId()).collect(Collectors.toSet());
        List<ExportOrganizationSkuVO> departmentSkuList = mapper.selectSkuByDepartmentIdSet(departmentIdSet, yearMonth);
        Map<String, List<ExportSkuVO>> departmentSkuMap = new HashMap<>();
        if (CommonUtil.ListUtils.isNotEmpty(departmentSkuList)) {
            departmentSkuList.forEach(s -> departmentSkuMap.put(s.getOrganizationId(), s.getSkuList()));
        }

        for (IndicatorsVO vo : list) {
            List<ExportSkuVO> itemSkuList = departmentSkuMap.get(vo.getDepartmentId());
            Map<String, ExportSkuVO> iskuMap = new HashMap<>();
            if (CommonUtil.ListUtils.isNotEmpty(itemSkuList)) {
                itemSkuList.forEach(s -> iskuMap.put(s.getSku(), s));
            }
            vo.setSkuList(fixedSku(skuTemplate, iskuMap));
        }
        return list;
    }

    /**
     * 按照模版顺序
     *
     * @param skuTemplate 模版
     * @param skuMap
     * @return: java.util.List<com.wantwant.sfa.backend.display.vo.ExportSkuVO>
     * @date: 8/11/22 10:45 AM
     */
    private List<ExportSkuVO> fixedSku(List<ExportSkuVO> skuTemplate, Map<String, ExportSkuVO> skuMap) {
        List<ExportSkuVO> res = new ArrayList<>();
        for (ExportSkuVO s : skuTemplate) {
            ExportSkuVO vo = new ExportSkuVO();
            if (Objects.nonNull(s)) {
                BeanUtils.copyProperties(s, vo);
            }
            ExportSkuVO exportSkuVO = skuMap.get(vo.getSku());
            if (Objects.nonNull(exportSkuVO)) {
                BeanUtils.copyProperties(exportSkuVO, vo);
            } else {
                vo.setAmount(BigDecimal.ZERO);
                vo.setTarget(BigDecimal.ZERO);
                vo.setRate(BigDecimal.ZERO);
            }
            res.add(vo);
        }
        return res;
    }

    /**
     * 总监汇总
     *
     * @param area
     * @param r
     * @return: void
     * @date: 3/23/22 2:37 PM
     */
    private void collectDirector(RecruitmentDirectorProgressVO area, List<RecruitmentDirectorProgressVO> r) {
        area.setVacancyDays(r.stream().filter(f -> null != f.getVacancyDays()).mapToInt(a -> a.getVacancyDays()).sum());
        area.setRecruitmentCycle(r.stream().filter(f -> null != f.getRecruitmentCycle()).mapToInt(a -> a.getRecruitmentCycle()).sum());
        int resumeNum = r.stream().filter(f -> null != f.getResumeNum()).mapToInt(a -> a.getResumeNum()).sum();
        area.setResumeNum(resumeNum);
        int interviewNum = r.stream().filter(f -> null != f.getInterviewNum()).mapToInt(a -> a.getInterviewNum()).sum();
        area.setInterviewNum(interviewNum);
        // 面试率(面试人数/简历数)
        if (interviewNum > 0 && resumeNum > 0) {
            area.setInterviewRate(new BigDecimal(interviewNum).divide(new BigDecimal(resumeNum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
            area.setInterviewRateStr(area.getInterviewRate() + "%");
        } else {
            area.setInterviewRateStr("0.0%");
        }
        int interviewPassCnt = r.stream().filter(f -> null != f.getInterviewPassCnt()).mapToInt(a -> a.getInterviewPassCnt()).sum();
        area.setInterviewPassCnt(interviewPassCnt);
        // 面试通过率(面试通过人数/面试人数)
        if (interviewPassCnt > 0 && interviewNum > 0) {
            area.setInterviewPassRate(new BigDecimal(interviewPassCnt).divide(new BigDecimal(interviewNum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
            area.setInterviewPassRateStr(area.getInterviewPassRate() + "%");
        } else {
            area.setInterviewPassRateStr("0.0%");
        }
        area.setOnboardingCnt(r.stream().filter(f -> null != f.getOnboardingCnt()).mapToInt(a -> a.getOnboardingCnt()).sum());
        int applyOnboardCnt = r.stream().filter(f -> null != f.getApplyOnboardCnt()).mapToInt(a -> a.getApplyOnboardCnt()).sum();
        area.setApplyOnboardCnt(applyOnboardCnt);
        // 入职率(入职人数/面试通过人数)
        if (applyOnboardCnt > 0 && interviewPassCnt > 0) {
            area.setApplyOnboardRate(new BigDecimal(applyOnboardCnt).divide(new BigDecimal(interviewPassCnt), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
            area.setApplyOnboardRateStr(area.getApplyOnboardRate() + "%");
        } else {
            area.setApplyOnboardRateStr("0.0%");
        }
    }

    /**
     * 合伙人汇总
     *
     * @param area
     * @param v
     * @return: void
     * @date: 3/23/22 12:57 PM
     */
    private void collectPartner(RecruitmentPartnerProgressVO area, List<RecruitmentPartnerProgressVO> v) {
        int recruitNum = v.stream().mapToInt(r -> r.getRecruitNum()).sum();
        area.setRecruitNum(recruitNum);
        int onboardingNum = v.stream().mapToInt(r -> r.getOnboardingNum()).sum();
        area.setOnboardingNum(onboardingNum);
        area.setPendingNum(v.stream().mapToInt(r -> r.getPendingNum()).sum());
        // 招聘达成率(已入职人数/招聘需求人数)

        if (onboardingNum > 0 && recruitNum > 0) {
            area.setRecruitmentCompletionRate(new BigDecimal(onboardingNum).divide(new BigDecimal(recruitNum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
            area.setRecruitmentCompletionRateStr(area.getRecruitmentCompletionRate() + "%");
        } else {
            area.setRecruitmentCompletionRateStr("0.0%");
        }
        area.setPartNum(v.stream().mapToInt(r -> r.getPartNum()).sum());
        int resumeNum = v.stream().mapToInt(r -> r.getResumeNum()).sum();
        area.setResumeNum(resumeNum);
        int interviewNum = v.stream().mapToInt(r -> r.getInterviewNum()).sum();
        area.setInterviewNum(interviewNum);
        // 面试率(面试人数/简历数)
        if (interviewNum > 0 && resumeNum > 0) {
            area.setInterviewRate(new BigDecimal(interviewNum).divide(new BigDecimal(resumeNum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
            area.setInterviewRateStr(area.getInterviewRate() + "%");
        } else {
            area.setInterviewRateStr("0.0%");
        }
        int interviewsPassedNum = v.stream().mapToInt(r -> r.getInterviewsPassedNum()).sum();
        area.setInterviewsPassedNum(interviewsPassedNum);
        // 面试通过率(面试通过人数/面试人数)
        if (interviewsPassedNum > 0 && interviewNum > 0) {
            area.setInterviewsPassedRate(new BigDecimal(interviewsPassedNum).divide(new BigDecimal(interviewNum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
            area.setInterviewsPassedRateStr(area.getInterviewsPassedRate() + "%");
        } else {
            area.setInterviewsPassedRateStr("0.0%");
        }
        area.setTrialNum(v.stream().mapToInt(r -> r.getTrialNum()).sum());
        area.setTrialFailedNum(v.stream().mapToInt(r -> r.getTrialFailedNum()).sum());
        int trialPassedNum = v.stream().mapToInt(r -> r.getTrialPassedNum()).sum();
        area.setTrialPassedNum(trialPassedNum);
        // 试岗通过率(试岗通过人数/面试通过人数)
        if (trialPassedNum > 0 && interviewsPassedNum > 0) {
            area.setTrialPassedRate(new BigDecimal(trialPassedNum).divide(new BigDecimal(interviewsPassedNum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
            area.setTrialPassedRateStr(area.getTrialPassedRate() + "%");
        } else {
            area.setTrialPassedRateStr("0.0%");
        }
        int applyOnboardCnt = v.stream().mapToInt(r -> r.getApplyOnboardCnt()).sum();
        area.setApplyOnboardCnt(applyOnboardCnt);
        // 试岗通过入职率(入职人数/试岗通过人数)
        if (applyOnboardCnt > 0 && trialPassedNum > 0) {
            area.setProbationOnboardRate(new BigDecimal(applyOnboardCnt).divide(new BigDecimal(trialPassedNum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
            area.setProbationOnboardRateStr(area.getProbationOnboardRate() + "%");
        } else {
            area.setProbationOnboardRateStr("0.0%");
        }
        // 面试入职率(入职人数/面试通过人数)
        if (applyOnboardCnt > 0 && interviewsPassedNum > 0) {
            area.setInterviewOnboardRate(new BigDecimal(applyOnboardCnt).divide(new BigDecimal(interviewsPassedNum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
            area.setInterviewOnboardRateStr(area.getInterviewOnboardRate() + "%");
        } else {
            area.setInterviewOnboardRateStr("0.0%");
        }
    }

    /**
     * excel
     *
     * @param servletRequestAttributes
     * @param response
     * @param wb
     * @param fileName
     * @return: void
     * @date: 5/5/22 3:31 PM
     */
    private void writeExcel(ServletRequestAttributes servletRequestAttributes, HttpServletResponse response, Workbook wb, String fileName) {
        try {
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    /**
     * 拆分大区和分公司code数组 2022-6-9 chen
     */
    public void splitIdsForAreaAndCompany(RecruitmentProgressRequest request) {
        String jsonStr = request.getAreaAndcompanyIds();
        JSONArray jsonArray = JSON.parseArray(jsonStr);

        if (jsonArray == null || jsonArray.isEmpty()) {
            return;
        }

        ArrayList<String> areas = new ArrayList<>();
        ArrayList<String> companys = new ArrayList<>();
        for (Object array : jsonArray) {

            if (array.getClass() == JSONArray.class) {
                JSONArray jsonArray1 = (JSONArray) array;

                if (jsonArray1.size() >= 2) {
                    companys.add(String.valueOf(jsonArray1.get(1)));
                } else if (jsonArray1.size() == 1) {
                    areas.add(String.valueOf(jsonArray1.get(0)));
                }
            }
        }

        request.setAreaOrganizationIdArr(areas);
        request.setCompanyOrganizationIdArr(companys);
    }


    @Override
    public CustomerPortraitVo getBranchFigure(BranchTradeRequest request) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        log.info("start RealtimeDataServiceImpl getBranchInformation request:{}", request);
        CustomerPortraitVo customerPortraitVo = new CustomerPortraitVo();
        ArrayList<PortraitDetailVo> radarList = new ArrayList<>();

        BranchTradeInformationVo performance = realtimeMapper.selPerformance(request);
        if (Objects.nonNull(performance)) {
            Field[] fields = performance.getClass().getDeclaredFields();
            for (Field fiel : fields) {
                Radar radar = fiel.getAnnotation(Radar.class);
                if (null != radar) {
                    PortraitDetailVo portraitDetailVo = new PortraitDetailVo();
                    portraitDetailVo.setName(fiel.getAnnotation(ApiModelProperty.class).value());
                    radar.value();
                    PropertyDescriptor pd = new PropertyDescriptor(fiel.getName(), BranchTradeInformationVo.class);
                    BigDecimal value = (BigDecimal) pd.getReadMethod().invoke(performance);
                    // 利润率超过10% 给100%，其它 超过100% 给100%
                    if (portraitDetailVo.getName().equals("利润率")) {
                        BigDecimal val = value.multiply(new BigDecimal(10));
                        if (val.compareTo(new BigDecimal(1)) == 1) {
                            portraitDetailVo.setValue(new BigDecimal(1));
                        } else {
                            portraitDetailVo.setValue(val);
                        }
                    } else {
                        if (value.compareTo(new BigDecimal(1)) == 1) {
                            portraitDetailVo.setValue(new BigDecimal(1));
                        } else {
                            portraitDetailVo.setValue(value);
                        }
                    }
                    portraitDetailVo.setMax(new BigDecimal(1));
                    radarList.add(portraitDetailVo);
                }
            }
        } else {
            PortraitDetailVo portraitDetailVo = new PortraitDetailVo();
            radarList.add(portraitDetailVo);
        }

        customerPortraitVo.setFilingCustomer(realtimeMapper.selPartnersClientPortrait(request.getYearMonth(), request.getOrganizationId(), "1", request.getBusinessGroup()));
        customerPortraitVo.setDisplayCustomer(realtimeMapper.selPartnersClientPortrait(request.getYearMonth(), request.getOrganizationId(), "2", request.getBusinessGroup()));
        customerPortraitVo.setCustomerLevel(realtimeMapper.selPartnersClientPortrait(request.getYearMonth(), request.getOrganizationId(), "3", request.getBusinessGroup()));
        customerPortraitVo.setRadarMap(radarList);
        return customerPortraitVo;
    }

    @Override
    public List<CustomerMarketVo> queryCustomerRanking(BranchTradeRequest request) {
        List<CustomerMarketVo> list = realtimeMapper.queryMarketList(request);
        return list;
    }


    @Override
    public List<ProductLineAnalysisQueryVo> queryProductLineAnalysis(ProductLineAnalysisQueryRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        if (StringUtils.isBlank(request.getOrganizationId())) {
            throw new ApplicationException("请选择组织");
        }
        request.setOrganizationType(organizationMapper.getOrganizationType(request.getOrganizationId()));
        if ("line".equals(request.getQueryType())) {
            return commodityMapper.queryLineAnalysis(request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getOrganizationType(),
                    request.getBusinessGroup(),
                    request.getStartYearMonth(),
                    request.getEndYearMonth(),
                    request.getQueryInfo(),
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.FALSE);
        } else if ("sku".equals(request.getQueryType())) {
            return commodityMapper.querySkuAnalysis(request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getOrganizationType(),
                    request.getBusinessGroup(),
                    request.getStartYearMonth(),
                    request.getEndYearMonth(),
                    request.getQueryInfo(),
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.FALSE);
        } else if ("spu".equals(request.getQueryType())) {
            return commodityMapper.querySpuAnalysis(request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getOrganizationType(),
                    request.getBusinessGroup(),
                    request.getStartYearMonth(),
                    request.getEndYearMonth(),
                    request.getQueryInfo(),
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.FALSE);
        }
        return CollectionUtil.newArrayList();
    }

    @Override
    public void exportProductLineAnalysis(ProductLineAnalysisQueryRequest request, HttpServletRequest req, HttpServletResponse res) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<ProductLineAnalysisQueryVo> list = new ArrayList<>();
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        if (StringUtils.isBlank(request.getOrganizationId())) {
            throw new ApplicationException("请选择组织");
        }
        request.setOrganizationType(organizationMapper.getOrganizationType(request.getOrganizationId()));
        if ("line".equals(request.getQueryType())) {
            list = commodityMapper.queryLineAnalysis(request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getOrganizationType(),
                    request.getBusinessGroup(),
                    request.getStartYearMonth(),
                    request.getEndYearMonth(),
                    request.getQueryInfo(),
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.TRUE);
            List<ProductLineAnalysisLineExportVo> lineList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(list)) {
                lineList = BeanUtil.copyToList(list, ProductLineAnalysisLineExportVo.class);
            }
            ExportUtil.writeEasyExcelResponse(res, req, "产品线趋势分析列表-line", ProductLineAnalysisLineExportVo.class, lineList);
        } else if ("sku".equals(request.getQueryType())) {
            list = commodityMapper.querySkuAnalysis(request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getOrganizationType(),
                    request.getBusinessGroup(),
                    request.getStartYearMonth(),
                    request.getEndYearMonth(),
                    request.getQueryInfo(),
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.TRUE);


            List<ProductLineAnalysisSkuExportVo> skuList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(list)) {
                skuList = BeanUtil.copyToList(list, ProductLineAnalysisSkuExportVo.class);
            }
            ExportUtil.writeEasyExcelResponse(res, req, "产品线趋势分析列表-sku", ProductLineAnalysisSkuExportVo.class, skuList);
        } else if ("spu".equals(request.getQueryType())) {
            list = commodityMapper.querySpuAnalysis(request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getOrganizationType(),
                    request.getBusinessGroup(),
                    request.getStartYearMonth(),
                    request.getEndYearMonth(),
                    request.getQueryInfo(),
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.TRUE);
            List<ProductLineAnalysisSpuExportVo> spuList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(list)) {
                spuList = BeanUtil.copyToList(list, ProductLineAnalysisSpuExportVo.class);
            }
            ExportUtil.writeEasyExcelResponse(res, req, "产品线趋势分析列表-spu", ProductLineAnalysisSpuExportVo.class, spuList);
        }

    }

    @Override
    public List<ProductLineAnalysisSelectVo> productLineAnalysisSelectList(ProductLineAnalysisSelectRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        if ("line".equals(request.getQueryType())) {
            return commodityMapper.queryLineAnalysisSelectInfo(request.getBusinessGroup(), request.getOrganizationId(), request.getDateTypeId(), request.getStartYearMonth(), request.getEndYearMonth());
        } else if ("sku".equals(request.getQueryType())) {
            return commodityMapper.querySkuAnalysisSelectInfo(request.getBusinessGroup(), request.getOrganizationId(), request.getDateTypeId(), request.getStartYearMonth(), request.getEndYearMonth());
        } else if ("spu".equals(request.getQueryType())) {
            return commodityMapper.querySpuAnalysisSelectInfo(request.getBusinessGroup(), request.getOrganizationId(), request.getDateTypeId(), request.getStartYearMonth(), request.getEndYearMonth());
        }
        return CollectionUtil.newArrayList();
    }

    @Override
    public List<ProductInfoLinkedVo> queryProductInfoLinkedList(ProductInfoLinkedRequest request) {
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        //数据处理 line保留一级 spu保留2级
        List<ProductInfoLinkedVo> productInfoLinkedVos = commodityMapper.queryProductInfoLinkedList(request);
        productInfoLinkedVos.stream().forEach(lineInfoLinkedVo -> {
            if ("line".equals(request.getQueryType())) {
                lineInfoLinkedVo.setChildrenList(null);
            } else if ("spu".equals(request.getQueryType())) {
                lineInfoLinkedVo.getChildrenList().stream().forEach(spuInfoLinkedVo -> {
                    spuInfoLinkedVo.setChildrenList(null);
                });
            }
        });
        return productInfoLinkedVos;
    }

    @Override
    public IPage<ProductLineContrastAnalysisQueryVo> queryProductLineContrastAnalysis(ProductLineContrastAnalysisQueryRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        if (StringUtils.isBlank(request.getOrganizationId())) {
            throw new ApplicationException("请选择组织");
        }
        IPage<ProductLineContrastAnalysisQueryVo> page = new Page<>(request.getPage(), request.getRows());
        List<ProductLineContrastAnalysisQueryVo> list = new ArrayList<>();
        if ("line".equals(request.getQueryType())) {
            list = commodityMapper.queryLineContrastAnalysis(page, request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getBusinessGroup(),
                    request.getYearMonth(),
                    request.getQueryInfos(),
                    null,
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.FALSE);
        } else if ("sku".equals(request.getQueryType())) {
            list = commodityMapper.querySkuContrastAnalysis(page, request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getBusinessGroup(),
                    request.getYearMonth(),
                    request.getQueryInfos(),
                    null,
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.FALSE);
        } else if ("spu".equals(request.getQueryType())) {
            list = commodityMapper.querySpuContrastAnalysis(page, request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getBusinessGroup(),
                    request.getYearMonth(),
                    request.getQueryInfos(),
                    null,
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.FALSE);
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public void exportProductLineContrastAnalysis(ProductLineContrastAnalysisQueryRequest request, HttpServletRequest req, HttpServletResponse res) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        // 参数校验 -- 全组导出限制
        if (request.getDetailQuery() && request.getBusinessGroup() == 99) {
            if (StringUtils.isBlank(request.getOrganizationId()) && CollectionUtil.isEmpty(request.getBusinessGroupList())) {
                throw new ApplicationException("请选择组织或选择三个产品组");
//                request.setOrganizationType(organizationMapper.getOrganizationType(request.getOrganizationId()));
            } else if (CollectionUtil.isNotEmpty(request.getBusinessGroupList()) && request.getBusinessGroupList().size() > 3) {
                throw new ApplicationException("最多只能选择三个产品组");
            }
        } else {
            if (StringUtils.isBlank(request.getOrganizationId())) {
                request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
            }
        }

        List<ProductLineContrastAnalysisQueryVo> list;
        if ("line".equals(request.getQueryType())) {
            list = commodityMapper.queryLineContrastAnalysis(null, request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getBusinessGroup(),
                    request.getYearMonth(),
                    request.getQueryInfos(),
                    request.getBusinessGroupList(),
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.TRUE);
            List<ProductLineContrastAnalysisLineExportVo> lineList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(list)) {
                lineList = BeanUtil.copyToList(list, ProductLineContrastAnalysisLineExportVo.class);
            }
            ExportUtil.writeEasyExcelResponse(res, req, "产品线趋势对比分析列表-line", ProductLineContrastAnalysisLineExportVo.class, lineList);
        } else if ("sku".equals(request.getQueryType())) {
            list = commodityMapper.querySkuContrastAnalysis(null, request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getBusinessGroup(),
                    request.getYearMonth(),
                    request.getQueryInfos(),
                    request.getBusinessGroupList(),
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.TRUE);
            List<ProductLineContrastAnalysisSkuExportVo> skuList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(list)) {
                skuList = BeanUtil.copyToList(list, ProductLineContrastAnalysisSkuExportVo.class);
            }
            ExportUtil.writeEasyExcelResponse(res, req, "产品线趋势对比分析列表-sku", ProductLineContrastAnalysisSkuExportVo.class, skuList);
        } else if ("spu".equals(request.getQueryType())) {
            list = commodityMapper.querySpuContrastAnalysis(null, request.getDateTypeId(),
                    request.getOrganizationId(),
                    request.getBusinessGroup(),
                    request.getYearMonth(),
                    request.getQueryInfos(),
                    request.getBusinessGroupList(),
                    request.getSortType(),
                    request.getSortName(),
                    request.getDetailQuery(),
                    Boolean.TRUE);
            List<ProductLineContrastAnalysisSpuExportVo> spuList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(list)) {
                spuList = BeanUtil.copyToList(list, ProductLineContrastAnalysisSpuExportVo.class);
            }
            ExportUtil.writeEasyExcelResponse(res, req, "产品线趋势对比分析列表-spu", ProductLineContrastAnalysisSpuExportVo.class, spuList);
        }
    }

    @Override
    public List<ProductLineContrastAnalysisInfoVo> queryProductLineContrastAnalysisInfo(ProductLineContrastAnalysisInfoRequest request) {
        List<ProductLineContrastAnalysisInfoVo> infoVos = new ArrayList<>();
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        if (CollectionUtils.isEmpty(request.getQueryInfos())) {
            //补充产品信息 各产品组默认显示前5名 全组默认显示前10名
            List<String> rankingProducts = commodityMapper.queryRankingProduct(request);
            if (CollectionUtils.isEmpty(rankingProducts)) {
                return infoVos;
            }
            request.setQueryInfos(rankingProducts);
        }
        if (!CollectionUtils.isEmpty(request.getQueryInfos()) && request.getQueryInfos().size() > 10) {
            throw new ApplicationException("每次选择,请控制在 10 条内。");
        }
        //对比上期和对比同期
        request.setSameTerm(realTimeUtils.querySameTerm(request.getDateTypeId(), request.getYearMonth()));
        request.setPriorPeriod(realTimeUtils.queryPriorPeriod(request.getDateTypeId(), request.getYearMonth()));
        //查询当前时间维度的所有数据
        List<ProductLineContrastAnalysisIndicatorInfoVo> indicatorInfoVos = commodityMapper.queryOnePerformanceIndicatorInfo(request);
        if (CollectionUtils.isEmpty(indicatorInfoVos)) {
            return infoVos;
        }
        //补全数据
        String startDate = null;
        String endDate = request.getYearMonth();

        //根据productInfoId分组
        Map<String, List<ProductLineContrastAnalysisIndicatorInfoVo>> indicatorInfoMap = indicatorInfoVos.stream().collect(groupingBy(ProductLineContrastAnalysisIndicatorInfoVo::getProductInfoId));
        for (Map.Entry<String, List<ProductLineContrastAnalysisIndicatorInfoVo>> entry : indicatorInfoMap.entrySet()) {
            String productInfoId = entry.getKey();
            List<ProductLineContrastAnalysisIndicatorInfoVo> indicatorInfoList = entry.getValue();
            String productInfoDesc = indicatorInfoList.get(0).getProductInfoDesc();
            //初始化最小时间
            if (startDate == null || startDate.compareTo(indicatorInfoList.get(0).getYearMonth()) > 0) {
                startDate = indicatorInfoList.get(0).getYearMonth();
            }
            ProductLineContrastAnalysisInfoVo infoVo = new ProductLineContrastAnalysisInfoVo();
            infoVo.setProductInfoId(productInfoId);
            infoVo.setProductInfoDesc(productInfoDesc);
            infoVo.setTrendAnalysisList(indicatorInfoList);
            //补充上期和同期和本期
            Map<String, ProductLineContrastAnalysisIndicatorInfoVo> analysisIndicatorInfoVoMap = indicatorInfoList.stream().collect(Collectors.toMap(ProductLineContrastAnalysisIndicatorInfoVo::getYearMonth, Function.identity()));
            //本期
            ProductLineContrastAnalysisIndicatorInfoVo currentPeriodContrast = analysisIndicatorInfoVoMap.get(request.getYearMonth());
            if (Objects.isNull(currentPeriodContrast)) {
                infoVo.setCurrentPeriodYearMonth(request.getYearMonth());
                infoVo.setCurrentPeriodPerformanceIndicator(BigDecimal.ZERO);
                infoVo.setCurrentPeriodPerformanceIndicatorChainRatio(BigDecimal.ZERO);
                infoVo.setCurrentPeriodPerformanceIndicatorYearRatio(BigDecimal.ZERO);
            } else {
                infoVo.setCurrentPeriodYearMonth(currentPeriodContrast.getYearMonth());
                infoVo.setCurrentPeriodPerformanceIndicator(currentPeriodContrast.getPerformanceIndicator());
                infoVo.setCurrentPeriodPerformanceIndicatorChainRatio(currentPeriodContrast.getPerformanceIndicatorChainRatio());
                infoVo.setCurrentPeriodPerformanceIndicatorYearRatio(currentPeriodContrast.getPerformanceIndicatorYearRatio());
            }

            //同期
            ProductLineContrastAnalysisIndicatorInfoVo sameTermContrast = analysisIndicatorInfoVoMap.get(request.getSameTerm());
            if (Objects.isNull(sameTermContrast)) {
                infoVo.setSameTermYearMonth(request.getSameTerm());
                infoVo.setSameTermPerformanceIndicator(BigDecimal.ZERO);
                infoVo.setSameTermPerformanceIndicatorChainRatio(BigDecimal.ZERO);
                infoVo.setSameTermPerformanceIndicatorYearRatio(BigDecimal.ZERO);
            } else {
                infoVo.setSameTermYearMonth(sameTermContrast.getYearMonth());
                infoVo.setSameTermPerformanceIndicator(sameTermContrast.getPerformanceIndicator());
                infoVo.setSameTermPerformanceIndicatorChainRatio(sameTermContrast.getPerformanceIndicatorChainRatio());
                infoVo.setSameTermPerformanceIndicatorYearRatio(sameTermContrast.getPerformanceIndicatorYearRatio());
            }
            //上期
            ProductLineContrastAnalysisIndicatorInfoVo priorPeriodContrast = analysisIndicatorInfoVoMap.get(request.getPriorPeriod());
            if (Objects.isNull(priorPeriodContrast)) {
                infoVo.setPriorPeriodYearMonth(request.getPriorPeriod());
                infoVo.setPriorPeriodPerformanceIndicator(BigDecimal.ZERO);
                infoVo.setPriorPeriodPerformanceIndicatorChainRatio(BigDecimal.ZERO);
                infoVo.setPriorPeriodPerformanceIndicatorYearRatio(BigDecimal.ZERO);
            } else {
                infoVo.setPriorPeriodYearMonth(priorPeriodContrast.getYearMonth());
                infoVo.setPriorPeriodPerformanceIndicator(priorPeriodContrast.getPerformanceIndicator());
                infoVo.setPriorPeriodPerformanceIndicatorChainRatio(priorPeriodContrast.getPerformanceIndicatorChainRatio());
                infoVo.setPriorPeriodPerformanceIndicatorYearRatio(priorPeriodContrast.getPerformanceIndicatorYearRatio());
            }

            infoVos.add(infoVo);
        }
        //补全数据 默认是0
        //确认开始时间和结束时间内时间列表
        List<String> yearMonthList = realtimeMapper.queryYearMonthList(request.getDateTypeId(), startDate, endDate);
        infoVos.stream().forEach(info -> {
            Map<String, ProductLineContrastAnalysisIndicatorInfoVo> indicatorInfoVoMap = info.getTrendAnalysisList().stream().collect(Collectors.toMap(ProductLineContrastAnalysisIndicatorInfoVo::getYearMonth, Function.identity(), (v1, v2) -> v1));
            String productInfoId = info.getTrendAnalysisList().get(0).getProductInfoId();
            String productInfoDesc = info.getTrendAnalysisList().get(0).getProductInfoDesc();
            List<ProductLineContrastAnalysisIndicatorInfoVo> indicatorInfoList = yearMonthList.stream().map(yearMonth -> {
                ProductLineContrastAnalysisIndicatorInfoVo indicatorInfoVo = indicatorInfoVoMap.get(yearMonth);
                if (Objects.isNull(indicatorInfoVo)) {
                    indicatorInfoVo = new ProductLineContrastAnalysisIndicatorInfoVo();
                    indicatorInfoVo.setYearMonth(yearMonth);
                    indicatorInfoVo.setProductInfoId(productInfoId);
                    indicatorInfoVo.setProductInfoDesc(productInfoDesc);
                    indicatorInfoVo.setPerformanceIndicator(BigDecimal.ZERO);
                    indicatorInfoVo.setPerformanceIndicatorChainRatio(BigDecimal.ZERO);
                    indicatorInfoVo.setPerformanceIndicatorYearRatio(BigDecimal.ZERO);
                }
                return indicatorInfoVo;
            }).sorted(Comparator.comparing(ProductLineContrastAnalysisIndicatorInfoVo::getYearMonth)).collect(Collectors.toList());
            info.setTrendAnalysisList(indicatorInfoList);
        });

        //根据本期的业绩指标从大到小排序
        infoVos = infoVos.stream().sorted(Comparator.comparing(infoVo -> infoVo.getCurrentPeriodPerformanceIndicator(), Comparator.reverseOrder())).collect(Collectors.toList());
        return infoVos;
    }

    /**
     * 人员达成趋势分析-合伙人达成分析
     *
     * @param yearMonth
     * @param organizationId
     * @return: java.util.List<com.wantwant.sfa.backend.realData.vo.PersonnelVO>
     * @date: 3/14/23 2:26 PM
     */
    @Override
    public List<PersonnelVO> personnelAchievement(String yearMonth, String organizationId, String partnerType) {
        String organizationType = organizationMapper.getOrganizationType(organizationId);
        if (StringUtils.isNotEmpty(yearMonth)) {
            yearMonth = yearMonth.replaceAll("-", "");
        }
        List<PersonnelVO> res = new ArrayList<>();
        //当前组织数据
        List<PersonnelVO> curList = realtimeMapper.selectByOrganizationId(yearMonth, organizationId, partnerType);
        Map<String, List<PersonnelVO>> curMap = Maps.newHashMap();
        if (CommonUtil.ListUtils.isNotEmpty(curList)) {
            curMap = curList.stream().collect(groupingBy(p -> p.getItemSupplypriceRange()));
        }
        //下级组织数据
        List<PersonnelVO> list = realtimeMapper.selectPersonnelList(yearMonth, organizationId, partnerType);
        Map<String, List<PersonnelVO>> rangeMap = Maps.newHashMap();
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            rangeMap = list.stream().collect(groupingBy(p -> p.getItemSupplypriceRange()));
        }
        //营业所显示当前
        if ("department".equals(organizationType)) {
            for (Map.Entry<String, List<PersonnelVO>> curEntry : curMap.entrySet()) {
                List<PersonnelVO> cur = curEntry.getValue();
                PersonnelVO vo = new PersonnelVO();
                vo.setItemSupplypriceRange(curEntry.getKey());
                if (CommonUtil.ListUtils.isNotEmpty(cur)) {
                    PersonnelVO c = cur.get(0);
                    vo.setPartnerNums(c.getPartnerNums());
                    vo.setPartnerRate(c.getPartnerRate());
                    vo.setOnboardDays(c.getOnboardDays());
                    vo.setOnboardMiddleDays(c.getOnboardMiddleDays());
                    vo.setPartnerNumsMom(c.getPartnerNumsMom());
                    vo.setPartnerNumsYoy(c.getPartnerNumsYoy());
                    vo.setOrganizationId(c.getOrganizationId());
                    vo.setOrganizationName(c.getOrganizationName());
                }
                //范围排序
                if (vo.getItemSupplypriceRange().contains("以上")) {
                    vo.setOrder(99999L);
                } else {
                    vo.setOrder(Long.valueOf(Pattern.compile("[^0-9]").matcher(vo.getItemSupplypriceRange()).replaceAll("").trim()));
                }
                res.add(vo);
            }
            res.sort(Comparator.comparing(PersonnelVO::getOrder));
        } else {
            for (Map.Entry<String, List<PersonnelVO>> entry : rangeMap.entrySet()) {
                List<PersonnelVO> children = entry.getValue();
                PersonnelVO vo = new PersonnelVO();
                vo.setItemSupplypriceRange(entry.getKey());
                if (MapUtils.isNotEmpty(curMap)) {
                    List<PersonnelVO> cur = curMap.get(entry.getKey());
                    if (CommonUtil.ListUtils.isNotEmpty(cur)) {
                        PersonnelVO c = cur.get(0);
                        vo.setPartnerNums(c.getPartnerNums());
                        vo.setPartnerRate(c.getPartnerRate());
                        vo.setOnboardDays(c.getOnboardDays());
                        vo.setOnboardMiddleDays(c.getOnboardMiddleDays());
                        vo.setPartnerNumsMom(c.getPartnerNumsMom());
                        vo.setPartnerNumsYoy(c.getPartnerNumsYoy());
                    }
                }
                children.sort(Comparator.comparing(PersonnelVO::getOrganizationId));
                vo.setChildren(children);
                //范围排序
                if (vo.getItemSupplypriceRange().contains("以上")) {
                    vo.setOrder(99999L);
                } else {
                    vo.setOrder(Long.valueOf(Pattern.compile("[^0-9]").matcher(vo.getItemSupplypriceRange()).replaceAll("").trim()));
                }
                res.add(vo);
            }
            res.sort(Comparator.comparing(PersonnelVO::getOrder));
        }
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            PersonnelVO hj = realtimeMapper.selectHjByOrganizationId(yearMonth, organizationId, partnerType);
            if (Objects.nonNull(hj)) {
                hj.setItemSupplypriceRange("总计");
            }
            res.add(hj);
        }

        return res;
    }

    @Override
    public List<PersonnelMonthVO> personnelAchievementMonth(String organizationId, String partnerType) {
        List<PersonnelMonthVO> res = new ArrayList<>();
        List<PersonnelVO> curList = realtimeMapper.selectByOrganizationId(null, organizationId, partnerType);
        //范围模版(保证每行顺序)
        Map<String, List<PersonnelVO>> rangeMap = curList.stream().collect(groupingBy(p -> p.getItemSupplypriceRange()));
        List<String> rangeList = rangeMap.keySet().stream().collect(Collectors.toCollection(ArrayList::new));
        List<PersonnelVO> personnelListTemplate = new ArrayList();
        rangeList.forEach(r -> {
            PersonnelVO vo = new PersonnelVO();
            vo.setItemSupplypriceRange(r);
            //设置排序值
            if (vo.getItemSupplypriceRange().contains("以上")) {
                vo.setOrder(99999L);
            } else {
                vo.setOrder(Long.valueOf(Pattern.compile("[^0-9]").matcher(vo.getItemSupplypriceRange()).replaceAll("").trim()));
            }
            personnelListTemplate.add(vo);
        });
        //按年月分组得到行
        Map<String, List<PersonnelVO>> personMap = Maps.newHashMap();
        if (CommonUtil.ListUtils.isNotEmpty(curList)) {
            personMap = curList.stream().collect(groupingBy(p -> p.getTheYearMonth()));
        }
        for (Map.Entry<String, List<PersonnelVO>> curEntry : personMap.entrySet()) {
            List<PersonnelVO> cur = curEntry.getValue();
            Map<String, PersonnelVO> curMap = new HashMap<>();
            cur.forEach(c -> curMap.put(c.getItemSupplypriceRange(), c));

            PersonnelMonthVO pm = new PersonnelMonthVO();
            pm.setTheYearMonth(curEntry.getKey());
            List<PersonnelVO> newlist = new ArrayList<>();
            personnelListTemplate.forEach(p -> {
                PersonnelVO vo = new PersonnelVO();
                vo.setItemSupplypriceRange(p.getItemSupplypriceRange());
                vo.setOrder(p.getOrder());
                PersonnelVO vo1 = curMap.get(p.getItemSupplypriceRange());
                if (Objects.nonNull(vo1)) {
                    BeanUtil.copyProperties(vo1, vo, "order");
                }
                newlist.add(vo);
            });
            newlist.sort(Comparator.comparing(PersonnelVO::getOrder));
            pm.setList(newlist);
            res.add(pm);
        }
        res.sort(Comparator.comparing(PersonnelMonthVO::getTheYearMonth));
        return res;
    }

    /**
     * 人员达成趋势分析-组织对比的趋势表
     *
     * @param organizationId
     * @param partnerType
     * @return: java.util.List<com.wantwant.sfa.backend.realData.vo.OrganizationMonthVO>
     * @date: 5/26/23 9:14 AM
     */
    @Override
    public List<OrganizationMonthVO> organizationAchievementMonth(String organizationId, String partnerType) {
        List<OrganizationMonthItemVO> item1 = realtimeMapper.selectOrganizationAchievementMonth(organizationId, partnerType, "2023-1");
        List<OrganizationMonthItemVO> item2 = realtimeMapper.selectOrganizationAchievementMonth(organizationId, partnerType, "2023-2");
        List<OrganizationMonthItemVO> item3 = realtimeMapper.selectOrganizationAchievementMonth(organizationId, partnerType, "2023-3");
        List<OrganizationMonthItemVO> item4 = realtimeMapper.selectOrganizationAchievementMonth(organizationId, partnerType, "2023-4");
        Map<String, OrganizationMonthItemVO> itemMap2 = new HashMap<>();
        item2.forEach(i -> itemMap2.put(i.getBizOrganizationId(), i));
        Map<String, OrganizationMonthItemVO> itemMap3 = new HashMap<>();
        item3.forEach(i -> itemMap3.put(i.getBizOrganizationId(), i));
        Map<String, OrganizationMonthItemVO> itemMap4 = new HashMap<>();
        item4.forEach(i -> itemMap4.put(i.getBizOrganizationId(), i));

        List<OrganizationMonthVO> list = new ArrayList<>();
        item1.forEach(i -> {
            OrganizationMonthVO vo = new OrganizationMonthVO();
            BeanUtils.copyProperties(i, vo);
            CeoBusinessOrganizationViewEntity organizationViewEntity = ceoBusinessOrganizationViewMapper.selectByOrganizationId(i.getBizOrganizationId(), 3);
            if (Objects.nonNull(organizationViewEntity)) {
                vo.setArea(organizationViewEntity.getOrgName3());
                vo.setAreaOrganizationId(organizationViewEntity.getOrgId3());
                vo.setCompany(organizationViewEntity.getOrgName2());
                vo.setCompanyOrganizationId(organizationViewEntity.getOrgId2());
                vo.setDepartmentName(organizationViewEntity.getDepartmentName());
                vo.setDepartmentId(organizationViewEntity.getDepartmentId());
                vo.setOrganizationType(organizationViewEntity.getOrganizationType());
            }
            List<OrganizationMonthItemVO> itemList = new ArrayList<>();
            itemList.add(i);
            itemList.add(itemMap2.get(i.getBizOrganizationId()));
            if (Objects.nonNull(itemMap3.get(i.getBizOrganizationId()))) {
                itemList.add(itemMap3.get(i.getBizOrganizationId()));
            }
            if (Objects.nonNull(itemMap4.get(i.getBizOrganizationId()))) {
                itemList.add(itemMap4.get(i.getBizOrganizationId()));
            }
            vo.setItemList(itemList);
            list.add(vo);
        });

        String organizationType = organizationMapper.getOrganizationTypeByChannel(organizationId, 3);

        List<OrganizationMonthVO> list1 = new ArrayList<>();
        if ("zb".equals(organizationType)) {
            Map<String, List<OrganizationMonthVO>> companyMap = list.stream().filter(f -> "company".equals(f.getOrganizationType())).collect(groupingBy(d -> d.getAreaOrganizationId()));
            Map<String, List<OrganizationMonthVO>> departmentIdMap = list.stream().filter(f -> "department".equals(f.getOrganizationType())).collect(groupingBy(d -> d.getCompany()));
            list.forEach(l -> {
                if ("area".equals(l.getOrganizationType())) {
                    l.setChildren(companyMap.get(l.getBizOrganizationId()));
                } else if ("company".equals(l.getOrganizationType())) {
                    l.setChildren(departmentIdMap.get(l.getCompany()));
                }
            });
            list1 = list.stream().filter(f -> "area".equals(f.getOrganizationType())).collect(Collectors.toList());
        } else if ("area".equals(organizationType)) {
            Map<String, List<OrganizationMonthVO>> departmentIdMap = list.stream().filter(f -> "department".equals(f.getOrganizationType())).collect(groupingBy(d -> d.getCompany()));
            list.forEach(l -> {
                if ("company".equals(l.getOrganizationType())) {
                    l.setChildren(departmentIdMap.get(l.getCompany()));
                }
            });
            list1 = list.stream().filter(f -> "company".equals(f.getOrganizationType())).collect(Collectors.toList());
        } else {
            list1 = list;
        }
        return list1;
    }

    @Override
    public void exportPersonnelAchievement(String yearMonth, String organizationId, String partnerType) {
        String organizationType = organizationMapper.getOrganizationType(organizationId);
        if (StringUtils.isNotEmpty(yearMonth)) {
            yearMonth = yearMonth.replaceAll("-", "");
        }
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        List<PersonnelVO> list = Lists.newArrayList();
        //分公司显示当前
        if ("company".equals(organizationType)) {
            list = realtimeMapper.selectByOrganizationId(yearMonth, organizationId, partnerType);
        } else {
            list = realtimeMapper.selectPersonnelList(yearMonth, organizationId, partnerType);
        }
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            PersonnelVO hj = realtimeMapper.selectHjByOrganizationId(yearMonth, organizationId, partnerType);
            if (Objects.nonNull(hj)) {
                hj.setItemSupplypriceRange("总计");
            }
            list.add(hj);
        }

        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), PersonnelVO.class, list);
        EasyPoiUtil.downLoadExcel("合伙人达成分析导出.xls", response, workbook);
    }

    /**
     * 人员达成趋势分析-组织人员达成分析
     *
     * @param yearMonth
     * @param organizationId
     * @return: java.util.List<com.wantwant.sfa.backend.realData.vo.OrganizationAchievementVO>
     * @date: 3/14/23 4:08 PM
     */
    @Override
    public List<OrganizationAchVO> organizationAchievement(String yearMonth, String organizationId) {
        Integer type = null;
        Integer curType = null;
        String organizationType = organizationMapper.getOrganizationType(organizationId);
        // 总部:查询分公司数量,大区:查询分公司下级查询营业所
        if ("zb".equals(organizationType)) {
            type = 0;
        } else if ("area".equals(organizationType)) {
            curType = 1;
        }
        if (StringUtils.isNotEmpty(yearMonth)) {
            yearMonth = yearMonth.replaceAll("-", "");
        }
        List<OrganizationAchVO> res = new ArrayList<>();
        List<OrganizationAchVO> list = realtimeMapper.selectOrganizationAchievements(yearMonth, organizationId, type);
        //当前组织数据
        List<OrganizationAchVO> curList = realtimeMapper.selectAchievementByOrganizationId(yearMonth, organizationId, curType);
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            Map<String, List<OrganizationAchVO>> rangeMap = list.stream().collect(groupingBy(o -> o.getItemSupplypriceRange()));
            Map<String, List<OrganizationAchVO>> curMap = Maps.newHashMap();
            if (CommonUtil.ListUtils.isNotEmpty(curList)) {
                curMap = curList.stream().collect(groupingBy(p -> p.getItemSupplypriceRange()));
            }
            for (Map.Entry<String, List<OrganizationAchVO>> entry : rangeMap.entrySet()) {
                OrganizationAchVO vo = new OrganizationAchVO();
                List<OrganizationAchVO> children = entry.getValue();
                vo.setItemSupplypriceRange(entry.getKey());
                if (MapUtils.isNotEmpty(curMap)) {
                    List<OrganizationAchVO> cur = curMap.get(entry.getKey());
                    if (CommonUtil.ListUtils.isNotEmpty(cur)) {
                        OrganizationAchVO c = cur.get(0);
                        vo.setBizOrganizationNums(c.getBizOrganizationNums());
                        vo.setBizOrganizationNumsRate(c.getBizOrganizationNumsRate());
                        vo.setCustomerNumsAvg(c.getCustomerNumsAvg());
                        vo.setCustomerAll(c.getCustomerAll());
                        vo.setGoalAchievementRateAvg(c.getGoalAchievementRateAvg());
                        vo.setOrganizationNumsMom(c.getOrganizationNumsMom());
                        vo.setOrganizationNumsYoy(c.getOrganizationNumsYoy());
                    }
                }
                children.sort(Comparator.comparing(OrganizationAchVO::getOrganizationId));
                vo.setChildren(children);
                //排序
                if (vo.getItemSupplypriceRange().contains("以上")) {
                    vo.setOrder(99999L);
                } else {
                    vo.setOrder(Long.valueOf(Pattern.compile("[^0-9]").matcher(vo.getItemSupplypriceRange()).replaceAll("").trim()));
                }
                res.add(vo);
            }
            res.sort(Comparator.comparing(OrganizationAchVO::getOrder));
            OrganizationAchVO hj = new OrganizationAchVO();
            hj.setItemSupplypriceRange("总计");
            hj.setBizOrganizationNums(list.stream().collect(Collectors.summingInt(OrganizationAchVO::getBizOrganizationNums)));
            hj.setBizOrganizationNumsRate(list.stream().collect(CollectorUtil.averagingBigDecimal(OrganizationAchVO::getBizOrganizationNumsRate, 2, BigDecimal.ROUND_HALF_UP)));
            hj.setCustomerNumsAvg(list.stream().collect(Collectors.averagingInt(OrganizationAchVO::getCustomerNumsAvg)).intValue());
            hj.setCustomerAll(list.stream().collect(Collectors.summingInt(OrganizationAchVO::getCustomerAll)));
            hj.setGoalAchievementRateAvg(list.stream().collect(CollectorUtil.averagingBigDecimal(OrganizationAchVO::getGoalAchievementRateAvg, 2, BigDecimal.ROUND_HALF_UP)));
            hj.setOrganizationNumsMom(list.stream().collect(CollectorUtil.averagingBigDecimal(OrganizationAchVO::getOrganizationNumsMom, 2, BigDecimal.ROUND_HALF_UP)));
            hj.setOrganizationNumsYoy(list.stream().collect(CollectorUtil.averagingBigDecimal(OrganizationAchVO::getOrganizationNumsYoy, 2, BigDecimal.ROUND_HALF_UP)));
            res.add(hj);
        }
        return res;
    }

    @Override
    public void exportOrganizationAchievement(String yearMonth, String organizationId) {
        Integer type = null;
        String organizationType = organizationMapper.getOrganizationType(organizationId);
        if ("zb".equals(organizationType)) {
            type = 0;
        } else if ("area".equals(organizationType)) {
            type = 1;
        }
        if (StringUtils.isNotEmpty(yearMonth)) {
            yearMonth = yearMonth.replaceAll("-", "");
        }
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();

        List<OrganizationAchVO> list = realtimeMapper.selectOrganizationAchievements(yearMonth, organizationId, type);
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            OrganizationAchVO hj = new OrganizationAchVO();
            hj.setItemSupplypriceRange("总计");
            hj.setBizOrganizationNums(list.stream().collect(Collectors.summingInt(OrganizationAchVO::getBizOrganizationNums)));
            hj.setBizOrganizationNumsRate(list.stream().collect(CollectorUtil.averagingBigDecimal(OrganizationAchVO::getBizOrganizationNumsRate, 2, BigDecimal.ROUND_HALF_UP)));
            hj.setCustomerNumsAvg(list.stream().collect(Collectors.averagingInt(OrganizationAchVO::getCustomerNumsAvg)).intValue());
            hj.setCustomerAll(list.stream().collect(Collectors.summingInt(OrganizationAchVO::getCustomerAll)));
            hj.setGoalAchievementRateAvg(list.stream().collect(CollectorUtil.averagingBigDecimal(OrganizationAchVO::getGoalAchievementRateAvg, 2, BigDecimal.ROUND_HALF_UP)));
            hj.setOrganizationNumsMom(list.stream().collect(CollectorUtil.averagingBigDecimal(OrganizationAchVO::getOrganizationNumsMom, 2, BigDecimal.ROUND_HALF_UP)));
            hj.setOrganizationNumsYoy(list.stream().collect(CollectorUtil.averagingBigDecimal(OrganizationAchVO::getOrganizationNumsYoy, 2, BigDecimal.ROUND_HALF_UP)));
            list.add(hj);
        }
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), OrganizationAchVO.class, list);
        EasyPoiUtil.downLoadExcel("组织人员达成分析导出.xls", response, workbook);

    }

    @Override
    public WeeklyProductVO getWeeklyProduct(WeeklyRequest request) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        WeeklyProductVO vo = new WeeklyProductVO();
        //营业所显示分公司库存到货(只有渠道分公司关系)
        String organizationId = request.getOrganizationId();
        String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
        if ("department".equals(organizationType)) {
            organizationId = organizationMapper.getOrganizationParentId(request.getOrganizationId());
        }
        List<InventoryDTO> inventoryList = realtimeMapper.selectInventoryNum(request.getEndDate(), organizationId, loginInfo.getBusinessGroup());
        Map<String, String> inventoryMap = Maps.newHashMap();
        inventoryList.forEach(i -> inventoryMap.put(i.getSku(), i.getInventoryNum()));
        List<ProductSkuDataVO> decline = realtimeMapper.selectSkuDataList(request, 1);
        List<ProductSkuDataVO> rise = realtimeMapper.selectSkuDataList(request, 2);
        decline.forEach(d -> d.setInventoryNum(inventoryMap.get(d.getSku())));
        rise.forEach(r -> r.setInventoryNum(inventoryMap.get(r.getSku())));
        vo.setDecline(decline);
        vo.setRise(rise);
        //主推品
        TradeGoodReq tradeGoodReq = new TradeGoodReq();
        tradeGoodReq.setYearMonth(request.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        tradeGoodReq.setOrganizationId(request.getOrganizationId());
        vo.setMain(realtimeMapper.getMainProductsThis(tradeGoodReq));

        //预计下周到货
        vo.setExpect(realtimeMapper.selectSkuExpectNum(request.getEndDate(), organizationId, loginInfo.getBusinessGroup()));
        return vo;
    }

    /**
     * 核心指标1～4
     *
     * @param request
     * @return: com.wantwant.sfa.backend.realData.vo.IndicatorDataVO
     * @date: 4/24/23 11:33 AM
     */
    @Override
    public IndicatorDataVO getIndicators(WeeklyRequest request) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setBusinessGroup(loginInfo.getBusinessGroup());
        IndicatorDataVO vo = new IndicatorDataVO();
        RealtimeDataFourVO dataFourVO = realtimeMapper.selectIndicators(request);
        if (Objects.nonNull(dataFourVO)) {
            vo.setItemsSupplyTotalCm(dataFourVO.getItemssupplytotalCm());
            vo.setSaleGoalAchievementRate(dataFourVO.getSaleGoalAchievementRate().multiply(new BigDecimal(100)));
            vo.setMonthRingRatio(dataFourVO.getMonthRingRatio().multiply(new BigDecimal(100)));
            vo.setMonthAchievement(dataFourVO.getMonthAchievement().multiply(new BigDecimal(100)));
            vo.setSaleGoal(dataFourVO.getSaleGoal());
            vo.setDepartmentEstablishmentNum(dataFourVO.getDepartmentNum());
            vo.setPartnerSalesAvgHb(dataFourVO.getBranchPerCapitaPerformanceYearOnYear());
            vo.setPartnerSalesTb(dataFourVO.getTotalCustomerNumsRate());
            vo.setManagementAvgGmv(dataFourVO.getManagementAvgGmv());
            RealtimeDataFourVO dataFourVO3 = realtimeMapper.selectDataFourV3(request);
            if (Objects.nonNull(dataFourVO3)) {
                vo.setDepartmentSalesAvg(dataFourVO3.getCityManagerAvgItemsSupplyTotal());
                vo.setDepartmentSalesAvgHb(dataFourVO3.getCityManagerAvgItemsSupplyTotalRingRatio());
                vo.setDepartmentSalesAvgTb(dataFourVO3.getCityManagerAvgItemsSupplyTotalYearOnYear());
                vo.setPartnerKdRate(dataFourVO3.getPartnerBillingRate());
                vo.setPartnerSalesAvgTb(dataFourVO3.getItemsSupplyTotalCmAvgYearOnYear());
                vo.setPartnerSalesHb(dataFourVO3.getTradingCustomersAllRingRatio());
            }
            vo.setPendingNum(dataFourVO.getPendingNum());
            vo.setItemsSupplyTotalCmAvg(dataFourVO.getItemssupplytotalCmAvg());
            vo.setTotalCustomerNums(dataFourVO.getTotalCustomerNums());
            vo.setFilingCustomerCm(dataFourVO.getFillingCustomerNumberCm());
            vo.setNoOrderCustomerCm(dataFourVO.getNoOrderCustomerNumberCm());
        }
        List<EmpInfoVO> trading = realtimeMapper.selectTradingPartners(request, 1);
        List<EmpInfoVO> unTrading = realtimeMapper.selectTradingPartners(request, 2);

        List<EmpInfoVO> weeklyEmpList = employeeMapper.selectWeeklyEmpList(request.getEndDate().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), request.getOrganizationId(), loginInfo.getBusinessGroup());
        if (CommonUtil.ListUtils.isNotEmpty(weeklyEmpList)) {
            vo.setInPost(weeklyEmpList.stream().filter(f -> f.getState() == 1).collect(Collectors.toList()));
            vo.setPending(weeklyEmpList.stream().filter(f -> f.getState() == 2).collect(Collectors.toList()));
            vo.setInPostNum(vo.getInPost().size());
            vo.setPendingNum(vo.getPending().size());
        }
        vo.setTrading(trading);
        vo.setUnTrading(unTrading);
        vo.setTradingPartnersNums(trading.size());
        vo.setUnTradingPartnersNums(unTrading.size());
        return vo;
    }

    @Override
    public WeeklyLineSpuVO getWeeklyLineSpu(WeeklyRequest request) {
        WeeklyLineSpuVO vo = new WeeklyLineSpuVO();
        vo.setLineList(realtimeMapper.selectLineList(request));
        vo.setSpuList(realtimeMapper.selectSpuList(request));
        return vo;
    }

    /**
     * 合伙人情况
     *
     * @param request
     * @return: java.util.List<com.wantwant.sfa.backend.realData.vo.EmpRankingVO>
     * @date: 5/9/23 4:59 PM
     */
    @Override
    public List<EmpRankingVO> getEmpRanking(WeeklyRequest request) {
        return realtimeMapper.getEmpRanking(request);
    }


    /**
     * 处理季度日期
     *
     * @param yearMonth
     * @return
     */
    public String publicHandleDate(String yearMonth) {
        String replace = "";
        if (yearMonth.contains("-01")) {
            yearMonth = yearMonth.replace("-01", "-04");
            Integer lastYear = Integer.valueOf(yearMonth.substring(0, 4)) - 1;
            replace = yearMonth.replace(yearMonth.substring(0, 4), String.valueOf(lastYear));
        } else if (yearMonth.contains("-04")) {
            replace = yearMonth.replace("-04", "-01");
        } else if (yearMonth.contains("-07")) {
            replace = yearMonth.replace("-07", "-02");
        } else if (yearMonth.contains("-10")) {
            replace = yearMonth.replace("-10", "-03");
        } else {
            replace = yearMonth;
        }
        return replace;
    }

    @Override
    public MainProductsDataAllVo getMainProductsList(String dateTypeId, String yearMonth, String organizationId) {
        log.info("start RealtimeDateServerImpl getMainProductsList dateTypeId:[{}] yearMonth:{},organizationId:{}", dateTypeId, yearMonth, organizationId);
        MainProductsDataAllVo mainProductsDataAllVo = new MainProductsDataAllVo();
        TradeGoodReq request = new TradeGoodReq();
        request.setOrganizationId(organizationId);
        request.setYearMonth(yearMonth);
        request.setDateTypeId(dateTypeId);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        //获取所有主推品图片
        List<MainProductsVo> mainProductsPictures = mainProductMapper.getMainProductsPicture();
        Map<String, String> productPictureMap;
        if (!CollectionUtils.isEmpty(mainProductsPictures)) {
            productPictureMap = mainProductsPictures.stream().collect(Collectors.toMap(MainProductsVo::getMainProductsName, MainProductsVo::getPicture));
        } else {
            productPictureMap = Maps.newHashMap();
        }
        //查询实时数据
        List<MainProductsVo> mainProducts = realtimeMapper.getMainProductsNew(request);
        if (!CollectionUtils.isEmpty(mainProducts)) {
            mainProducts.forEach(m -> {
                m.setPicture(productPictureMap.get(m.getMainProductsName()));
            });

            mainProductsDataAllVo.setMainProducts(mainProducts);
        }

        return mainProductsDataAllVo;
    }

    /**
     * @param yearMonth
     * @param organizationId
     * @param monthlyType    月报类型数据(1.月报;2.季报;3.季报的月报) --目前月报这块不展示 暂时不需要管
     * @return
     */
    @Override
    public MainProductsDataAllVo getMainProductsList(String yearMonth, String organizationId, int monthlyType) {
        log.info("start RealtimeDateServerImpl getMainProductsList yearMonth:{},organizationId:{}", yearMonth, organizationId);
        if (monthlyType == 2) {
            yearMonth = publicHandleDate(yearMonth);
        }
        MainProductsDataAllVo mainProductsDataAllVo = new MainProductsDataAllVo();
        TradeGoodReq request = new TradeGoodReq();
        request.setOrganizationId(organizationId);
        request.setYearMonth(yearMonth);
        request.setMonthlyType(monthlyType);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());

        ArrayList<MainProductsVo> mainProductsVosMonth = new ArrayList<>();
        ArrayList<MainProductsVo> mainProductsVosYear = new ArrayList<>();
        //获取所有主推品图片
        List<MainProductsVo> mainProductsPicture = mainProductMapper.getMainProductsPicture();
        //月主推品
        List<MainProductsVo> monthMainProducts = realtimeMapper.getMainProducts(request);
        request.setYear(request.getYearMonth().substring(0, 4));
        //年主推品
        List<MainProductsVo> yearMainProducts = realtimeMapper.getMainProductsYear(request);
        if (CommonUtil.ListUtils.isNotEmpty(monthMainProducts)) {
            mainProductsPicture.forEach(m -> {
                monthMainProducts.forEach(mon -> {
                    if (mon.getMainProductsName().contains(m.getMainProductsName())) {
                        mon.setPicture(m.getPicture());
                    }
                });
            });
            mainProductsVosMonth.addAll(monthMainProducts);
        }
        if (CommonUtil.ListUtils.isNotEmpty(yearMainProducts)) {
            mainProductsPicture.forEach(m -> {
                yearMainProducts.forEach(year -> {
                    if (year.getMainProductsName().contains(m.getMainProductsName())) {
                        year.setPicture(m.getPicture());
                    }
                });
            });
            mainProductsVosYear.addAll(yearMainProducts);
        }
        mainProductsDataAllVo.setMonthMainProducts(mainProductsVosMonth);
        mainProductsDataAllVo.setYearMainProducts(mainProductsVosYear);

        for (MainProductsVo y : yearMainProducts) {
            MainProductsYearVo mainProductsYearVo = new MainProductsYearVo();
            List<MainProductsVo> mainProductsByName = realtimeMapper.getMainProductsByName(organizationId, yearMonth.substring(0, 4), yearMonth, y.getMainProductsName(), 1, request.getBusinessGroup());
            List<String> month = mainProductsByName.stream().map(m -> m.getMonth()).collect(Collectors.toList());
            List<BigDecimal> mainProductsSalegoal = mainProductsByName.stream().map(m -> m.getMainProductsSalegoal()).collect(Collectors.toList());
            List<BigDecimal> mainProductsItemsSupplyTotal = mainProductsByName.stream().map(m -> m.getMainProductsItemsSupplyTotal()).collect(Collectors.toList());
            List<BigDecimal> mainProductsSaleGoalAchievementRate = mainProductsByName.stream().map(m -> m.getMainProductsSaleGoalAchievementRate()).collect(Collectors.toList());
            List<BigDecimal> yearOnYearGrowthRate = mainProductsByName.stream().map(m -> m.getYearOnYearGrowthRate()).collect(Collectors.toList());
            mainProductsYearVo.setMonth(month);
            mainProductsYearVo.setMainProductsSalegoal(mainProductsSalegoal);
            mainProductsYearVo.setMainProductsItemsSupplyTotal(mainProductsItemsSupplyTotal);
            mainProductsYearVo.setMainProductsSaleGoalAchievementRate(mainProductsSaleGoalAchievementRate);
            mainProductsYearVo.setYearOnYearGrowthRate(yearOnYearGrowthRate);
            y.setMainProductsYear(mainProductsYearVo);
        }
        return mainProductsDataAllVo;
    }

    @Override
    public XSSFWorkbook getMainProductsListExport(String dateTypeId, String yearMonth, String organizationId) {
        log.info("start RealtimeDateServerImpl getMainProductsListExport yearMonth:{},organizationId:{}", yearMonth, organizationId);
        //主推品数据
        List<MainProductExport> mainProductExports = new ArrayList<>();

        TradeGoodReq request = new TradeGoodReq();
        request.setIsYearMainProducts(1);
        request.setYear(yearMonth.substring(0, 4));
        request.setOrganizationId(organizationId);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setYearMonth(yearMonth);
        request.setDateTypeId(dateTypeId);
        //找出这一年的所有主推品
        List<MainProductsVo> yearMainProducts = realtimeMapper.getMainProductsYear(request);
        if (CommonUtil.ListUtils.isNotEmpty(yearMainProducts)) {
            yearMainProducts.forEach(y -> {
                List<MainProductsVo> monthAll = realtimeMapper.getMainProductsModel(organizationId, yearMonth.substring(0, 4), yearMonth, null, 0, request.getBusinessGroup());
                MainProductExport mainProductExport = new MainProductExport();
                mainProductExport.setMainProductsName(y.getMainProductsName());
                //根据年份与主推品名称查出月份主推
                List<MainProductsVo> mainProductsByName = realtimeMapper.getMainProductsByName(organizationId, yearMonth.substring(0, 4), yearMonth, y.getMainProductsName(), 1, request.getBusinessGroup());
                if (CommonUtil.ListUtils.isNotEmpty(mainProductsByName)) {
                    monthAll.forEach(m -> {
                        //主推品月份返回数据  找出主推品数据放入所有 月份下的主推品
                        List<MainProductsVo> collect = mainProductsByName.stream().filter(month -> month.getMonth().equals(m.getMonth())).collect(Collectors.toList());
                        if (CommonUtil.ListUtils.isNotEmpty(collect)) {
                            MainProductsVo mainProductsVo = collect.get(0);
                            BeanUtils.copyProperties(mainProductsVo, m);
                        }
                    });
                }
                mainProductExport.setMonthList(monthAll);
                mainProductExports.add(mainProductExport);
            });
        }
        List<List<Object>> listList = solveMainProductsListExportList(mainProductExports);
        return export(listList);
    }

    List<List<Object>> solveMainProductsListExportList(List<MainProductExport> list) {
        log.info("start RealtimeDateServerImpl solveMainProductsListExportList list:{}", list);
        List<List<Object>> listList = new ArrayList<>();
        List<Object> headList = new ArrayList<>();
        headList.add("主推品名称");
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            MainProductExport mainProductExport = list.get(0);
            if (null != mainProductExport) {
                List<MainProductsVo> labelList = mainProductExport.getMonthList();
                if (CommonUtil.ListUtils.isNotEmpty(labelList)) {
                    for (MainProductsVo lable : labelList) {
                        headList.add(lable.getMonth() + "(业绩目标)");
                        headList.add(lable.getMonth() + "(盘价业绩)");
                        headList.add(lable.getMonth() + "(目标达成率)");
                        headList.add(lable.getMonth() + "(业绩同比增长率)");
                    }
                }
                listList.add(headList);
                for (MainProductExport mainProduct : list) {
                    ArrayList<Object> headListTwo = new ArrayList<>();
                    headListTwo.add(mainProduct.getMainProductsName());
                    List<MainProductsVo> monthList = mainProduct.getMonthList();
                    if (CommonUtil.ListUtils.isNotEmpty(monthList)) {
                        for (MainProductsVo m : monthList) {
                            if (null != m.getMainProductsSalegoal()) {
                                headListTwo.add(m.getMainProductsSalegoal());
                            } else {
                                headListTwo.add(0);
                            }
                            if (null != m.getMainProductsItemsSupplyTotal()) {
                                headListTwo.add(m.getMainProductsItemsSupplyTotal());
                            } else {
                                headListTwo.add(0);
                            }
                            if (null != m.getMainProductsSaleGoalAchievementRate()) {
                                headListTwo.add(m.getMainProductsSaleGoalAchievementRate());
                            } else {
                                headListTwo.add(0);
                            }
                            if (null != m.getYearOnYearGrowthRate()) {
                                headListTwo.add(m.getYearOnYearGrowthRate());
                            } else {
                                headListTwo.add(0);
                            }

                        }
                    }
                    listList.add(headListTwo);
                }
            } else {
                listList.add(headList);
            }
        }
        return listList;
    }

    public static XSSFWorkbook export(List<List<Object>> list) {
        log.info("start export list:{}", list);
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();//创建excel工作簿对象
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            xssfWorkbook = setSheetValue(list, xssfWorkbook);
        }
        return xssfWorkbook;
    }


    /**
     * 修改表格内容
     *
     * @param list         表头数组
     * @param xssfWorkbook 工作簿
     */
    private static XSSFWorkbook setSheetValue(List<List<Object>> list, XSSFWorkbook xssfWorkbook) {
        log.info("start RealtimeDateServerImpl setSheetValue list:{}", list);
        XSSFSheet sheet = null;
        sheet = xssfWorkbook.createSheet("工作表");
        for (int i = 0; i < list.get(0).size(); i++) {
            sheet.setColumnWidth(i, 4000);//列宽
        }

        //表头
        XSSFRow xssfRowTwo = sheet.createRow(0);  //创建第1行
        for (int i = 0; i < list.get(0).size(); i++) {
            if (null != list.get(0).get(i)) {
                String s = " ";
                xssfRowTwo.createCell(i).setCellValue(list.get(0).get(i) + s);
            }
        }

        for (int i = 1; i < list.size(); i++) {
            //创建项目  （公司下的信息）
            XSSFRow xssfRowTwoProject = sheet.createRow(i);  //创建行
            for (int j = 0; j < list.get(i).size(); j++) {
                xssfRowTwoProject.createCell(j).setCellValue(String.valueOf(list.get(i).get(j)));
            }
        }
        return xssfWorkbook;
    }

    @Override
    public SpecialChenVo querySpecialChenData(TradeGoodReq request) {
        log.info("start RealtimeDataServiceImpl querySpecialChenData request:{}", request);
        if (null == request.getIsPage()) {
            request.setIsPage(1);
        }
      /*  if(RequestUtils.getLoginInfo().getOrganizationType().equals("zb")&&request.getOrganizationId().equals("ZB_Z")){
            List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(), RequestUtils.getLoginInfo());
            if(CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)){
                request.setOrganizationId(employeeOrganizationId.get(0));
            }
        }*/
        String month = request.getYearMonth() + "-01";
        request.setYearMonth(LocalDate.parse(month).minusMonths(1).toString().substring(0, 7));
        ArrayList<SpecialChenProductVo> list = new ArrayList<>();
        //ResultsDataVo resultsDataVo = realtimeDataMapper.queryResultsDate(request);

        List<CeoBusinessOrganizationPositionRelation> ceoBusinessOrganizationEntity = relationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", request.getOrganizationId()).eq("business_group", RequestUtils.getBusinessGroup()).eq("channel", 3));

        SpecialChenVo special = new SpecialChenVo();
        // SpecialChenVo specialChenVo =null;
        if (CommonUtil.ListUtils.isNotEmpty(ceoBusinessOrganizationEntity)) {
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganization = ceoBusinessOrganizationEntity.get(0);
            SpecialChenVo specialChenVo = realtimeMapper.selDisplayskuTotal(request.getOrganizationId(), request.getYearMonth(), ceoBusinessOrganization.getPositionTypeId(), RequestUtils.getLoginInfo().getBusinessGroup());
            if (Objects.nonNull(specialChenVo)) {
                BeanUtils.copyProperties(specialChenVo, special);
                List<SpecialChenProductVo> specialChenProductVos = realtimeMapper.selDisplayskuDetail(request.getOrganizationId(), request.getYearMonth(), ceoBusinessOrganization.getPositionTypeId(), RequestUtils.getLoginInfo().getBusinessGroup(), request.getIsPage(), request.getOffset(), request.getLimit());
                if (CommonUtil.ListUtils.isNotEmpty(specialChenProductVos)) {
                    special.setSpecialChenList(specialChenProductVos);
                    List<SpecialChenProductVo> specialChenProductVosTotal = realtimeMapper.selDisplayskuDetail(request.getOrganizationId(), request.getYearMonth(), ceoBusinessOrganization.getPositionTypeId(), RequestUtils.getLoginInfo().getBusinessGroup(), 0, request.getOffset(), request.getLimit());
                    special.setCount(specialChenProductVosTotal.size());
                } else {
                    special.setSpecialChenList(list);
                }
            } else {
                special.setSpecialChenList(list);
            }
        } else {
            special.setSpecialChenList(list);
        }
        return special;
    }

    @Override
    public List<RealtimeOrganizationVo> getRealtimeValue(String employeeId, String dateTypeId, String yearMonth, String year, String organizationId, String value) {
        log.info("start RealtimeDataServiceImpl getRealtimeValue yearMonth:{},organizationId:{},value:{}", yearMonth, organizationId, value);
        List<RealtimeOrganizationVo> reatimeValue = null;
        if (StringUtils.isNotBlank(employeeId) && StringUtils.isBlank(organizationId)) {
            List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(employeeId, RequestUtils.getLoginInfo());
            if (CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)) {
                organizationId = employeeOrganizationId.get(0);
            }
        }
        if (StringUtils.isBlank(year)) {
            Integer positionTypeId = organizationMapper.getPositionTypeIdByOrganizationId(organizationId);
            if (Objects.isNull(positionTypeId)) {
                throw new ApplicationException("组织id查找失败");
            }
            positionTypeId = positionTypeId.intValue() == 7 ? 4 : positionTypeId;//(SFA的总部是7，大数据是4)
            //岗位类型,1-总督导,2-区域总监,3-合伙人,4-数位运营部主管,5-测试助手,6-C端运营课主管,7-总部,8-客服,9-稽核,10-区域经理,11-省区总监,12-大区总监
            if (StringUtils.isBlank(dateTypeId)) {
                dateTypeId = "10";
            }
            reatimeValue = realtimeMapper.getRealtimeValue(realTimeUtils.getNearMonth(dateTypeId, yearMonth), organizationId, positionTypeId, value, RequestUtils.getBusinessGroup());
        } else {
            RealTimeYearRequest timeYearRequest = new RealTimeYearRequest();
            timeYearRequest.setYear(year);
            timeYearRequest.setOrganizationId(organizationId);
            RealTimeYearVo emplyeeDate = realtimeMapper.getResultsPerformanceYear(timeYearRequest);
            if (Objects.isNull(emplyeeDate)) {
                throw new ApplicationException(BizExceptionLanguageEnum.DATA_GENERATION_IN_PROGRESS.getTextMsg());
            }
            reatimeValue = realtimeMapper.getReatimeValueYear(year, emplyeeDate.getPositionTypeId(), value, RequestUtils.getBusinessGroup());
        }
        return reatimeValue;
    }

    @Override
    public List<CeoBusinessOrganizationEntity> getPartJobOrganization(String person) {
        List<CeoBusinessOrganizationEntity> partJobOrganization = organizationMapper.getPartJobOrganization(person, RequestUtils.getBusinessGroup(), RequestUtils.getChannel());

        if (!CollectionUtils.isEmpty(partJobOrganization)) {
            partJobOrganization.stream().filter(f -> !f.getOrganizationType().equals("area") && !f.getOrganizationType().equals("zb")).forEach(e -> {
                List<String> path = new ArrayList<>();

                String organizationParentId = organizationMapper.getOrganizationParentId(e.getOrganizationId());
                while (StringUtils.isNotBlank(organizationParentId)) {
                    path.add(organizationParentId);
                    organizationParentId = organizationMapper.getOrganizationParentId(organizationParentId);
                }
                Collections.reverse(path);
                e.setPath(path);
            });
        }


        return partJobOrganization;
    }

    @Override
    public List<RealtimeMainProductVo> getMainProductListNew(RealtimeMainProductNewRequest request) {
        log.info("start RealtimeDataServiceImpl getMainProductListNew request:{}", request);
        List<RealtimeMainProductVo> resultList = new ArrayList<>();
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        //查询岗位信息 1:战区，2：分公司，3：合伙人，10：区域经理，11：省区，12：大区
        Integer positionTypeId = realtimeMapper.getMainOrganizationTypeNew(request.getDateTypeId(), request.getYearMonth(), request.getOrganizationId(), request.getBusinessGroup());
        if (Objects.isNull(positionTypeId)) {
            return resultList;
        }
        request.setOrganizationType(positionTypeId);

        //不展示下级
        if (request.getIsNextRealtime() == 0) {
            RealtimeMainProductVo realtimeMainProductVo = realtimeMapper.getMainProductNew(request);
            //查下级
            List<RealtimeMainProductVo> realtimeMainProductVos = realtimeMapper.getMainProductNextNew(request);
            if (Objects.nonNull(realtimeMainProductVo)) {
                resultList.add(realtimeMainProductVo);
            }
            if (!CollectionUtils.isEmpty(realtimeMainProductVos)) {
                resultList.addAll(realtimeMainProductVos);
            }
            if (CollectionUtils.isEmpty(resultList)) {
                return resultList;
            }
            resultList.forEach(result -> {
                if (StringUtils.isNotBlank(result.getOrganizationId()) && result.getOrganizationId().equals(request.getOrganizationId())) {
                    result.setAreaName("合计");
                }
            });
        } else {
            List<RealtimeMainProductVo> realtimeMainProductVos = realtimeMapper.getMainProductNextNew(request);
            if (CollectionUtils.isEmpty(realtimeMainProductVos)) {
                return resultList;
            }
            resultList.addAll(realtimeMainProductVos);
        }
        //获取主推品模版
        List<MainProductVo> mainProductMonthTemplate = realtimeMapper.getMainProductTemplateNew(request.getDateTypeId(), request.getYearMonth(), request.getBusinessGroup());

        resultList.forEach(l -> {
            ArrayList<MainProductVo> mainProductVos = new ArrayList<>();
            BeanUtils.copyProperties(mainProductMonthTemplate, mainProductVos, MainProductVo.class, MainProductVo.class);
            if (CommonUtil.ListUtils.isNotEmpty(l.getMainList())) {
                List<MainProductVo> mainList = l.getMainList();
                mainProductVos.forEach(m -> {
                    List<MainProductVo> mainProduct = mainList.stream().filter(main -> main.getMainProductName().equals(m.getMainProductName())).collect(Collectors.toList());
                    if (CommonUtil.ListUtils.isNotEmpty(mainProduct)) {
                        //MainProductVo mainProductVo1 = mainProduct.get(0);
                        m.setBiddingPerformance(mainProduct.get(0).getBiddingPerformance());
                        m.setGoal(mainProduct.get(0).getGoal());
                        m.setGoalAchievementRate(mainProduct.get(0).getGoalAchievementRate());
                        m.setSequentialGrowthRate(mainProduct.get(0).getSequentialGrowthRate());
                        m.setYearGrowthRate(mainProduct.get(0).getYearGrowthRate());
                        m.setInvoicingPartnersNumber(mainProduct.get(0).getInvoicingPartnersNumber());
                        m.setPartnerBillingRate(mainProduct.get(0).getPartnerBillingRate());
                        m.setBillingRateSequentialIncreaseRate(mainProduct.get(0).getBillingRateSequentialIncreaseRate());
                        m.setPerCapitaPerformance(mainProduct.get(0).getPerCapitaPerformance());
                        m.setPerCapitaPerformanceGrowthRate(mainProduct.get(0).getPerCapitaPerformanceGrowthRate());
                        m.setTradingClients(mainProduct.get(0).getTradingClients());
                        m.setNumberOfDisplayCustomers(mainProduct.get(0).getNumberOfDisplayCustomers());
                        m.setGuestUnitPrice(mainProduct.get(0).getGuestUnitPrice());
                        m.setMainProductsCashPerformance(mainProduct.get(0).getMainProductsCashPerformance());
                        if (StringUtils.isNotBlank(m.getGoalAchievementRate())) {
                            m.setMainProductsSaleGoalAchievementColor(realTimeUtils.saleGoalAchievementColor(request.getDateTypeId(), request.getYearMonth(), new BigDecimal(m.getGoalAchievementRate())));
                        }
                    }
                });
            }
            l.setMainList(mainProductVos);
        });


        return resultList;
    }

    @Override
    public IPage<RealtimeMainProductVo> getMainProductTileList(RealtimeMainProductTileNewRequest request) {
        log.info("start RealtimeDataServiceImpl getMainProductTileList request:{}", request);
        List<RealtimeMainProductVo> resultList = new ArrayList<>();
        Page<RealtimeMainProductVo> page = new Page<>(request.getPage(), request.getRows());

        String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
        if (StringUtils.isBlank(organizationType)) {
            return page;
        }
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setOrganizationType(organizationType);

        int startNumber = (request.getPage() - 1) * request.getRows();
        int pageSize = request.getRows();

        //查询类型:0：查询所有 1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人
        if (request.getSearchType() == 0) {
            //下级组织查询时分页
            Page<OrganizationAllChildInfoVo> pageOrganizations = new Page<>(request.getPage(), request.getRows());
            if (!RankingListPositionEnums.ZB.getOrganizationType().equals(organizationType)) {

                resultList = realtimeMapper.getMainProductTileListSearchAll(request, startNumber, pageSize);
                page.setTotal(realtimeMapper.getMainProductTileListSearchAllCount(request));
            } else {
                //总部已经在sql级别限制-->查所有
//                resultList = realtimeMapper.getMainProductTileList(page, request);
                resultList = realtimeMapper.getMainProductTileListLimit(request, startNumber, pageSize);
                page.setTotal(realtimeMapper.getMainProductTileListLimitCount(request));
            }
            generateMainProductVo(request, resultList);
            page.setRecords(resultList);
            return page;
        }

        //其他在查询主推品时分页
        if (request.getSearchType() == 1) {
            List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + request.getOrganizationId());
            log.info("【next org code】codes:{}", nextOrgCode);
            if (CollectionUtil.isEmpty(nextOrgCode)) {
                return page;
            }
            request.setNextOrgCodes(nextOrgCode);
        }
//        resultList = realtimeMapper.getMainProductTileList(page, request);
        resultList = realtimeMapper.getMainProductTileListLimit(request, startNumber, pageSize);
        page.setTotal(realtimeMapper.getMainProductTileListLimitCount(request));
        generateMainProductVo(request, resultList);
        page.setRecords(resultList);
        return page;
    }

    @Override
    public void downTileList(RealtimeMainProductTileNewRequest request, HttpServletRequest req, HttpServletResponse res) {
        log.info("start RealtimeDataServiceImpl downTileList request:{}", request);
        List<RealtimeMainProductVo> resultList = new ArrayList<>();

        String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setOrganizationType(organizationType);

        //查询类型:0：查询所有 1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人
        if (request.getSearchType() == 0) {
            //下级组织查询时分页
            Page<OrganizationAllChildInfoVo> pageOrganizations = new Page<>(request.getPage(), request.getRows());
            if (!RankingListPositionEnums.ZB.getOrganizationType().equals(organizationType)) {
                List<OrganizationAllChildInfoVo> allChildren = organizationMapper.getAllChildrenIncludeOrganizationIds(pageOrganizations, request.getOrganizationId(), request.getOrganizationIds());
                if (CollectionUtil.isNotEmpty(allChildren)) {
                    request.setNextOrgCodes(allChildren.stream().map(OrganizationAllChildInfoVo::getRegions).sorted(Comparator.reverseOrder()).collect(Collectors.toList()));
                    resultList = realtimeMapper.getMainProductTileList(request);
                }
            } else {
                //总部已经在sql级别限制-->查所有
//                resultList = realtimeMapper.getMainProductTileList(page, request);
                resultList = realtimeMapper.getMainProductTileListLimit(request, null, null);

            }
            generateMainProductVo(request, resultList);
        }

        //其他在查询主推品时分页
        if (request.getSearchType() == 1) {
            List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + request.getOrganizationId());
            log.info("【next org code】codes:{}", nextOrgCode);
            if (CollectionUtil.isNotEmpty(nextOrgCode)) {
                request.setNextOrgCodes(nextOrgCode);
            }
        }
//        resultList = realtimeMapper.getMainProductTileList(page, request);
        resultList = realtimeMapper.getMainProductTileListLimit(request, null, null);
        generateMainProductVo(request, resultList);
        //执行下载操作
        String fileName = "主推品列表.xlsx";
        // 定义表头
        List<List<String>> header = new ArrayList<>();
        //默认表头
        header.add(Arrays.asList("战区"));
        header.add(Arrays.asList("大区"));
        header.add(Arrays.asList("省区"));
        header.add(Arrays.asList("分公司"));
        header.add(Arrays.asList("营业所"));
        header.add(Arrays.asList("成员数量"));
        header.add(Arrays.asList("岗位"));
        header.add(Arrays.asList("头像"));
        header.add(Arrays.asList("姓名"));
        header.add(Arrays.asList("在职天数"));
        //生成数据
        List<List<String>> dataList = new ArrayList<>();
        //主推品数量极少可以直接导出 -->后续数量大的话需要分页
        if (CollectionUtil.isNotEmpty(resultList)) {
            //找出所有的主推品-->去重获取名字
            List<String> mainProductNameList = new ArrayList<>();
            resultList.forEach(result -> {
                result.getMainList().forEach(product -> {
                    mainProductNameList.add(product.getMainProductName());
                });
            });
            //去重
            List<String> finalMainProductNameList = mainProductNameList.stream().distinct().collect(Collectors.toList());
            //生成动态表头 顺序生成
            for (int i = 1; i <= finalMainProductNameList.size(); i++) {
                String title = StringUtils.join(finalMainProductNameList.get(i - 1), "[", i, "]");
                header.add(Arrays.asList(title + "-" + "业绩"));
                header.add(Arrays.asList(title + "-" + "现金业绩"));
                header.add(Arrays.asList(title + "-" + "目标"));
                header.add(Arrays.asList(title + "-" + "达成率"));
                header.add(Arrays.asList(title + "-" + "环比"));
                header.add(Arrays.asList(title + "-" + "同比"));
                header.add(Arrays.asList(title + "-" + "交易客户数"));
                header.add(Arrays.asList(title + "-" + "客单价"));
                header.add(Arrays.asList(title + "-" + "陈列客户数"));
            }

            //生成数据
            for (int j = 0; j < resultList.size(); j++) {
                RealtimeMainProductVo mainProductVo = resultList.get(j);
                List<String> data = new ArrayList<>();
                data.add(mainProductVo.getAreaName());
                data.add(mainProductVo.getVirtualAreaName());
                data.add(mainProductVo.getProvinceName());
                data.add(mainProductVo.getCompanyName());
                data.add(mainProductVo.getDepartmentName());
                data.add(mainProductVo.getMemberCount());
                data.add(mainProductVo.getPost());
                data.add(mainProductVo.getAvatar());
                data.add(mainProductVo.getName());
                data.add(mainProductVo.getOnboardDays());
                //主推品列表顺序写
                Map<String, MainProductVo> mainProductVoMap = mainProductVo.getMainList().stream().collect(Collectors.toMap(MainProductVo::getMainProductName, Function.identity()));
                for (int i = 1; i <= finalMainProductNameList.size(); i++) {
                    MainProductVo productVo = mainProductVoMap.get(finalMainProductNameList.get(i - 1));
                    data.add(productVo.getBiddingPerformance() == null ? "-" : productVo.getBiddingPerformance());
                    data.add(productVo.getMainProductsCashPerformance() == null ? "-" : productVo.getMainProductsCashPerformance().toPlainString());
                    data.add(productVo.getGoal() == null ? "-" : productVo.getGoal());
                    data.add(productVo.getGoalAchievementRate() == null ? "-" : productVo.getGoalAchievementRate());
                    data.add(productVo.getSequentialGrowthRate() == null ? "-" : productVo.getSequentialGrowthRate());
                    data.add(productVo.getYearGrowthRate() == null ? "-" : productVo.getYearGrowthRate());
                    data.add(productVo.getTradingClients() == null ? "-" : productVo.getTradingClients());
                    data.add(productVo.getGuestUnitPrice() == null ? "-" : productVo.getGuestUnitPrice());
                    data.add(productVo.getNumberOfDisplayCustomers() == null ? "-" : productVo.getNumberOfDisplayCustomers());
                }
                dataList.add(data);
            }
        }
        ExportUtil.writeEasyExcelResponseBySelf(res, req, fileName, header, dataList);
    }

    //封装数据
    private List<RealtimeMainProductVo> generateMainProductVo(RealtimeMainProductTileNewRequest request, List<RealtimeMainProductVo> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        //获取主推品模版
        List<MainProductVo> mainProductMonthTemplate = realtimeMapper.getMainProductTemplateNew(request.getDateTypeId(), request.getYearMonth(), request.getBusinessGroup());

        resultList.forEach(l -> {
            ArrayList<MainProductVo> mainProductVos = new ArrayList<>();
            BeanUtils.copyProperties(mainProductMonthTemplate, mainProductVos, MainProductVo.class, MainProductVo.class);
            if (CommonUtil.ListUtils.isNotEmpty(l.getMainList())) {
                List<MainProductVo> mainList = l.getMainList();
                mainProductVos.forEach(m -> {
                    List<MainProductVo> mainProduct = mainList.stream().filter(main -> main.getMainProductName().equals(m.getMainProductName())).collect(Collectors.toList());
                    if (CommonUtil.ListUtils.isNotEmpty(mainProduct)) {
                        //MainProductVo mainProductVo1 = mainProduct.get(0);
                        m.setBiddingPerformance(mainProduct.get(0).getBiddingPerformance());
                        m.setGoal(mainProduct.get(0).getGoal());
                        m.setGoalAchievementRate(mainProduct.get(0).getGoalAchievementRate());
                        m.setSequentialGrowthRate(mainProduct.get(0).getSequentialGrowthRate());
                        m.setYearGrowthRate(mainProduct.get(0).getYearGrowthRate());
                        m.setInvoicingPartnersNumber(mainProduct.get(0).getInvoicingPartnersNumber());
                        m.setPartnerBillingRate(mainProduct.get(0).getPartnerBillingRate());
                        m.setBillingRateSequentialIncreaseRate(mainProduct.get(0).getBillingRateSequentialIncreaseRate());
                        m.setPerCapitaPerformance(mainProduct.get(0).getPerCapitaPerformance());
                        m.setPerCapitaPerformanceGrowthRate(mainProduct.get(0).getPerCapitaPerformanceGrowthRate());
                        m.setTradingClients(mainProduct.get(0).getTradingClients());
                        m.setNumberOfDisplayCustomers(mainProduct.get(0).getNumberOfDisplayCustomers());
                        m.setGuestUnitPrice(mainProduct.get(0).getGuestUnitPrice());
                        m.setMainProductsCashPerformance(mainProduct.get(0).getMainProductsCashPerformance());
                        if (StringUtils.isNotBlank(m.getGoalAchievementRate())) {
                            m.setMainProductsSaleGoalAchievementColor(realTimeUtils.saleGoalAchievementColor(request.getDateTypeId(), request.getYearMonth(), new BigDecimal(m.getGoalAchievementRate())));
                        }
                    }
                });
            }

            l.setMainList(mainProductVos);
        });
        return resultList;
    }

    @Override
    public List<RealtimeMainProductVo> getMainProductList(RealtimeMainProductRequest request) {
        log.info("start RealtimeDataServiceImpl getMainProductList request:{}", request);
        if (request.getMonthlyType() == 2) {
            request.setDate(publicHandleDate(request.getDate()));
        }
        List<RealtimeMainProductVo> list = new ArrayList<>();
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        TradeGoodReq tradeGoodReq = new TradeGoodReq();
        tradeGoodReq.setOrganizationId(request.getOrganizationId());
        tradeGoodReq.setYearMonth(request.getDate());
        /*tradeGoodReq.setIsMonthly(request.getIsMonthly());
        tradeGoodReq.setIsQuarter(request.getIsQuarter());
        tradeGoodReq.setIsQuarterMonth(request.getIsQuarterMonth());*/
        tradeGoodReq.setMonthlyType(request.getMonthlyType());
        //岗位类型id,1:战区，2：分公司，3：合伙人，10：区域经理，11：省区，12：大区
        Integer positionTypeId = realtimeMapper.getMainOrganizationType(request.getOrganizationId(), request.getType(), request.getDate(), RequestUtils.getBusinessGroup(), request.getMonthlyType());
        if (null == positionTypeId) {
            //throw new ApplicationException("数据生成中，请稍后在试");
            return list;
        }
        if (positionTypeId != null) {
            request.setOrganizationType(positionTypeId);
        }


        if (request.getIsNextRealtime() == 0) {
            //总部查自己 也要查下级
            // 自己
            RealtimeMainProductVo resultsDate = realtimeMapper.getMainProductMonth(request);

            if (positionTypeId != null) {
                List<RealtimeMainProductVo> Resultslist = realtimeMapper.getMainProductMonthNext(request);
                if (Objects.nonNull(resultsDate)) {
                    list.add(resultsDate);
                }
                list.addAll(Resultslist);
            }
            if (CommonUtil.ListUtils.isNotEmpty(list)) {
                list.forEach(l -> {
                    if (StringUtils.isNotBlank(l.getOrganizationId()) && l.getOrganizationId().equals(request.getOrganizationId())) {
                        l.setAreaName("合计");
                    }
                });
            }
        } else {
            List<RealtimeMainProductVo> Resultslist = realtimeMapper.getMainProductMonthNext(request);
            list.addAll(Resultslist);
        }
        //获取主推品模版
        List<MainProductVo> mainProductMonthTemplate = realtimeMapper.getMainProductMonthTemplate(request);
        list.forEach(l -> {
            ArrayList<MainProductVo> mainProductVos = new ArrayList<>();
            BeanUtils.copyProperties(mainProductMonthTemplate, mainProductVos, MainProductVo.class, MainProductVo.class);
            if (CommonUtil.ListUtils.isNotEmpty(l.getMainList())) {
                List<MainProductVo> mainList = l.getMainList();
                mainProductVos.forEach(m -> {
                    List<MainProductVo> mainProduct = mainList.stream().filter(main -> main.getMainProductName().equals(m.getMainProductName())).collect(Collectors.toList());
                    if (CommonUtil.ListUtils.isNotEmpty(mainProduct)) {
                        //MainProductVo mainProductVo1 = mainProduct.get(0);
                        m.setBiddingPerformance(mainProduct.get(0).getBiddingPerformance());
                        m.setGoal(mainProduct.get(0).getGoal());
                        m.setGoalAchievementRate(mainProduct.get(0).getGoalAchievementRate());
                        m.setSequentialGrowthRate(mainProduct.get(0).getSequentialGrowthRate());
                        m.setYearGrowthRate(mainProduct.get(0).getYearGrowthRate());
                        m.setInvoicingPartnersNumber(mainProduct.get(0).getInvoicingPartnersNumber());
                        m.setPartnerBillingRate(mainProduct.get(0).getPartnerBillingRate());
                        m.setBillingRateSequentialIncreaseRate(mainProduct.get(0).getBillingRateSequentialIncreaseRate());
                        m.setPerCapitaPerformance(mainProduct.get(0).getPerCapitaPerformance());
                        m.setPerCapitaPerformanceGrowthRate(mainProduct.get(0).getPerCapitaPerformanceGrowthRate());
                        m.setTradingClients(mainProduct.get(0).getTradingClients());
                        m.setNumberOfDisplayCustomers(mainProduct.get(0).getNumberOfDisplayCustomers());
                        m.setGuestUnitPrice(mainProduct.get(0).getGuestUnitPrice());
                    }
                });
            }
            l.setMainList(mainProductVos);
        });
        return list;
    }

    @Override
    public IPage<WarehouseTheoryVo> queryWarehouseTheoryList(WarehouseTheoryReq request) {
        Page<WarehouseTheoryVo> page = new Page<>();
        List<WarehouseTheoryVo> list = mapper.queryWarehouseTheoryList(request);
        int count = mapper.queryWarehouseTheoryListCount(request);
        if (list.size() > 1) {
            WarehouseTheoryVo warehouseTheoryVo = mapper.queryWarehouseTheoryTotal(request);
            if (ObjectUtil.isNotNull(warehouseTheoryVo)) {
                list.add(warehouseTheoryVo);
            }
        }
        page.setRecords(list);
        page.setTotal(count);
        return page;
    }

    @Override
    public void exportWarehouseTheoryList(WarehouseTheoryReq request) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        List<WarehouseTheoryVo> list = mapper.exportWarehouseTheoryList(request);
        WarehouseTheoryVo warehouseTheoryVo = mapper.queryWarehouseTheoryTotal(request);
        if (ObjectUtil.isNotNull(warehouseTheoryVo)) {
            list.add(warehouseTheoryVo);
        }
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), WarehouseTheoryVo.class, list);
        EasyPoiUtil.downLoadExcel("仓储理论实际.xls", response, workbook);

    }

    @Override
    public List<SkuSpecificationNewVo> querySKULine(String theYearMon) {
        return mapper.querySKULine(theYearMon);
    }


    @Override
    public com.wantwant.commons.pagination.Page<TeamResultsVo> getResultsDateBusinessCompany(TradeGoodReq request) {
        com.wantwant.commons.pagination.Page<TeamResultsVo> result = new com.wantwant.commons.pagination.Page<>();
        List<TeamResultsVo> list = new ArrayList<>();

        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        TeamResultsVo emplyeeDate = realtimeMapper.getResultsDateMonthlyMonth(request);
        if (Objects.nonNull(emplyeeDate)) {
            request.setOrganizationType(emplyeeDate.getPositionType());
            //找到大区，战区下的分公司
            if (request.getOrganizationType() == 1 || request.getOrganizationType() == 12) {
                List<TeamResultsVo> Resultslist = null;
                if (request.getMonthlyType() == 1) {
                    Resultslist = realtimeMapper.getResultsDateNextComapnyMonth(request);
                } else {
                    request.setYearMonth(publicHandleDate(request.getYearMonth()));
                    Resultslist = realtimeMapper.getResultsDateNextComapnyQuarter(request);
                }
                list.addAll(Resultslist);
                List<TeamResultsVo> teamResultsVos = publicHandleColor(list);
                result.setList(teamResultsVos);
                result.setTotalItem(teamResultsVos.size());
            }
        }


        if (!CollectionUtils.isEmpty(list)) {
            settingTag(list);
        }


        return result;
    }

    @Override
    public com.wantwant.commons.pagination.Page<TeamResultsVo> getResultsDateBusinessMonthly(TradeGoodReq request) {
        log.info("start RealtimeDataServiceImpl getResultsDateBusiness request:{}", request);

        com.wantwant.commons.pagination.Page<TeamResultsVo> result = new com.wantwant.commons.pagination.Page<>();
        request.setBusinessGroup(RequestUtils.getLoginInfo().getBusinessGroup());
        SfaReviewReportEntity sfaReviewReportEntity = sfaReviewReportMapper.selectById(request.getReportId());
        LocalDate month = LocalDate.parse(sfaReviewReportEntity.getYearMonth() + "-01").minusMonths(1);
        request.setYearMonth(month.toString().substring(0, 7));
        //TeamResultsVo emplyeeDate = null;

        if (StringUtils.isNotBlank(request.getYearMonth())) {
            //复盘月份减两月
            LocalDate localDate = LocalDate.parse(sfaReviewReportEntity.getYearMonth() + "-01").minusMonths(2);
            request.setLastTowMonth(localDate.toString().substring(0, 7));
        } else {
            request.setLastTowMonth(LocalDate.now().toString().substring(0, 7));
        }

        List<TeamResultsVo> list = new ArrayList<>();
        if (request.getMonthlyType() == 1) {
            Integer postionId = realtimeMapper.getResultsDateMonthlyPostionType(request);
            if (null == postionId) {
                throw new ApplicationException(BizExceptionLanguageEnum.DATA_GENERATION_IN_PROGRESS.getTextMsg());
            }
            request.setOrganizationType(postionId);
            if (request.getIsNextRealtime() == 0) {
                //总部查自己 也要查下级
                // 自己
                TeamResultsVo resultsDate = realtimeMapper.getResultsDateMonthlyMonth(request);
                if (null != postionId) {
                    //查看下级*/
                    List<TeamResultsVo> Resultslist = realtimeMapper.getResultsDateNextMonthlyMonth(request);
                    if (Objects.nonNull(resultsDate)) {

                        List<TeamPerformanceVo> performanceVos = resultsDate.getList();
                        if (!CollectionUtils.isEmpty(performanceVos)) {
                            performanceVos.forEach(e -> {
                                e.setTitleMonth("月度");
                            });
                        }

                        list.add(resultsDate);
                    }
                    list.addAll(Resultslist);
                } else {
                    list.add(resultsDate);
                }
                if (CommonUtil.ListUtils.isNotEmpty(list)) {
                    list.forEach(l -> {
                        if (l.getOrganizationId().equals(request.getOrganizationId())) {
                            l.setArea("合计");
                        }

                        String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
                        if (l.getOrganizationId().equals(request.getOrganizationId()) && !organizationType.equals("zb")) {

                            SfaPositionRelationEntity sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getOrganizationCode, request.getOrganizationId()).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
                            if (Objects.nonNull(sfaPositionRelationEntity)) {
                                SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(sfaPositionRelationEntity.getEmployeeInfoId());
                                if (Objects.nonNull(sfaEmployeeInfoModel)) {
                                    ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
                                    l.setUrl(applyMemberPo.getPicUrl());
                                }
                            }

                        }

                    });
                }
            } else {
                List<TeamResultsVo> Resultslist = realtimeMapper.getResultsDateNextMonthlyMonth(request);
                list.addAll(Resultslist);
            }
        } else {
            String replace = "";
            if (request.getYearMonth().contains("-12")) {
                //年份减1
               /* final int year = Integer.valueOf(request.getYearMonth().substring(0, 4)) - 1;
                request.setYearMonth(String.valueOf(year)+"-04");*/
                replace = request.getYearMonth().replace("-12", "-04");
            } else if (request.getYearMonth().contains("-03")) {
                replace = request.getYearMonth().replace("-03", "-01");
            } else if (request.getYearMonth().contains("-06")) {
                replace = request.getYearMonth().replace("-06", "-02");
            } else if (request.getYearMonth().contains("-09")) {
                replace = request.getYearMonth().replace("-09", "-03");
            } else {
                replace = request.getYearMonth();
            }
            request.setYearMonth(replace);
            //放年份数据
            request.setYear(String.valueOf(request.getYearMonth().substring(0, 4)));
            Integer postionId = realtimeMapper.getResultsDateMonthlyPostionType(request);
            if (null == postionId) {
                throw new ApplicationException("数据生成中，请稍后在试");
            }
            request.setOrganizationType(postionId);
            LocalDate yearmonth = LocalDate.parse(request.getYearMonth() + "-01");
            LocalDate quater = LocalDate.parse("2023-04-01");
            //处理季度月份
            if (request.getIsNextRealtime() == 0) {
                // 总部查自己 也要查下级
                // 自己
                TeamResultsVo resultsDate = null;
                if (yearmonth.isBefore(quater)) {
                    resultsDate = realtimeMapper.getResultsDateMonthlyQuarterOld(request);
                } else {
                    resultsDate = realtimeMapper.getResultsDateMonthlyQuarter(request);
                }

                if (null != postionId) {
                    List<TeamResultsVo> Resultslist = null;
                    //查看下级*/
                    if (yearmonth.isBefore(quater)) {
                        Resultslist = realtimeMapper.getResultsDateNextMonthlyQuarterOld(request);
                    } else {
                        Resultslist = realtimeMapper.getResultsDateNextMonthlyQuarter(request);
                    }

                    if (Objects.nonNull(resultsDate)) {
                        List<TeamPerformanceVo> teamPerformanceVos = resultsDate.getList();
                        if (CollectionUtils.isEmpty(teamPerformanceVos)) {
                            teamPerformanceVos.forEach(e -> {
                                e.setTitleMonth("季度");
                            });
                        }
                        list.add(resultsDate);
                    }
                    if (CommonUtil.ListUtils.isNotEmpty(Resultslist)) {
                        list.addAll(Resultslist);
                    }
                } else {
                    list.add(resultsDate);
                }
                if (CommonUtil.ListUtils.isNotEmpty(list)) {
                    list.forEach(l -> {
                        if (l.getOrganizationId().equals(request.getOrganizationId())) {
                            l.setArea("合计");
                        }
                    });
                }
            } else {
                List<TeamResultsVo> Resultslist = null;
                if (yearmonth.isBefore(quater)) {
                    Resultslist = realtimeMapper.getResultsDateNextMonthlyQuarterOld(request);
                } else {
                    Resultslist = realtimeMapper.getResultsDateNextMonthlyQuarter(request);
                }
                list.addAll(Resultslist);
            }
        }
        List<TeamResultsVo> teamResultsVos = publicHandleColor(list);
        // 设置标签
        settingTag(teamResultsVos);

        result.setList(teamResultsVos);
        result.setTotalItem(list.size());
        return result;
    }

    @Override
    public IPage<TeamResultsVo> getResultsDateBusinessMonthlyTileMode(TradeGoodReq request) {

        request.setBusinessGroup(RequestUtils.getLoginInfo().getBusinessGroup());
        SfaReviewReportEntity sfaReviewReportEntity = sfaReviewReportMapper.selectById(request.getReportId());
        LocalDate month = LocalDate.parse(sfaReviewReportEntity.getYearMonth() + "-01").minusMonths(1);
        request.setOrgType(organizationMapper.getOrganizationType(request.getOrganizationId()));
        request.setYearMonth(month.toString().substring(0, 7));
        //TeamResultsVo emplyeeDate = null;
        IPage<TeamResultsVo> page = new Page<>(request.getPage(), request.getRows());
        if (StringUtils.isNotBlank(request.getYearMonth())) {
            //复盘月份减两月
            LocalDate localDate = LocalDate.parse(sfaReviewReportEntity.getYearMonth() + "-01").minusMonths(2);
            request.setLastTowMonth(localDate.toString().substring(0, 7));
        } else {
            request.setLastTowMonth(LocalDate.now().toString().substring(0, 7));
        }

        Integer positionTypeId = request.getPositionTypeId();
        if (positionTypeId == 99) {
            List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + sfaReviewReportEntity.getOrganizationId());
            if (CollectionUtils.isEmpty(nextOrgCode)) {
                return page;
            }
            request.setOrganizationIds(nextOrgCode);
        }

        List<TeamResultsVo> result = new ArrayList<>();

        if (request.getMonthlyType() == 1) {
            result = realtimeMapper.getResultsDateBusinessMonthlyTileMode(page, request);
        } else {
            String replace = "";
            if (request.getYearMonth().contains("-12")) {
                //年份减1
               /* final int year = Integer.valueOf(request.getYearMonth().substring(0, 4)) - 1;
                request.setYearMonth(String.valueOf(year)+"-04");*/
                replace = request.getYearMonth().replace("-12", "-04");
            } else if (request.getYearMonth().contains("-03")) {
                replace = request.getYearMonth().replace("-03", "-01");
            } else if (request.getYearMonth().contains("-06")) {
                replace = request.getYearMonth().replace("-06", "-02");
            } else if (request.getYearMonth().contains("-09")) {
                replace = request.getYearMonth().replace("-09", "-03");
            } else {
                replace = request.getYearMonth();
            }
            request.setYearMonth(replace);
            result = realtimeMapper.getResultsDateNextMonthlyQuarterTileMode(page, request);
        }


        List<TeamResultsVo> teamResultsVos = publicHandleColor(result);
        // 设置标签
        settingTag(teamResultsVos);
        page.setRecords(teamResultsVos);
        return page;
    }

    private void settingTag(List<TeamResultsVo> teamResultsVos) {
        teamResultsVos.forEach(e -> {
            BigDecimal saleGoalAchievementRate = e.getSaleGoalAchievementRate();

            TargetRule target = targetFilterChainComponent.getTarget(saleGoalAchievementRate, targetFilterChainComponent.getSaleGoalAchievementRateChain());
            if (Objects.nonNull(target)) {
                e.setSaleGoalAchievementRateTag(target.getTargetName());
                e.setSaleGoalAchievementRateTagColor(target.getColor());
            }


            BigDecimal itemSupplyTotalYearOnYear = e.getItemSupplyTotalYearOnYear();
            TargetRule itemSupplyTotalYearOnYearTarget = targetFilterChainComponent.getTarget(itemSupplyTotalYearOnYear, targetFilterChainComponent.getItemSupplyTotalYearOnYearChain());
            if (Objects.nonNull(itemSupplyTotalYearOnYearTarget)) {
                e.setItemSupplyTotalYearOnYearTag(itemSupplyTotalYearOnYearTarget.getTargetName());
                e.setItemSupplyTotalYearOnYearTagColor(itemSupplyTotalYearOnYearTarget.getColor());
            }

            List<TeamPerformanceVo> list = e.getList();

            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(l -> {
                    Double goalAchievementRate = Optional.ofNullable(l.getGoalAchievementRate()).orElse(0.0);

                    TargetRule t = targetFilterChainComponent.getTarget(new BigDecimal(goalAchievementRate), targetFilterChainComponent.getSaleGoalAchievementRateChain());
                    if (Objects.nonNull(t)) {
                        l.setSaleGoalAchievementRateTag(t.getTargetName());
                        l.setSaleGoalAchievementRateTagColor(t.getColor());
                    }

                    Double yearAchievementRate = Optional.ofNullable(l.getYearAchievementRate()).orElse(0.0);
                    TargetRule t2 = targetFilterChainComponent.getTarget(new BigDecimal(yearAchievementRate), targetFilterChainComponent.getItemSupplyTotalYearOnYearChain());
                    if (Objects.nonNull(t2)) {
                        l.setItemSupplyTotalYearOnYearTag(t2.getTargetName());
                        l.setItemSupplyTotalYearOnYearTagColor(t2.getColor());
                    }
                });
            }
        });

    }

    //获取当前组织下级的下级在职的异常人员
    //根据组织找到它的下级组织，看是不是在职异常
    public List<AbnormalPersonVo> getResultsDateBusinessNextMonthly(List<AbnormalPersonVo> abnormalPerson, TradeGoodReq request) {
        //找到当前组织的下级 （）
        if (request.getOrganizationType() == 2) {
            return abnormalPerson;
        }
        //找到离职的下级人员
        ArrayList<AbnormalPersonVo> list = new ArrayList<>();
        abnormalPerson.forEach(r -> {
            if (StringUtils.isBlank(r.getAbnormalName()) && null != r.getAbnormalNamePositionType()) {
                List<String> orgIds = new ArrayList<>();
                List<Integer> postionType = new ArrayList<>();
                orgIds.add(r.getAbnormalOrganizationId());
                postionType.add(r.getAbnormalNamePositionType());
                request.setOrganizationId(r.getAbnormalOrganizationId());
                request.setOrganizationType(r.getAbnormalNamePositionType());
                List<AbnormalPersonVo> nextLevelData = new ArrayList<>();
                getNextLevelData(nextLevelData, orgIds, request);
                list.addAll(nextLevelData);
            }
        });
        return list;
    }

    ;

    //找到离职人员的下级异常人员，如果下级离职再找下级
    private void getNextLevelData(List<AbnormalPersonVo> currentList, List<String> orgIds, TradeGoodReq request) {
        //查找当前orgIds对应的下级异常人员
        List<AbnormalPersonVo> list = new ArrayList<>();
        for (String s : orgIds) {
            request.setOrganizationId(s);
            Integer postionType = realtimeMapper.getResultsDateMonthlyPostionType(request);
            if (postionType == 10 || postionType == 3) {//容错区域经理及以下
                continue;
            }
            // 查询 当前 组织的类型
            request.setOrganizationType(postionType);
            // 找到当前组织的下级
            List<TeamResultsVo> resultslist = realtimeMapper.getResultsDateNextMonthlyQuarterDimission(request);
            List<TeamResultsVo> teamResultsVos = publicHandleColor(resultslist);
            if (CommonUtil.ListUtils.isNotEmpty(resultslist)) {
                // 找到当前组织的下级异常人员
                List<AbnormalPersonVo> abnormalPersonList = getAbnormalPerson(teamResultsVos, request);
                list.addAll(abnormalPersonList);
            }
        }
        List<String> orgList = new ArrayList<>();
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            List<AbnormalPersonVo> finalCurrentList = currentList;
            list.forEach(e -> {
                //找到异常 在岗的人加进来
                if (StringUtils.isNotBlank(e.getAbnormalName())) {
                    finalCurrentList.add(e);
                } else {//找到异常 缺岗的人加进来
                    if (e.getAbnormalNamePositionType() != 10) {
                        orgList.add(e.getAbnormalOrganizationId());
                    }
                }
            });
        }
        if (!CollectionUtils.isEmpty(orgList)) {
            getNextLevelData(currentList, orgList, request);
        }
    }

    /**
     * 1.首先找的状态是异常
     * 2.如果下级是异常拉出来，如果下级是缺岗找下下级的异常
     *
     * @param request
     * @return
     */
    public AbnormalVo getResultsDateAbnormalPerson(TradeGoodReq request) {
        log.info("start RealtimeDataServiceImpl getResultsDateAbnormalPerson request:{}", request);
        AbnormalVo abnormalVo = new AbnormalVo();
        if (StringUtils.isBlank(request.getEmployeeId())) {
            throw new ApplicationException("员工工号不能为空");
        }
        String organizationId1 = request.getOrganizationId();
        //区域经理及以下无异常人员
        String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
        if (Objects.isNull(organizationType) || "department".equals(organizationType)) {
            return abnormalVo;
        }


        com.wantwant.commons.pagination.Page<TeamResultsVo> resultsDateBusinessMonthly = getResultsDateBusinessMonthly(request);
        List<TeamResultsVo> list = resultsDateBusinessMonthly.getList();
        List<AbnormalPersonVo> abnormalPersonList = new ArrayList<AbnormalPersonVo>();
        //获取当前组织下级的异常人员数据
        List<AbnormalPersonVo> personList = getAbnormalPerson(list, request);


        //去掉合计
        List<AbnormalPersonVo> collect1 = personList.stream().collect(Collectors.toList());


        //获取离职下级的异常人员 (异常人员为空)
        List<AbnormalPersonVo> collectDimission = collect1.stream().filter(p -> StringUtils.isBlank(p.getAbnormalName())).collect(Collectors.toList());
        List<AbnormalPersonVo> resultsDateBusinessNextMonthly = null;
        if (CommonUtil.ListUtils.isNotEmpty(collectDimission)) {
            //获当前组织下级的下级在职的异常人员
            resultsDateBusinessNextMonthly = getResultsDateBusinessNextMonthly(collectDimission, request);
        }
        //获取当前组织下级在职的异常人员
        List<AbnormalPersonVo> collecttt = collect1.stream().filter(p -> StringUtils.isNotBlank(p.getAbnormalName()) || (StringUtils.isBlank(p.getAbnormalName()) && StringUtils.isNotBlank(p.getOrganizationName()) && p.getOrganizationName().equals("总部"))).collect(Collectors.toList());
        if (CommonUtil.ListUtils.isNotEmpty(collecttt)) {
            abnormalPersonList.addAll(collecttt);
        }
        if (CommonUtil.ListUtils.isNotEmpty(resultsDateBusinessNextMonthly)) {

            List<AbnormalPersonVo> collect = resultsDateBusinessNextMonthly.stream().filter(f -> !abnormalPersonList.stream().filter(af -> af.getAbnormalOrganizationId().equals(f.getAbnormalOrganizationId())).findFirst().isPresent()).collect(Collectors.toList());

            abnormalPersonList.addAll(collect);
        }


        SfaReviewReportEntity sfaReviewReportEntity = sfaReviewReportMapper.selectById(request.getReportId());


        List<TeamResultsVo> collect = list.stream().filter(a -> a.getArea().equals("合计")).collect(Collectors.toList());
        if (CommonUtil.ListUtils.isNotEmpty(collect)) {


            //根据工号得到它的组织信息，这是上级信息
            CeoBusinessOrganizationPositionRelationVo parentOrganizationIdByPerson = relationMapper.getOrganizationByPersonPart(request.getEmployeeId(), RequestUtils.getBusinessGroup(), organizationId1);


            List<String> orgCodes = abnormalPersonList.stream().map(AbnormalPersonVo::getAbnormalOrganizationId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orgCodes)) {
                return null;
            }

            // 获取月报ID
            List<SfaReviewReportEntity> sfaReviewReportEntities = sfaReviewReportMapper.selectList(new LambdaQueryWrapper<SfaReviewReportEntity>().in(SfaReviewReportEntity::getOrganizationId, orgCodes).eq(SfaReviewReportEntity::getYearMonth, sfaReviewReportEntity.getYearMonth()).eq(SfaReviewReportEntity::getDeleteFlag, 0));
            if (CollectionUtils.isEmpty(sfaReviewReportEntities)) {
                return null;
            }
            List<Long> reportIds = sfaReviewReportEntities.stream().map(SfaReviewReportEntity::getReviewId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(reportIds)) {
                List<SfaAbnormalPersonDetail> sfaAbnormalPersonDetails = sfaAbnormalPersonDetailMapper.selectList(new LambdaQueryWrapper<SfaAbnormalPersonDetail>().in(SfaAbnormalPersonDetail::getReportId, reportIds).eq(SfaAbnormalPersonDetail::getIsDelete, 0));

                abnormalPersonList.forEach(a -> {
                    a.setIsAbnormal(1);
                    int isLeader = 0;

                    if (!a.getAbnormalOrganizationId().equals(organizationId1)) {
                        isLeader = 1;
                    }


                    Optional<TeamResultsVo> first = list.stream().filter(f -> f.getOrganizationId().equals(a.getAbnormalOrganizationId())).findFirst();
                    TeamResultsVo teamResultsVo = new TeamResultsVo();
                    if (first.isPresent()) {
                        teamResultsVo = first.get();

                        // 目标达成率
                        a.setGoalAchievementRate(teamResultsVo.getSaleGoalAchievementRate().doubleValue());
                        a.setSaleGoalAchievementRateTag(teamResultsVo.getSaleGoalAchievementRateTag());
                        a.setSaleGoalAchievementRateTagColor(teamResultsVo.getSaleGoalAchievementRateTagColor());

                        // 业绩同比
                        a.setItemSupplyTotalYearOnYearTag(teamResultsVo.getItemSupplyTotalYearOnYearTag());
                        a.setItemSupplyTotalYearOnYearTagColor(teamResultsVo.getItemSupplyTotalYearOnYearTagColor());

                    }

                    Double achievementRate = Optional.ofNullable(a.getGoalAchievementRate()).orElse(0.0);
                    TargetRule target = targetFilterChainComponent.getTarget(new BigDecimal(achievementRate), targetFilterChainComponent.getSaleGoalAchievementRateChain());
                    if (Objects.nonNull(target)) {
                        a.setSaleGoalAchievementRateTag(target.getTargetName());
                        a.setSaleGoalAchievementRateTagColor(target.getColor());
                    }

                    Double yearRate = Optional.ofNullable(a.getYearAchievementRate()).orElse(0.0);
                    TargetRule itemSupplyTotalYearOnYearTarget = targetFilterChainComponent.getTarget(new BigDecimal(yearRate), targetFilterChainComponent.getItemSupplyTotalYearOnYearChain());
                    if (Objects.nonNull(itemSupplyTotalYearOnYearTarget)) {
                        a.setItemSupplyTotalYearOnYearTag(itemSupplyTotalYearOnYearTarget.getTargetName());
                        a.setItemSupplyTotalYearOnYearTagColor(itemSupplyTotalYearOnYearTarget.getColor());
                    }


                    String organizationId = a.getAbnormalOrganizationId();
                    SfaPositionRelationEntity positionRelationEntity = null;
                    if (!organizationId.contains("ZB")) {
                        positionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getOrganizationCode, organizationId).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
                    }


                    if (Objects.nonNull(positionRelationEntity)) {
                        Integer employeeInfoId = positionRelationEntity.getEmployeeInfoId();
                        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(employeeInfoId);
                        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
                        a.setAvatar(applyMemberPo.getPicUrl());

                        // 获取在职天数
                        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new LambdaQueryWrapper<SfaInterviewProcessModel>().eq(SfaInterviewProcessModel::getApplicationId, applyMemberPo.getId()));
                        if (Objects.nonNull(sfaInterviewProcessModel)) {
                            Date onboardTime = sfaInterviewProcessModel.getOnboardTime();
                            if (Objects.nonNull(onboardTime)) {
                                Date offTime = sfaInterviewProcessModel.getOffTime();
                                if (Objects.isNull(offTime)) {
                                    offTime = new Date();
                                }
                                long betweenDay = DateUtil.betweenDay(onboardTime, offTime, false);
                                a.setOnBoardDate(betweenDay);
                            }

                        }

                    }


                    if (CommonUtil.ListUtils.isNotEmpty(sfaAbnormalPersonDetails)) {
                        int finalIsLeader = isLeader;
                        Optional<SfaAbnormalPersonDetail> personDetailOptional = sfaAbnormalPersonDetails.stream().filter(f -> f.getIsLeader() == finalIsLeader && f.getAbnormalOrganizationId().equals(a.getAbnormalOrganizationId())).findFirst();
                        if (!personDetailOptional.isPresent() && isLeader == 1) {
                            personDetailOptional = sfaAbnormalPersonDetails.stream().filter(f -> f.getIsLeader() == 0 && f.getAbnormalOrganizationId().equals(a.getAbnormalOrganizationId())).findFirst();
                        }

                        if (personDetailOptional.isPresent()) {
                            SfaAbnormalPersonDetail sfaAbnormalPersonDetail = personDetailOptional.get();

                            a.setParentAbnormalName(sfaAbnormalPersonDetail.getParentAbnormalName());
                            a.setParentOrganizationName(sfaAbnormalPersonDetail.getParentOrganizationName());
                            a.setGoalAchievementRateReason(sfaAbnormalPersonDetail.getGoalAchievementRateReason());
                            a.setGoalAchievementImprovement(sfaAbnormalPersonDetail.getGoalAchievementRateImprovement());
                            a.setManagementReason(sfaAbnormalPersonDetail.getManagementReason());
                            a.setManagementImprovement(sfaAbnormalPersonDetail.getManagementReasonImprovement());
                            a.setItemSupplyTotalYearOnYearReason(sfaAbnormalPersonDetail.getItemSupplyTotalYearOnYearReason());
                            a.setItemSupplyTotalYearOnYearImprovement(sfaAbnormalPersonDetail.getItemSupplyTotalYearOnYearImprovement());
                        }
                    }
                });
            }


        }
        //最后再过滤掉缺岗的人
        List<AbnormalPersonVo> result = abnormalPersonList.stream().filter(a -> StringUtils.isNotBlank(a.getAbnormalName()) && !a.getAbnormalOrganizationId().equals(organizationId1) && Objects.nonNull(relationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, a.getAbnormalOrganizationId()).isNotNull(CeoBusinessOrganizationPositionRelation::getEmployeeId).last("limit 1")))).collect(Collectors.toList());
        //省区和大区总监过滤区域经理
        if (RankingListPositionEnums.PROVINCE.getOrganizationType().equals(organizationType) || RankingListPositionEnums.V_AREA.getOrganizationType().equals(organizationType) || RankingListPositionEnums.AREA.getOrganizationType().equals(organizationType) || RankingListPositionEnums.ZB.getOrganizationType().equals(organizationType)) {
            result = result.stream().filter(abnormalPersonVo -> abnormalPersonVo.getAbnormalNamePositionType() != 10).collect(Collectors.toList());
        }
        abnormalVo.setNextAbnormalPersonList(result);
        Optional<AbnormalPersonVo> first = abnormalPersonList.stream().filter(a -> (StringUtils.isNotBlank(a.getAbnormalName()) && a.getAbnormalOrganizationId().equals(organizationId1)) || "总部".equals(a.getOrganizationName())).findFirst();
        if (first.isPresent()) {
            abnormalVo.setAbnormalPersonVo(first.get());
        }
        return abnormalVo;

    }

    //找到异常人员集合数据
    public List<AbnormalPersonVo> getAbnormalPerson(List<TeamResultsVo> list, TradeGoodReq request) {
        ArrayList<AbnormalPersonVo> abnormalPersonList = new ArrayList<>();
        list.forEach(l -> {
            //找出姓名不为空
            /*  if(!l.getOrganizationId().equals(request.getOrganizationId())){*/
            AbnormalPersonVo abnormalPersonVo = new AbnormalPersonVo();
            List<TeamPerformanceVo> collect = l.getList().stream().filter(ll -> ll.getTitle().equals("季度") || (StringUtils.isNotBlank(ll.getTitleMonth()) && ll.getTitleMonth().equals("月度"))).collect(Collectors.toList());
            if (CommonUtil.ListUtils.isNotEmpty(collect)) {

                boolean isUnNormal = false;

                TeamPerformanceVo teamPerformanceVo = collect.get(0);
                abnormalPersonVo.setOrganizationName(l.getOrganizationName());
                abnormalPersonVo.setPostName(l.getPositionName());
                abnormalPersonVo.setAbnormalNamePositionType(l.getPositionType());
                abnormalPersonVo.setAbnormalName(l.getEmployeeName());
                abnormalPersonVo.setAbnormalOrganizationId(l.getOrganizationId());
                abnormalPersonVo.setAbnormalOrganizationName(l.getOrganizationName());
                abnormalPersonVo.setPerformance(teamPerformanceVo.getBiddingPerformance());
                abnormalPersonVo.setGoalAchievementRate(teamPerformanceVo.getGoalAchievementRate());
                abnormalPersonVo.setManagementPosition(teamPerformanceVo.getManagementPositionPerPerformance().toString());
                // 目标达成率 #F59A23
                if (StringUtils.isNotBlank(teamPerformanceVo.getGoalAchievementRateColor()) && (teamPerformanceVo.getGoalAchievementRateColor().equals("#D9001B") || teamPerformanceVo.getGoalAchievementRateColor().equals("#F59A23"))) {
                    abnormalPersonVo.setGoalAchievementRateStatus("异常");
                    isUnNormal = true;
                } else {
                    abnormalPersonVo.setGoalAchievementRateStatus("正常");
                }

                // 管理岗人均业绩
                Double managementPositionPerPerformance = teamPerformanceVo.getManagementPositionPerPerformance();
                abnormalPersonVo.setManagementPositionPerPerformance(managementPositionPerPerformance);
                if (StringUtils.isNotBlank(teamPerformanceVo.getManagementPositionPerPerformanceColor()) && (teamPerformanceVo.getManagementPositionPerPerformanceColor().equals("#D9001B") || teamPerformanceVo.getManagementPositionPerPerformanceColor().equals("#F59A23"))) {
                    abnormalPersonVo.setManagementPositionStatus("异常");
                    isUnNormal = true;
                } else {
                    abnormalPersonVo.setManagementPositionStatus("正常");
                }

                // 同比增长率
                String yearAchievementRateColor = teamPerformanceVo.getYearAchievementRateColor();
                Double yearAchievementRate = teamPerformanceVo.getYearAchievementRate();
                abnormalPersonVo.setYearAchievementRate(yearAchievementRate);
                if (StringUtils.isNotBlank(yearAchievementRateColor) && (yearAchievementRateColor.equals("#D9001B") || yearAchievementRateColor.equals("#F59A23"))) {
                    abnormalPersonVo.setItemSupplyTotalYearOnYearStatus("异常");
                    isUnNormal = true;
                } else {
                    abnormalPersonVo.setItemSupplyTotalYearOnYearStatus("正常");
                }


                if (isUnNormal) {
                    abnormalPersonList.add(abnormalPersonVo);
                }
            }

        });

        return abnormalPersonList;
    }


    @Override
    public void updateResultsDateAbnormalPerson(AbnormalPersonRequest request) {
        log.info("start RealtimeDataServiceImpl updateResultsDateAbnormalPerson request:{}", request);
        if (StringUtils.isBlank(request.getEmployeeId())) {
            throw new ApplicationException("员工工号不能为空");
        }

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getEmployeeId(), RequestUtils.getLoginInfo());
        //根据工号得到它的组织信息，这是上级信息
        CeoBusinessOrganizationPositionRelationVo parentOrganizationIdByPerson = relationMapper.getParentOrganizationIdByPerson(request.getEmployeeId(), RequestUtils.getBusinessGroup());
        String organizationId = parentOrganizationIdByPerson.getOrganizationId();
        String organizationName = parentOrganizationIdByPerson.getOrganizationName();
        String employeeName = parentOrganizationIdByPerson.getEmployeeName();

        //List<SfaAbnormalPersonDetail> sfaAbnormalPerson = sfaAbnormalPersonDetailMapper.selectList(new QueryWrapper<SfaAbnormalPersonDetail>().eq("report_id", request.getReportId()));
        AtomicReference<Integer> inster = new AtomicReference<>(0);

        AbnormalPersonVo abnormalPersonVo = request.getAbnormalPersonVo();
        if (Objects.nonNull(abnormalPersonVo)) {
            // 检查是否有填写过
            SfaAbnormalPersonDetail sfaAbnormalPersonDetail = sfaAbnormalPersonDetailMapper.selectOne(new LambdaQueryWrapper<SfaAbnormalPersonDetail>().eq(SfaAbnormalPersonDetail::getReportId, request.getReportId()).eq(SfaAbnormalPersonDetail::getIsLeader, 0).eq(SfaAbnormalPersonDetail::getIsDelete, 0));
            if (Objects.isNull(sfaAbnormalPersonDetail)) {
                sfaAbnormalPersonDetail = new SfaAbnormalPersonDetail();
            }

            BeanUtils.copyProperties(abnormalPersonVo, sfaAbnormalPersonDetail);
            sfaAbnormalPersonDetail.setReportId(request.getReportId());
            String organizationParentId = organizationId;
            if (StringUtils.isBlank(organizationId)) {
                organizationId = organizationMapper.getZbOrganizationIdByBusinessGroup(RequestUtils.getBusinessGroup());
            }
            sfaAbnormalPersonDetail.setAbnormalOrganizationId(abnormalPersonVo.getAbnormalOrganizationId());
            sfaAbnormalPersonDetail.setAbnormalOrganizationName(abnormalPersonVo.getAbnormalOrganizationName());
            sfaAbnormalPersonDetail.setAbnormalName(abnormalPersonVo.getAbnormalName());
            sfaAbnormalPersonDetail.setParentOrganizationId(organizationId);
            sfaAbnormalPersonDetail.setParentOrganizationName(organizationMapper.getOrganizationName(organizationId));

            sfaAbnormalPersonDetail.setCreatePeople(personInfo.getEmployeeId());
            sfaAbnormalPersonDetail.setCreateTime(LocalDateTime.now());
            sfaAbnormalPersonDetail.setIsLeader(0);
            sfaAbnormalPersonDetail.setGoalAchievementRateImprovement(abnormalPersonVo.getGoalAchievementImprovement());
            sfaAbnormalPersonDetail.setManagementReasonImprovement(abnormalPersonVo.getManagementImprovement());

            Long id = sfaAbnormalPersonDetail.getId();
            if (Objects.nonNull(id)) {
                sfaAbnormalPersonDetailMapper.updateById(sfaAbnormalPersonDetail);
            } else {
                sfaAbnormalPersonDetailMapper.insert(sfaAbnormalPersonDetail);
            }


        }

        SfaReviewReportEntity sfaReviewReportEntity = sfaReviewReportMapper.selectById(request.getReportId());

        List<AbnormalPersonVo> nextAbnormalPersonList = request.getNextAbnormalPersonList();
        if (!CollectionUtils.isEmpty(nextAbnormalPersonList)) {
            nextAbnormalPersonList.forEach(e -> {


                // 找到reportID
                SfaReviewReportEntity sfaReviewReportEntity1 = sfaReviewReportMapper.selectOne(new LambdaQueryWrapper<SfaReviewReportEntity>().eq(SfaReviewReportEntity::getOrganizationId, e.getAbnormalOrganizationId()).eq(SfaReviewReportEntity::getDeleteFlag, 0).eq(SfaReviewReportEntity::getYearMonth, sfaReviewReportEntity.getYearMonth()));

                if (Objects.nonNull(sfaReviewReportEntity1)) {
                    SfaAbnormalPersonDetail sfaAbnormalPersonDetail = sfaAbnormalPersonDetailMapper.selectOne(new LambdaQueryWrapper<SfaAbnormalPersonDetail>().eq(SfaAbnormalPersonDetail::getReportId, sfaReviewReportEntity1.getReviewId()).eq(SfaAbnormalPersonDetail::getIsLeader, 1).eq(SfaAbnormalPersonDetail::getIsDelete, 0));
                    if (Objects.isNull(sfaAbnormalPersonDetail)) {
                        sfaAbnormalPersonDetail = new SfaAbnormalPersonDetail();
                    }
                    BeanUtils.copyProperties(e, sfaAbnormalPersonDetail);

                    if (Objects.nonNull(sfaReviewReportEntity1)) {
                        sfaAbnormalPersonDetail.setAbnormalOrganizationId(e.getAbnormalOrganizationId());
                        sfaAbnormalPersonDetail.setAbnormalOrganizationName(e.getAbnormalOrganizationName());
                        sfaAbnormalPersonDetail.setReportId(sfaReviewReportEntity1.getReviewId().intValue());
                        sfaAbnormalPersonDetail.setParentOrganizationId(parentOrganizationIdByPerson.getOrganizationId());
                        sfaAbnormalPersonDetail.setParentOrganizationName(parentOrganizationIdByPerson.getOrganizationName());
                        sfaAbnormalPersonDetail.setParentAbnormalName(parentOrganizationIdByPerson.getEmployeeName());
                        sfaAbnormalPersonDetail.setCreatePeople(personInfo.getEmployeeId());
                        sfaAbnormalPersonDetail.setCreateTime(LocalDateTime.now());
                        sfaAbnormalPersonDetail.setAbnormalName(e.getAbnormalName());
                        sfaAbnormalPersonDetail.setIsLeader(1);
                        sfaAbnormalPersonDetail.setGoalAchievementRateImprovement(e.getGoalAchievementImprovement());
                        sfaAbnormalPersonDetail.setGoalAchievementRateImprovement(e.getGoalAchievementImprovement());
                        sfaAbnormalPersonDetail.setManagementReasonImprovement(e.getManagementImprovement());

                        Long id = sfaAbnormalPersonDetail.getId();
                        if (Objects.nonNull(id)) {
                            sfaAbnormalPersonDetailMapper.updateById(sfaAbnormalPersonDetail);
                        } else {
                            sfaAbnormalPersonDetailMapper.insert(sfaAbnormalPersonDetail);
                        }
                    }
                }


            });
        }
    }

    /**
     * 处理团队达成颜色
     *
     * @param Resultslist
     * @return
     */
    public List<TeamResultsVo> publicHandleColor(List<TeamResultsVo> Resultslist) {
        Resultslist.forEach(r -> {
            if (null != r.getEmploymentRateSeason()) {
                if (r.getEmploymentRateSeason() > 20 || r.getEmploymentRateSeason() == 20) {
                    r.setEmploymentRateSeasonColor("#D9001B");
                } else if (r.getEmploymentRateSeason() < 20 && r.getEmploymentRateSeason() > 15) {
                    r.setEmploymentRateSeasonColor("#F59A23");
                } else if (r.getEmploymentRateSeason() < 15 && r.getEmploymentRateSeason() > 10) {
                    r.setEmploymentRateSeasonColor("#555555");
                } else if (r.getEmploymentRateSeason() < 10 || r.getEmploymentRateSeason() == 10) {
                    r.setEmploymentRateSeasonColor("#68A603");
                }
            }
            if (null != r.getEmploymentRateYears()) {
                if (r.getEmploymentRateYears() > 20 || r.getEmploymentRateYears() == 20) {
                    r.setEmploymentRateYearsColor("#D9001B");
                } else if (r.getEmploymentRateYears() < 20 && r.getEmploymentRateYears() > 15) {
                    r.setEmploymentRateYearsColor("#F59A23");
                } else if (r.getEmploymentRateYears() < 15 && r.getEmploymentRateYears() > 10) {
                    r.setEmploymentRateYearsColor("#555555");
                } else if (r.getEmploymentRateYears() < 10 || r.getEmploymentRateYears() == 10) {
                    r.setEmploymentRateYearsColor("#68A603");
                }
            }
            List<TeamPerformanceVo> list = r.getList();
            list.forEach(l -> {
                /** #D9001B 红  #F59A23 黄  #555555 灰 #68A603 绿   */
                // 盘价业绩目标达成率 红色，橙色，灰色，绿色
                if (null != l.getGoalAchievementRate()) {
                    if (l.getGoalAchievementRate() > 100) {
                        l.setGoalAchievementRateColor("#68A603");
                    } else if (l.getGoalAchievementRate() >= 75 && l.getGoalAchievementRate() < 100) {
                        l.setGoalAchievementRateColor("#F59A23");
                    } else if (l.getGoalAchievementRate() < 75) {
                        l.setGoalAchievementRateColor("#D9001B");
                    }
                }
                // 盘价业绩环比增长率 红色，灰色，绿色
                if (null != l.getPerformanceAchievementRate()) {
                    if (l.getPerformanceAchievementRate() < 0) {
                        l.setPerformanceAchievementRateColor("#D9001B");
                    } else if ((l.getPerformanceAchievementRate() > 0 && l.getPerformanceAchievementRate() < 25) || l.getPerformanceAchievementRate() == 25) {
                        l.setPerformanceAchievementRateColor("#555555");
                    } else if (l.getPerformanceAchievementRate() > 25) {
                        l.setPerformanceAchievementRateColor("#68A603");
                    }
                }
                if (null != l.getYearAchievementRate()) {
                    // 盘价业绩同比增长率 红色，灰色，绿
                    if (l.getYearAchievementRate() >= 100) {
                        l.setYearAchievementRateColor("#68A603");
                    } else if ((l.getYearAchievementRate() < 0 && l.getYearAchievementRate() >= -25)) {
                        l.setYearAchievementRateColor("#F59A23");
                    } else if (l.getYearAchievementRate() < -25) {
                        l.setYearAchievementRateColor("#D9001B");
                    }
                }
                if (null != l.getManagementPositionPerPerformance()) {
                    // 管理岗人均业绩 红色
                    if (l.getManagementPositionPerPerformance() < 30000) {
                        l.setManagementPositionPerPerformanceColor("#D9001B");
                    }
                }
                if (null != l.getEmploymentRate()) {
                    // 用人费用率 红色，橙色，灰色，绿色
                    if (l.getEmploymentRate() > 20 || l.getEmploymentRate() == 20) {
                        l.setEmploymentRateColor("#D9001B");
                    } else if (l.getEmploymentRate() < 20 && l.getEmploymentRate() > 15) {
                        l.setEmploymentRateColor("#F59A23");
                    } else if (l.getEmploymentRate() < 15 && l.getEmploymentRate() > 10) {
                        l.setEmploymentRateColor("#555555");
                    } else if (l.getEmploymentRate() < 10 || l.getEmploymentRate() == 10) {
                        l.setEmploymentRateColor("#68A603");
                    }
                }
            });
        });
        return Resultslist;
    }


    @Override
    public IPage<MarkVo> getEvaluatorList(MarkRequest request) {
        Page<MarkVo> result = new Page<>();

        //查出固定模版
        List<MarkContentVo> markListTemplate = markMapper.getMarkListTemplate(request);

        List<MarkVo> markList = markMapper.getMarkList(request);
        markList.forEach(m -> {
            ArrayList<MarkContentVo> markContentVos = new ArrayList<>();
            BeanUtils.copyProperties(markListTemplate, markContentVos, MarkContentVo.class, MarkContentVo.class);
            //json字符串转自定义对象
            JSONArray jsonArray = JSON.parseArray(m.getContent());
            List<MarkContentVo> markVos = JSONObject.parseArray(jsonArray.toJSONString(), MarkContentVo.class);
            markContentVos.forEach(template -> {
                markVos.forEach(mark -> {
                    if (template.getTitle().equals(mark.getTitle())) {
                        template.setScore(mark.getScore());
                        template.setReason(mark.getReason());
                    }
                });
            });
            m.setMarkContentVo(markContentVos);
        });

        if (!request.getEmployeeId().contains("00272473")) {
            SfaEmployeeInfoModel employee = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("employee_id", request.getEmployeeId()));
            if (Objects.nonNull(employee)) {
                List<MarkVo> collect = markList.stream().filter(m -> !m.getBeEvaluateUserMemberkey().equals(employee.getMemberKey())).collect(Collectors.toList());
                result.setRecords(collect);
            }
        } else {
            result.setRecords(markList);
        }
        result.setTotal(markList.size());
        return result;
    }

    @Override
    public MarkContentAvgVo getEvaluatorListAvg(MarkRequest request) {
        MarkContentAvgVo markContentAvgVo = new MarkContentAvgVo();
       /* -- 如果类型是7 就是是总部 ，判断工号是00272473 ，老板可以看所有人
        -- 类型不是7  根据它传的组织得到类型 如果是等于，就是同级看不了，其它的可以看*/
        //查出固定模版
        List<MarkContentVo> markListTemplate = markMapper.getMarkListTemplate(request);

        IPage<MarkVo> evaluator = null;
        //找到评分列表
        if (RequestUtils.getLoginInfo().getPositionTypeId() == 7 && request.getEmployeeId().equals("00272473")) {
            evaluator = getEvaluatorList(request);
        } else {
            Integer positionId = organizationMapper.getPositionTypeIdByOrganizationId(request.getBeOrganization());
            if (RequestUtils.getLoginInfo().getPositionTypeId() != positionId) {
                evaluator = getEvaluatorList(request);
            }
        }
        //评分列表不为空 处理数据到固定模版中
        if (null != evaluator && CommonUtil.ListUtils.isNotEmpty(evaluator.getRecords())) {
            List<MarkVo> records = evaluator.getRecords();
            List<MarkContentVo> markContentVos = new ArrayList<>();
            records.forEach(r -> {
                List<MarkContentVo> markContentVo = r.getMarkContentVo();
                markContentVos.addAll(markContentVo);
            });
            Map<String, List<MarkContentVo>> collect = markContentVos.stream().filter(m -> StringUtils.isNotBlank(m.getTitle())).collect(groupingBy(MarkContentVo::getTitle));
            for (Map.Entry<String, List<MarkContentVo>> col : collect.entrySet()) {
                for (MarkContentVo m : markListTemplate) {
                    if (m.getTitle().equals(col.getKey())) {
                        int sum = col.getValue().stream().mapToInt(MarkContentVo::getScore).sum();
                        //四舍五入取平均
                        long round = Math.round(Double.valueOf(sum / col.getValue().size()));
                        m.setScore((int) round);
                    }
                }
            }
            markContentAvgVo.setIsMark(0);
            markContentAvgVo.setList(markListTemplate);
        }
        return markContentAvgVo;
    }


    @Override
    public RealTimeYearVo queryResultsDateYear(RealTimeYearRequest request) {
        log.info("start RealtimeDataServiceImpl queryResultsDateYear request:{}", request);
        if (StringUtils.isNotBlank(request.getEmployeeId()) && StringUtils.isBlank(request.getOrganizationId())) {
            List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(), RequestUtils.getLoginInfo());
            if (CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)) {
                request.setOrganizationId(employeeOrganizationId.get(0));
            }
        }
        RealTimeYearVo realTimeYearVo = new RealTimeYearVo();
        RealTimeYearVo resultsPerformanceYear = realtimeMapper.getResultsPerformanceYear(request);

        if (Objects.nonNull(resultsPerformanceYear)) {
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("mobile", resultsPerformanceYear.getMobile()).eq("employee_status", 2));
            if (Objects.nonNull(sfaEmployeeInfoModel)) {
                ApplyMemberPo applyMember = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
                if (Objects.nonNull(applyMember)) {
                    if (null != applyMember.getGender()) {
                        if (applyMember.getGender() == 1) {
                            resultsPerformanceYear.setSex("男");
                        } else if (applyMember.getGender() == 2) {
                            resultsPerformanceYear.setSex("女");
                        }
                    }
                }
            }
        }

        if (request.getOrganizationId().contains("ZB_Z")) {
            RrecruitmentManagementDataVo rrecruitmentManagement = new RrecruitmentManagementDataVo();
            if (Objects.nonNull(resultsPerformanceYear)) {
                BeanUtils.copyProperties(resultsPerformanceYear, rrecruitmentManagement);
                TradeGoodReq tradeGoodReq = new TradeGoodReq();
                BeanUtils.copyProperties(request, tradeGoodReq);
                RrecruitmentManagementDataVo rrecruitmentManagementDataVo = publicZBInformation(tradeGoodReq, rrecruitmentManagement);
                resultsPerformanceYear.setEmployeeName(rrecruitmentManagementDataVo.getEmployeeName());
                resultsPerformanceYear.setOnboardTime(rrecruitmentManagementDataVo.getOnboardTime());
                resultsPerformanceYear.setOnboardDays(rrecruitmentManagementDataVo.getOnboardDays());
            }
        }
        request.setLastYear(Integer.valueOf(request.getYear()) - 1);
        if (Objects.nonNull(resultsPerformanceYear)) {
            //累计业绩
            resultsPerformanceYear.setCumulativePerformance(realtimeMapper.getYearPerformanceAchievedTrendsTotal(request));
            //每日业绩
            resultsPerformanceYear.setDailyPerformance(realtimeMapper.getYearPerformanceAchievedTrends(request));
        }
        if (Objects.nonNull(resultsPerformanceYear)) {
            return resultsPerformanceYear;
        } else {
            return realTimeYearVo;
        }
    }

    @Override
    public com.wantwant.commons.pagination.Page<NextResultsYearVo> getResultsDateBusinessYear(RealTimeYearRequest request) {
        log.info("start RealtimeDataServiceImpl getResultsDateBusinessYear request:{}", request);
        if (StringUtils.isNotBlank(request.getEmployeeId()) && StringUtils.isBlank(request.getOrganizationId())) {
            List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(), RequestUtils.getLoginInfo());
            if (CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)) {
                request.setOrganizationId(employeeOrganizationId.get(0));
            }
        }
        com.wantwant.commons.pagination.Page<NextResultsYearVo> result = new com.wantwant.commons.pagination.Page<>();

        RealTimeYearVo resultsYear = realtimeMapper.getResultsPerformanceYear(request);
        if (Objects.isNull(resultsYear)) {
            throw new ApplicationException("数据生成中，请稍后在试");
        }
        if (Objects.nonNull(resultsYear)) {
            request.setOrganizationType(resultsYear.getPositionTypeId());
        }
        List<NextResultsYearVo> resultslist = null;
        List<NextResultsYearVo> list = new ArrayList<>();
        if (request.getPositionTypeId() != null) {//app
            resultslist = realtimeMapper.getResultsNextYearByPositionTypeId(request);
            if (CommonUtil.ListUtils.isNotEmpty(resultslist)) {
                list.addAll(resultslist);
            }
        } else {//backend
            if (request.getIsNextRealtime() == 0) {
                //总部查自己 也要查下级
                // 自己
                NextResultsYearVo resultsDate = realtimeMapper.getResultsYear(request);
                if (Objects.nonNull(resultsYear)) {
                    //查看下级*/
                    resultslist = realtimeMapper.getResultsNextYear(request);
                    if (Objects.nonNull(resultsDate)) {
                        list.add(resultsDate);
                        list.addAll(resultslist);
                    }
                }
                if (CommonUtil.ListUtils.isNotEmpty(list)) {
                    list.forEach(l -> {
                        if (l.getOrganizationId().equals(request.getOrganizationId())) {
                            l.setArea("合计");
                        }
                    });
                }
            } else {
                resultslist = realtimeMapper.getResultsNextYear(request);
                if (CommonUtil.ListUtils.isNotEmpty(resultslist)) {
                    list.addAll(resultslist);
                }
            }
        }

        List<NextResultsYearVo> collect = null;
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            if (StringUtils.isNotBlank(request.getOrderType()) && StringUtils.isNotBlank(request.getOrderName())) {
                if (request.getOrderType().equals("desc")) {
                    if (request.getOrderName().equals("cumulativePerformance")) {
                        collect = list.stream().sorted(Comparator.comparing(NextResultsYearVo::getCumulativePerformance).reversed()).collect(Collectors.toList());
                    }
                    if (request.getOrderName().equals("contemporaneousPerformance")) {
                        collect = list.stream().sorted(Comparator.comparing(NextResultsYearVo::getContemporaneousPerformance).reversed()).collect(Collectors.toList());
                    }
                    if (request.getOrderName().equals("yearOnYear")) {
                        collect = list.stream().sorted(Comparator.comparing(NextResultsYearVo::getYearOnYear).reversed()).collect(Collectors.toList());
                    }
                } else {
                    if (request.getOrderName().equals("cumulativePerformance")) {
                        collect = list.stream().sorted(Comparator.comparing(NextResultsYearVo::getCumulativePerformance)).collect(Collectors.toList());
                    }
                    if (request.getOrderName().equals("contemporaneousPerformance")) {
                        collect = list.stream().sorted(Comparator.comparing(NextResultsYearVo::getContemporaneousPerformance)).collect(Collectors.toList());
                    }
                    if (request.getOrderName().equals("yearOnYear")) {
                        collect = list.stream().sorted(Comparator.comparing(NextResultsYearVo::getYearOnYear)).collect(Collectors.toList());
                    }
                }
            } else {
                collect = list.stream().sorted(Comparator.comparing(NextResultsYearVo::getCumulativePerformance).reversed()).collect(Collectors.toList());
            }
        }
        result.setList(collect);
        if (CommonUtil.ListUtils.isNotEmpty(collect)) {
            result.setTotalItem(collect.size());
        }
        return result;
    }

    @Override
    public IPage<CommodityYearSkuLineVo> queryGoodsProductLineDataYear(RealTimeYearRequest request) {
        log.info("start RealtimeDataServiceImpl queryGoodsProductLineDataYear request:{}", request);
        Page<CommodityYearSkuLineVo> result = new Page<>();
        if (StringUtils.isNotBlank(request.getEmployeeId()) && StringUtils.isBlank(request.getOrganizationId())) {
            List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(), RequestUtils.getLoginInfo());
            if (CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)) {
                request.setOrganizationId(employeeOrganizationId.get(0));
            }
        }
        List<CommodityYearSkuLineVo> productList = realtimeMapper.queryGoodsProductLineDataYear(request);
        if (request.getProductType() == 1) {
            for (CommodityYearSkuLineVo p : productList) {
                List<SkuDataStockVo> skuDataStockVos = realtimeMapper.selectClassification(request.getYear(), p.getProductNmae());
                p.setList(skuDataStockVos);
            }
        }
        int count = realtimeMapper.queryGoodsProductLineDataYearCount(request);
        result.setRecords(productList);
        result.setTotal(count);
        return result;
    }

    @Override
    public List<NextResultsYearVo> getResultsDateBusinessYearExport(RealTimeYearRequest request) {
        request.setBusinessGroup(RequestUtils.getLoginInfo().getBusinessGroup());
        if (StringUtils.isNotBlank(request.getEmployeeId()) && StringUtils.isBlank(request.getOrganizationId())) {
            List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(), RequestUtils.getLoginInfo());
            if (CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)) {
                request.setOrganizationId(employeeOrganizationId.get(0));
            }
        }
        RealTimeYearVo resultsYear = realtimeMapper.getResultsPerformanceYear(request);
        if (Objects.isNull(resultsYear)) {
            throw new ApplicationException("数据生成中，请稍后在试");
        }
        if (Objects.nonNull(resultsYear)) {
            request.setOrganizationType(resultsYear.getPositionTypeId());
        }

        List<NextResultsYearVo> list = new ArrayList<>();
        // 自己
        NextResultsYearVo resultsDate = realtimeMapper.getResultsYear(request);
        resultsDate.setArea("合计");
        list.add(resultsDate);
        if (Objects.nonNull(resultsYear)) {
            //查看下级 战区*/
            List<NextResultsYearVo> resultslist = realtimeMapper.getResultsNextYear(request);
            //list.addAll(Resultslist);
            if (CommonUtil.ListUtils.isNotEmpty(resultslist)) {
                for (NextResultsYearVo r : resultslist) {
                    list.add(r);
                    //战区组织 岗位类型*/
                    request.setPositionTypeId(r.getPositionTypeId());
                    request.setOrganizationId(r.getOrganizationId());
                    NextResultsYearVo emplyeeDateArea = realtimeMapper.getResultsYear(request);
                    if (Objects.nonNull(emplyeeDateArea)) {
                        request.setOrganizationType(emplyeeDateArea.getPositionTypeId());
                        List<NextResultsYearVo> resultslistVarea = null;
                        if (emplyeeDateArea.getPositionTypeId() != 3) {
                            //战区组织获取下级 获取大区总监*/
                            resultslistVarea = realtimeMapper.getResultsNextYear(request);
                        }

                        if (CommonUtil.ListUtils.isNotEmpty(resultslistVarea) && emplyeeDateArea.getPositionTypeId() != 3) {
                            for (NextResultsYearVo v : resultslistVarea) {
                                list.add(v);
                                request.setPositionTypeId(v.getPositionTypeId());
                                //省区组织 岗位类型*/
                                request.setOrganizationId(v.getOrganizationId());
                                NextResultsYearVo emplyeeDateProvince = realtimeMapper.getResultsYear(request);
                                if (Objects.nonNull(emplyeeDateProvince)) {
                                    request.setOrganizationType(emplyeeDateProvince.getPositionTypeId());
                                    List<NextResultsYearVo> resultslistCompany = null;
                                    if (emplyeeDateProvince.getPositionTypeId() != 3) {
                                        //省区组织获取下级 分公司组织*/
                                        resultslistCompany = realtimeMapper.getResultsNextYear(request);
                                    }

                                    if (CommonUtil.ListUtils.isNotEmpty(resultslistCompany) && emplyeeDateProvince.getPositionTypeId() != 3) {
                                        for (NextResultsYearVo c : resultslistCompany) {
                                            list.add(c);
                                            //分公司组织  岗位类型
                                            request.setPositionTypeId(v.getPositionTypeId());
                                            request.setOrganizationId(c.getOrganizationId());
                                            NextResultsYearVo emplyeeDateCompany = realtimeMapper.getResultsYear(request);

                                            if (Objects.nonNull(emplyeeDateCompany)) {
                                                request.setOrganizationType(emplyeeDateCompany.getPositionTypeId());
                                                List<NextResultsYearVo> resultslistDepartment = null;
                                                if (emplyeeDateCompany.getPositionTypeId() != 3) {
                                                    ////获取下级 区域经理组织
                                                    resultslistDepartment = realtimeMapper.getResultsNextYear(request);
                                                }

                                                if (CommonUtil.ListUtils.isNotEmpty(resultslistDepartment) && emplyeeDateCompany.getPositionTypeId() != 3) {
                                                    for (NextResultsYearVo d : resultslistDepartment) {
                                                        list.add(d);
                                                        //分公司组织  岗位类型
                                                        request.setPositionTypeId(v.getPositionTypeId());
                                                        request.setOrganizationId(d.getOrganizationId());
                                                        NextResultsYearVo emplyeeDateBranch = realtimeMapper.getResultsYear(request);
                                                        if (Objects.nonNull(emplyeeDateBranch)) {
                                                            request.setOrganizationType(emplyeeDateBranch.getPositionTypeId());
                                                            ////获取下级 合伙人
                                                            List<NextResultsYearVo> resultslistBranch = new ArrayList<>();
                                                            if (emplyeeDateBranch.getPositionTypeId() != 3) {
                                                                ////获取下级 区域经理组织
                                                                List<NextResultsYearVo> resultsBranch = realtimeMapper.getResultsNextYear(request);
                                                                resultslistBranch.addAll(resultsBranch);
                                                            }
                                                            list.addAll(resultslistBranch);
                                                        }
                                                    }
                                                }

                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            list.add(resultsDate);
        }
        return list;
    }

    @Override
    public Map<String, String> queryPerformanceSerialNumber() {
        HashMap<String, String> map = new HashMap<>();
        List<SfaMetricsEntity> sfaMetricsEntities = sfaMetricsMapper.selectList(new LambdaQueryWrapper<SfaMetricsEntity>().eq(SfaMetricsEntity::getDeleteFlag, 0).eq(SfaMetricsEntity::getStatus, 1));
        sfaMetricsEntities.forEach(s -> {
            map.put(String.valueOf(s.getId()), s.getCalculateLogic());
        });
        return map;
    }

    @Override
    public List<GroupPerformanceVo> queryAllGroupPerformanceDate(AllGroupPerformanceRequest request) {
        //自然月获取当前日期是不是在本月，是本月获取当天日期，不是本月获取月的最后一天-->取当日业绩
        if ("10".equals(request.getDateTypeId())) {
            if (LocalDate.now().toString().contains(request.getYearMonth())) {
                request.setTheDate(LocalDate.now().toString());
            } else {
                LocalDate parse = LocalDate.parse(request.getYearMonth() + "-01");
                LocalDate lastDayOfMonth = parse.withDayOfMonth(parse.lengthOfMonth());
                request.setTheDate(lastDayOfMonth.toString());
            }
        }
        List<GroupPerformanceVo> groupPerformanceVos = realtimeMapper.queryAllGroupPerformanceDate(request);
        if (CollectionUtils.isEmpty(groupPerformanceVos)) {
            log.error("queryAllGroupPerformanceDate:groupPerformanceVos 查询不到全组数据");
            return null;
        }
        BigDecimal totalPerformance = groupPerformanceVos.stream().map(GroupPerformanceVo::getPerformance).reduce(BigDecimal.ZERO, BigDecimal::add);
        groupPerformanceVos.forEach(p -> {
            if (BigDecimal.ZERO.compareTo(totalPerformance) == 0) {
                p.setPerformanceRatio(BigDecimal.ZERO);
            } else {
                p.setPerformanceRatio(p.getPerformance().multiply(new BigDecimal("100")).divide(totalPerformance, 1, BigDecimal.ROUND_HALF_UP));
            }
            p.setSaleGoalAchievementColor(realTimeUtils.saleGoalAchievementColor(request.getDateTypeId(), request.getYearMonth(), p.getPerformanceAchievementRate()));
        });

        return groupPerformanceVos.stream().sorted(Comparator.comparingInt(GroupPerformanceVo::getSort)).collect(Collectors.toList());

    }

    @Override
    public PerformanceDateVo queryPerformanceDate(TradeGoodNewReq request) {
        log.info("start RealtimeDataServiceImpl queryPerformanceDate request:{}", request);
        PerformanceDateVo performanceDateVo = null;
        //自然月获取当前日期是不是在本月，是本月获取当天日期，不是本月获取月的最后一天-->取当日业绩
        request.setTheDate(realTimeUtils.getNearDate(request.getDateTypeId(), request.getYearMonth()));

        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setRelationMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        if (request.getIsPartner() == 0) {
            performanceDateVo = realtimeMapper.queryPerformanceDate(request);
            if (Objects.nonNull(performanceDateVo)) {
                //简历
                performanceDateVo.setResumeUrl(applyMemberMapper.getResumeUrl(performanceDateVo.getPositionId()));

                SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getPositionId, performanceDateVo.getPositionId()).in(SfaEmployeeInfoModel::getEmployeeStatus, 1, 2).orderByDesc(SfaEmployeeInfoModel::getId).last("limit 1"));
                if (Objects.nonNull(sfaEmployeeInfoModel)) {
                    performanceDateVo.setMemberKey(sfaEmployeeInfoModel.getMemberKey());
                }

                List<CeoBusinessOrganizationEntity> partJobOrganization = organizationMapper.getPartJobOrganization(request.getEmployeeId(), RequestUtils.getBusinessGroup(), RequestUtils.getChannel());
                if (CommonUtil.ListUtils.isNotEmpty(partJobOrganization)) {
                    CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = partJobOrganization.get(0);
                    if (request.getOrganizationId().contains("ZB_Z")) {
                        // 查询查询组主管
                        String departmentManagerEmpId = businessGroupService.getDepartmentManagerEmpId(request.getBusinessGroup());
                        EmployeeInfoVO employeeInfo = sfaPositionRelationMapper.selectZbEmployee(departmentManagerEmpId, request.getBusinessGroup());
                        if (Objects.nonNull(employeeInfo)) {
                            performanceDateVo.setEmployeeName(employeeInfo.getEmployeeName());
                            if (Objects.nonNull(employeeInfo.getOnboardTime())) {
                                performanceDateVo.setOnboardTime(LocalDate.parse(employeeInfo.getOnboardTime().toString().substring(0, 10)));
                            }
                            performanceDateVo.setOnboardDays(Objects.nonNull(employeeInfo.getOnboardDays()) ? String.valueOf(employeeInfo.getOnboardDays()) : "");
                            performanceDateVo.setUrl(employeeInfo.getAvatar());
                        }
                    }
                    if (ceoBusinessOrganizationEntity.getOrganizationType().equals("zb")) {
                        String isPay = applyMemberMapper.getIsPay(request.getEmployeeId());
                        //根据工号判断是总部，在这些部门有权限看薪资   再判断组织人员不在，就没有权限看薪资，有人才有权限
                        if (StringUtils.isNotBlank(isPay)) {
                            if (StringUtils.isNotBlank(performanceDateVo.getEmployeeName())) {
                                performanceDateVo.setIsPay(1);
                            }
                            performanceDateVo.setIsPay(1);
                        }
                    } else {
                        //不是总部，判断人员不在，就没有权限看薪资，有人才有权限
                        if (StringUtils.isNotBlank(performanceDateVo.getEmployeeName())) {
                            performanceDateVo.setIsPay(1);
                        }
                    }
                }

                //用人费用管控
                setCompanyAndDepartmentAllocatedHistory(request, performanceDateVo);

                performanceDateVo.setEmployeeStatus(EmployeeStatus.getDescByEnv(performanceDateVo.getEmployeeStatus()));
            }
        } else {

            performanceDateVo = realtimeMapper.queryPerformanceDatePartner(request);

            if (Objects.nonNull(performanceDateVo) && performanceDateVo.getMemberKey() != null) {
                // 再查一次旺铺表补充信息
                Map<String, String> extendInfo = customerMapper.selectCustomerExtendInfoByMemberKey(performanceDateVo.getMemberKey());
                if (CollectionUtil.isNotEmpty(extendInfo)) {
                    performanceDateVo.setSystemType(extendInfo.get("systemType"));
                    performanceDateVo.setSystemName(extendInfo.get("systemName"));
                }
                // 补充人员状态
                performanceDateVo.setEmployeeStatus(EmployeeStatus.getDescription(performanceDateVo.getEmployeeStatusEnum()));
                // 回访 拜访信息查询
                Integer callbackCount = customerManagementCallBackInfoMapper.selectCount(new LambdaQueryWrapper<CustomerManagementCallBackInfo>()
                        .eq(CustomerManagementCallBackInfo::getCustomerId, performanceDateVo.getMemberKey())
                        .eq(CustomerManagementCallBackInfo::getDeleteFlag, 0));
                performanceDateVo.setBeCallBackedFlag(callbackCount > 0);

//                Integer visitCount = customerVisitInfoMapper.selectCount(new LambdaQueryWrapper<CustomerVisitInfoModel>()
//                        .eq(CustomerVisitInfoModel::getCustomerId, performanceDateVo.getMemberKey())
//                        .eq(CustomerVisitInfoModel::getDelete_flag, 0));
//                performanceDateVo.setBeVisitedFlag(visitCount > 0);
            }
        }
        if (Objects.nonNull(performanceDateVo)) {
            performanceDateVo.setSaleGoalAchievementColor(realTimeUtils.saleGoalAchievementColor(request.getDateTypeId(), request.getYearMonth(), performanceDateVo.getPerformanceAchievementRate()));

            Integer employeeInfoId = performanceDateVo.getEmployeeInfoId();
            if (Objects.nonNull(employeeInfoId)) {
                Integer interviewRecordId = sfaInterviewProcessRecordMapper.selectCurrentRecordByEmployeeInfoId(employeeInfoId);
                performanceDateVo.setInterviewRecordId(interviewRecordId);
            }
            performanceDateVo.setPositionName(OrganizationPositionRelationEnums.getPositionNameByEnv(performanceDateVo.getPositionName()));
            performanceDateVo.setType(OrganizationPositionRelationEnums.getPositionNameByEnv(performanceDateVo.getType()));
            personalDataShowStrategy(performanceDateVo);
        }


        return performanceDateVo;
    }

    /**
     * 人员模块数据展示策略
     *
     * @param performanceDateVo 全部产品组的 总督导、大区总监、省区总监、区域总监 、业务BD、导购人员 人员岗位
     *                          当前权限内除了【编制数】指标；其他指标有数据展示，无数据 则不显示该岗位
     *                          <p>
     *                          在职人数、入职人数、离职人数、离职率
     */
    private void personalDataShowStrategy(PerformanceDateVo performanceDateVo) {
        //导购人员
        Boolean shoppingGuideShowFlag = Boolean.TRUE;
        if (BigDecimalUtil.isNullOrLessThanOrEqualToZero(performanceDateVo.getOnJobShoppingGuideCount()) && BigDecimalUtil.isNullOrLessThanOrEqualToZero(performanceDateVo.getCloseShoppingGuideCount()) && BigDecimalUtil.isNullOrLessThanOrEqualToZero(performanceDateVo.getEntryShoppingGuideCount())) {
            shoppingGuideShowFlag = Boolean.FALSE;
        }
        performanceDateVo.setShoppingGuideShowFlag(shoppingGuideShowFlag);

        //业务BD
        Boolean businessDevelopmentShowFlag = Boolean.TRUE;
        if ((performanceDateVo.getOnJobNumBusinessDevelopment() == null || performanceDateVo.getOnJobNumBusinessDevelopment() == 0) && (performanceDateVo.getEntryNumBusinessDevelopment() == null || performanceDateVo.getEntryNumBusinessDevelopment() == 0) && (performanceDateVo.getDimissionNumBusinessDevelopment() == null || performanceDateVo.getDimissionNumBusinessDevelopment() == 0) && BigDecimalUtil.isNullOrLessThanOrEqualToZero(performanceDateVo.getQuitRateBusinessDevelopment())) {
            businessDevelopmentShowFlag = Boolean.FALSE;
        }
        performanceDateVo.setBusinessDevelopmentShowFlag(businessDevelopmentShowFlag);

        //区域经理
        Boolean departmentShowFlag = Boolean.TRUE;
        if ((performanceDateVo.getOnJobNumDepartment() == null || performanceDateVo.getOnJobNumDepartment() == 0) && (performanceDateVo.getEntryNumDepartment() == null || performanceDateVo.getEntryNumDepartment() == 0) && (performanceDateVo.getDimissionNumDepartment() == null || performanceDateVo.getDimissionNumDepartment() == 0) && BigDecimalUtil.isNullOrLessThanOrEqualToZero(performanceDateVo.getQuitRateDepartment())) {
            departmentShowFlag = Boolean.FALSE;
        }
        performanceDateVo.setDepartmentShowFlag(departmentShowFlag);

        //区域总监
        Boolean companyShowFlag = Boolean.TRUE;
        if ((performanceDateVo.getOnJobNumCompany() == null || performanceDateVo.getOnJobNumCompany() == 0) && (performanceDateVo.getEntryNumCompany() == null || performanceDateVo.getEntryNumCompany() == 0) && (performanceDateVo.getDimissionNumCompany() == null || performanceDateVo.getDimissionNumCompany() == 0) && BigDecimalUtil.isNullOrLessThanOrEqualToZero(performanceDateVo.getQuitRateCompany())) {
            companyShowFlag = Boolean.FALSE;
        }
        performanceDateVo.setCompanyShowFlag(companyShowFlag);
        //省区总监
        Boolean provinceShowFlag = Boolean.TRUE;
        if ((performanceDateVo.getOnJobNumProvince() == null || performanceDateVo.getOnJobNumProvince() == 0) && (performanceDateVo.getEntryNumProvince() == null || performanceDateVo.getEntryNumProvince() == 0) && (performanceDateVo.getDimissionNumProvince() == null || performanceDateVo.getDimissionNumProvince() == 0) && BigDecimalUtil.isNullOrLessThanOrEqualToZero(performanceDateVo.getQuitRateProvince())) {
            provinceShowFlag = Boolean.FALSE;
        }

        performanceDateVo.setProvinceShowFlag(provinceShowFlag);

        //大区总监
        Boolean vareaShowFlag = Boolean.TRUE;
        if ((performanceDateVo.getOnJobNumVrea() == null || performanceDateVo.getOnJobNumVrea() == 0) && (performanceDateVo.getEntryNumVrea() == null || performanceDateVo.getEntryNumVrea() == 0) && (performanceDateVo.getDimissionNumVrea() == null || performanceDateVo.getDimissionNumVrea() == 0) && BigDecimalUtil.isNullOrLessThanOrEqualToZero(performanceDateVo.getQuitRateVrea())) {
            vareaShowFlag = Boolean.FALSE;
        }

        performanceDateVo.setVareaShowFlag(vareaShowFlag);

        //总督导
        Boolean areaShowFlag = Boolean.TRUE;
        if ((performanceDateVo.getOnJobNumArea() == null || performanceDateVo.getOnJobNumArea() == 0) && (performanceDateVo.getEntryNumArea() == null || performanceDateVo.getEntryNumArea() == 0) && (performanceDateVo.getDimissionNumArea() == null || performanceDateVo.getDimissionNumArea() == 0) && BigDecimalUtil.isNullOrLessThanOrEqualToZero(performanceDateVo.getQuitRateArea())) {
            areaShowFlag = Boolean.FALSE;
        }
        performanceDateVo.setAreaShowFlag(areaShowFlag);
    }

    /**
     * 用人费用管控
     * 管理岗 区域总监 区域经理
     */
    private PerformanceDateVo setCompanyAndDepartmentAllocatedHistory(TradeGoodNewReq request, PerformanceDateVo performanceDateVo) {
        //根据岗位类型展示额度
        String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
        //区域经理不做处理
        if (!RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(organizationType)) {
            SalaryControlSearchRequest salaryControlSearchRequest = new SalaryControlSearchRequest();
            if (!RankingListPositionEnums.ZB.getOrganizationType().equals(organizationType)) {
                salaryControlSearchRequest.setOrgCode(request.getOrganizationId());
            }
            List<SalaryControlConfigVo> salaryControlConfigVos = salaryMiddlewareService.getQuotaControl(salaryControlSearchRequest);
            //标记管理岗额度查询是否有数据
            Boolean managementTabQueryResult = Boolean.FALSE;
            //管理岗已使用额度
            BigDecimal managementUsedQuota = null;
            //管理岗剩余额度
            BigDecimal managementSurplus = null;
            if (CollectionUtil.isNotEmpty(salaryControlConfigVos)) {
                managementTabQueryResult = Boolean.TRUE;
                //不为空则取值
                managementUsedQuota = salaryControlConfigVos.stream().map(SalaryControlConfigVo::getUsedQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
                performanceDateVo.setManagementUsedQuota(managementUsedQuota);
                managementSurplus = salaryControlConfigVos.stream().map(SalaryControlConfigVo::getSurplus).reduce(BigDecimal.ZERO, BigDecimal::add);
                performanceDateVo.setManagementSurplus(managementSurplus);
            }
            if (RankingListPositionEnums.ZB.getOrganizationType().equals(organizationType)) {
                //总部剩余额度为null直接不计算
                if (!managementTabQueryResult) {
                    return performanceDateVo;
                }
                //总部需要遍历所有组织下的 区域总监和区域经理 11.15新增省区总监
                // 1总督导 12大区总监 2区域总监 11省区总监 10区域经理
                List<SalaryAllocatedVo> allocatedHistory = zWSalaryService.getGroupCompanyAndDepartmentAndProvinceAllocatedHistory(request.getBusinessGroup());
                if (CollectionUtil.isNotEmpty(allocatedHistory)) {
                    //1总督导
                    List<SalaryAllocatedVo> areaHistory = allocatedHistory.stream().filter(x -> "1".equals(x.getPositionTypeId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(areaHistory)) {
                        Integer areaCount = areaHistory.size();
                        BigDecimal areaUsed = areaHistory.stream().map(SalaryAllocatedVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                        performanceDateVo.setAreaUsedQuota(areaUsed);
                        //剩余额度预计可使用人数 = 剩余额度/(已使用额度/已使用人数)
                        //areaCount--用人费用可能为0 做条件筛选
                        if (BigDecimal.ZERO.compareTo(managementSurplus) >= 0 || BigDecimal.ZERO.compareTo(areaUsed) >= 0) {
                            performanceDateVo.setAreaSurplusQuotaExpectedAvailabilityCount(0);
                        } else {
                            BigDecimal divide = managementSurplus.divide(areaUsed.divide(new BigDecimal(areaCount), 2, BigDecimal.ROUND_HALF_UP), 0, BigDecimal.ROUND_DOWN);
                            performanceDateVo.setAreaSurplusQuotaExpectedAvailabilityCount(divide.intValue());
                        }
                    }


                    //大区总监
                    List<SalaryAllocatedVo> vareaHistory = allocatedHistory.stream().filter(x -> "12".equals(x.getPositionTypeId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(vareaHistory)) {
                        Integer vareaCount = vareaHistory.size();
                        BigDecimal vareaUsed = vareaHistory.stream().map(SalaryAllocatedVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                        performanceDateVo.setVareaUsedQuota(vareaUsed);
                        //剩余额度预计可使用人数 = 剩余额度/(已使用额度/已使用人数)
                        //vareaUsed--用人费用可能为0 做条件筛选
                        if (BigDecimal.ZERO.compareTo(managementSurplus) >= 0 || BigDecimal.ZERO.compareTo(vareaUsed) >= 0) {
                            performanceDateVo.setVareaSurplusQuotaExpectedAvailabilityCount(0);
                        } else {
                            BigDecimal divide = managementSurplus.divide(vareaUsed.divide(new BigDecimal(vareaCount), 2, BigDecimal.ROUND_HALF_UP), 0, BigDecimal.ROUND_DOWN);
                            performanceDateVo.setVareaSurplusQuotaExpectedAvailabilityCount(divide.intValue());
                        }
                    }


                    //区域总监
                    List<SalaryAllocatedVo> companyHistory = allocatedHistory.stream().filter(x -> "2".equals(x.getPositionTypeId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(companyHistory)) {
                        Integer companyCount = companyHistory.size();
                        BigDecimal companyUsed = companyHistory.stream().map(SalaryAllocatedVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                        performanceDateVo.setCompanyUsedQuota(companyUsed);
                        //剩余额度预计可使用人数 = 剩余额度/(已使用额度/已使用人数)
                        //companyUsed--用人费用可能为0 做条件筛选
                        if (BigDecimal.ZERO.compareTo(managementSurplus) >= 0 || BigDecimal.ZERO.compareTo(companyUsed) >= 0) {
                            performanceDateVo.setCompanySurplusQuotaExpectedAvailabilityCount(0);
                        } else {
                            BigDecimal divide = managementSurplus.divide(companyUsed.divide(new BigDecimal(companyCount), 2, BigDecimal.ROUND_HALF_UP), 0, BigDecimal.ROUND_DOWN);
                            performanceDateVo.setCompanySurplusQuotaExpectedAvailabilityCount(divide.intValue());
                        }
                    }
                    //区域经理
                    List<SalaryAllocatedVo> departmentHistory = allocatedHistory.stream().filter(x -> "10".equals(x.getPositionTypeId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(departmentHistory)) {
                        Integer departmentCount = departmentHistory.size();
                        BigDecimal departmentUsed = departmentHistory.stream().map(SalaryAllocatedVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                        performanceDateVo.setDepartmentUsedQuota(departmentUsed);
                        //剩余额度预计可使用人数 = 剩余额度/(已使用额度/已使用人数)
                        if (BigDecimal.ZERO.compareTo(managementSurplus) >= 0 || BigDecimal.ZERO.compareTo(departmentUsed) >= 0) {
                            performanceDateVo.setDepartmentSurplusQuotaExpectedAvailabilityCount(0);
                        } else {
                            BigDecimal divide = managementSurplus.divide(departmentUsed.divide(new BigDecimal(departmentCount), 2, BigDecimal.ROUND_HALF_UP), 0, BigDecimal.ROUND_DOWN);
                            performanceDateVo.setDepartmentSurplusQuotaExpectedAvailabilityCount(divide.intValue());
                        }

                    }
                    //省区总监
                    List<SalaryAllocatedVo> provinceHistory = allocatedHistory.stream().filter(x -> "11".equals(x.getPositionTypeId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(provinceHistory)) {
                        Integer provinceCount = provinceHistory.size();
                        BigDecimal provinceUsed = provinceHistory.stream().map(SalaryAllocatedVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                        performanceDateVo.setProvinceUsedQuota(provinceUsed);
                        //剩余额度预计可使用人数 = 剩余额度/(已使用额度/已使用人数)
                        if (BigDecimal.ZERO.compareTo(managementSurplus) >= 0 || BigDecimal.ZERO.compareTo(provinceUsed) >= 0) {
                            performanceDateVo.setProvinceSurplusQuotaExpectedAvailabilityCount(0);
                        } else {
                            BigDecimal divide = managementSurplus.divide(provinceUsed.divide(new BigDecimal(provinceCount), 2, BigDecimal.ROUND_HALF_UP), 0, BigDecimal.ROUND_DOWN);
                            performanceDateVo.setProvinceSurplusQuotaExpectedAvailabilityCount(divide.intValue());
                        }
                    }
                }
            } else {
                //1总督导 12大区总监 2区域总监 11省区总监 10区域经理
                //管理岗已使用额度
                BigDecimal managementUsedQuotaResult = BigDecimal.ZERO;
                //非总部只需要查询组织下的区域总监和区域经理 11.15新增省区总监 12.30新增总督导 大区总监

                //总督导已使用额度
                SearchAllocatedHistoryRequest areaAllocatedHistoryRequest = new SearchAllocatedHistoryRequest();
                areaAllocatedHistoryRequest.setOrganizationId(request.getOrganizationId());
                areaAllocatedHistoryRequest.setPositionTypeId(1);
                List<SalaryAllocatedVo> areaHistory = zWSalaryService.getAllocatedHistory(areaAllocatedHistoryRequest);
                if (CollectionUtil.isNotEmpty(areaHistory)) {
                    Integer areaCount = areaHistory.size();
                    BigDecimal areaUsed = areaHistory.stream().map(SalaryAllocatedVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                    performanceDateVo.setAreaUsedQuota(areaUsed);
                    managementUsedQuotaResult = managementUsedQuotaResult.add(areaUsed);
                    //剩余额度预计可使用人数 = 剩余额度/(已使用额度/已使用人数)
                    if (managementTabQueryResult) {
                        if (BigDecimal.ZERO.compareTo(managementSurplus) >= 0 || BigDecimal.ZERO.compareTo(areaUsed) >= 0) {
                            performanceDateVo.setAreaSurplusQuotaExpectedAvailabilityCount(0);
                        } else {
                            BigDecimal divide = managementSurplus.divide(areaUsed.divide(new BigDecimal(areaCount), 2, BigDecimal.ROUND_HALF_UP), 0, BigDecimal.ROUND_DOWN);
                            performanceDateVo.setAreaSurplusQuotaExpectedAvailabilityCount(divide.intValue());
                        }
                    }

                }


                //大区总监已使用额度
                SearchAllocatedHistoryRequest vareaAllocatedHistoryRequest = new SearchAllocatedHistoryRequest();
                vareaAllocatedHistoryRequest.setOrganizationId(request.getOrganizationId());
                vareaAllocatedHistoryRequest.setPositionTypeId(12);
                List<SalaryAllocatedVo> vareaHistory = zWSalaryService.getAllocatedHistory(vareaAllocatedHistoryRequest);
                if (CollectionUtil.isNotEmpty(vareaHistory)) {
                    Integer vareaCount = vareaHistory.size();
                    BigDecimal vareaUsed = vareaHistory.stream().map(SalaryAllocatedVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                    performanceDateVo.setVareaUsedQuota(vareaUsed);
                    managementUsedQuotaResult = managementUsedQuotaResult.add(vareaUsed);
                    //剩余额度预计可使用人数 = 剩余额度/(已使用额度/已使用人数)
                    if (managementTabQueryResult) {
                        if (BigDecimal.ZERO.compareTo(managementSurplus) >= 0 || BigDecimal.ZERO.compareTo(vareaUsed) >= 0) {
                            performanceDateVo.setVareaSurplusQuotaExpectedAvailabilityCount(0);
                        } else {
                            BigDecimal divide = managementSurplus.divide(vareaUsed.divide(new BigDecimal(vareaCount), 2, BigDecimal.ROUND_HALF_UP), 0, BigDecimal.ROUND_DOWN);
                            performanceDateVo.setVareaSurplusQuotaExpectedAvailabilityCount(divide.intValue());
                        }
                    }

                }

                //区域总监已使用额度
                SearchAllocatedHistoryRequest companyAllocatedHistoryRequest = new SearchAllocatedHistoryRequest();
                companyAllocatedHistoryRequest.setOrganizationId(request.getOrganizationId());
                companyAllocatedHistoryRequest.setPositionTypeId(2);
                List<SalaryAllocatedVo> companyHistory = zWSalaryService.getAllocatedHistory(companyAllocatedHistoryRequest);
                if (CollectionUtil.isNotEmpty(companyHistory)) {
                    Integer companyCount = companyHistory.size();
                    BigDecimal companyUsed = companyHistory.stream().map(SalaryAllocatedVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                    performanceDateVo.setCompanyUsedQuota(companyUsed);
                    managementUsedQuotaResult = managementUsedQuotaResult.add(companyUsed);
                    //剩余额度预计可使用人数 = 剩余额度/(已使用额度/已使用人数)
                    if (managementTabQueryResult) {
                        if (BigDecimal.ZERO.compareTo(managementSurplus) >= 0 || BigDecimal.ZERO.compareTo(companyUsed) >= 0) {
                            performanceDateVo.setCompanySurplusQuotaExpectedAvailabilityCount(0);
                        } else {
                            BigDecimal divide = managementSurplus.divide(companyUsed.divide(new BigDecimal(companyCount), 2, BigDecimal.ROUND_HALF_UP), 0, BigDecimal.ROUND_DOWN);
                            performanceDateVo.setCompanySurplusQuotaExpectedAvailabilityCount(divide.intValue());
                        }
                    }

                }
                //区域经理已使用额度
                SearchAllocatedHistoryRequest departmentAllocatedHistoryRequest = new SearchAllocatedHistoryRequest();
                departmentAllocatedHistoryRequest.setOrganizationId(request.getOrganizationId());
                departmentAllocatedHistoryRequest.setPositionTypeId(10);
                List<SalaryAllocatedVo> departmentHistory = zWSalaryService.getAllocatedHistory(departmentAllocatedHistoryRequest);
                if (CollectionUtil.isNotEmpty(departmentHistory)) {
                    Integer departmentCount = departmentHistory.size();
                    BigDecimal departmentUsed = departmentHistory.stream().map(SalaryAllocatedVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                    performanceDateVo.setDepartmentUsedQuota(departmentUsed);
                    managementUsedQuotaResult = managementUsedQuotaResult.add(departmentUsed);
                    if (managementTabQueryResult) {
                        //剩余额度预计可使用人数 = 剩余额度/(已使用额度/已使用人数)
                        if (BigDecimal.ZERO.compareTo(managementSurplus) >= 0 || BigDecimal.ZERO.compareTo(departmentUsed) >= 0) {
                            performanceDateVo.setDepartmentSurplusQuotaExpectedAvailabilityCount(0);
                        } else {
                            BigDecimal divide = managementSurplus.divide(departmentUsed.divide(new BigDecimal(departmentCount), 2, BigDecimal.ROUND_HALF_UP), 0, BigDecimal.ROUND_DOWN);
                            performanceDateVo.setDepartmentSurplusQuotaExpectedAvailabilityCount(divide.intValue());
                        }
                    }
                }
                //省区总监已使用额度
                SearchAllocatedHistoryRequest provinceAllocatedHistoryRequest = new SearchAllocatedHistoryRequest();
                provinceAllocatedHistoryRequest.setOrganizationId(request.getOrganizationId());
                provinceAllocatedHistoryRequest.setPositionTypeId(11);
                List<SalaryAllocatedVo> provinceHistory = zWSalaryService.getAllocatedHistory(provinceAllocatedHistoryRequest);
                if (CollectionUtil.isNotEmpty(provinceHistory)) {
                    Integer provinceCount = provinceHistory.size();
                    BigDecimal provinceUsed = provinceHistory.stream().map(SalaryAllocatedVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                    performanceDateVo.setProvinceUsedQuota(provinceUsed);
                    managementUsedQuotaResult = managementUsedQuotaResult.add(provinceUsed);
                    if (managementTabQueryResult) {
                        //剩余额度预计可使用人数 = 剩余额度/(已使用额度/已使用人数)
                        if (BigDecimal.ZERO.compareTo(managementSurplus) >= 0 || BigDecimal.ZERO.compareTo(provinceUsed) >= 0) {
                            performanceDateVo.setProvinceSurplusQuotaExpectedAvailabilityCount(0);
                        } else {
                            BigDecimal divide = managementSurplus.divide(provinceUsed.divide(new BigDecimal(provinceCount), 2, BigDecimal.ROUND_HALF_UP), 0, BigDecimal.ROUND_DOWN);
                            performanceDateVo.setProvinceSurplusQuotaExpectedAvailabilityCount(divide.intValue());
                        }
                    }
                }
                //查询当前组织无管理岗已使用额度 使用区域总监和区域经理和省区总监 的汇总额度
                if (!managementTabQueryResult) {
                    if (BigDecimal.ZERO.compareTo(managementUsedQuotaResult) != 0) {
                        performanceDateVo.setManagementUsedQuota(managementUsedQuotaResult);
                    }
                }


            }


        }
        return performanceDateVo;
    }


    @Override
    public IPage<QuerySamePostPerformanceVo> querySamePostPerformance(QuerySamePostPerformanceRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        Page<QuerySamePostPerformanceVo> page = new Page<>(request.getPage(), request.getRows());
        //登录人员是不是总部-->总部都展示
        List<String> employeeOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(), RequestUtils.getLoginInfo());
        if (CollectionUtil.isEmpty(employeeOrganizationIds)) {
            return page;
        }
        request.setOwnOrganizationId(employeeOrganizationIds.get(0));
        //总部直接展示被查看人同岗位的所有数据
        String ownOrganizationType = organizationMapper.getOrganizationType(request.getOwnOrganizationId());
        String readOrganizationType = organizationMapper.getOrganizationType(request.getReadOrganizationId());
        if (StringUtils.isBlank(ownOrganizationType) || StringUtils.isBlank(readOrganizationType)) {
            log.warn("同岗位对比请求参数[{}]登陆人或者被查看人的组织类型为空", request);
            return page;
        }
        request.setOwnOrganizationType(ownOrganizationType);
        request.setReadOrganizationType(readOrganizationType);
        request.setReadPositionTypeId(RankingListPositionEnums.getPositionTypeId(readOrganizationType));
        request.setRelationMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));

        if (RankingListPositionEnums.ZB.getOrganizationType().equals(ownOrganizationType)) {
            if (RankingListPositionEnums.ZB.getOrganizationType().equals(readOrganizationType)) {
                //总部查看自己不展示
                return page;
            }
            //只限制岗位类型和产品组-->根据当前查看的组织id获得所有的岗位类型id
            List<QuerySamePostPerformanceVo> querySamePostPerformanceVos = realtimeMapper.querySamePostPerformance(page, request);
            page.setRecords(querySamePostPerformanceVos);
        } else {
            //最终查询的同岗位类型的组织id
            List<String> samePostOrganizationIds = new ArrayList<>();
            //查询当前登陆人的所有下级
            List<OrganizationAllChildInfoVo> allChildren = organizationMapper.getAllChildren(request.getOwnOrganizationId());
            if (CollectionUtils.isEmpty(allChildren)) {
                log.warn("同岗位对比请求参数[{}]查询不到下级数据", request);
                return page;
            }
            Map<String, List<OrganizationAllChildInfoVo>> allChildrenMap = allChildren.stream().collect(Collectors.toMap(children -> String.valueOf(children.getLevel()), children -> {
                List<OrganizationAllChildInfoVo> list = new ArrayList<>();
                list.add(children);
                return list;
            }, (v1, v2) -> {
                v1.addAll(v2);
                return v1;
            }));
            //根据登陆人的组织类型  和 被查看人的组织类型 获取最终的组织id
            if (RankingListPositionEnums.AREA.getOrganizationType().equals(ownOrganizationType)) {
                if (RankingListPositionEnums.V_AREA.getOrganizationType().equals(readOrganizationType)) {
                    List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
                    if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos1)) {
                        samePostOrganizationIds.addAll(organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                    }
                } else if (RankingListPositionEnums.PROVINCE.getOrganizationType().equals(readOrganizationType)) {
                    List<OrganizationAllChildInfoVo> organizationAllChildInfoVos2 = allChildrenMap.get("2");
                    if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos2)) {
                        samePostOrganizationIds.addAll(organizationAllChildInfoVos2.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                    }
                } else if (RankingListPositionEnums.COMPANY.getOrganizationType().equals(readOrganizationType)) {
                    List<OrganizationAllChildInfoVo> organizationAllChildInfoVos3 = allChildrenMap.get("3");
                    if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos3)) {
                        samePostOrganizationIds.addAll(organizationAllChildInfoVos3.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                    }
                } else if (RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(readOrganizationType)) {
                    List<OrganizationAllChildInfoVo> organizationAllChildInfoVos4 = allChildrenMap.get("4");
                    if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos4)) {
                        samePostOrganizationIds.addAll(organizationAllChildInfoVos4.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                    }
                }
            } else if (RankingListPositionEnums.V_AREA.getOrganizationType().equals(ownOrganizationType)) {
                if (RankingListPositionEnums.PROVINCE.getOrganizationType().equals(readOrganizationType)) {
                    List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
                    if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos1)) {
                        samePostOrganizationIds.addAll(organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                    }
                } else if (RankingListPositionEnums.COMPANY.getOrganizationType().equals(readOrganizationType)) {
                    List<OrganizationAllChildInfoVo> organizationAllChildInfoVos2 = allChildrenMap.get("2");
                    if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos2)) {
                        samePostOrganizationIds.addAll(organizationAllChildInfoVos2.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                    }
                } else if (RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(readOrganizationType)) {
                    List<OrganizationAllChildInfoVo> organizationAllChildInfoVos3 = allChildrenMap.get("3");
                    if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos3)) {
                        samePostOrganizationIds.addAll(organizationAllChildInfoVos3.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                    }

                }
            } else if (RankingListPositionEnums.PROVINCE.getOrganizationType().equals(ownOrganizationType)) {
                if (RankingListPositionEnums.COMPANY.getOrganizationType().equals(readOrganizationType)) {
                    List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
                    if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos1)) {
                        samePostOrganizationIds.addAll(organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                    }
                } else if (RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(readOrganizationType)) {
                    List<OrganizationAllChildInfoVo> organizationAllChildInfoVos2 = allChildrenMap.get("2");
                    if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos2)) {
                        samePostOrganizationIds.addAll(organizationAllChildInfoVos2.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                    }
                }
            } else if (RankingListPositionEnums.COMPANY.getOrganizationType().equals(ownOrganizationType)) {
                if (RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(readOrganizationType)) {
                    List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
                    if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos1)) {
                        samePostOrganizationIds.addAll(organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                    }

                }
            }
            if (CollectionUtil.isEmpty(samePostOrganizationIds)) {
                return page;
            }
            request.setSamePostOrganizationIds(samePostOrganizationIds);
            List<QuerySamePostPerformanceVo> querySamePostPerformanceVos = realtimeMapper.querySamePostPerformance(page, request);
            page.setRecords(querySamePostPerformanceVos);
        }

        // 填充面试记录ID
        List<QuerySamePostPerformanceVo> records = page.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            List<Integer> employeeInfoIds = records.stream().filter(f -> Objects.nonNull(f.getEmployeeInfoId()))
                    .map(QuerySamePostPerformanceVo::getEmployeeInfoId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(employeeInfoIds)) {
                List<EmployeeDTO> employeeDTOS = Optional.ofNullable(sfaEmployeeInfoMapper.selectEmployeeInfoByIds(employeeInfoIds)).orElse(new ArrayList<>());
                records.stream().filter(f ->Objects.nonNull(f.getEmployeeInfoId())).forEach(e -> {
                    EmployeeDTO employeeDTO = employeeDTOS.stream().filter(f -> e.getEmployeeInfoId().equals(f.getEmployeeInfoId())).findFirst().orElse(null);
                    if (Objects.nonNull(employeeDTO)) {
                        e.setInterviewRecordId(employeeDTO.getInterviewRecordId());
                    }
                });
            }
        }

        return page;
    }

    @Override
    public void querySamePostPerformanceDown(QuerySamePostPerformanceRequest request, HttpServletRequest req, HttpServletResponse res) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<QuerySamePostPerformanceVo> querySamePostPerformanceVos = new ArrayList<>();
        //登录人员是不是总部-->总部都展示
        List<String> employeeOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(), RequestUtils.getLoginInfo());
        if (CollectionUtil.isNotEmpty(employeeOrganizationIds)) {

            request.setOwnOrganizationId(employeeOrganizationIds.get(0));
            //总部直接展示被查看人同岗位的所有数据
            String ownOrganizationType = organizationMapper.getOrganizationType(request.getOwnOrganizationId());
            String readOrganizationType = organizationMapper.getOrganizationType(request.getReadOrganizationId());
            if (StringUtils.isBlank(ownOrganizationType) || StringUtils.isBlank(readOrganizationType)) {
                throw new ApplicationException("请稍后再尝试下载");
            }
            request.setOwnOrganizationType(ownOrganizationType);
            request.setReadOrganizationType(readOrganizationType);
            request.setReadPositionTypeId(RankingListPositionEnums.getPositionTypeId(readOrganizationType));
            request.setRelationMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));

            if (RankingListPositionEnums.ZB.getOrganizationType().equals(ownOrganizationType)) {
                if (!RankingListPositionEnums.ZB.getOrganizationType().equals(readOrganizationType)) {
                    //总部查看自己不展示
                    //只限制岗位类型和产品组-->根据当前查看的组织id获得所有的岗位类型id
                    querySamePostPerformanceVos = realtimeMapper.querySamePostPerformance(null, request);
                }
            } else {
                //最终查询的同岗位类型的组织id
                List<String> samePostOrganizationIds = new ArrayList<>();
                //查询当前登陆人的所有下级
                List<OrganizationAllChildInfoVo> allChildren = organizationMapper.getAllChildren(request.getOwnOrganizationId());
                if (CollectionUtils.isEmpty(allChildren)) {
                    log.warn("同岗位对比请求参数[{}]查询不到下级数据", request);
                    throw new ApplicationException("请稍后再尝试下载");
                }
                Map<String, List<OrganizationAllChildInfoVo>> allChildrenMap = allChildren.stream().collect(Collectors.toMap(children -> String.valueOf(children.getLevel()), children -> {
                    List<OrganizationAllChildInfoVo> list = new ArrayList<>();
                    list.add(children);
                    return list;
                }, (v1, v2) -> {
                    v1.addAll(v2);
                    return v1;
                }));
                //根据登陆人的组织类型  和 被查看人的组织类型 获取最终的组织id
                if (RankingListPositionEnums.AREA.getOrganizationType().equals(ownOrganizationType)) {
                    if (RankingListPositionEnums.V_AREA.getOrganizationType().equals(readOrganizationType)) {
                        List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
                        if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos1)) {
                            samePostOrganizationIds.addAll(organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                        }
                    } else if (RankingListPositionEnums.PROVINCE.getOrganizationType().equals(readOrganizationType)) {
                        List<OrganizationAllChildInfoVo> organizationAllChildInfoVos2 = allChildrenMap.get("2");
                        if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos2)) {
                            samePostOrganizationIds.addAll(organizationAllChildInfoVos2.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                        }
                    } else if (RankingListPositionEnums.COMPANY.getOrganizationType().equals(readOrganizationType)) {
                        List<OrganizationAllChildInfoVo> organizationAllChildInfoVos3 = allChildrenMap.get("3");
                        if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos3)) {
                            samePostOrganizationIds.addAll(organizationAllChildInfoVos3.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                        }
                    } else if (RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(readOrganizationType)) {
                        List<OrganizationAllChildInfoVo> organizationAllChildInfoVos4 = allChildrenMap.get("4");
                        if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos4)) {
                            samePostOrganizationIds.addAll(organizationAllChildInfoVos4.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                        }
                    }
                } else if (RankingListPositionEnums.V_AREA.getOrganizationType().equals(ownOrganizationType)) {
                    if (RankingListPositionEnums.PROVINCE.getOrganizationType().equals(readOrganizationType)) {
                        List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
                        if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos1)) {
                            samePostOrganizationIds.addAll(organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                        }
                    } else if (RankingListPositionEnums.COMPANY.getOrganizationType().equals(readOrganizationType)) {
                        List<OrganizationAllChildInfoVo> organizationAllChildInfoVos2 = allChildrenMap.get("2");
                        if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos2)) {
                            samePostOrganizationIds.addAll(organizationAllChildInfoVos2.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                        }
                    } else if (RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(readOrganizationType)) {
                        List<OrganizationAllChildInfoVo> organizationAllChildInfoVos3 = allChildrenMap.get("3");
                        if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos3)) {
                            samePostOrganizationIds.addAll(organizationAllChildInfoVos3.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                        }

                    }
                } else if (RankingListPositionEnums.PROVINCE.getOrganizationType().equals(ownOrganizationType)) {
                    if (RankingListPositionEnums.COMPANY.getOrganizationType().equals(readOrganizationType)) {
                        List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
                        if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos1)) {
                            samePostOrganizationIds.addAll(organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                        }
                    } else if (RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(readOrganizationType)) {
                        List<OrganizationAllChildInfoVo> organizationAllChildInfoVos2 = allChildrenMap.get("2");
                        if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos2)) {
                            samePostOrganizationIds.addAll(organizationAllChildInfoVos2.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                        }
                    }
                } else if (RankingListPositionEnums.COMPANY.getOrganizationType().equals(ownOrganizationType)) {
                    if (RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(readOrganizationType)) {
                        List<OrganizationAllChildInfoVo> organizationAllChildInfoVos1 = allChildrenMap.get("1");
                        if (CollectionUtil.isNotEmpty(organizationAllChildInfoVos1)) {
                            samePostOrganizationIds.addAll(organizationAllChildInfoVos1.stream().map(OrganizationAllChildInfoVo::getRegions).collect(Collectors.toList()));
                        }

                    }
                }
                if (CollectionUtil.isNotEmpty(samePostOrganizationIds)) {
                    request.setSamePostOrganizationIds(samePostOrganizationIds);
                    querySamePostPerformanceVos = realtimeMapper.querySamePostPerformance(null, request);
                }


            }
        }
        ExportUtil.writeEasyExcelResponse(res, req, "同岗位对比列表", QuerySamePostPerformanceVo.class, querySamePostPerformanceVos);

    }

    @Override
    public List<ServiceDataDetailVo> queryGoodsPrqueryPerformanceoductDate(TradeGoodNewReq request) {
        log.info("start RealtimeDataServiceImpl queryGoodsPrqueryPerformanceoductDate request:{}", request);
        request.setTheDate(realTimeUtils.getNearDate(request.getDateTypeId(), request.getYearMonth()));
        request.setRelationMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        request.setYearMonthYoy(realTimeUtils.querySameTerm(request.getDateTypeId(), request.getYearMonth()));
        PerformanceDateVo emplyeeDate = realtimeMapper.queryPerformanceDate(request);
        if (Objects.isNull(emplyeeDate)) {
            throw new ApplicationException(BizExceptionLanguageEnum.DATA_GENERATION_IN_PROGRESS.getTextMsg());
        }
        if (Objects.nonNull(emplyeeDate)) {
            request.setOrganizationType(emplyeeDate.getPositionTypeId() == 7 ? 4 : emplyeeDate.getPositionTypeId());
        }
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<ServiceDataDetailVo> list = new ArrayList<>();

        if (request.getIsNextRealtime() == 0) {
            //总部查自己 也要查下级
            // 自己
            ServiceDataDetailVo resultsDate = realtimeMapper.getResultsDateNew(request);

            if (Objects.nonNull(emplyeeDate)) {

                String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
                if ("department".equals(organizationType)) {
                    List<ServiceDataDetailVo> Resultslist = realtimeMapper.queryCeoPerformanceDate(
                            null,
                            request.getOrganizationId(),
                            organizationMapper.getOrganizationType(request.getOrganizationId()),
                            RequestUtils.getBusinessGroup(),
                            request.getYearMonth(),
                            request.getDateTypeId(),
                            request.getRelationMonth(),
                            request.getOrganizationIds(),
                            request.getYearMonthYoy(), null, null);
                    list.add(resultsDate);
                    list.addAll(Resultslist);
                } else {
                    //查看下级*/
                    List<ServiceDataDetailVo> Resultslist = realtimeMapper.getResultsDateNextNew(request);
                    list.add(resultsDate);
                    list.addAll(Resultslist);
                }

            } else {
                list.add(resultsDate);
            }
            if (CommonUtil.ListUtils.isNotEmpty(list)) {
                list.forEach(l -> {
                    if (Objects.nonNull(l)) {
                        if (l.getOrganizationId().equals(request.getOrganizationId())) {
                            if (CommonConstant.LANGUAGE_ENGLISH.equals(RequestUtils.getLanguage())) {
                                l.setArea("Total");
                            } else {
                                l.setArea("合计");
                            }
                        }
                    }
                });
            }
        } else {

            String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
            if ("department".equals(organizationType)) {
                List<ServiceDataDetailVo> Resultslist = realtimeMapper.queryCeoPerformanceDate(null,
                        request.getOrganizationId(),
                        organizationMapper.getOrganizationType(request.getOrganizationId()),
                        RequestUtils.getBusinessGroup(),
                        request.getYearMonth(),
                        request.getDateTypeId(),
                        request.getRelationMonth(),
                        request.getOrganizationIds(),
                        request.getYearMonthYoy(), null, null);

                list.addAll(Resultslist);
            } else {
                List<ServiceDataDetailVo> Resultslist = realtimeMapper.getResultsDateNextNew(request);
                list.addAll(Resultslist);
            }


        }
        if (CollectionUtil.isNotEmpty(list)) {
            Integer businessGroup = RequestUtils.getBusinessGroup();

            List<EmployeeDTO> employeeDTOS = new ArrayList<>();
            List<Integer> empIds = list.stream().filter(f -> Objects.nonNull(f.getEmployeeInfoId()))
                    .map(ServiceDataDetailVo::getEmployeeInfoId)
                    .map(Long::intValue).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(empIds)) {
                employeeDTOS = Optional.ofNullable(sfaEmployeeInfoMapper.selectEmployeeInfoByIds(empIds)).orElse(new ArrayList<>());
            }


            List<EmployeeDTO> finalEmployeeDTOS = employeeDTOS;
            list.stream().forEach(l -> {

                Long employeeInfoId = l.getEmployeeInfoId();
                if (Objects.nonNull(employeeInfoId)) {
                    EmployeeDTO employeeDTO = finalEmployeeDTOS.stream().filter(f -> f.getEmployeeInfoId().equals(employeeInfoId.intValue())).findFirst().orElse(null);
                    if(Objects.nonNull(employeeDTO)) {
                        l.setInterviewRecordId(employeeDTO.getInterviewRecordId());
                    }
                }
                l.setEmployeeStatus(EmployeeStatus.getDescByEnv(l.getEmployeeStatus()));
                l.setPostName(OrganizationPositionRelationEnums.getPositionNameByEnv(l.getPostName()));
                if (businessGroup == 8 || businessGroup == 6) {
                    //区域总监改名：系统负责人 区域经理改名：系统经理
                    if (Objects.nonNull(l.getPositionTypeId())) {
                        if (l.getPositionTypeId() == 2) {
                            l.setPostName("系统负责人");
                        } else if (l.getPositionTypeId() == 10) {
                            l.setPostName("系统经理");
                        }
                    }
                }

                l.setOverallPerformanceColor(realTimeUtils.saleGoalAchievementColor(request.getDateTypeId(), request.getYearMonth(), l.getOverallPerformanceRate()));

            });
        }


        return list;
    }

    @Override
    public IPage<ServiceDataDetailVo> queryGoodsPrqueryPerformanceoductDateByPage(TradeGoodNewReq request) {
        Integer searchType = request.getSearchType();

        IPage<ServiceDataDetailVo> page = new Page<>(request.getPage(), request.getRows());
        List<ServiceDataDetailVo> resultList = new ArrayList<>();
        request.setRelationMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        request.setYearMonthYoy(realTimeUtils.querySameTerm(request.getDateTypeId(), request.getYearMonth()));
        Integer businessGroup = RequestUtils.getBusinessGroup();
        String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
        switch (searchType) {
            // 1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人

            case 1:
                List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + request.getOrganizationId());
                if ((businessGroup == 8 || businessGroup == 6) && (RankingListPositionEnums.ZB.getOrganizationType().equals(organizationType))) {
                    //直营组总部直属下级为省区总监 查询产品组下所有的省区总监
                    nextOrgCode = organizationMapper.getOrganizationIdByOrganizationTypeAndGroup(businessGroup, RankingListPositionEnums.PROVINCE.getOrganizationType());
                }
                log.info("【next org code】codes:{}", nextOrgCode);
                if (!CollectionUtils.isEmpty(nextOrgCode)) {
                    resultList = realtimeMapper.getResultBySearchType(page,
                            searchType,
                            nextOrgCode,
                            request.getOrganizationId(),
                            organizationType,
                            RequestUtils.getBusinessGroup(),
                            request.getYearMonth(),
                            request.getDateTypeId(),
                            request.getRelationMonth(),
                            request.getOrganizationIds(),
                            request.getYearMonthYoy(), request.getSortName(), request.getSortOrder());
                }
                break;
            case 7:
                resultList = realtimeMapper.queryCeoPerformanceDate(page,
                        request.getOrganizationId(),
                        organizationType,
                        RequestUtils.getBusinessGroup(),
                        request.getYearMonth(),
                        request.getDateTypeId(),
                        request.getRelationMonth(),
                        request.getOrganizationIds(),
                        request.getYearMonthYoy(),
                        request.getSortName(), request.getSortOrder());
                break;
            default:
                resultList = realtimeMapper.getResultBySearchType(page,
                        searchType,
                        null,
                        request.getOrganizationId(),
                        organizationType,
                        RequestUtils.getBusinessGroup(),
                        request.getYearMonth(),
                        request.getDateTypeId(),
                        request.getRelationMonth(),
                        request.getOrganizationIds(),
                        request.getYearMonthYoy(),
                        request.getSortName(), request.getSortOrder());
                break;
        }

        List<EmployeeDTO> employeeDTOS = new ArrayList<>();
        List<Integer> empIds = resultList.stream().filter(f -> Objects.nonNull(f.getEmployeeInfoId()))
                .map(ServiceDataDetailVo::getEmployeeInfoId)
                .map(Long::intValue).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(empIds)) {
            employeeDTOS = Optional.ofNullable(sfaEmployeeInfoMapper.selectEmployeeInfoByIds(empIds)).orElse(new ArrayList<>());
        }
        List<EmployeeDTO> finalEmployeeDTOS = employeeDTOS;
        if (CollectionUtil.isNotEmpty(resultList)) {

            resultList.stream().forEach(l -> {

                Long employeeInfoId = l.getEmployeeInfoId();
                if (Objects.nonNull(employeeInfoId)) {
                    EmployeeDTO employeeDTO = finalEmployeeDTOS.stream().filter(f -> f.getEmployeeInfoId().equals(employeeInfoId.intValue())).findFirst().orElse(null);
                    if (Objects.nonNull(employeeDTO)) {
                        l.setInterviewRecordId(employeeDTO.getInterviewRecordId());
                    }
                }
                l.setEmployeeStatus(EmployeeStatus.getDescByEnv(l.getEmployeeStatus()));
                l.setPostName(OrganizationPositionRelationEnums.getPositionNameByEnv(l.getPostName()));

                if (businessGroup == 8 || businessGroup == 6) {
                    //区域总监改名：系统负责人 区域经理改名：系统经理
                    if (Objects.nonNull(l.getPositionTypeId())) {
                        if (l.getPositionTypeId() == 2) {
                            l.setPostName("系统负责人");
                        } else if (l.getPositionTypeId() == 10) {
                            l.setPostName("系统经理");
                        }
                    }

                }

                l.setOverallPerformanceColor(realTimeUtils.saleGoalAchievementColor(request.getDateTypeId(), request.getYearMonth(), l.getOverallPerformanceRate()));

            });
        }

        page.setRecords(resultList);

        return page;
    }


    @Override
    public void queryGoodsPrqueryPerformanceoductDateByPageExport(TradeGoodNewReq request) {
        Integer searchType = request.getSearchType();
        List<ServiceDataDetailVo> resultList = new ArrayList<>();
        request.setRelationMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        request.setYearMonthYoy(realTimeUtils.querySameTerm(request.getDateTypeId(), request.getYearMonth()));
        switch (searchType) {
            // 1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人

            case 1:
                List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + request.getOrganizationId());
                log.info("【next org code】codes:{}", nextOrgCode);
                if (!CollectionUtils.isEmpty(nextOrgCode)) {
                    resultList = realtimeMapper.getResultBySearchType(null,
                            searchType,
                            nextOrgCode, request.getOrganizationId(),
                            organizationMapper.getOrganizationType(request.getOrganizationId()),
                            RequestUtils.getBusinessGroup(),
                            request.getYearMonth(),
                            request.getDateTypeId(),
                            request.getRelationMonth(),
                            request.getOrganizationIds(),
                            request.getYearMonthYoy(), null, null);
                }
                break;
            case 7:
                resultList = realtimeMapper.queryCeoPerformanceDate(null,
                        request.getOrganizationId(),
                        organizationMapper.getOrganizationType(request.getOrganizationId()),
                        RequestUtils.getBusinessGroup(),
                        request.getYearMonth(),
                        request.getDateTypeId(),
                        request.getRelationMonth(),
                        request.getOrganizationIds(),
                        request.getYearMonthYoy(), null, null);
                break;
            default:
                resultList = realtimeMapper.getResultBySearchType(null,
                        searchType, null,
                        request.getOrganizationId(),
                        organizationMapper.getOrganizationType(request.getOrganizationId()),
                        RequestUtils.getBusinessGroup(),
                        request.getYearMonth(),
                        request.getDateTypeId(),
                        request.getRelationMonth(),
                        request.getOrganizationIds(),
                        request.getYearMonthYoy(), null, null);
                break;
        }

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();

        if (CollectionUtil.isNotEmpty(resultList)) {
            Integer businessGroup = RequestUtils.getBusinessGroup();
            resultList.stream().forEach(l -> {
                l.setEmployeeStatus(EmployeeStatus.getDescByEnv(l.getEmployeeStatus()));
                l.setPostName(OrganizationPositionRelationEnums.getPositionNameByEnv(l.getPostName()));
                if (businessGroup == 8 || businessGroup == 6) {
                    //区域总监改名：系统负责人 区域经理改名：系统经理
                    if (l.getPositionTypeId() == 2) {
                        l.setPostName("系统负责人");
                    } else if (l.getPositionTypeId() == 10) {
                        l.setPostName("系统经理");
                    }
                }
            });
        }

        Workbook workbook;
        String name;
        if (CommonConstant.REGION_CHINESE.equals(RequestUtils.getRegion())) {
            name = "业务数据明细";
            workbook = ExcelExportUtil.exportExcel(new ExportParams(name, name), ServiceDataDetailVo.class, resultList);
        } else {
            name = "Business Data Details";
            workbook = ExcelExportUtil.exportExcel(new ExportParams(name, name), ServiceDataDetailEnVo.class, BeanUtil.copyToList(resultList, ServiceDataDetailEnVo.class));
        }



        response.setContentType("application/vnd.ms-excel");

        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(name + ".xlsx"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }


    @Override
    public PerformanceDateVo queryPerformanceDateSnapshot(TradeGoodNewReq request) {
        log.info("start RealtimeDataServiceImpl queryPerformanceDateSnapshot request:{}", request);
        PerformanceDateVo performanceDateVo = null;

        request.setTheDate(realTimeUtils.getNearDate(request.getDateTypeId(), request.getYearMonth()));
        request.setRelationMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));

        //始终使用月的最后一天数据
        YearMonth yearMonth = YearMonth.of(Integer.valueOf(request.getRelationMonth().substring(0, 4)), Integer.valueOf(request.getRelationMonth().substring(5)));
        LocalDate localDate = yearMonth.atEndOfMonth();
        if (localDate.isBefore(LocalDate.parse("2024-06-01"))) {
            request.setEtlDate("2024-05-31");
        } else {
            request.setEtlDate(localDate.toString());
        }

        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if (request.getIsPartner() == 0) {
            performanceDateVo = realtimeMapper.queryPerformanceDateSnapshot(request);
            if (Objects.nonNull(performanceDateVo)) {
                //简历
                performanceDateVo.setResumeUrl(applyMemberMapper.getResumeUrl(performanceDateVo.getPositionId()));
                List<CeoBusinessOrganizationEntity> partJobOrganization = organizationMapper.getPartJobOrganization(request.getEmployeeId(), RequestUtils.getBusinessGroup(), RequestUtils.getChannel());
                if (CommonUtil.ListUtils.isNotEmpty(partJobOrganization)) {
                    CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = partJobOrganization.get(0);
                    if (request.getOrganizationId().contains("ZB_Z")) {
                        TradeGoodReq tradeGoodReq = new TradeGoodReq();
                        tradeGoodReq.setEmployeeId(request.getEmployeeId());
                        RrecruitmentManagementDataVo rrecruitmentManagement = new RrecruitmentManagementDataVo();
                        RrecruitmentManagementDataVo rrecruitmentManagementDataVo = publicZBInformation(tradeGoodReq, rrecruitmentManagement);
                        performanceDateVo.setEmployeeName(rrecruitmentManagementDataVo.getEmployeeName());
                        performanceDateVo.setOnboardTime(LocalDate.parse(rrecruitmentManagementDataVo.getOnboardTime().substring(0, 10)));
                        performanceDateVo.setOnboardDays(rrecruitmentManagementDataVo.getOnboardDays());
                    }
                    if (ceoBusinessOrganizationEntity.getOrganizationType().equals("zb")) {
                        String isPay = applyMemberMapper.getIsPay(request.getEmployeeId());
                        //根据工号判断是总部，在这些部门有权限看薪资   再判断组织人员不在，就没有权限看薪资，有人才有权限
                        if (StringUtils.isNotBlank(isPay)) {
                            if (StringUtils.isNotBlank(performanceDateVo.getEmployeeName())) {
                                performanceDateVo.setIsPay(1);
                            }
                            performanceDateVo.setIsPay(1);
                        }
                    } else {
                        //不是总部，判断人员不在，就没有权限看薪资，有人才有权限
                        if (StringUtils.isNotBlank(performanceDateVo.getEmployeeName())) {
                            performanceDateVo.setIsPay(1);
                        }
                    }
                }
                //用人费用管控
                setCompanyAndDepartmentAllocatedHistory(request, performanceDateVo);
            }
        } else {
            performanceDateVo = realtimeMapper.queryPerformanceDatePartner(request);
        }

        if (Objects.nonNull(performanceDateVo)) {
            personalDataShowStrategy(performanceDateVo);
        }
        return performanceDateVo;
    }

    @Override
    public void queryPerformanceoductDateExport(TradeGoodNewReq request, HttpServletResponse response) {
        log.info("start RealtimeDataServiceImpl queryPerformanceoductDateExport request:{}", request);
        request.setTheDate(realTimeUtils.getNearDate(request.getDateTypeId(), request.getYearMonth()));
        request.setRelationMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        request.setYearMonthYoy(realTimeUtils.querySameTerm(request.getDateTypeId(), request.getYearMonth()));
        PerformanceDateVo emplyeeDate = realtimeMapper.queryPerformanceDate(request);
        if (Objects.isNull(emplyeeDate)) {
            throw new ApplicationException(BizExceptionLanguageEnum.DATA_GENERATION_IN_PROGRESS.getTextMsg());
        }
        if (Objects.nonNull(emplyeeDate)) {

            request.setOrganizationType(emplyeeDate.getPositionTypeId() == 7 ? 4 : emplyeeDate.getPositionTypeId());
        }
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<ServiceDataDetailVo> list = new ArrayList<>();
        ServiceDataDetailVo resultsDate = realtimeMapper.getResultsDateNew(request);
        if (CommonConstant.LANGUAGE_ENGLISH.equals(RequestUtils.getLanguage())) {
            resultsDate.setArea("Total");
        } else {
            resultsDate.setArea("合计");
        }
        list.add(resultsDate);

        if (Objects.nonNull(emplyeeDate)) {
            //查看下级 战区*/
            List<ServiceDataDetailVo> Resultslist = realtimeMapper.getResultsDateNextNew(request);
            //list.addAll(Resultslist);
            if (CommonUtil.ListUtils.isNotEmpty(Resultslist)) {
                for (ServiceDataDetailVo r : Resultslist) {
                    list.add(r);
                    //战区组织 岗位类型*/
                    request.setOrganizationId(r.getOrganizationId());
                    ServiceDataDetailVo emplyeeDateArea = realtimeMapper.getResultsDateNew(request);
                    if (Objects.nonNull(emplyeeDateArea)) {
                        request.setOrganizationType(emplyeeDateArea.getPositionTypeId());
                        List<ServiceDataDetailVo> resultslistVarea = null;
                        if (emplyeeDateArea.getPositionTypeId() != 3) {
                            //战区组织获取下级 获取大区总监*/
                            resultslistVarea = realtimeMapper.getResultsDateNextNew(request);
                        }

                        if (CommonUtil.ListUtils.isNotEmpty(resultslistVarea) && emplyeeDateArea.getPositionTypeId() != 3) {
                            for (ServiceDataDetailVo v : resultslistVarea) {
                                list.add(v);
                                //省区组织 岗位类型*/
                                request.setOrganizationId(v.getOrganizationId());
                                ServiceDataDetailVo emplyeeDateProvince = realtimeMapper.getResultsDateNew(request);
                                if (Objects.nonNull(emplyeeDateProvince)) {
                                    request.setOrganizationType(emplyeeDateProvince.getPositionTypeId());
                                    List<ServiceDataDetailVo> resultslistCompany = null;
                                    if (emplyeeDateProvince.getPositionTypeId() != 3) {
                                        //省区组织获取下级 分公司组织*/
                                        resultslistCompany = realtimeMapper.getResultsDateNextNew(request);
                                    }

                                    if (CommonUtil.ListUtils.isNotEmpty(resultslistCompany) && emplyeeDateProvince.getPositionTypeId() != 3) {
                                        for (ServiceDataDetailVo c : resultslistCompany) {
                                            list.add(c);
                                            //分公司组织  岗位类型
                                            request.setOrganizationId(c.getOrganizationId());
                                            ServiceDataDetailVo emplyeeDateCompany = realtimeMapper.getResultsDateNew(request);

                                            if (Objects.nonNull(emplyeeDateCompany)) {
                                                request.setOrganizationType(emplyeeDateCompany.getPositionTypeId());
                                                List<ServiceDataDetailVo> resultslistDepartment = null;
                                                if (emplyeeDateCompany.getPositionTypeId() != 3) {
                                                    ////获取下级 区域经理组织
                                                    resultslistDepartment = realtimeMapper.getResultsDateNextNew(request);
                                                }

                                                if (CommonUtil.ListUtils.isNotEmpty(resultslistDepartment) && emplyeeDateCompany.getPositionTypeId() != 3) {
                                                    for (ServiceDataDetailVo d : resultslistDepartment) {
                                                        list.add(d);
                                                        //分公司组织  岗位类型
                                                        request.setOrganizationId(d.getOrganizationId());
                                                        ServiceDataDetailVo emplyeeDateBranch = realtimeMapper.getResultsDateNew(request);
                                                        if (Objects.nonNull(emplyeeDateBranch)) {
                                                            request.setOrganizationType(emplyeeDateBranch.getPositionTypeId());
                                                            ////获取下级 合伙人
                                                            List<ServiceDataDetailVo> resultslistBranch = new ArrayList<>();
                                                            if (emplyeeDateBranch.getPositionTypeId() != 3) {
                                                                ////获取下级 区域经理组织
                                                                List<ServiceDataDetailVo> resultsBranch = realtimeMapper.getResultsDateNextNew(request);
                                                                resultslistBranch.addAll(resultsBranch);
                                                            }
                                                            list.addAll(resultslistBranch);
                                                        }
                                                    }
                                                }

                                            }
                                        }
                                    }
//                                            }
//                                        }
//                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            list.add(resultsDate);
        }
        if (CollectionUtil.isNotEmpty(list)) {
            Integer businessGroup = RequestUtils.getBusinessGroup();
            list.forEach(l -> {
                l.setEmployeeStatus(EmployeeStatus.getDescByEnv(l.getEmployeeStatus()));
                l.setPostName(OrganizationPositionRelationEnums.getPositionNameByEnv(l.getPostName()));
                if (businessGroup == 8 || businessGroup == 6) {
                    //区域总监改名：系统负责人 区域经理改名：系统经理
                    if (l.getPositionTypeId() == 2) {
                        l.setPostName("系统负责人");
                    } else if (l.getPositionTypeId() == 10) {
                        l.setPostName("系统经理");
                    }
                }
            });
        }
        if (CommonConstant.REGION_CHINESE.equals(RequestUtils.getRegion())) {
            EasyPoiUtil.exportExcel(list, null, "sheet1", ServiceDataDetailVo.class, "导出业务数据.xls", response);
        } else {
            EasyPoiUtil.exportExcel(BeanUtil.copyToList(list, ServiceDataDetailEnVo.class), null, "sheet1", ServiceDataDetailEnVo.class, "Business Data Details.xls", response);
        }

    }

    @Override
    public CommodityNewVo queryGoodsProductDate(TradeGoodNewReq request) {
        //前端分页
        request.setYearMonthYoy(realTimeUtils.querySameTerm(request.getDateTypeId(), request.getYearMonth()));
        log.info("start RealtimeDataServiceImpl queryGoodsProductDate request:{}", request);
        CommodityNewVo commodityNewVo = new CommodityNewVo();
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<CommodityDataNewVo> sku = realtimeMapper.queryGoodsProductSku(request);
        commodityNewVo.setSkuList(sku);
        commodityNewVo.setSkuCount(realtimeMapper.queryGoodsProductSkuCount(request));
        commodityNewVo.setSumPricePerformanceSku(sku.stream().collect(CollectorUtil.summingBigDecimal(CommodityDataNewVo::getMonthlyPerformance)));
        List<CommodityDataNewVo> spu = realtimeMapper.queryGoodsProductSpu(request);
        commodityNewVo.setSpuList(spu);
        commodityNewVo.setSpuCount(realtimeMapper.queryGoodsProductSpuCount(request));
        commodityNewVo.setSumPricePerformanceSpu(spu.stream().collect(CollectorUtil.summingBigDecimal(CommodityDataNewVo::getMonthlyPerformance)));
        List<CommodityDataNewVo> line = realtimeMapper.queryGoodsProductLine(request);
        commodityNewVo.setLineList(line);
        commodityNewVo.setLineCount(realtimeMapper.queryGoodsProductLineCount(request));
        commodityNewVo.setSumPricePerformanceLine(line.stream().collect(CollectorUtil.summingBigDecimal(CommodityDataNewVo::getMonthlyPerformance)));
        return commodityNewVo;
    }

    @Override
    public List<PerformanceOperateDateVo> getOperateTendencyAnalyze(PerformanceOperateDateRequest request) {
        log.info("start RealtimeDataServiceImpl getOperateTendencyAnalyze request:{}", request);
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }

        List<PerformanceOperateDateVo> operateTendencyAnalyze = realtimeMapper.getOperateTendencyAnalyze(request);

        if (CollectionUtil.isNotEmpty(operateTendencyAnalyze)) {
            for (PerformanceOperateDateVo operateTendency : operateTendencyAnalyze) {
                operateTendency.setSaleGoalAchievementColor(realTimeUtils.saleGoalAchievementColor(request.getDateTypeId(), operateTendency.getMonth(), operateTendency.getPerformanceAchievementRate()));
            }
        }

        // 有时间区间，查询去年同期，支持折线图展示去年同期数据
        if (StrUtil.hasBlank(request.getStartYearMonth(), request.getEndYearMonth())) {
            return operateTendencyAnalyze;
        }

        PerformanceOperateDateRequest request2 = BeanUtil.toBean(request, PerformanceOperateDateRequest.class);
        request2.setStartYearMonth(getLastYearMonth(request2.getStartYearMonth()));
        request2.setEndYearMonth(getLastYearMonth(request2.getEndYearMonth()));

        List<PerformanceOperateDateVo> operateTendencyAnalyzeLastYear = realtimeMapper.getOperateTendencyAnalyze(request2);
        Map<String, BigDecimal> lastYearPerformanceMap = operateTendencyAnalyzeLastYear.stream().collect(Collectors.toMap((it) -> {
            return it.getMonth() + it.getOrganizationId();
        }, PerformanceOperateDateVo::getPerformance, (v1, v2) -> v1));

        if (CollectionUtil.isNotEmpty(operateTendencyAnalyze)) {
            for (PerformanceOperateDateVo operateTendency : operateTendencyAnalyze) {
                operateTendency.setPerformanceLast(lastYearPerformanceMap.getOrDefault(getLastYearMonth(operateTendency.getMonth()) + operateTendency.getOrganizationId(), BigDecimal.ZERO));
            }
        }
        return operateTendencyAnalyze;
    }

    private String getLastYearMonth(String yearMonth) {
        String[] split = yearMonth.split("-");
        int yearInt = Integer.parseInt(split[0]);
        return yearInt - 1 + (split.length >= 2 ? "-" + split[1] : "");
    }

    @Override
    public void downOperateTendencyAnalyze(PerformanceOperateDateRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {
        log.info("start RealtimeDataServiceImpl downOperateTendencyAnalyze request:{}", request);
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        List<PerformanceOperateDateVo> operateTendencyAnalyze = realtimeMapper.getOperateTendencyAnalyze(request);
        ExportUtil.writeEasyExcelResponse(httpServletResponse, httpServletRequest, "运营趋势分析列表", PerformanceOperateDateVo.class, operateTendencyAnalyze);
    }

    @Override
    public List<PerformancePersonnelDateVo> getPersonnelTendencyAnalyze(PerformancePersonnelDateRequest request) {
        log.info("start RealtimeDataServiceImpl getPersonnelTendencyAnalyze request:{}", request);
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        return realtimeMapper.getPersonnelTendencyAnalyze(request);
    }

    @Override
    public void exportPersonnelTendencyAnalyze(PerformancePersonnelDateRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {
        log.info("start RealtimeDataServiceImpl exportPersonnelTendencyAnalyze request:{}", request);
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        List<PerformancePersonnelDateVo> personnelTendencyAnalyze = realtimeMapper.getPersonnelTendencyAnalyze(request);
        ExportUtil.writeEasyExcelResponse(httpServletResponse, httpServletRequest, "人员趋势分析列表", PerformancePersonnelDateVo.class, personnelTendencyAnalyze);

    }

    @Override
    public List<PerformanceOperateDateVo> getOperateTendencyAnalyzeSnapshot(PerformanceOperateDateRequest request) {
        log.info("start RealtimeDataServiceImpl getOperateTendencyAnalyze request:{}", request);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        if (CommonUtil.StringUtils.isBlank(request.getOrganizationId())) {
            List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(), loginInfo);
            if (RequestUtils.getLoginInfo().getOrganizationType().equals("zb")) {
                request.setOrganizationId(employeeOrganizationId.get(0));
            }
        }

        LocalDate etlDate = LocalDate.parse("2024-05-31");
        LocalDate parse = LocalDate.parse(request.getYearMonth() + "-01");
        if (parse.isAfter(etlDate)) {
            etlDate = parse;
        }


        List<PerformanceOperateDateVo> operateTendencyAnalyze = realtimeMapper.getOperateTendencyAnalyzeSnapshot(request.getOrganizationId(), request.getDateTypeId(), request.getYearMonth(), etlDate.toString().substring(0, 7), request.getSortName(), request.getSortType());
        return operateTendencyAnalyze;
    }

    @Override
    public IPage<BusinessDevelopmentVo> queryBusinessDevelopment(BusinessDevelopmentRequest request) {
        log.info("start RealtimeDataServiceImpl queryBusinessDevelopment request:{}", request);
        //查询岗位
        String organizationId;
        String organizationType;
        if (request.getMemberKey() != null) {
            organizationType = RankingListPositionEnums.BRANCH.getOrganizationType();
        } else {
            organizationId = request.getOrganizationId();
            organizationType = organizationMapper.getOrganizationType(organizationId);
        }
        if (StringUtils.isBlank(organizationType)) {
            throw new ApplicationException(request.getOrganizationId() + "无对应的区域类型");
        }
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setOrganizationType(organizationType);
        //人员信息查询关联日期
        String nearMonth = realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth());
        request.setTheDate(nearMonth);
        Page<BusinessDevelopmentVo> page = new Page<>(request.getPage(), request.getRows());
        List<BusinessDevelopmentVo> list = realtimeMapper.queryBusinessDevelopment(page, request);

        List<EmployeeDTO> employeeDTOS = null;
        List<Integer> empIds = list.stream().filter(f -> Objects.nonNull(f.getEmployeeInfoId())).map(BusinessDevelopmentVo::getEmployeeInfoId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(empIds)) {
            employeeDTOS = Optional.ofNullable(sfaEmployeeInfoMapper.selectEmployeeInfoByIds(empIds)).orElse(new ArrayList<>());
        }

        List<EmployeeDTO> finalEmployeeDTOS = Optional.ofNullable(employeeDTOS).orElse(new ArrayList<>());
        list.stream().filter(f -> Objects.nonNull(f.getEmployeeInfoId())).forEach(f -> {
            EmployeeDTO employeeDTO = finalEmployeeDTOS.stream().filter(e -> e.getEmployeeInfoId().equals(f.getEmployeeInfoId())).findFirst().orElse(null);
            if (Objects.nonNull(employeeDTO)) {
                f.setPositionTypeId(employeeDTO.getPositionTypeId());
                f.setEmployeeStatus(employeeDTO.getEmployeeStatusStr());
                f.setInterviewRecordId(employeeDTO.getInterviewRecordId());
            }
        });


        page.setRecords(list);
        return page;
    }

    @Override
    public void downBusinessDevelopment(BusinessDevelopmentRequest req, HttpServletRequest request, HttpServletResponse response) {
        log.info("start RealtimeDataServiceImpl downBusinessDevelopment request:{}", req);
        //查询岗位
        String organizationId;
        String organizationType;
        if (req.getMemberKey() != null) {
            organizationType = RankingListPositionEnums.BRANCH.getOrganizationType();
        } else {
            organizationId = req.getOrganizationId();
            organizationType = organizationMapper.getOrganizationType(organizationId);
        }

        if (StringUtils.isBlank(organizationType)) {
            throw new ApplicationException(req.getOrganizationId() + "无对应的区域类型");
        }
        req.setBusinessGroup(RequestUtils.getBusinessGroup());
        req.setOrganizationType(organizationType);
        //人员信息查询关联日期
        String nearMonth = realTimeUtils.getNearMonth(req.getDateTypeId(), req.getYearMonth());
        req.setTheDate(nearMonth);
        List<BusinessDevelopmentVo> list = realtimeMapper.queryBusinessDevelopment(null, req);
        if (CommonConstant.REGION_CHINESE.equals(RequestUtils.getRegion())) {
            ExportUtil.writeEasyExcelResponse(response, request, "业务BD列表", BusinessDevelopmentVo.class, list);
        } else {
            ExportUtil.writeEasyExcelResponse(response, request, "Business Development List", BusinessDevelopmentEnVo.class, BeanUtil.copyToList(list, BusinessDevelopmentEnVo.class));
        }

    }

    @Override
    public IPage<AdvanceOrderBoardPageListVo> queryAdvanceOrderPageList(AdvanceOrderBoardPageListRequest request) {
        //补充请求信息
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if (CollectionUtil.isNotEmpty(request.getTagNames()) && request.getTagNames().contains("-")) {
            request.setTagNameFlag(0);
        }
        Page<AdvanceOrderBoardPageListVo> page = new Page<>(request.getPage(), request.getRows());
        List<AdvanceOrderBoardPageListVo> pageListVos = realtimeMapper.queryAdvanceOrderPageList(page, request);
        page.setRecords(pageListVos);
        return page;
    }

    @Override
    public void downAdvanceOrderPageList(AdvanceOrderBoardPageListRequest request, HttpServletRequest req, HttpServletResponse res) {
        //补充请求信息
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if (CollectionUtil.isNotEmpty(request.getTagNames()) && request.getTagNames().contains("-")) {
            request.setTagNameFlag(0);
        }
        List<AdvanceOrderBoardPageListVo> pageListVos = realtimeMapper.queryAdvanceOrderPageList(null, request);
        ExportUtil.writeEasyExcelResponse(res, req, "预订单看板", AdvanceOrderBoardPageListVo.class, pageListVos);
    }

    @Override
    public IPage<AdvanceOrderBoardPageDetailListVo> queryAdvanceOrderDetailPageList(AdvanceOrderBoardPageDetailListRequest request) {
        //补充请求信息
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        Page<AdvanceOrderBoardPageDetailListVo> page = new Page<>(request.getPage(), request.getRows());

        List<AdvanceOrderBoardPageDetailListVo> detailListVos = realtimeMapper.queryAdvanceOrderDetailPageList(page, request);
        page.setRecords(detailListVos);
        return page;
    }

    @Override
    public void downAdvanceOrderDetailPageList(AdvanceOrderBoardPageDetailListRequest request, HttpServletRequest req, HttpServletResponse res) {
        //补充请求信息
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<AdvanceOrderBoardPageDetailListVo> detailListVos = realtimeMapper.queryAdvanceOrderDetailPageList(null, request);
        ExportUtil.writeEasyExcelResponse(res, req, "预订单发货明细", AdvanceOrderBoardPageDetailListVo.class, detailListVos);
    }

    @Override
    public AdvanceOrderBoardEnumsVo getAdvanceOrderBoardEnums(AdvanceOrderBoardEnumsRequest request) {
        //补充请求信息
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(getOrganizationIdZB(request.getEmployeeId()));
        }
        request.setBusinessGroup(RequestUtils.getBusinessGroup());

        AdvanceOrderBoardEnumsVo vo = new AdvanceOrderBoardEnumsVo();
        vo.setAttributeTypes(realtimeMapper.advanceOrderBoardAttributeTypes(request));
        vo.setLineNames(realtimeMapper.advanceOrderBoardLineNames(request));
        vo.setChannelNames(realtimeMapper.advanceOrderBoardChannelNames(request));
        vo.setSkuInfos(realtimeMapper.advanceOrderBoardSkuNames(request));
        vo.setTagNames(realtimeMapper.advanceOrderBoardTagNames(request));
        return vo;
    }

    @Override
    public List<PartnerInventoryCheckTrendQueryInfoVo> queryPartnerInventoryCheckTrendInfo(PartnerInventoryCheckTrendRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        String nearMonth = realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth());
        YearMonth yearMonth = YearMonth.of(Integer.valueOf(nearMonth.substring(0, 4)), Integer.valueOf(nearMonth.substring(5)));
        YearMonth minusMonths = yearMonth.minusMonths(3);
        request.setStartDate(minusMonths + "-01");
        request.setEndDate(yearMonth.atEndOfMonth().toString());
        return realtimeMapper.queryPartnerInventoryCheckTrendInfo(request);
    }

    @Override
    public List<PartnerInventoryCheckTrendVo> queryPartnerInventoryCheckTrendList(PartnerInventoryCheckTrendRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        String nearMonth = realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth());
        YearMonth yearMonth = YearMonth.of(Integer.valueOf(nearMonth.substring(0, 4)), Integer.valueOf(nearMonth.substring(5)));
        YearMonth minusMonths = yearMonth.minusMonths(3);
        request.setStartDate(minusMonths + "-01");
        request.setEndDate(yearMonth.atEndOfMonth().toString());
        List<PartnerInventoryCheckTrendVo> checkTrendVos = realtimeMapper.queryPartnerInventoryCheckTrendList(request);
        if (CollectionUtil.isNotEmpty(checkTrendVos)) {
            checkTrendVos = checkTrendVos.stream().sorted(Comparator.comparing(PartnerInventoryCheckTrendVo::getCheckTime)).collect(Collectors.toList());
        }
        return checkTrendVos;
    }

    @Override
    public IPage<PartnerInventoryCheckListVo> queryPartnerInventoryCheckList(PartnerInventoryCheckListRequest request) {
        IPage<PartnerInventoryCheckListVo> page = new Page<>(request.getPage(), request.getRows());
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setNearMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        List<PartnerInventoryCheckListVo> checkListVos = realtimeMapper.queryPartnerInventoryCheckList(page, request);
        page.setRecords(checkListVos);
        return page;
    }

    @Override
    public void downPartnerInventoryCheckList(PartnerInventoryCheckListRequest request, HttpServletResponse res, HttpServletRequest req) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setNearMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        List<PartnerInventoryCheckListVo> checkListVos = realtimeMapper.queryPartnerInventoryCheckList(null, request);
        if (CommonConstant.REGION_CHINESE.equals(RequestUtils.getRegion())) {
            ExportUtil.writeEasyExcelResponse(res, req, "合伙人库存盘点信息", PartnerInventoryCheckListVo.class, checkListVos);
        } else {
            ExportUtil.writeEasyExcelResponse(res, req, "Partner inventory check list", PartnerInventoryCheckListEnVo.class, BeanUtil.copyToList(checkListVos, PartnerInventoryCheckListEnVo.class));
        }

    }

    @Override
    public IPage<PartnerGoalAttainmentVo> queryPartnerGoalAttainmentList(PartnerGoalAttainmentRequest request) {
        IPage<PartnerGoalAttainmentVo> page = new Page<>(request.getPage(), request.getRows());
        request.setNearMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if (CollectionUtil.isEmpty(request.getOrganizationIds())) {
            request.setOrganizationType(organizationMapper.getOrganizationType(request.getOrganizationId()));
        }
        List<PartnerGoalAttainmentVo> goalAttainmentVos = realtimeMapper.queryPartnerGoalAttainmentList(page, request);
        if (CollectionUtil.isNotEmpty(goalAttainmentVos)) {
            goalAttainmentVos.stream().forEach(goalAttainmentVo -> {
                goalAttainmentVo.setSaleGoalAchievementColor(realTimeUtils.saleGoalAchievementColor(request.getDateTypeId(), request.getYearMonth(), goalAttainmentVo.getPerformanceAchievementRate()));
                goalAttainmentVo.setPartnerType(ComonLanguageEnum.getDescByEnv(goalAttainmentVo.getPartnerType()));
            });
        }
        page.setRecords(goalAttainmentVos);
        return page;
    }

    @Override
    public void downPartnerGoalAttainmentList(PartnerGoalAttainmentRequest request, HttpServletRequest req, HttpServletResponse res) {
        request.setNearMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if (CollectionUtil.isEmpty(request.getOrganizationIds())) {
            request.setOrganizationType(organizationMapper.getOrganizationType(request.getOrganizationId()));
        }
        List<PartnerGoalAttainmentVo> goalAttainmentVos = realtimeMapper.queryPartnerGoalAttainmentList(null, request);
        if (CollectionUtil.isNotEmpty(goalAttainmentVos)) {
            goalAttainmentVos.forEach(goalAttainmentVo -> {
                goalAttainmentVo.setPartnerType(ComonLanguageEnum.getDescByEnv(goalAttainmentVo.getPartnerType()));
            });
        }
        if (CommonConstant.REGION_CHINESE.equals(RequestUtils.getRegion())) {
            ExportUtil.writeEasyExcelResponse(res, req, "合伙人达成", PartnerGoalAttainmentVo.class, goalAttainmentVos);
        } else {
            ExportUtil.writeEasyExcelResponse(res, req, "Partner Achievement", PartnerGoalAttainmentEnVo.class,
                    BeanUtil.copyToList(goalAttainmentVos, PartnerGoalAttainmentEnVo.class));
        }

    }

    @Override
    public IPage<PartnerGoalAttainmentOrderVo> partnerGoalAttainmentOrderList(PartnerGoalAttainmentOrderRequest request) {
        IPage<PartnerGoalAttainmentOrderVo> page = new Page<>(request.getPage(), request.getRows());
        //根据查询的时间查询开始月份和结束月份
        List<String> months = realTimeUtils.queryTimeStartMonth(request.getDateTypeId(), request.getYearMonth());
        request.setStartMonth(months.get(0));
        request.setEndMonth(months.get(1));
        List<PartnerGoalAttainmentOrderVo> attainmentOrderVos = realtimeMapper.partnerGoalAttainmentOrderList(page, request);
        if (CollectionUtil.isNotEmpty(attainmentOrderVos)) {
            attainmentOrderVos.forEach(attainmentOrderVo -> {
                attainmentOrderVo.setOrderStatus(ComonLanguageEnum.getDescByEnv(attainmentOrderVo.getOrderStatus()));
            });
        }
        page.setRecords(attainmentOrderVos);
        return page;
    }

    @Override
    public IPage<PartnerGoalAttainmentVisitRecordVo> partnerGoalAttainmentVisitRecords(PartnerGoalAttainmentVisitRecordRequest request) {
        IPage<PartnerGoalAttainmentVisitRecordVo> page = new Page<>(request.getPage(), request.getRows());
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        //根据查询的时间查询开始月份和结束月份
        List<String> months = realTimeUtils.queryTimeStartMonth(request.getDateTypeId(), request.getYearMonth());
        request.setStartMonth(months.get(0));
        request.setEndMonth(months.get(1));
        List<PartnerGoalAttainmentVisitRecordVo> timeVisitRecords = customerVisitInfoMapper.getVisitInfoForRealTimeVisitRecords(page, request);
        if (CollectionUtil.isNotEmpty(timeVisitRecords)) {
            timeVisitRecords.forEach(timeVisitRecord -> {
                timeVisitRecord.setPositionType(ComonLanguageEnum.getDescByEnv(timeVisitRecord.getPositionType()));
            });
        }
        page.setRecords(timeVisitRecords);
        return page;
    }


    @Override
    public CeoMonthTrendVo selectCeoTrend(CeoTrendSearchReq request) {
        /**
         * 财年：财年为单位展示  截止到当前年
         * 季度：季度为单位展示 当前年的所有季度和上一年的所有季度
         * 月度:月为单位展示 当前年的所有月份和上一年的的所有月份
         *
         * 同环比只需要当前时间的同环比
         *
         * 时间类型不能为空 10:自然月,11:自然季,2:财务年
         * 月份:自然月(2024-05)/自然季(2024-Q2)/财务年(2024)
         */
        List<String> presentYearMonthList = new ArrayList<>();
        List<String> lastYearMonthList = new ArrayList<>();
        List<String> yearMonthList = new ArrayList<>();
        //当前年
        String presentYear = request.getYearMonth().substring(0, 4);
        //上一年
        String lastYear = String.valueOf(Integer.valueOf(presentYear) - 1);

        if (!"2".equals(request.getDateTypeId())) {
            int count = 0;
            if ("11".equals(request.getDateTypeId())) {
                count = 4;
            } else if ("10".equals(request.getDateTypeId())) {
                count = 12;
            }
            for (int i = 1; i <= count; i++) {
                String month = null;
                if ("11".equals(request.getDateTypeId())) {
                    month = String.format("-Q%d", i);
                } else if ("10".equals(request.getDateTypeId())) {
                    month = String.format("-%02d", i);
                }
                presentYearMonthList.add(presentYear + month);
                lastYearMonthList.add(lastYear + month);
            }
        }

        if (!CollectionUtils.isEmpty(presentYearMonthList)) {
            yearMonthList.addAll(presentYearMonthList);
            yearMonthList.addAll(lastYearMonthList);
        }


        CeoMonthTrendVo ceoMonthTrendVo = new CeoMonthTrendVo();


        List<CeoTrendDTO> list = realtimeMapper.selectCeoTrend(request.getDateTypeId(), yearMonthList, request.getYearMonth(), request.getMemberKey(), RequestUtils.getBusinessGroup());

        if (CollectionUtils.isEmpty(list)) {
            return ceoMonthTrendVo;
        }


        List<TrendVo> currentMonthPerformance = new ArrayList<>();
        List<TrendVo> lastMonthPerformance = new ArrayList<>();
        List<TrendVo> performanceChainRatio = new ArrayList<>();
        List<TrendVo> performanceYearRatio = new ArrayList<>();

        if ("2".equals(request.getDateTypeId())) {
            list.stream().forEach(ceoTrendDTO -> {
                TrendVo current = new TrendVo();
                current.setYearMonth(ceoTrendDTO.getTheYearMonth());
                current.setData(ceoTrendDTO.getPerformance());
                currentMonthPerformance.add(current);

                TrendVo currentChain = new TrendVo();
                currentChain.setYearMonth(ceoTrendDTO.getTheYearMonth());
                currentChain.setData(ceoTrendDTO.getPerformanceChainRatio());
                performanceChainRatio.add(currentChain);

                TrendVo currentYear = new TrendVo();
                currentYear.setYearMonth(ceoTrendDTO.getTheYearMonth());
                currentYear.setData(ceoTrendDTO.getPerformanceYearRatio());
                performanceYearRatio.add(currentYear);
            });

        } else {
            Map<String, CeoTrendDTO> ceoTrendDTOMap = list.stream().collect(Collectors.toMap(CeoTrendDTO::getTheYearMonth, Function.identity(), (x, y) -> x));
            //2024-07-30
            String now = LocalDate.now().toString().substring(0, 7);
            presentYearMonthList.stream().forEach(presentYearMonth -> {
                CeoTrendDTO ceoTrendDTO = ceoTrendDTOMap.get(presentYearMonth);

                TrendVo current = new TrendVo();
                current.setYearMonth(presentYearMonth);

                TrendVo currentChain = new TrendVo();
                currentChain.setYearMonth(presentYearMonth);

                TrendVo currentYear = new TrendVo();
                currentYear.setYearMonth(presentYearMonth);

                if (Objects.nonNull(ceoTrendDTO)) {

                    current.setData(ceoTrendDTO.getPerformance());
                    currentMonthPerformance.add(current);

                    currentChain.setData(ceoTrendDTO.getPerformanceChainRatio());
                    performanceChainRatio.add(currentChain);

                    currentYear.setData(ceoTrendDTO.getPerformanceYearRatio());
                    performanceYearRatio.add(currentYear);

                } else if (now.compareTo(presentYearMonth) >= 0) {
                    //小于当前月份补充数据
                    current.setData(BigDecimal.ZERO);
                    currentMonthPerformance.add(current);

                    currentChain.setData(BigDecimal.ZERO);
                    performanceChainRatio.add(currentChain);

                    currentYear.setData(BigDecimal.ZERO);
                    performanceYearRatio.add(currentYear);
                }
            });
            lastYearMonthList.stream().forEach(lastYearMonth -> {
                CeoTrendDTO ceoTrendDTO = ceoTrendDTOMap.get(lastYearMonth);
                TrendVo vo = new TrendVo();
                vo.setYearMonth(lastYearMonth);
                if (Objects.nonNull(ceoTrendDTO)) {
                    vo.setData(ceoTrendDTO.getPerformance());
                } else {
                    vo.setData(BigDecimal.ZERO);
                }
                lastMonthPerformance.add(vo);
            });

        }

        ceoMonthTrendVo.setCurrentMonthPerformance(currentMonthPerformance);
        ceoMonthTrendVo.setLastMonthPerformance(lastMonthPerformance);
        ceoMonthTrendVo.setPerformanceChainRatio(performanceChainRatio);
        ceoMonthTrendVo.setPerformanceYearRatio(performanceYearRatio);
        return ceoMonthTrendVo;
    }


}
