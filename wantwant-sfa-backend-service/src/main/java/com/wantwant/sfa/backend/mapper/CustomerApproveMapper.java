package com.wantwant.sfa.backend.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.sfa.backend.customer.request.CustomerApproveRequest;
import com.wantwant.sfa.backend.customer.vo.CustomerApproveVO;
import com.wantwant.sfa.backend.customer.vo.CustomerWarehouseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @description 客户审批Mapper
 * <AUTHOR>
 * @date 2025-01-29 11:40
 **/
@Mapper
public interface CustomerApproveMapper {

    List<String> pageCustomerApprove1(Page<CustomerApproveVO> page,@Param("params") CustomerApproveRequest query);

    List<CustomerApproveVO> pageCustomerApproveIds(@Param("ids") List<String> ids,@Param("params") CustomerApproveRequest query);

    List<CustomerApproveVO> customerApproveCount(@Param("params") CustomerApproveRequest query);

    List<CustomerApproveVO> pageCustomerApproveIds1(@Param("ids") List<String> ids,@Param("params") CustomerApproveRequest query);

    List<CustomerApproveVO> listCustomerApprove(@Param("params") CustomerApproveRequest query);

    List<CustomerApproveVO> customerModifyList(@Param("params") CustomerApproveRequest query);

    CustomerApproveVO getCustomerApproveById(@Param("customerId") String customerId, @Param("storeType") Integer storeType);
    Integer getCustomerStoreType(String customerId);
    /**
     * 根据客户id获取仓库信息
     *
     * @param customerId
     * @return: java.util.List<com.wantwant.sfa.backend.customer.vo.CustomerWarehouseVO>
     * @date: 2022-02-19 18:20
     */
    List<CustomerWarehouseVO> listWarehouse(@Param("customerId") String customerId);

    @Select("SELECT \n" +
            "CONCAT_WS('/',province,city,district,towns,street)\n" +
            "from `hotkidceo_member`.`member_address` where active =1 and customerId =#{customerId}")
    String getReceiveAddressById(@Param("customerId") String customerId);

    String queryBusinessGroupCode(@Param("id") Integer businessGroup);

    List<String> querySmallMarketByCustomerId(@Param("customerId") String customerId);
    
    /**
     * @description 统计可审核的客户数量（BShowBtn为true的数量）
     * <AUTHOR>
     * @date 2025-01-29 11:40
     **/
    Long countAuditableCustomers(@Param("params") CustomerApproveRequest query, 
                                @Param("currentPositionType") Integer currentPositionType,
                                @Param("currentPerson") String currentPerson);
}
