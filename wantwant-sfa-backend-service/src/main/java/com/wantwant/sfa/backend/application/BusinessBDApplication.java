package com.wantwant.sfa.backend.application;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.activityQuota.model.PenaltyImportModel;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.businessBd.DO.SalaryPackageRatio;
import com.wantwant.sfa.backend.domain.businessBd.service.BusinessBdOrgQuotaService;
import com.wantwant.sfa.backend.domain.businessBd.service.BusinessBdSalaryControlService;
import com.wantwant.sfa.backend.domain.emp.DO.BusinessBDRuleDO;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.service.IBusinessBDRuleService;
import com.wantwant.sfa.backend.domain.emp.service.IEmpService;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.salary.request.*;
import com.wantwant.sfa.backend.salary.vo.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/20/下午4:28
 */
@Service
public class BusinessBDApplication {
    @Resource
    private IEmpService empService;
    @Resource
    private IBusinessBDRuleService businessBDRuleService;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private BusinessBdOrgQuotaService businessBdOrgQuotaService;
    @Resource
    private BusinessBdSalaryControlService businessBdSalaryControlService;

    /**
     * 导入业务BD规则
     *
     * @param file
     * @param person
     */
    public void importRule( MultipartFile file,String person){
        ProcessUserDO processUserDO = empService.getUserById(person);

        List<BusinessBDRuleDO> businessBDRuleDOList = null;
        try {
            ImportParams params = new ImportParams();
            businessBDRuleDOList = ExcelImportUtil.importExcel(file.getInputStream(), BusinessBDRuleDO.class, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        businessBDRuleDOList = Optional.ofNullable(businessBDRuleDOList).orElse(new ArrayList<>()).stream().filter(f -> StringUtils.isNotBlank(f.getCompanyName())).collect(Collectors.toList());
        businessBDRuleService.importRule(businessBDRuleDOList,processUserDO);

    }

    /**
     * 查询业务BD自动化
     *
     * @param request
     * @return
     */
    public IPage<BusinessBDRuleVo> searchBusinessBDRule(BusinessBDRuleSearchRequest request) {
        List<String> organizationIds = request.getOrganizationIds();
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if(!CollectionUtils.isEmpty(organizationIds)){
            String orgId = organizationIds.stream().findFirst().get();
            String organizationType = organizationMapper.getOrganizationType(orgId);
            request.setOrgType(organizationType);
        }

        return businessBDRuleService.searchBusinessBDRule(request);
    }

    public void exportBusinessBDRule(BusinessBDRuleSearchRequest request) {
        List<String> organizationIds = request.getOrganizationIds();
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if(!CollectionUtils.isEmpty(organizationIds)){
            String orgId = organizationIds.stream().findFirst().get();
            String organizationType = organizationMapper.getOrganizationType(orgId);
            request.setOrgType(organizationType);
        }
        businessBDRuleService.exportBusinessBDRule(request);
    }

    public List<BusinessBDDetailVO> getBusinessBDDetail(String theYearMonth, String organizationId) {
        return businessBDRuleService.getBusinessBDDetail(theYearMonth,organizationId);
    }

    public IPage<BusinessBDDetailVO> selectBusinessBDDetail(BusinessBDSearchRequest businessBDSearchRequest) {
        return businessBDRuleService.selectBusinessBDDetail(businessBDSearchRequest);
    }




    public void exportBusinessDetail(BusinessBDSearchRequest businessBDSearchRequest) {
        businessBDRuleService.exportBusinessDetail(businessBDSearchRequest);
    }

    public IPage<BusinessBDOrgDetailVO> selectBusinessBDOrgDetail(BusinessBDSearchRequest businessBDSearchRequest) {
        return businessBdOrgQuotaService.selectBusinessBDOrgDetail(businessBDSearchRequest);
    }

    public void exportBusinessOrgDetail(BusinessBDSearchRequest businessBDSearchRequest) {
        businessBdOrgQuotaService.exportBusinessOrgDetail(businessBDSearchRequest);
    }

    public BigDecimal getBusinessBDPackageSalary(String departmentId, Integer applyId) {
        return businessBDRuleService.getBusinessBDPackageSalary(departmentId,applyId);
    }

    public IPage<BusinessBDQuotaConfigVo> getBusinessBDQuotaControl(SalaryControlSearchRequest salaryControlSearchRequest) {
        return businessBdSalaryControlService.getBusinessBDQuotaControl(salaryControlSearchRequest);
    }

    public void exportBusinessBDQuotaControl(SalaryControlSearchRequest salaryControlSearchRequest) {
        businessBdSalaryControlService.exportBusinessBDQuotaControl(salaryControlSearchRequest);
    }

    public IPage<BusinessBDSalaryVo> getAllocatedBusinessBDHistory(BusinessBDAllocatedRequest businessBDAllocatedRequest) {
        return businessBdSalaryControlService.getAllocatedBusinessBDHistory(businessBDAllocatedRequest);
    }

    public void exportAllocatedBusinessBDHistory(BusinessBDAllocatedRequest businessBDAllocatedRequest) {
        businessBdSalaryControlService.exportAllocatedBusinessBDHistory(businessBDAllocatedRequest);
    }

    public IPage<BusinessBDControlDetailVO> selectBusinessBDControl(BusinessBDControlSearch businessBDControlSearch) {
        return businessBdSalaryControlService.selectBusinessBDControl(businessBDControlSearch);
    }

    public List<SalaryPackageRateLogVO> getSalaryPackageLog(String departmentId) {
        return businessBdSalaryControlService.getSalaryPackageLog(departmentId);
    }

    public void exportBusinessBDControl(BusinessBDControlSearch businessBDControlSearch) {
        businessBdSalaryControlService.exportBusinessBDControl(businessBDControlSearch);
    }

    public void importSalaryPackageRatio(MultipartFile file, String employeeId) {
        ProcessUserDO processUserDO = empService.getUserById(employeeId);
        ImportParams params = new ImportParams();

        List<SalaryPackageRatio> list = null;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SalaryPackageRatio.class, params);
        } catch (Exception e) {
            throw new ApplicationException("导入失败");
        }

        list = list.stream().filter(f -> StringUtils.isNotBlank(f.getBusinessGroupName())
        && StringUtils.isNotBlank(f.getBusinessGroupName()) && StringUtils.isNotBlank(f.getRatio())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            throw new ApplicationException("导入文件为空");
        }

        businessBdSalaryControlService.importSalaryPackageRatio(list,processUserDO);
    }

    public void setBusinessBDSalaryPackageConfig(BusinessBDSalaryPackageConfig businessBDSalaryPackageConfig) {
        ProcessUserDO processUserDO = empService.getUserById(businessBDSalaryPackageConfig.getPerson());

        businessBdSalaryControlService.setBusinessBDSalaryPackageConfig(businessBDSalaryPackageConfig,processUserDO);
    }
}
