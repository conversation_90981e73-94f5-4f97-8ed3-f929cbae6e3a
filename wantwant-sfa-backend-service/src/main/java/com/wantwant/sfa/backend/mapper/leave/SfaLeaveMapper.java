package com.wantwant.sfa.backend.mapper.leave;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.sfa.backend.leave.entity.SfaLeave;
import com.wantwant.sfa.backend.leave.request.NewLeaveDetailRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveListRequest;
import com.wantwant.sfa.backend.leave.vo.NewLeaveListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface SfaLeaveMapper extends BaseMapper<SfaLeave> {
    
    @Select("SELECT\n" +
            "\tid\n" +
            "FROM\n" +
            "\tsfa_leave\n" +
            "WHERE\n" +
            "\tleave_start_time = #{leaveStartTime} and leave_status in (0,1) and apply_employee_info_id = #{applyEmployeeInfoId} limit 1\n")
    SfaLeave checkExists(@Param("leaveStartTime") LocalDateTime leaveStartTime, @Param("applyEmployeeInfoId") Integer applyEmployeeInfoId);

    @Select("SELECT\n" +
            "\tleave_type,\n" +
            "\tleave_start_time,\n" +
            "\tleave_end_time\n" +
            "FROM\n" +
            "\tsfa_leave\n" +
            "WHERE\n" +
            "\tattendance_start_date = #{attendanceStartDate} and attendance_end_date = #{attendanceEndDate} and leave_status in (0,1,4) and apply_employee_info_id = #{applyEmployeeInfoId} and delete_flag=0\n")
    List<SfaLeave> getExists(@Param("attendanceStartDate") LocalDate attendanceStartDate, @Param("attendanceEndDate") LocalDate attendanceEndDate, @Param("applyEmployeeInfoId") Integer applyEmployeeInfoId);

    @Select("SELECT\n" +
            "\tmonth_already_leave_hours,\n" +
            "\tleave_hours\n" +
            "FROM\n" +
            "\tsfa_leave\n" +
            "WHERE\n" +
            "\tattendance_start_date = #{attendanceStartDate} and attendance_end_date = #{attendanceEndDate} and leave_status = 1 and apply_employee_info_id = #{applyEmployeeInfoId} and delete_flag=0 order by update_time desc limit 1\n")
    SfaLeave getLastestApprovalRecord(@Param("attendanceStartDate") LocalDate attendanceStartDate, @Param("attendanceEndDate") LocalDate attendanceEndDate, @Param("applyEmployeeInfoId") Integer applyEmployeeInfoId);

    /*
     * type 1:相加 2：相减
     */
    void updateAttendanceMonthAlreadyLeaveHours(@Param("attendanceStartDate") LocalDate attendanceStartDate, @Param("attendanceEndDate") LocalDate attendanceEndDate, @Param("applyEmployeeInfoId") Integer applyEmployeeInfoId, @Param("leaveHours") Integer leaveHours, @Param("type") int type);

    void updateLeaveById(SfaLeave sfaLeave);

    List<NewLeaveListVo> queryLeaveList(Page<NewLeaveListVo> page, @Param("params") NewLeaveListRequest request, @Param("auditType") int auditType, @Param("businessGroup") Integer businessGroup);

    NewLeaveListVo queryLeaveDetail(@Param("params") NewLeaveDetailRequest request);

    List<SfaLeave> queryAuditLeaveList(@Param("businessNumList") List<String> businessNumList);

    List<SfaLeave> queryLeaveAuditList();

    /**
     * 获取某一天某人请假数据
     * @return
     */
    @Select("select * from sfa_leave where apply_employee_info_id = #{employeeInfoId} and leave_status in(0,1) and DATE_FORMAT(leave_start_time ,'%Y-%m-%d') <= #{theDay}  and DATE_FORMAT( leave_end_time ,'%Y-%m-%d') >= #{theDay} limit 1 ")
    SfaLeave getLeaveInfoAtThatDay(Integer employeeInfoId, String theDay);

}
