package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/10/23/下午12:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_employee_info")
@ApiModel(value = "sfa_employee_info对象", description = "")
@ToString
public class SfaEmployeeInfoModel {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("channel")
    @ApiModelProperty(name="渠道",allowableValues="1.sfa 2.旺江山 3.造旺")
    private Integer channel;

    @TableField("position_id")
    @ApiModelProperty(name="岗位ID",allowableValues="岗位ID")
    private String positionId;

    @TableField("application_id")
    @ApiModelProperty(name="申请ID")
    private Integer applicationId;

    @TableField("employee_name")
    @ApiModelProperty(name="员工姓名")
    private String employeeName;

    @TableField("gender")
    @ApiModelProperty(name="员工性别",allowableValues="0.未知 1.男 2.女")
    private Integer gender;

    @TableField("province")
    @ApiModelProperty(name="省")
    private String province;
    @TableField("city")
    @ApiModelProperty(name="市")
    private String city;
    @TableField("district")
    @ApiModelProperty(name="区")
    private String district;

    @TableField("mobile")
    @ApiModelProperty(name="手机号")
    private String mobile;

    @ApiModelProperty(name="员工工号")
    @TableField(value = "employee_id",strategy= FieldStrategy.IGNORED)
    private String employeeId;

    @ApiModelProperty(name="主岗事业部",allowableValues="1.旺旺在职 2.第三方兼职")
    @TableField("affiliation")
    private Integer affiliation;

    @ApiModelProperty(name="岗位类型",allowableValues="1.全职 2.兼职")
    @TableField("post_type")
    private Integer postType;

    @ApiModelProperty(name="入职日期")
    @TableField(value = "onboard_time",strategy= FieldStrategy.IGNORED)
    private LocalDateTime onboardTime;

    @ApiModelProperty(name="试岗日期")
    @TableField("probation_time")
    private String probationTime;

    @ApiModelProperty(name="大区CODE")
    @TableField("area_code")
    private String areaCode;
    @ApiModelProperty(name="大区名称")
    @TableField("area_name")
    private String areaName;

    @ApiModelProperty(name="虚拟大区CODE")
    @TableField(value="varea_organization_id" ,strategy = FieldStrategy.IGNORED)
    private String vareaOrganizationId;
    @ApiModelProperty(name="虚拟大区名称")
    @TableField(value="varea_organization_name" ,strategy = FieldStrategy.IGNORED)
    private String vareaOrganizationName;

    @ApiModelProperty(name="省区CODE")
    @TableField(value="province_organization_id" ,strategy = FieldStrategy.IGNORED)
    private String provinceOrganizationId;
    @ApiModelProperty(name="省区名称")
    @TableField(value="province_organization_name" ,strategy = FieldStrategy.IGNORED)
    private String provinceOrganizationName;

    @TableField(value="company_code" ,strategy = FieldStrategy.IGNORED)
    @ApiModelProperty(name="分公司CODE")
    private String companyCode;
    @TableField(value="company_name" ,strategy = FieldStrategy.IGNORED)
    @ApiModelProperty(name="分公司名称")
    private String companyName;
    @TableField(value = "branch_code" ,strategy = FieldStrategy.IGNORED)
    @ApiModelProperty(name="营业所CODE")
    private String branchCode;
    @TableField(value = "branch_name",strategy = FieldStrategy.IGNORED)
    @ApiModelProperty(name="营业所Name")
    private String branchName;
    @TableField(value = "department_code",strategy = FieldStrategy.IGNORED)
    @ApiModelProperty(name="营业所code")
    private String departmentCode;
    @TableField(value = "department_name",strategy = FieldStrategy.IGNORED)
    @ApiModelProperty(name="营业所名字")
    private String departmentName;

    @TableField("employee_status")
    @ApiModelProperty(name="员工状态")
    private Integer employeeStatus;


    @TableField("create_user_id")
    @ApiModelProperty(name="创建人id")
    private String createUserId;
    @TableField("create_user_name")
    @ApiModelProperty(name="创建人姓名")
    private String createUserName;
    @TableField("create_time")
    @ApiModelProperty(name="创建时间")
    private Date createTime;
    @TableField("update_user_id")
    @ApiModelProperty(name="修改人id")
    private String updateUserId;
    @TableField("update_user_name")
    @ApiModelProperty(name="修改人姓名")
    private String updateUserName;
    @TableField("update_time")
    @ApiModelProperty(name="修改时间")
    private Date updateTime;
    @TableField("`status`")
    @ApiModelProperty(name="状态")
    private Integer status;

    @TableField(value="contract_company",strategy = FieldStrategy.IGNORED)
    private String contractCompany;

    @TableField("certification_time")
    private LocalDateTime certificationTime;

    @TableField("agency_time")
    private LocalDateTime agencyTime;

    @TableField("member_key")
    private Long memberKey;
    /**
     * 入职公司
     */
    @TableField(value="joining_company",strategy = FieldStrategy.IGNORED)
    private String joiningCompany;
    @TableField(value="actual_joining_company",strategy = FieldStrategy.IGNORED)
    private String actualJoiningCompany;
    @TableField("`type`")
    private Integer type;
    @TableField("work_mode")
    private Integer workMode;
}
