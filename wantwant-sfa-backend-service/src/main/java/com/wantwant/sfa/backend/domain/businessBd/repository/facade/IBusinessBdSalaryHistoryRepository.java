package com.wantwant.sfa.backend.domain.businessBd.repository.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.businessBd.DO.OrgSalaryDO;
import com.wantwant.sfa.backend.domain.businessBd.DO.SalaryAllocatedDO;
import com.wantwant.sfa.backend.domain.businessBd.repository.po.BusinessBdSalaryHistoryPO;
import com.wantwant.sfa.backend.salary.request.BusinessBDAllocatedRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDSalaryVo;
import com.wantwant.sfa.backend.salary.vo.SalaryAllocatedVo;

import java.util.List;

public interface IBusinessBdSalaryHistoryRepository {

    /**
     * 批量保存
     *
     * @param businessBdSalaryHistoryPOS
     */
    void batchInsert(List<BusinessBdSalaryHistoryPO> businessBdSalaryHistoryPOS);

    /**
     * 历史费用使用查询
     *
     * @param theYearMonth
     * @return
     */
    List<OrgSalaryDO> selectHistoryTotalSalary(String theYearMonth);

    /**
     * 删除历史
     *
     * @param theYearMonth
     */
    void deleteByTheYearMonth(String theYearMonth);

    /**
     * 查询业务BD薪资历史
     *
     * @param page
     * @param businessBDAllocatedRequest
     * @return
     */
    List<SalaryAllocatedDO> selectHistory(IPage<BusinessBDSalaryVo> page, BusinessBDAllocatedRequest businessBDAllocatedRequest);

    /**
     * 查询业务BD薪资历史
     *
     * @param departmentId
     * @param theYearMonth
     * @return
     */
    List<BusinessBdSalaryHistoryPO>  selectByOrgIdAndDate(String departmentId, String theYearMonth);

    /**
     * 根据日期范围查询业务BD薪资历史
     *
     * @param companyCode
     * @param startYearMonth
     * @param endYearMonth
     * @return
     */
    List<BusinessBdSalaryHistoryPO>  selectByOrgIdAndDateRange(String companyCode, String startYearMonth, String endYearMonth);
}
