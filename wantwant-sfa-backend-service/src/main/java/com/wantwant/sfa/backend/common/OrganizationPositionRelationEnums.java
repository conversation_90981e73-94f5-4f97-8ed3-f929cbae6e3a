package com.wantwant.sfa.backend.common;

import com.wantwant.sfa.common.base.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum OrganizationPositionRelationEnums {
    ZB("zb", 7, "总部", "Headquarters"),
    AREA("area", 1, "总督导", "National Sales Manager"),
    V_AREA("varea", 12, "大区总监", "Area Sales Manager"),
    PROVINCE("province", 11, "省区总监", "Provincial Director"),
    COMPANY("company", 2, "区域总监", "Area Sales Supervisor"),
    DEPARTMENT("department", 10, "区域经理", "Regional Manager"),
    BRANCH("branch", 3, "合伙人", "Partner"),

    /**
     * 注意：合伙人需要这样关联查询  业务 bd 直接使用sfa_position_relation表即可
     * sfa_position_relation.position_type_id + sfa_employee_info.type + sfa_employee_info.post_type
     * <p>
     * when t.position_type_id = 3  and hsein.post_type = 2 and hsein.`type` = 1 then '兼职合伙人'
     * when t.position_type_id = 3  and hsein.post_type = 1 and hsein.`type` = 1 then '全职合伙人'
     * when t.position_type_id = 3  and hsein.`type` = 2 then '流通合伙人'   -- 企业合伙人
     * when t.position_type_id = 3  and hsein.`type` = 3 then '承揽合伙人'
     * when t.position_type_id = 3  and hsein.`type` = 8 then '直营合伙人'
     */
    FULL_TIME_PARTNER("branch", 311, "全职合伙人", "Full-time Partner"),
    PART_TIME_PARTNER("branch", 312, "兼职合伙人", "Part-time Partner"),
    DISTRIBUTION_PARTNER("branch", 322, "流通合伙人", "Distribution Partner"),
    CONTRACT_PARTNER("branch", 332, "承揽合伙人", "Contracting Partner"),
    DIRECT_OPERATED_PARTNER("branch", 382, "直营合伙人", "Direct-operated Partner"),


    BD_FULL_TIME("branch", 361, "全职业务BD", "Full-time Business BD"),
    BD_PART_TIME("branch", 362, "兼职业务BD", "Part-time Business BD"),
    BD_PART_TIME_CONTRACT("branch", 372, "承揽业务BD", "Part-time Contracting Business BD"),
    ;
    private final String organizationType;
    private final Integer positionTypeId;
    private final String positionName;
    private final String positionEnName;


    public static String getPositionName(Integer positionTypeId) {
        for (OrganizationPositionRelationEnums value : OrganizationPositionRelationEnums.values()) {
            if (value.getPositionTypeId().equals(positionTypeId)) {
                return value.getPositionName();
            }
        }
        return null;
    }

    public static String getPositionName(Integer positionTypeId, Integer i18nFlag) {
        for (OrganizationPositionRelationEnums value : OrganizationPositionRelationEnums.values()) {
            if (value.getPositionTypeId().equals(positionTypeId)) {
                if (i18nFlag == 1) {
                    return value.getPositionEnName();
                }
                return value.getPositionName();
            }
        }
        return null;
    }

    public static String getPositionName(Integer positionTypeId, String flag) {
        for (OrganizationPositionRelationEnums value : OrganizationPositionRelationEnums.values()) {
            if (value.getPositionTypeId().equals(positionTypeId)) {
                if (CommonConstant.TIMEZONE_ASIA_JAKARTA.equals(flag)
                        || CommonConstant.REGION_INDONESIA.equals(flag)
                        || CommonConstant.LANGUAGE_ENGLISH.equals(flag)) {
                    return value.getPositionEnName();
                }
                return value.getPositionName();
            }
        }
        return null;
    }
    public static String getDescByPositionTypeId(Integer positionTypeId) {
        String language = RequestUtils.getLoginInfo().getLanguage();
        for (OrganizationPositionRelationEnums value : OrganizationPositionRelationEnums.values()) {
            if (value.getPositionTypeId().equals(positionTypeId)) {
                if (CommonConstant.LANGUAGE_CHINESE.equals(language)) {
                    return value.getPositionName();
                } else {
                    return value.getPositionEnName();
                }
            }
        }
       return null;
    }

    /**
     * 根据中文岗位名获取岗位名
     */
    public static String getPositionNameByEnv(String positionName) {
        if (StringUtils.isBlank(positionName)) {
            return null;
        }
        String language = RequestUtils.getLoginInfo().getLanguage();
        for (OrganizationPositionRelationEnums value : OrganizationPositionRelationEnums.values()) {
            if (value.getPositionName().equals(positionName)) {
                if (CommonConstant.LANGUAGE_ENGLISH.equals(language)) {
                    return value.getPositionEnName();
                }
                return value.getPositionName();
            }
        }
        return null;
    }
}
