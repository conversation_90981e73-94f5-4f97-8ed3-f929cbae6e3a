package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2021-11-04
 */
@TableName("sfa_apply_member")
@Data
@ToString
public class ApplyMemberPo extends Model<ApplyMemberPo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /**
     * 0:非旺旺员工 1:旺旺员工
     */
    @TableField("ceo_flag")
    private Integer ceoFlag;

    /**
     * 工号
     */
    @TableField("employ_id")
    private String employId;

    /**
     * 主岗位
     */
    @TableField(value = "ww_position", strategy = FieldStrategy.IGNORED)
    private String wwPosition;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 用户手机号
     */
    @TableField("user_mobile")
    private String userMobile;

    /**
     * 性别(1:男,2:女)
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 报名岗位(1:造旺合伙人,2:造旺区域总监,3:战区总,4:区域经理人) 5 大区总监 6 省区总监 7业务BD 8 客户主任
     */
    @TableField("position")
    private Integer position;

    /**
     * 代理省
     */
    @TableField("agent_province")
    private String agentProvince;

    /**
     * 市
     */
    @TableField("agent_city")
    private String agentCity;

    /**
     * 区
     */
    @TableField("agent_district")
    private String agentDistrict;


    /**
     * 区
     */
    @TableField("agent_town")
    private String agentTown;

    /**
     * 详情区域
     */
    @TableField("agent_street")
    private String agentStreet;

    /**
     * 大区
     */
    @TableField("area")
    private String area;

    /**
     * 大区组织ID
     */
    @TableField("area_organization_id")
    private String areaOrganizationId;

    /**
     * 分公司
     */
    @TableField("company")
    private String company;

    /**
     * 分公司组织ID
     */
    @TableField("company_organization_id")
    private String companyOrganizationId;


    /**
     * 营业所
     */
    @TableField(value ="branch_organization_name",strategy = FieldStrategy.IGNORED)
    private String branchOrganizationName;

    /**
     * 营业所ID
     */
    @TableField(value = "branch_organization_id",strategy = FieldStrategy.IGNORED)
    private String branchOrganizationId;


    /**
     * 来源(1:员工推荐,2:第三方外包,3:自主报名, 4:合伙人推荐)
     */
    @TableField("source")
    private Integer source;

    /**
     * 推荐人工号
     */
    @TableField("superior_employ_id")
    private String superiorEmployId;

    /**
     * 推荐人姓名
     */
    @TableField("superior_name")
    private String superiorName;

    /**
     * 推荐人手机号
     */
    @TableField("superior_mobile")
    private String superiorMobile;

    /**
     * 推荐公司
     */
    @TableField("superior_company")
    private String superiorCompany;

    /**
     * 0:未删除 1:删除
     */
    @TableField("delete_flag")
    private Integer deleteFlag;

    /**
     * 个人描述
     */
    @TableField("remark")
    private String remark;

    /**
     * 简历url
     */
    @TableField("resume_url")
    private String resumeUrl;

    /**
     * 简历url
     */
    @TableField(value = "partner_company_name",strategy = FieldStrategy.IGNORED)
    private String partnerCompanyName;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private LocalDateTime applyTime;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    /**
     * 1:全职 2:兼职
     */
    @TableField("jobs_type")
    private Integer jobsType;

    /**
     * 报名来源(1:外包公司-睿秉,2:英格瑪,3:卡思,4:自主报名-推广广告,5:旺旺,6:魔方,7:易才,8,:華服,9: 大瀚, 10:合伙人推荐,11:安可人力,12:人瑞,13:万宝盛华,14:斗米,15:杰博,16:CDP)
     */
    @TableField("registration_source")
    private Integer registrationSource;

    /**
     * 头像地址
     */
    @TableField("`pic_url`")
    private String picUrl;

    /**
     * 建议底薪
     */
    @TableField("`advice_salary`")
    private Integer adviceSalary;

    /**
     * 身份
     */
    @TableField("identity")
    private String identity;

    /**
     * 行业
     */
    @TableField("industry")
    private String industry;

    /**
     * 居住年限
     */
    @TableField("residence_years")
    private String residenceYears;

    /**
     * 销售经验年限
     */
    @TableField("sales_years")
    private String salesYears;

    /**
     * 客户资源
     */
    @TableField("customer_resources")
    private String customerResources;

    /**
     * 特殊渠道资源
     */
    @TableField("special_channel_resources")
    private String specialChannelResources;

    /**
     * 食品饮料经验(1:有,2:无)
     */
    @TableField("food_experience")
    private String foodExperience;

    /**
     * 身份证号
     */
    @TableField("idCard_num")
    private String idCardNum;

    /**
     * 实名认证-身份证正面
     */
    @TableField("idCard_front_url")
    private String idCardFrontUrl;

    /**
     * 实名认证-身份证反面
     */
    @TableField("idCard_back_url")
    private String idCardBackUrl;

    /**
     * 实名认证-手持照片
     */
    @TableField("hold_card_url")
    private String holdCardUrl;

    /**
     * 是否来自ehr系统
     */
    @TableField("ehr_source")
    private Integer ehrSource;

    /**
     * 出生日期
     */
    @TableField("birth_date")
    private String birthDate;

    /**
     * 最高学历
     */
    @TableField("highest_education")
    private String highestEducation;

    /**
     * 管理经验年限
     */
    @TableField("management_experience_years")
    private String managementExperienceYears;

    /**
     * 期望薪资
     */
    @TableField("salary_expectation")
    private String salaryExpectation;

    /**
     * 市场调查报告地址
     */
    @TableField("market_research_report_url")
    private String marketResearchReportUrl;

    @TableField("ehr_apply_id")
    private Long ehrApplicationId;

    @TableField("ceo_type")
    private Integer ceoType;

    /** 企业合伙人 ⬇⬇ */
    @TableField(value = "business_license",strategy = FieldStrategy.IGNORED)
    private String businessLicense;
    @TableField(value = "business_license_photo",strategy = FieldStrategy.IGNORED)
    private String businessLicensePhoto;
    @TableField("team_count")
    private String teamCount;
    @TableField("motor_count")
    private String motorCount;
    @TableField(value = "street",strategy = FieldStrategy.IGNORED)
    private String street;
    @TableField(value="warehouse",strategy = FieldStrategy.IGNORED)
    private String warehouse;
    @TableField(value="bank_statement",strategy = FieldStrategy.IGNORED)
    private String bankStatements;
    @TableField("terminal_count")
    private String terminalCount;
    @TableField("invoicing_sys")
    private String invoicingSys;
    /**  企业合伙人 ⬆⬆*/

    @TableField("graduation_date")
    private String graduationDate;
    @TableField("graduate_school")
    private String graduateSchool;
    @TableField("specializing")
    private String specializing;
    @TableField("current_status")
    private Integer currentStatus;
    @TableField("production_group_code")
    private String productionGroupCode;

    @TableField("business_group")
    private Integer businessGroup;
    /**
     * 平台来源(1:旺铺)
     */
    @TableField("platform_source")
    private Integer platformSource;

    @TableField("score")
    private BigDecimal score;

    @TableField("score_comment")
    private String scoreComment;

    @TableField("recruitment_source")
    private String recruitmentSource;

    @ApiModelProperty("社保缴纳地-省")
    private String socialInsuranceProvince;
    @ApiModelProperty("社保缴纳地-市")
    private String socialInsuranceCity;
    @ApiModelProperty("社保缴纳地-区")
    private String socialInsuranceDistrict;
    @ApiModelProperty("省区CODE")
    private String provinceOrganizationId;
    @ApiModelProperty("省区名称")
    private String provinceOrganizationName;
    @ApiModelProperty("虚拟大区CODE")
    @TableField(value="varea_organization_id")
    private String vareaOrganizationId;
    @TableField(value="varea_organization_name")
    private String vareaOrganizationName;
    @TableField(value="original_user_name")
    private String originalUserName;
    @TableField(value="original_id_card_num")
    private String originalIdCardNum;
    @TableField(value="market_channel")
    private String marketChannel;
    @TableField(value="company_photo")
    private String companyPhoto;

    @TableField(exist = false)
    private String joiningCompany;
    @TableField(value="optional_retest_time")
    @ApiModelProperty(value = "可选复试时间")
    private String optionalRetestTime;

    @TableField(value="license_owner")
    private Integer licenseOwner;

    @TableField(value="relative_in_company")
    private Integer relativeInCompany;

    @TableField(value="work_place",strategy = FieldStrategy.IGNORED)
    private String workPlace;

    @TableField(value="channel")
    private String channel;

    @TableField(value = "work_mode")
    private Integer workMode;
}
