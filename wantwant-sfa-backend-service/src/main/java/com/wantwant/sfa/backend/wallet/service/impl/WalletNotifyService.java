package com.wantwant.sfa.backend.wallet.service.impl;

import com.wantwant.arch.notification.api.dto.NotifyJobMessageRequestDto;
import com.wantwant.arch.notification.api.dto.NotifyWeComMessageRequestDto;
import com.wantwant.arch.notification.api.dto.WcImSendTextContentDto;
import com.wantwant.arch.notification.api.enumeration.WcImMessageTypeEnum;
import com.wantwant.arch.notification.api.service.NotificationApi;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletLogMapper;
import com.wantwant.sfa.backend.wallet.service.IWalletNotifyService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/10/14/下午6:41
 */
@Service
public class WalletNotifyService implements IWalletNotifyService {

    @Resource
    private NotificationApi notificationApi;

    @Resource
    private WantWalletLogMapper wantWalletLogMapper;
    @Resource
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private OrganizationMapper organizationMapper;

    @Override
    public void check() {

        List<String> orgCodes =  wantWalletLogMapper.selectExceptionOrg();
        if(CollectionUtils.isEmpty(orgCodes)){
            return;
        }

        List<String> errOrgInfo = new ArrayList<>();

        orgCodes.forEach(e -> {
            String organizationName = organizationMapper.getOrganizationName(e);

            SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(organizationMapper.getBusinessGroupById(e));

            errOrgInfo.add(sfaBusinessGroupEntity.getBusinessGroupName()+"-"+organizationName);
        });

        if(!CollectionUtils.isEmpty(errOrgInfo)){
            NotifyWeComMessageRequestDto notifyWeComMessageRequestDto = new NotifyWeComMessageRequestDto();
            notifyWeComMessageRequestDto.setRobotKey("68c4a451-aa3f-4821-b8df-c0e121d397ad");
            WcImSendTextContentDto wcImSendTextContentDto = new WcImSendTextContentDto();
            notifyWeComMessageRequestDto.setMessageType(WcImMessageTypeEnum.TEXT_MESSAGE.getType());
            wcImSendTextContentDto.setContent("旺金币账户有如下组织存在异常:" + String.join(",",errOrgInfo));
            notifyWeComMessageRequestDto.setText(wcImSendTextContentDto);

            notificationApi.notifyWeComMessage(notifyWeComMessageRequestDto);
        }

    }
}
