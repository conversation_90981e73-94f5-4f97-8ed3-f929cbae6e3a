package com.wantwant.sfa.backend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.sfa.backend.arch.entity.SfaReplaceExamine;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.enums.SpecialApprovalTypeEnum;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.interview.EliminateSpecialApprovalMapper;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.CompanyClassificationMapper;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.workReport.WorkReportTaskMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.model.SfaRuleSetCompanyModel;
import com.wantwant.sfa.backend.model.SfaRuleSetModel;
import com.wantwant.sfa.backend.model.marketAndPersonnel.CompanyClassificationPO;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO;
import com.wantwant.sfa.backend.organization.vo.OrganizationTreeVO;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.replacing.request.*;
import com.wantwant.sfa.backend.replacing.vo.*;
import com.wantwant.sfa.backend.service.IReplacingService;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReplacingService implements IReplacingService {
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Autowired
    private SfaRuleSetMapper sfaRuleSetMapper;

    @Autowired
    private SfaRuleSetCompanyMapper sfaRuleSetCompanyMapper;

    @Autowired
    private ConfigMapper configMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private ReplacingTwoMapper replacingTwoMapper;

    @Autowired
    private EmployeeSalaryMapper employeeSalaryMapper;

    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper
            ceoBusinessOrganizationPositionRelationMapper;

    @Autowired
    private CompanyClassificationMapper companyClassificationMapper;

    @Autowired
    private WorkReportTaskMapper workReportTaskMapper;

    @Autowired
    private EliminateSpecialApprovalMapper eliminateSpecialApprovalMapper;

    @Autowired
    private SfaReplaceExamineMapper sfaReplaceExamineMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;


    @Override
    public ReplacingVo selReplacingList(ReplacingRequest request) {
        log.info("start ReplacingService selReplacingList request:{}", request);
        ReplacingVo replacingVo = new ReplacingVo();
        CeoBusinessOrganizationPositionRelation organization = null;
        if (StringUtils.isNotBlank(request.getOrganizationId())) {
            organization = ceoBusinessOrganizationPositionRelationMapper.selectOne(
                    new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", request.getOrganizationId()).eq("employee_id", request.getPost()).eq("business_group", RequestUtils.getBusinessGroup()));
        } else {
            organization = ceoBusinessOrganizationPositionRelationMapper.selectOne(
                    new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("employee_id", request.getPost()).eq("business_group", RequestUtils.getBusinessGroup()));
        }
        //换字段，把字符串数字赋值检数字类型
        if (CommonUtil.StringUtils.isNotBlank(request.getPostType())) {
            request.setPost(Integer.valueOf(request.getPostType()));
        }
        if (Objects.nonNull(organization)) {
            if (organization.getPositionTypeId() == 1) {
                String areaName = organizationMapper.getOrganizationName(request.getOrganizationId());
                request.setArea(areaName);
                request.setOrganizationName(areaName);
            }
            if (organization.getPositionTypeId() == 2) {
                String companyName = organizationMapper.getOrganizationName(request.getOrganizationId());
                request.setCompany(companyName);
                request.setOrganizationName(companyName);
            }
            if (organization.getPositionTypeId() == 10) {
                List<OrganizationTreeVO> children = organizationMapper.getChildren(request.getBranch(), RequestUtils.getChannel());
                if (!CollectionUtils.isEmpty(children)) {
                    request.setBranchList(children.stream().map(OrganizationTreeVO::getTitle).collect(Collectors.toList()));
                }
            }
        }
        String yearMonth = null;
        if (StringUtils.isNotBlank(request.getMonth())) {
            yearMonth = request.getMonth();
        } else {
            LocalDateTime now = LocalDateTime.now();
            yearMonth = now.toString().substring(0, 7);
        }
        if (request.getReplacingType() == 1) {
            List<ReplacingVo> replacingVos = replacingTwoMapper.selReplacingMapper(request, yearMonth);
            if (CommonUtil.ListUtils.isNotEmpty(replacingVos)) {
                //组织名称为营业所 --区域经理人员则不查用人
                Double aDouble = replacingTwoMapper.selEmploymentByOrganizationId(request.getOrganizationName(), yearMonth);
                ArrayList<ReplacingListVo> replacingListVos = new ArrayList<>();
                BeanUtils.copyProperties(replacingVos, replacingListVos, ReplacingVo.class, ReplacingListVo.class);
                //差额处理成为负数处理成0
                replacingListVos.forEach(r -> {
                    if (r.getDifferenceAmount() < 0) {
                        r.setDifferenceAmount(0);
                    }
                });
                //copyFieldToBean(replacingVos, replacingListVos,replacingVos);
                ReplacingVo replacingVo1 = replacingVos.get(0);
                if (null == aDouble) {
                    replacingVo1.setEmploymentCostRatio(BigDecimal.ZERO);
                } else {
                    replacingVo1.setEmploymentCostRatio(new BigDecimal(aDouble));
                }
                BigDecimal reduce =
                        replacingVos.stream()
                                .map(ReplacingVo::getZwPerformance)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                replacingVo1.setPerformance(reduce);
                replacingVo1.setOnboardNum(replacingVos.size());
                //未达标人数 ( 0 未达标 1达标)
                List<ReplacingVo> notReplacedNum =
                        replacingVos.stream()
                                .filter(
                                        r ->
                                                null != r.getAchievementStandards() && r.getAchievementStandards() == 0)
                                .collect(Collectors.toList());
                //达标人数
                List<ReplacingVo> replacedNum =
                        replacingVos.stream()
                                .filter(
                                        r ->
                                                null != r.getAchievementStandards() && r.getAchievementStandards() == 1)
                                .collect(Collectors.toList());
                //未达标人数 全职
                List<ReplacingVo> fullTimeBusinessPartner =
                        notReplacedNum.stream()
                                .filter(
                                        r ->
                                                r.getPostType().equals("全职"))
                                .collect(Collectors.toList());
                //未达标人数 承揽
                List<ReplacingVo> undertakePartner =
                        notReplacedNum.stream()
                                .filter(
                                        r ->
                                                r.getPostType().equals("承揽"))
                                .collect(Collectors.toList());
                replacingVo1.setStandardNum(replacedNum.size());
                replacingVo1.setNotStandardNum(notReplacedNum.size());
                replacingVo1.setFullTimeBusinessPartner(fullTimeBusinessPartner.size());
                if (null == replacingVo1.getNotStandardNum() || replacingVo1.getNotStandardNum() != 0) {
                    replacingVo1.setFullTimeBusinessPartnerRate(new BigDecimal(fullTimeBusinessPartner.size()).divide(new BigDecimal(replacingVo1.getNotStandardNum()), BigDecimal.ROUND_HALF_UP, 2).multiply(new BigDecimal(100)));
                    replacingVo1.setUndertakePartnerRate(new BigDecimal(undertakePartner.size()).divide(new BigDecimal(replacingVo1.getNotStandardNum()), BigDecimal.ROUND_HALF_UP, 2).multiply(new BigDecimal(100)));
                } else {
                    replacingVo1.setFullTimeBusinessPartnerRate(BigDecimal.ZERO);
                    replacingVo1.setUndertakePartnerRate(BigDecimal.ZERO);
                }
                replacingVo1.setUndertakePartner(undertakePartner.size());
                if (null == replacingVo1.getOnboardNum() || replacingVo1.getOnboardNum() != 0) {
                    replacingVo1.setNotStandardRate(new BigDecimal(notReplacedNum.size()).divide(new BigDecimal(replacingVo1.getOnboardNum()), BigDecimal.ROUND_HALF_UP, 2).multiply(new BigDecimal(100)));
                    replacingVo1.setGmvAvg(replacingVo1.getPerformance().divide(new BigDecimal(replacingVo1.getOnboardNum()), BigDecimal.ROUND_HALF_UP, 2));
                } else {
                    replacingVo1.setNotStandardRate(BigDecimal.ZERO);
                    replacingVo1.setGmvAvg(BigDecimal.ZERO);
                }
                copyFieldToBean(replacingVo1, replacingVo);
                //根据排序返回数据,并且分区    大数据计算排名(保护区/正常区业绩从高到低排名，汰换区业绩从低到高排名（1～N）
                List<ReplacingListVo> protectedList = null;
                List<ReplacingListVo> normalList = null;
                List<ReplacingListVo> replacementList = null;
                protectedList = replacingListVos.stream().filter(r -> StringUtils.isNotBlank(r.getAreaBelongs()) && r.getAreaBelongs().equals("保护区")).collect(Collectors.toList());
                normalList = replacingListVos.stream().filter(r -> StringUtils.isNotBlank(r.getAreaBelongs()) && r.getAreaBelongs().equals("正常区")).collect(Collectors.toList());
                replacementList = replacingListVos.stream().filter(r -> StringUtils.isNotBlank(r.getAreaBelongs()) && r.getAreaBelongs().equals("汰换区")).collect(Collectors.toList());
                if (StringUtils.isBlank(request.getSortName())) {
                    request.setSortName("performance");
                }
                if (StringUtils.isBlank(request.getSortType())) {
                    request.setSortType("asc");
                }
                if (request.getSortName().equals("performance") && request.getSortType().equals("asc")) {
                    //按业绩正序排序，业绩由低到高。则保护区/正常区排名由大到小，汰换区由小到大
                    protectedList = protectedList.stream().sorted(Comparator.comparing(ReplacingListVo::getReplacementRanking).reversed()).collect(Collectors.toList());
                    normalList = normalList.stream().sorted(Comparator.comparing(ReplacingListVo::getReplacementRanking).reversed()).collect(Collectors.toList());
                    replacementList = replacementList.stream().sorted(Comparator.comparing(ReplacingListVo::getReplacementRanking)).collect(Collectors.toList());
                } else if (request.getSortName().equals("performance") && request.getSortType().equals("desc")) {
                    //按业绩倒序排序，业绩由高到低。则保护区/正常区排名小到大，汰换区由大到小
                    protectedList = protectedList.stream().sorted(Comparator.comparing(ReplacingListVo::getReplacementRanking)).collect(Collectors.toList());
                    normalList = normalList.stream().sorted(Comparator.comparing(ReplacingListVo::getReplacementRanking)).collect(Collectors.toList());
                    replacementList = replacementList.stream().sorted(Comparator.comparing(ReplacingListVo::getReplacementRanking).reversed()).collect(Collectors.toList());
                }
                replacingVo.setReplacingProtectedList(protectedList);
                replacingVo.setReplacingNormalList(normalList);
                replacingVo.setReplacingReplacementList(replacementList);
            }
        } else {
            List<ReplacingVo> replacingVos =
                    replacingTwoMapper.selReplacingHumanResourcesMapper(request, yearMonth);
            if (CommonUtil.ListUtils.isNotEmpty(replacingVos)) {
                ArrayList<ReplacingListVo> replacingListVos = new ArrayList<>();
                copyFieldToBean(replacingVos, replacingListVos);
                ReplacingVo replacingVo1 = replacingVos.get(0);
                List<ReplacingVo> replacedNum =
                        replacingVos.stream()
                                .filter(
                                        r -> r.getAssessmentStatus() != null && r.getAssessmentStatus().equals("已汰换"))
                                .collect(Collectors.toList());
                List<ReplacingVo> notReplacedNum =
                        replacingVos.stream()
                                .filter(
                                        r -> r.getAssessmentStatus() != null && r.getAssessmentStatus().equals("待汰换"))
                                .collect(Collectors.toList());
                replacingVo1.setReplaced(replacedNum.size());
                replacingVo1.setNotReplaced(notReplacedNum.size());
                replacingVo1.setTotalNumber(replacedNum.size() + notReplacedNum.size());
                copyFieldToBean(replacingVo1, replacingVo);
                replacingVo.setReplacingList(replacingListVos);
            }
        }
        return replacingVo;
    }

    @Override
    public ReplacingDetailVo selReplacingDateDtailsInfo(ReplacingRequest request) {
        log.info("start ReplacingService selReplacingDateDtailsInfo request:{}", request);
        if (CommonUtil.StringUtils.isNotBlank(request.getArea())) {
            request.setArea(organizationMapper.getOrganizationName(request.getArea()));
        }
        if (CommonUtil.StringUtils.isNotBlank(request.getCompany())) {
            request.setCompany(organizationMapper.getOrganizationName(request.getCompany()));
        }
//    if (CommonUtil.StringUtils.isNotBlank(request.getBranch())) {
//      List<OrganizationTreeVO> children = organizationMapper.getChildren(request.getBranch(), RequestUtils.getChannel());
//      if(!CollectionUtils.isEmpty(children)){
//
//      }
//      request.setBranchList();
//    }
        ReplacingDetailVo replacingVos = null;
        /*LocalDateTime now = LocalDateTime.now();
        String yearMonth = now.toString().substring(0, 7);*/
        String yearMonth = null;
        if (StringUtils.isNotBlank(request.getMonth())) {
            yearMonth = request.getMonth();
        } else {
            LocalDateTime now = LocalDateTime.now();
            yearMonth = now.toString().substring(0, 7);
        }
        if (request.getReplacingType() == 1) {
            replacingVos = replacingTwoMapper.selReplacingDtailsInfoMapper(request, yearMonth);
            if (Objects.nonNull(replacingVos)) {
                if (replacingVos.getDifferenceAmount() < 0) {
                    replacingVos.setDifferenceAmount(0);
                }
                if (replacingVos.getAreaBelongs().equals("汰换区")) {
                    replacingVos.setAssessmentStatus("汰换");
                } else {
                    replacingVos.setAssessmentStatus("正常");
                }
                List<EmployeeSalaryPO> employeeSalaryPO = employeeSalaryMapper.selectList(new QueryWrapper<EmployeeSalaryPO>().eq("employee_info_id", replacingVos.getEmployeeInfoId()));
                if (CommonUtil.ListUtils.isNotEmpty(employeeSalaryPO)) {
                    List<EmployeeSalaryPO> collect = employeeSalaryPO.stream().sorted(Comparator.comparing(EmployeeSalaryPO::getStartDate)).collect(Collectors.toList());
                    ArrayList<ReplacingSalaryHistoricalVo> list = new ArrayList<>();
                    BeanUtils.copyProperties(collect, list, EmployeeSalaryPO.class, ReplacingSalaryHistoricalVo.class);
                    replacingVos.setSalaryHistoricalRecord(list);
                }
            }
        } else {
            replacingVos = replacingTwoMapper.selReplacingHumanResourcesInfoMapper(request, yearMonth);
        }

        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(replacingVos.getEmployeeInfoId());
        if (Objects.nonNull(sfaEmployeeInfoModel)) {
            replacingVos.setMobile(sfaEmployeeInfoModel.getMobile());
        }
        return replacingVos;
    }

    @Override
    public ReplacingWarningVo selReplacingWarningList(ReplacingWarningRequest request) {
        log.info("start ReplacingService selReplacingList request:{}", request);
        ReplacingVo replacingVo = new ReplacingVo();
        CeoBusinessOrganizationPositionRelation organization = null;

        if (StringUtils.isNotBlank(request.getOrganizationId())) {
            List<CeoBusinessOrganizationPositionRelation> ceoBusinessOrganizationPositionRelations = ceoBusinessOrganizationPositionRelationMapper.selectList(
                    new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", request.getOrganizationId()).eq("business_group", RequestUtils.getBusinessGroup()));
            if (CommonUtil.ListUtils.isNotEmpty(ceoBusinessOrganizationPositionRelations)) {
                organization = ceoBusinessOrganizationPositionRelations.get(0);
            }
        }

        if (Objects.nonNull(organization)) {
            if (organization.getPositionTypeId() == 1) {//战区
                String areaName = organizationMapper.getOrganizationName(request.getOrganizationId());
                request.setAreaName(areaName);
            }
            if (organization.getPositionTypeId() == 12) {//大区
                String vareaName = organizationMapper.getOrganizationName(request.getOrganizationId());
                request.setVareaName(vareaName);
            }
            if (organization.getPositionTypeId() == 11) {//省区
                String provinceName = organizationMapper.getOrganizationName(request.getOrganizationId());
                request.setProvinceName(provinceName);
            }
            if (organization.getPositionTypeId() == 2) {//分公司
                String companyName = organizationMapper.getOrganizationName(request.getOrganizationId());
                request.setCompanyName(companyName);
            }
            if (organization.getPositionTypeId() == 10) {//区域经理
                String deparmentName = organizationMapper.getOrganizationName(request.getOrganizationId());
                request.setDepartmentName(deparmentName);
            }
            request.setBusinessGroup(RequestUtils.getBusinessGroup());
        } else {
            request.setBusinessGroup(RequestUtils.getBusinessGroup());
        }
        //查看汰换页面自己表头人数
        ReplacingWarningVo replacingWarningVo = replacingTwoMapper.selReplacingWarningPeopleNumMapper(request);
        //查看下级汰换列表 如果是总部查看所有下级
        List<ReplacingWarningAssembleVo> replacingWarningAssembleVos = replacingTwoMapper.selReplacingWarningMapper(request);
        ArrayList<ReplacingWarningAssembleVo> replaceCollect = new ArrayList<>();
        ArrayList<ReplacingWarningAssembleVo> normalCollect = new ArrayList<>();
        //汰换区
        List<ReplacingWarningAssembleVo> replaceList = replacingWarningAssembleVos.stream().filter(r -> r.getAreaBelongs() == 0).collect(Collectors.toList());
        replaceList.forEach(e -> {
            e.setSpecialApprovalNum(eliminateSpecialApprovalMapper.selectSpecialApproval(e.getEmployeeInfoId()));
        });
        if (CommonUtil.ListUtils.isNotEmpty(replaceList)) {
            List<ReplacingWarningAssembleVo> collect = null;
            if (request.getSortName().equals("goalAchievementRate") && request.getSortType().equals("asc")) {
                collect = replaceList.stream().sorted(Comparator.comparing(ReplacingWarningAssembleVo::getGoalAchievementRate)).collect(Collectors.toList());
            } else {
                collect = replaceList.stream().sorted(Comparator.comparing(ReplacingWarningAssembleVo::getGoalAchievementRate).reversed()).collect(Collectors.toList());
            }
            replaceCollect.addAll(collect);
        }
        //正常区
        List<ReplacingWarningAssembleVo> normalList = replacingWarningAssembleVos.stream().filter(r -> r.getAreaBelongs() != 0).collect(Collectors.toList());
        normalList.forEach(d -> {
            d.setSpecialApprovalNum(eliminateSpecialApprovalMapper.selectSpecialApproval(d.getEmployeeInfoId()));
        });
        if (CommonUtil.ListUtils.isNotEmpty(normalList)) {
            List<ReplacingWarningAssembleVo> collect = null;
            if (request.getSortName().equals("goalAchievementRate") && request.getSortType().equals("asc")) {
                collect = normalList.stream().sorted(Comparator.comparing(ReplacingWarningAssembleVo::getGoalAchievementRate)).collect(Collectors.toList());
            } else {
                collect = normalList.stream().sorted(Comparator.comparing(ReplacingWarningAssembleVo::getGoalAchievementRate).reversed()).collect(Collectors.toList());
            }
            normalCollect.addAll(collect);
        }
        replacingWarningVo.setReplacingReplacementList(replaceCollect);
        replacingWarningVo.setReplacingNormalList(normalCollect);
        return replacingWarningVo;
    }

    @Override
    public ReplacingWarningDetailVo selReplacingDateWarningDtailsInfo(ReplacingWarningRequest request) {
        log.info("start ReplacingService selReplacingDateWarningDtailsInfo request:{}", request);
        ReplacingWarningDetailVo replacingWarningDetailVo = replacingTwoMapper.selReplacingWarningDtailsInfoMapper(request);
        //根据参考指标类型判断时间
        ReplacingTimeVo time = getTime(request);
        String startTime = time.getStartTime();
        String endTime = time.getEndTime();
        List<String> monthList = getTimeList(request);

        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(request.getEmployeeInfoId());
        if (Objects.isNull(sfaEmployeeInfoModel)) {
            throw new ApplicationException("人员信息错误");
        }
        // 查询兼岗表的入岗信息
        SfaPositionRelationEntity sfaPositionRelation = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId())
                .eq(SfaPositionRelationEntity::getPositionId, replacingWarningDetailVo.getPositionId())
                .eq(SfaPositionRelationEntity::getPositionTypeId, replacingWarningDetailVo.getPositionType())
                .eq(SfaPositionRelationEntity::getStatus, 1)
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                .last("limit 1")
        );
        if (Objects.nonNull(sfaPositionRelation)) {
            replacingWarningDetailVo.setEntryPositionDate(sfaPositionRelation.getStartValidDate().toLocalDate());
        }
        //获取头像
        String picUrl = sfaEmployeeInfoMapper.getPicUrl(Integer.valueOf(request.getEmployeeInfoId()));
        if (Objects.nonNull(replacingWarningDetailVo)) {
            replacingWarningDetailVo.setPicUrl(picUrl);
            replacingWarningDetailVo.setMonthlyFillingRate(
                    workReportTaskMapper.getOrganizationMonthlyRateNew(replacingWarningDetailVo.getEmpId(), replacingWarningDetailVo.getPositionId(), monthList));
            replacingWarningDetailVo.setWeeklyFillingRate(
                    workReportTaskMapper.getOrganizationWeeklyRateNew(replacingWarningDetailVo.getEmpId(), replacingWarningDetailVo.getPositionId(), startTime, endTime));
            replacingWarningDetailVo.setWeeklyStatementList(
                    workReportTaskMapper.getWeeklyDetailNew(replacingWarningDetailVo.getEmpId(), replacingWarningDetailVo.getPositionId(), startTime, endTime));
            List<ReplacingStatementDetailVo> monthlyDetail = workReportTaskMapper.getMonthlyDetailNew(replacingWarningDetailVo.getEmpId(), replacingWarningDetailVo.getPositionId(), monthList);
            monthlyDetail.forEach(m -> {
                LocalDate parse = LocalDate.parse(m.getStatementTitle() + "-05");
                //得到复盘月份5号
                if (!m.getStatementTitle().equals("2024-01") && StringUtils.isNotBlank(m.getSendTime())) {
                    //如果发送时间超过复盘月份5号，就是逾期提交
                    LocalDate sendTime = LocalDate.parse(m.getSendTime().substring(0, 10));
                    if (sendTime.isAfter(parse)) {
                        m.setStatus("逾期提交");
                    }
                }
                m.setStatementTitle(m.getStatementTitle().replace("-", "年"));
                if (m.getStatementTitle().contains("01") || m.getStatementTitle().contains("04")
                        || m.getStatementTitle().contains("07") || m.getStatementTitle().contains("10")) {
                    m.setStatementTitle(m.getStatementTitle() + "月 季报");
                } else {
                    m.setStatementTitle(m.getStatementTitle() + "月 月报");
                }
            });
            replacingWarningDetailVo.setMontthStatementList(monthlyDetail);
            //更新状态 如果是汰换状态，没有审核，状态更新为汰换审核中 。审核过后，汰换中/特批中
            if (replacingWarningDetailVo.getAreaBelongs().equals("汰换")) {
                ReplacingWarningDetailVo replaceExamine = sfaReplaceExamineMapper.getReplaceExamine(request);
                if (Objects.nonNull(replaceExamine)) {
                    replacingWarningDetailVo.setExamineIdea(replaceExamine.getExamineIdea());
                    replacingWarningDetailVo.setExamineType(replaceExamine.getExamineType());
                    replacingWarningDetailVo.setExaminePersonnel(replaceExamine.getExaminePersonnel());
                    replacingWarningDetailVo.setExamineTime(replaceExamine.getExamineTime());
                    replacingWarningDetailVo.setAreaBelongs(replaceExamine.getExamineType());

                } else {
                    replacingWarningDetailVo.setAreaBelongs("汰换审核中");
                }
            }

            Integer numberCheck = Optional.ofNullable(replacingTwoMapper.selectNumberCheck(sfaEmployeeInfoModel.getMemberKey(),RequestUtils.getBusinessGroup(),request.getMonth())).orElse(0);
            replacingWarningDetailVo.setTotalNumberChecks(String.valueOf(numberCheck));

            // 特批次数
            int specialApprovalCount = eliminateSpecialApprovalMapper.selectSpecialApproval(request.getEmployeeInfoId());

            replacingWarningDetailVo.setGiveSpecialApprovalFrequency(String.valueOf(specialApprovalCount));
            // 特批明细列表
            replacingWarningDetailVo.setSpecialApprovalList(setSpecialApprovalList(Integer.valueOf(request.getEmployeeInfoId())));

            if (request.getEmployee().equals("00272473")) {
                replacingWarningDetailVo.setIsExamine(1);
            }
            //传参组织是战区或总督导 只能是总部审核
            if ((replacingWarningDetailVo.getPositionType() == 1 || replacingWarningDetailVo.getPositionType() == 12)) {
                if (request.getEmployee().equals("00272473")) {
                    replacingWarningDetailVo.setIsExamine(1);
                }
            } else {
                //获取当前组织的所有上级组织 判断登录人工号是不是这个组织的直属上级
                List<String> lastOrganizationId = ceoBusinessOrganizationPositionRelationMapper.getLastOrganizationId(request.getOrganizationId());
                //获取登录人工号的组织id 会兼岗
                List<CeoBusinessOrganizationPositionRelation> ceoBusinessOrganizationPositionRelations = ceoBusinessOrganizationPositionRelationMapper.selectByEmployeeIdByLine(request.getEmployee(), RequestUtils.getBusinessGroup(), RequestUtils.getLoginInfo().getPositionTypeId());
                ceoBusinessOrganizationPositionRelations.forEach(c -> {
                    List<String> collect1 = lastOrganizationId.stream().filter(o -> o.equals(c.getOrganizationId())).collect(Collectors.toList());
                    if (CommonUtil.ListUtils.isNotEmpty(collect1)) {
                        replacingWarningDetailVo.setIsExamine(1);
                    }
                });
            }

        }
        return replacingWarningDetailVo;
    }

    private List<ReplacingWarningSpecialApprovalVo> setSpecialApprovalList(Integer employeeInfoId) {
        List<ReplacingWarningSpecialApprovalVo> specialApprovalVos = eliminateSpecialApprovalMapper.selectSpecialApprovalList(employeeInfoId);
        if (CollectionUtil.isNotEmpty(specialApprovalVos)) {
            specialApprovalVos.forEach(s -> {
                s.setAssessMonthDisplay(formatAssessMonth(s.getAssessMonth()));
                // 类型(1.普通特批 2.特案特批)
                s.setTypeDisplay(SpecialApprovalTypeEnum.getDesc(s.getType()));
            });
        }

        return specialApprovalVos;
    }

    private String formatAssessMonth(String assessMonth) {
        if (assessMonth == null || assessMonth.trim().isEmpty()) {
            return "";
        }
        int year;
        int month;
        try {
            String[] parts = assessMonth.split("-");
            if (parts.length < 2) {
                return assessMonth;
            }
            year = Integer.parseInt(parts[0]);
            month = Integer.parseInt(parts[1]);
            if (month < 1 || month > 12) {
                return assessMonth;
            }
        } catch (Exception e) {
            return assessMonth;
        }

        // 财年：4-12 为当年财年，1-3 为上一年财年
        int fiscalYear = (month >= 4) ? year : (year - 1);
        int fy2 = ((fiscalYear % 100) + 100) % 100; // 两位年，确保非负

        // 季度考核月：6、9、12、3
        Integer quarter = null;
        if (month == 6) quarter = 1;
        else if (month == 9) quarter = 2;
        else if (month == 12) quarter = 3;
        else if (month == 3) quarter = 4;

        if (quarter != null) {
            // 例：25年1财季-7月
            return String.format("%02d年%d财季-%d月", fy2, quarter, month);
        } else {
            // 例：25年5月
            return String.format("%02d年%d月", fy2, month);
        }
    }


    /*通过反射 把原对象放入到目标对象中*/
    public static void copyFieldToBean(Object srcObj, Object destObj) {
        Map<String, Object> srcMap = new HashMap<String, Object>();
        Field[] srcFields = srcObj.getClass().getDeclaredFields();
        for (Field srcField : srcFields) {
            try {
                srcField.setAccessible(true);
                srcMap.put(srcField.getName(), srcField.get(srcObj)); // 获取属性值
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        Field[] destFields = destObj.getClass().getDeclaredFields();
        for (Field destField : destFields) {
            destField.setAccessible(true);
            if (srcMap.get(destField.getName()) == null) {
                continue;
            }
            try {
                destField.set(destObj, srcMap.get(destField.getName())); // 给属性赋值
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    //获取周报的开始与结束日期
    public ReplacingTimeVo getTime(ReplacingWarningRequest request) {
        ReplacingTimeVo replacingTimeVo = new ReplacingTimeVo();
        LocalDate startTime = null;
        LocalDate endTime = null;
        String month = request.getMonth() + "-01";
        //判断传参月份是 第几季度，上半年还是半年
        if (request.getExamineType() == 1) {//月度
            LocalDate parse = LocalDate.parse(month);
            startTime = parse.withDayOfMonth(1);
            endTime = parse.withDayOfMonth(parse.lengthOfMonth());
        } else if (request.getExamineType() == 2) {//季度
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, -1);
            Date date = null;
            try {
                date = df.parse(month);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            //获取本季度第一天
            cal.setTime(date);
            cal.set(Calendar.MONTH, (((int) cal.get(Calendar.MONTH)) / 3) * 3);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            startTime = LocalDate.parse(df.format(cal.getTime()));
            //获取本季度的最后一天
            cal.set(Calendar.MONTH, (((int) cal.get(Calendar.MONTH)) / 3) * 3 + 2);
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            endTime = LocalDate.parse(df.format(cal.getTime()));
        } else {
            if (request.getMonth().contains("-04") || request.getMonth().contains("-05") || request.getMonth().contains("-06") ||
                    request.getMonth().contains("-07") || request.getMonth().contains("-08") || request.getMonth().contains("-09")) {
                //财年上半年的开始日期，与结束日期  4月到9月
                String start = new StringBuffer(month).replace(5, 7, "04").toString();
                String end = new StringBuffer(month).replace(5, 7, "09").toString();
                LocalDate startParse = LocalDate.parse(start);
                LocalDate endParse = LocalDate.parse(end);
                startTime = startParse.withDayOfMonth(1);
                endTime = endParse.withDayOfMonth(endParse.lengthOfMonth());
            } else {//下半年的开始日期，与结束日期 10月到 下一年的 3月 2024-10 到 2025-03
                ArrayList<String> timeList = new ArrayList<>();
                //得到下一年 去年
                Integer lastyear = Integer.valueOf(request.getMonth().substring(0, 4)) - 1;
                //得到下一年 明年
                Integer nextYear = Integer.valueOf(request.getMonth().substring(0, 4)) + 1;
                if (request.getMonth().contains("-10") || request.getMonth().contains("-11") || request.getMonth().contains("-12")) {
                    timeList.add(request.getMonth().substring(0, 5) + "10-01");
                    timeList.add(nextYear + "-03-01");
                }
                if (request.getMonth().contains("-01") || request.getMonth().contains("-02") || request.getMonth().contains("-03")) {
                    timeList.add(lastyear + "-10-01");
                    timeList.add(request.getMonth().substring(0, 5) + "03-01");
                }
                String start = timeList.get(0);
                String end = timeList.get(1);
                LocalDate startParse = LocalDate.parse(start);
                LocalDate endParse = LocalDate.parse(end);
                startTime = startParse.withDayOfMonth(1);
                endTime = endParse.withDayOfMonth(endParse.lengthOfMonth());
            }
        }
        replacingTimeVo.setStartTime(startTime.toString());
        replacingTimeVo.setEndTime(endTime.toString());
        return replacingTimeVo;
    }

    //获取月报的日期集合
    List<String> getTimeList(ReplacingWarningRequest request) {
        ArrayList<String> timeList = new ArrayList<>();
        if (request.getExamineType() == 1) {
            timeList.add(request.getMonth());
        } else if (request.getExamineType() == 2) {
            if (request.getMonth().contains("-01") || request.getMonth().contains("-02") || request.getMonth().contains("-03")) {
                timeList.add(request.getMonth().substring(0, 5) + "01");
                timeList.add(request.getMonth().substring(0, 5) + "02");
                timeList.add(request.getMonth().substring(0, 5) + "03");
            } else if (request.getMonth().contains("-04") || request.getMonth().contains("-05") || request.getMonth().contains("-06")) {
                timeList.add(request.getMonth().substring(0, 5) + "04");
                timeList.add(request.getMonth().substring(0, 5) + "05");
                timeList.add(request.getMonth().substring(0, 5) + "06");
            } else if (request.getMonth().contains("-07") || request.getMonth().contains("-07") || request.getMonth().contains("-09")) {
                timeList.add(request.getMonth().substring(0, 5) + "07");
                timeList.add(request.getMonth().substring(0, 5) + "08");
                timeList.add(request.getMonth().substring(0, 5) + "09");
            } else {
                timeList.add(request.getMonth().substring(0, 5) + "10");
                timeList.add(request.getMonth().substring(0, 5) + "11");
                timeList.add(request.getMonth().substring(0, 5) + "12");
            }
        } else {
            if (request.getMonth().contains("-04") || request.getMonth().contains("-05") || request.getMonth().contains("-06") ||
                    request.getMonth().contains("-07") || request.getMonth().contains("-08") || request.getMonth().contains("-09")) {
                timeList.add(request.getMonth().substring(0, 5) + "04");
                timeList.add(request.getMonth().substring(0, 5) + "05");
                timeList.add(request.getMonth().substring(0, 5) + "06");
                timeList.add(request.getMonth().substring(0, 5) + "07");
                timeList.add(request.getMonth().substring(0, 5) + "08");
                timeList.add(request.getMonth().substring(0, 5) + "09");
            } else {
                //得到下一年 去年
                Integer lastyear = Integer.valueOf(request.getMonth().substring(0, 4)) - 1;
                //得到下一年 明年
                Integer nextYear = Integer.valueOf(request.getMonth().substring(0, 4)) + 1;
                if (request.getMonth().contains("-10") || request.getMonth().contains("-11") || request.getMonth().contains("-12")) {
                    timeList.add(request.getMonth().substring(0, 5) + "10");
                    timeList.add(request.getMonth().substring(0, 5) + "11");
                    timeList.add(request.getMonth().substring(0, 5) + "12");
                    timeList.add(nextYear + "-01");
                    timeList.add(nextYear + "-02");
                    timeList.add(nextYear + "-03");
                }
                if (request.getMonth().contains("-01") || request.getMonth().contains("-02") || request.getMonth().contains("-03")) {
                    timeList.add(lastyear + "-10");
                    timeList.add(lastyear + "-11");
                    timeList.add(lastyear + "-12");
                    timeList.add(request.getMonth().substring(0, 5) + "01");
                    timeList.add(request.getMonth().substring(0, 5) + "02");
                    timeList.add(request.getMonth().substring(0, 5) + "03");
                }

            }
        }
        return timeList;
    }

    /**
     * 获取大数据汰换信息
     *
     * @param employeeInfoIds
     * @param yyyyMM
     * @return: java.util.Map<java.lang.Long, com.wantwant.sfa.backend.replacing.vo.EmpAssessmentVO>
     * @date: 2021-11-24 11:00
     */
    @Override
    public Map<Long, EmpAssessmentVO> getEmpAssessment(List<Long> employeeInfoIds, String yyyyMM) {
        return replacingTwoMapper.getEmpAssessment(employeeInfoIds, yyyyMM);
    }

    @Override
    @Transactional
    public Integer replacingDateRuleSetUp(ReplacingRuleRequest request) {
        log.info("start ReplacingService replacingDateRuleSetUp request:{}", request);
        publicbigSetValue(request); // 判断最大设置值
        List<SfaRuleSetModel> sfaRuleSetList =
                sfaRuleSetMapper.selectList(new QueryWrapper<SfaRuleSetModel>().eq("delete_flag", 0));
        int insert = 0;
        if (CommonUtil.ListUtils.isNotEmpty(sfaRuleSetList)) {
            List<ReplacingOrganizationRequest> companyList = request.getCompanyList();
            publicRuleCompany(companyList, request, 0);
            publicRuleSetList(companyList, request, 0);
        } else {
            SfaRuleSetModel sfaRuleSetModel = new SfaRuleSetModel();
            BeanUtils.copyProperties(request, sfaRuleSetModel);
            sfaRuleSetModel.setCreateId(request.getProcessUserId());
            sfaRuleSetModel.setCreateTime(LocalDateTime.now());
            sfaRuleSetModel.setCreateOrganiztaionId(request.getOrganiztaionId());
            insert = sfaRuleSetMapper.insert(sfaRuleSetModel);
            if (insert > 0) {
                List<ReplacingOrganizationRequest> replacingOrganizationDate = request.getCompanyList();
                if (CommonUtil.ListUtils.isNotEmpty(replacingOrganizationDate)) {
                    for (ReplacingOrganizationRequest organization : replacingOrganizationDate) {
                        SfaRuleSetCompanyModel sfaRuleSetCompanyModel = new SfaRuleSetCompanyModel();
                        sfaRuleSetCompanyModel.setRuleSetId(sfaRuleSetModel.getId());
                        sfaRuleSetCompanyModel.setCompanyId(organization.getCompanyId());
                        sfaRuleSetCompanyModel.setCompanyName(organization.getCompanyName());
                        sfaRuleSetCompanyModel.setCreateId(request.getProcessUserId());
                        sfaRuleSetCompanyModel.setCreateTime(LocalDateTime.now());
                        insert = sfaRuleSetCompanyMapper.insert(sfaRuleSetCompanyModel);
                    }
                }
            } else {
                throw new ApplicationException("设置失败");
            }
        }
        return insert;
    }

    @Override
    public List<ReplacingRuleDateVo> replacingDateRuleSetUpList(ReplacingRuleDateRequest request) {
        log.info("start ReplacingService replacingDateRuleSetUpList request:{}", request);
        LocalDateTime time = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth());
        // String format = new SimpleDateFormat("yyyy-MM").format(LocalDateTime.now());
        String format = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String organiztaionId = request.getOrganiztaionId();
        String organizationType = organizationMapper.getOrganizationType(request.getOrganiztaionId());

        CompanyClassificationPO companyClassificationPO =
                companyClassificationMapper.selectOne(
                        new QueryWrapper<CompanyClassificationPO>()
                                .eq("organization_id", organiztaionId)
                                .like("effective_date", format));

        ArrayList<ReplacingRuleDateVo> replacingRuleDateVos = new ArrayList<>();
        if (null != companyClassificationPO
                && null != companyClassificationPO.getClassification()) { // 有标签
            /*A类分公司和总总可以设置规则，所以根据创建人组织id查询,BC类分公司，根据分公司规则表查看*/
            /*目前A类分公司不能给自己设置规则，只能查看*/
      /*  if (companyClassificationPO.getClassification().equals("A")) { // 根据组织表的创建人组织查询
        replacingRuleDateVos = getRultList(request);//A类分公司根据 创建人组织查询
      } else { // 根据规则分公司表的组织查询*/
            List<SfaRuleSetCompanyModel> sfaRuleSetCompanyModels =
                    sfaRuleSetCompanyMapper.selRuleByCompanyId(request);
            BeanUtils.copyProperties(
                    sfaRuleSetCompanyModels,
                    replacingRuleDateVos,
                    SfaRuleSetCompanyModel.class,
                    ReplacingRuleDateVo.class);
            /* }*/
        } else { // 总部情况 根据创建人组织查询
            if (CommonUtil.StringUtils.isNotBlank(organizationType)) {
                replacingRuleDateVos = getRultList(request, organizationType);
            }
        }
        if (CommonUtil.ListUtils.isNotEmpty(replacingRuleDateVos)) {
            for (ReplacingRuleDateVo replacing : replacingRuleDateVos) {
                if (replacing.getRuleRangeType() == 2) {
                    replacing.setRuleIndicatorsType(null);
                    replacing.setRuleIndicatorsOperationType(null);
                    replacing.setRuleIndicatorsThreshold(null);
                }
            }
        }
        return replacingRuleDateVos;
    }

    /**
     * 根据创建人ceo_business_organization组织查询
     *
     * @param request
     * @return
     */
    ArrayList<ReplacingRuleDateVo> getRultList(
            ReplacingRuleDateRequest request, String organizationType) {
        ArrayList<ReplacingRuleDateVo> replacingRuleDateVos = new ArrayList<>();
        if (CommonUtil.StringUtils.isNotBlank(organizationType)
                && organizationType.equals("area")) { // 目前只有总部看规则，大区也看总部的规则
            request.setOrganiztaionId("ZB_Z");
        }
        List<SfaRuleSetModel> sfaRuleSetList =
                sfaRuleSetMapper.selectList(
                        new QueryWrapper<SfaRuleSetModel>()
                                .eq("create_organiztaion_id", request.getOrganiztaionId())
                                .eq("rule_type", request.getRuleType())
                                .eq("delete_flag", 0));
        if (CommonUtil.ListUtils.isNotEmpty(sfaRuleSetList)) {
            for (SfaRuleSetModel ruleSet : sfaRuleSetList) {
                ReplacingRuleDateVo replacingRuleDateVo = new ReplacingRuleDateVo();
                replacingRuleDateVo.setRuleSetId(ruleSet.getId());
                BeanUtils.copyProperties(ruleSet, replacingRuleDateVo);
                List<SfaRuleSetCompanyModel> ruleSetCompanys =
                        sfaRuleSetCompanyMapper.selectList(
                                new QueryWrapper<SfaRuleSetCompanyModel>()
                                        .eq("rule_set_id", ruleSet.getId())
                                        .eq("delete_flag", 0));
                ArrayList<ReplacingOrganizationVo> replacingOrganizationVos = new ArrayList<>();
                BeanUtils.copyProperties(
                        ruleSetCompanys,
                        replacingOrganizationVos,
                        SfaRuleSetCompanyModel.class,
                        ReplacingOrganizationVo.class);
                replacingRuleDateVo.setCompanyList(replacingOrganizationVos);
                replacingRuleDateVos.add(replacingRuleDateVo);
            }
        }
        return replacingRuleDateVos;
    }

    /**
     * 规则添加
     *
     * @param request
     * @param company
     * @return
     */
    public Integer publicInster(ReplacingRuleRequest request, ReplacingOrganizationRequest company) {
        // 先判断规则主表有没有数据
        List<SfaRuleSetModel> sfaRuleSetOne = null;
        if (request.getRuleRangeType() == 1) {
            sfaRuleSetOne =
                    sfaRuleSetMapper.selectList(
                            new QueryWrapper<SfaRuleSetModel>()
                                    .eq("rule_type", request.getRuleType())
                                    .eq("rule_range_type", request.getRuleRangeType())
                                    .eq("rule_range_threshold", request.getRuleRangeThreshold())
                                    .eq("rule_indicators_threshold", request.getRuleIndicatorsThreshold())
                                    .eq("perform_state", request.getPerformState())
                                    .eq("delete_flag", 0));
        } else {
            sfaRuleSetOne =
                    sfaRuleSetMapper.selectList(
                            new QueryWrapper<SfaRuleSetModel>()
                                    .eq("rule_type", request.getRuleType())
                                    .eq("rule_range_type", request.getRuleRangeType())
                                    .eq("rule_range_threshold", request.getRuleRangeThreshold())
                                    .eq("perform_state", request.getPerformState())
                                    .eq("delete_flag", 0));
        }

        SfaRuleSetModel ruleSetModel = null;
        if (CommonUtil.ListUtils.isNotEmpty(sfaRuleSetOne)) {
            if (sfaRuleSetOne.size() > 1) {
                throw new ApplicationException("数据有误");
            } else {
                ruleSetModel = sfaRuleSetOne.get(0);
            }
        }
        int insert = 0;
        Integer ruleSetId = 0;
        if (CommonUtil.ListUtils.isEmpty(sfaRuleSetOne)) {
            SfaRuleSetModel sfaRuleSetMod = new SfaRuleSetModel();
            BeanUtils.copyProperties(request, sfaRuleSetMod);
            sfaRuleSetMod.setCreateId(request.getProcessUserId());
            sfaRuleSetMod.setCreateTime(LocalDateTime.now());
            sfaRuleSetMod.setCreateOrganiztaionId(request.getOrganiztaionId());
            insert = sfaRuleSetMapper.insert(sfaRuleSetMod);
            if (insert > 0) {
                ruleSetId = sfaRuleSetMod.getId();
            } else {
                throw new ApplicationException("添加规则操作失败");
            }
        } else {
            ruleSetId = ruleSetModel.getId();
        }
        if (null != company) {
            SfaRuleSetCompanyModel sfaRuleSetCompanyModel = new SfaRuleSetCompanyModel();
            sfaRuleSetCompanyModel.setRuleSetId(ruleSetId);
            sfaRuleSetCompanyModel.setCompanyId(company.getCompanyId());
            sfaRuleSetCompanyModel.setCompanyName(company.getCompanyName());
            sfaRuleSetCompanyModel.setCreateId(request.getProcessUserId());
            sfaRuleSetCompanyModel.setCreateTime(LocalDateTime.now());
            ruleSetId = sfaRuleSetCompanyMapper.insert(sfaRuleSetCompanyModel);
        }
        return insert;
    }

    /**
     * 规则修改
     *
     * @param request
     * @param company
     * @return
     */
    public Integer publicUpdate(ReplacingRuleRequest request, ReplacingOrganizationRequest company) {
        int update = 0;
        if (null != company) {
            SfaRuleSetCompanyModel sfaRuleSetCompanyModel =
                    sfaRuleSetCompanyMapper.selectOne(
                            new QueryWrapper<SfaRuleSetCompanyModel>()
                                    .eq("company_id", company.getCompanyId())
                                    .eq("rule_set_id", request.getRuleSetId())
                                    .eq("delete_flag", 0));

            if (null == sfaRuleSetCompanyModel) {
                SfaRuleSetCompanyModel sfaRuleSetCompany = new SfaRuleSetCompanyModel();
                sfaRuleSetCompany.setRuleSetId(request.getRuleSetId());
                sfaRuleSetCompany.setCompanyId(company.getCompanyId());
                sfaRuleSetCompany.setCompanyName(company.getCompanyName());
                sfaRuleSetCompany.setCreateId(request.getProcessUserId());
                sfaRuleSetCompany.setCreateTime(LocalDateTime.now());
                update = sfaRuleSetCompanyMapper.insert(sfaRuleSetCompany);
            }
        }
        SfaRuleSetModel ruleSetModel = new SfaRuleSetModel();
        BeanUtils.copyProperties(request, ruleSetModel);
        ruleSetModel.setId(request.getRuleSetId());
        ruleSetModel.setUpdateId(request.getOrganiztaionId());
        ruleSetModel.setUpdateTime(LocalDateTime.now());
        update = sfaRuleSetMapper.updateById(ruleSetModel);
        return update;
    }

    /**
     * 判断分公司规则条件是否相同（目前只判断考核）
     */
    public void publicRuleCompany(
            List<ReplacingOrganizationRequest> companyList,
            ReplacingRuleRequest request,
            Integer IsInster) {
        // IsUpdate 是否做添加处理 （0.是、1.否）
        if (CommonUtil.ListUtils.isNotEmpty(companyList)) {
            for (ReplacingOrganizationRequest company : companyList) {
                List<ReplacingRuleDateVo> replacingRuleVos =
                        sfaRuleSetMapper.selectRuleSet(company.getCompanyId());
                if (CommonUtil.ListUtils.isNotEmpty(replacingRuleVos)) { // 找出分公司规则表中的分公司规则数据
                    if (request.getRuleRangeType() == 2 && IsInster == 0) {
                        List<ReplacingRuleDateVo> collect1 =
                                replacingRuleVos.stream()
                                        .filter(r -> r.getRuleRangeType() == request.getRuleRangeType())
                                        .filter(r -> r.getRuleType() == request.getRuleType())
                                        .collect(Collectors.toList());
                        if (CommonUtil.ListUtils.isNotEmpty(collect1)) {
                            throw new ApplicationException(company.getCompanyName() + "分公司已经存在连续异常规则");
                        }
                    } else {
                        if (IsInster == 0) {
                            List<ReplacingRuleDateVo> collect1 =
                                    replacingRuleVos.stream()
                                            .filter(
                                                    r ->
                                                            r.getRuleRangeType() == request.getRuleRangeType()
                                                                    && r.getRuleRangeThreshold() == request.getRuleRangeThreshold()
                                                                    && r.getPerformState() == request.getPerformState())
                                            .collect(Collectors.toList());
                            if (CommonUtil.ListUtils.isNotEmpty(collect1)) {
                                throw new ApplicationException(company.getCompanyName() + "分公司已经存在这条规则");
                            }
                        } else {
                            List<ReplacingRuleDateVo> collect1 =
                                    replacingRuleVos.stream()
                                            .filter(
                                                    r ->
                                                            r.getRuleRangeType() == request.getRuleRangeType()
                                                                    && r.getRuleRangeThreshold() == request.getRuleRangeThreshold()
                                                                    && r.getPerformState() == request.getPerformState()
                                                                    && r.getRuleSetId() != request.getRuleSetId())
                                            .collect(Collectors.toList());
                            if (CommonUtil.ListUtils.isNotEmpty(collect1)) {
                                throw new ApplicationException(company.getCompanyName() + "分公司已经存在这条规则");
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 判断规则条件是否符合条件 考核的在岗月份 （设置月份大于已有月份时，业绩必须大于已设置月份的业绩，设置月份小于最小月份时 ，业绩需小于最小月份的业绩） 扣款的在岗天数
     * （设置天数大于已有天数时，扣款必须大于已设置天数的捐款，设置天数小于已有天数时，扣款必须小于已设置天数的捐款）
     *
     * @param companyList
     * @param request
     * @param type
     * @return
     */
    public Integer publicRuleSetList(
            List<ReplacingOrganizationRequest> companyList, ReplacingRuleRequest request, Integer type) {
        // type 0 添加 1 修改
        Integer operation = 0;
        Integer num = request.getRuleRangeThreshold();
        if (request.getRuleType() == 0) { // 扣款
            // 规则的分公司原有值
            if (null != request.getRuleSetId()) {
                List<SfaRuleSetCompanyModel> ruleSetCompanyList =
                        sfaRuleSetCompanyMapper.selectList(
                                new QueryWrapper<SfaRuleSetCompanyModel>()
                                        .eq("rule_set_id", request.getRuleSetId())
                                        .eq("delete_flag", 0));
                List<ReplacingOrganizationRequest> companyList1 = request.getCompanyList();
                /*for (SfaRuleSetCompanyModel comapny : ruleSetCompanyList) {*/
                for (ReplacingOrganizationRequest com : companyList1) {
                    /*if (comapny.getCompanyId().equals(com.getCompanyId())) {*/
                    List<SfaRuleSetCompanyModel> collect =
                            ruleSetCompanyList.stream()
                                    .filter(r -> r.getCompanyId().equals(com.getCompanyId()))
                                    .collect(Collectors.toList());
                    if (CommonUtil.ListUtils.isNotEmpty(collect)) {
                        ruleSetCompanyList.remove(collect.get(0));
                    }
                }

                if (CommonUtil.ListUtils.isNotEmpty(ruleSetCompanyList)) {
                    for (SfaRuleSetCompanyModel comapny : ruleSetCompanyList) {
                        comapny.setDeleteFlag(1);
                        sfaRuleSetCompanyMapper.update(
                                comapny,
                                new QueryWrapper<SfaRuleSetCompanyModel>()
                                        .eq("rule_set_id", request.getRuleSetId())
                                        .eq("company_id", comapny.getCompanyId())
                                        .eq("delete_flag", 0));
                    }
                }
            }
            for (ReplacingOrganizationRequest company : companyList) {
                List<ReplacingRuleDateVo> replacingRuleVos =
                        sfaRuleSetMapper.selectRuleSet(company.getCompanyId());
                if (CommonUtil.ListUtils.isNotEmpty(replacingRuleVos)) { // 找出分公司规则表中的分公司规则数据
                    // 找出分公司与传参相同的执行状态
                    List<ReplacingRuleDateVo> performStates =
                            replacingRuleVos.stream()
                                    .filter(r -> r.getPerformState().equals(request.getPerformState()))
                                    .collect(Collectors.toList());
                    if (CommonUtil.ListUtils.isNotEmpty(performStates)) { // 分公司的这个状态为空，则添加，不为空，则判断规则
                        List<ReplacingRuleDateVo> collect =
                                performStates.stream()
                                        .sorted(Comparator.comparing(ReplacingRuleDateVo::getRuleRangeThreshold))
                                        .collect(Collectors.toList()); // 在岗月份从大到小排序
                        if (CommonUtil.ListUtils.isNotEmpty(collect)) {
                            if (request.getRuleRangeType() != 2) {
                                ReplacingRuleDateVo replacingRuleDateMin = recursionSubtract(num, collect);
                                ReplacingRuleDateVo replacingRuleDateMax =
                                        recursionAdd(num, request.getRuleType(), collect);
                                if (null != replacingRuleDateMax
                                        && null != replacingRuleDateMin
                                        && replacingRuleDateMax.getRuleRangeType() != 2
                                        && replacingRuleDateMin.getRuleRangeType() != 2) { // 数值在区间范围 ()
                                    if (replacingRuleDateMin.getRuleRangeThreshold()
                                            == request.getOnJobMonthOriginal()
                                            || replacingRuleDateMax.getRuleRangeThreshold()
                                            == request.getOnJobMonthOriginal()) {
                                        // 如果原有的值等于 区间的前包含数，则只判断后面的，前端不判断
                                        if (replacingRuleDateMax.getRuleRangeThreshold()
                                                == request.getOnJobMonthOriginal()) {
                                            publicMax(request, replacingRuleDateMax, company);
                                        } else {
                                            publicMin(request, replacingRuleDateMin, company);
                                        }
                                    } else {
                                        if (((request.getRuleRangeThreshold()
                                                < replacingRuleDateMax.getRuleRangeThreshold()
                                                && request.getRuleRangeThreshold()
                                                > replacingRuleDateMin.getRuleRangeThreshold()))
                                                && ((request.getRuleIndicatorsThreshold().doubleValue()
                                                < replacingRuleDateMin.getRuleIndicatorsThreshold().doubleValue())
                                                || (request.getRuleIndicatorsThreshold().doubleValue()
                                                > replacingRuleDateMax.getRuleIndicatorsThreshold().doubleValue())
                                                || (request.getRuleIndicatorsThreshold().doubleValue()
                                                == replacingRuleDateMax
                                                .getRuleIndicatorsThreshold()
                                                .doubleValue()
                                                || request.getRuleIndicatorsThreshold().doubleValue()
                                                == replacingRuleDateMin
                                                .getRuleIndicatorsThreshold()
                                                .doubleValue()))) {
                                            throw new ApplicationException(
                                                    company.getCompanyName()
                                                            + "分公司在岗月份在"
                                                            + replacingRuleDateMax.getRuleRangeThreshold()
                                                            + ","
                                                            + replacingRuleDateMin.getRuleRangeThreshold()
                                                            + "月区间,设置的业绩要在区间内，不要等于");
                                        }
                                    }
                                } else {
                                    if (null != replacingRuleDateMax
                                            && replacingRuleDateMax.getRuleRangeThreshold()
                                            != request.getOnJobMonthOriginal()
                                            && replacingRuleDateMax.getRuleRangeType()
                                            != 2) { // 找出比传的值大的数//为null刚传参刚为最大数值
                                        publicMax(request, replacingRuleDateMax, company);
                                    }
                                    if (null != replacingRuleDateMin
                                            && replacingRuleDateMin.getRuleRangeThreshold()
                                            != request.getOnJobMonthOriginal()
                                            && replacingRuleDateMin.getRuleRangeType()
                                            != 2) { //  找出比传的值小的数//为null刚传参刚为最小数值
                                        publicMin(request, replacingRuleDateMin, company);
                                    }
                                }
                            }
                            if (type == 0) {
                                operation = publicInster(request, company);
                            } else {
                                operation = publicUpdate(request, company);
                            }
                        }
                    } else {
                        if (type == 0) {
                            operation = publicInster(request, company);
                        } else {
                            operation = publicUpdate(request, company);
                        }
                    }
                } else { // 根据传参分公司在表里查看没有则添加数据。
                    if (type == 0) {
                        operation = publicInster(request, company);
                    } else {
                        operation = publicUpdate(request, company);
                    }
                }
            }
        } else { // 汰换
            List<SfaRuleSetModel> ruleSetList =
                    sfaRuleSetMapper.selectList(
                            new QueryWrapper<SfaRuleSetModel>().eq("rule_type", 1).eq("delete_flag", 0));
            List<SfaRuleSetModel> collect1 =
                    ruleSetList.stream()
                            .filter(
                                    r ->
                                            r.getRuleRangeType() == request.getRuleRangeType()
                                                    && r.getRuleRangeThreshold() == request.getRuleRangeThreshold()
                                                    && r.getPerformState() == request.getPerformState()
                                                    && r.getRuleRangeThreshold() != request.getOnJobMonthOriginal())
                            .collect(Collectors.toList());
            if (CommonUtil.ListUtils.isNotEmpty(collect1)) {
                throw new ApplicationException("在岗天数已经存在这条规则");
            }

            ArrayList<ReplacingRuleDateVo> replacingRuleDateVos = new ArrayList<>();
            BeanUtils.copyProperties(
                    ruleSetList, replacingRuleDateVos, SfaRuleSetModel.class, ReplacingRuleDateVo.class);
            ReplacingRuleDateVo replacingRuleDateMin =
                    recursionSubtract(request.getRuleRangeThreshold(), replacingRuleDateVos);
            ReplacingRuleDateVo replacingRuleDateMax =
                    recursionAdd(
                            request.getRuleRangeThreshold(), request.getRuleType(), replacingRuleDateVos);
      /*
      不比较编辑前的值
      * */
            if (null != replacingRuleDateMax) {
                if (request.getOnJobMonthOriginal() != replacingRuleDateMax.getRuleRangeThreshold()
                        && request.getRuleRangeThreshold() < replacingRuleDateMax.getRuleRangeThreshold()
                        && (request.getPerformDeductions().doubleValue()
                        > replacingRuleDateMax.getPerformDeductions().doubleValue()
                        || request.getPerformDeductions().doubleValue()
                        == replacingRuleDateMax.getPerformDeductions().doubleValue())) {
                    throw new ApplicationException(
                            "在岗天数小于"
                                    + replacingRuleDateMax.getRuleRangeThreshold()
                                    + "扣款大于或等于"
                                    + replacingRuleDateMax.getPerformDeductions()
                                    + "不符合规则条件");
                }
            }
            if (null != replacingRuleDateMin) {
                if (request.getOnJobMonthOriginal() != replacingRuleDateMin.getRuleRangeThreshold()
                        && request.getRuleRangeThreshold() > replacingRuleDateMin.getRuleRangeThreshold()
                        && (request.getPerformDeductions() < replacingRuleDateMin.getPerformDeductions()
                        || request.getPerformDeductions().doubleValue()
                        == replacingRuleDateMin.getPerformDeductions().doubleValue())) {
                    throw new ApplicationException(
                            "在岗天数大于"
                                    + replacingRuleDateMin.getRuleRangeThreshold()
                                    + "扣款小于或等于"
                                    + replacingRuleDateMin.getPerformDeductions()
                                    + "不符合规则条件");
                }
            }
            if (type == 0) { // 添加
                operation = publicInster(request, null);
            } else { // 修改
                operation = publicUpdate(request, null);
            }
        }
        return operation;
    }

    public void publicMax(
            ReplacingRuleRequest request,
            ReplacingRuleDateVo replacingRuleDateMax,
            ReplacingOrganizationRequest company) {
        if (request.getRuleRangeThreshold() > replacingRuleDateMax.getRuleRangeThreshold()
                && ((request.getRuleIndicatorsThreshold().doubleValue()
                < replacingRuleDateMax.getRuleIndicatorsThreshold().doubleValue())
                || (request.getRuleIndicatorsThreshold().doubleValue()
                == replacingRuleDateMax.getRuleIndicatorsThreshold().doubleValue()))) {
            throw new ApplicationException(
                    company.getCompanyName()
                            + "分公司在岗月份大于"
                            + replacingRuleDateMax.getRuleRangeThreshold()
                            + ",业绩小于或等于"
                            + replacingRuleDateMax.getRuleIndicatorsThreshold()
                            + "不符合规则条件");
        }
        if (request.getRuleRangeThreshold() < replacingRuleDateMax.getRuleRangeThreshold()
                && ((request.getRuleIndicatorsThreshold().doubleValue()
                > replacingRuleDateMax.getRuleIndicatorsThreshold().doubleValue())
                || (request.getRuleIndicatorsThreshold().doubleValue()
                == replacingRuleDateMax.getRuleIndicatorsThreshold().doubleValue()))) {
            throw new ApplicationException(
                    company.getCompanyName()
                            + "分公司在岗月份小于"
                            + replacingRuleDateMax.getRuleRangeThreshold()
                            + ",业绩大于或等于"
                            + replacingRuleDateMax.getRuleIndicatorsThreshold()
                            + "不符合规则条件");
        }
    }

    public void publicMin(
            ReplacingRuleRequest request,
            ReplacingRuleDateVo replacingRuleDateMin,
            ReplacingOrganizationRequest company) {
        if ((request.getRuleRangeThreshold() < replacingRuleDateMin.getRuleRangeThreshold())
                && (request.getRuleIndicatorsThreshold().doubleValue()
                > replacingRuleDateMin.getRuleIndicatorsThreshold().doubleValue()
                || (request.getRuleIndicatorsThreshold().doubleValue()
                == replacingRuleDateMin.getRuleIndicatorsThreshold().doubleValue()))) {
            throw new ApplicationException(
                    company.getCompanyName()
                            + "分公司在岗月份小于"
                            + replacingRuleDateMin.getRuleRangeThreshold()
                            + ",业绩大于或等于"
                            + replacingRuleDateMin.getRuleIndicatorsThreshold()
                            + "不符合规则条件");
        }
        if ((request.getRuleRangeThreshold() > replacingRuleDateMin.getRuleRangeThreshold())
                && (request.getRuleIndicatorsThreshold().doubleValue()
                < replacingRuleDateMin.getRuleIndicatorsThreshold().doubleValue()
                || (request.getRuleIndicatorsThreshold().doubleValue()
                == replacingRuleDateMin.getRuleIndicatorsThreshold().doubleValue()))) {
            throw new ApplicationException(
                    company.getCompanyName()
                            + "分公司在岗月份大于"
                            + replacingRuleDateMin.getRuleRangeThreshold()
                            + ",业绩小于或等于"
                            + replacingRuleDateMin.getRuleIndicatorsThreshold()
                            + "不符合规则条件");
        }
    }

    /**
     * 递规找出相邻的最小值
     *
     * @param num
     * @param collect
     * @return
     */
    public ReplacingRuleDateVo recursionSubtract(Integer num, List<ReplacingRuleDateVo> collect) {
        ReplacingRuleDateVo replacingRuleDateVo = null;
        if (num != 1) {
            num = num - 1;
            Integer numm = num;
            List<ReplacingRuleDateVo> collect1 =
                    collect.stream()
                            .filter(r -> r.getRuleRangeThreshold() == numm)
                            .collect(Collectors.toList());
            if (CommonUtil.ListUtils.isNotEmpty(collect1)) {
                return collect1.get(0);
            } else {
                return recursionSubtract(numm, collect);
            }
        }
        return replacingRuleDateVo;
    }

    /**
     * 递规找出相邻的最大值
     *
     * @param num
     * @param collect
     * @return
     */
    public ReplacingRuleDateVo recursionAdd(
            Integer num, Integer RuleType, List<ReplacingRuleDateVo> collect) {
        ReplacingRuleDateVo replacingRuleDateVo = null;
        int setting = 0;
        String ruleAssess = configMapper.getValueByCode("rule_assess"); // 规则考核最大设定值
        String ruleWithhold = configMapper.getValueByCode("rule_withhold"); // 规则扣款最大设定值
        if (RuleType == 0) {
            setting = Integer.valueOf(ruleAssess);
        } else {
            setting = Integer.valueOf(ruleWithhold);
        }
        if (num != setting) {
            num = num + 1;
            Integer numm = num;
            List<ReplacingRuleDateVo> collect1 =
                    collect.stream()
                            .filter(r -> r.getRuleRangeThreshold() == numm)
                            .collect(Collectors.toList());
            if (CommonUtil.ListUtils.isNotEmpty(collect1)) {
                return collect1.get(0);
            }
            return recursionAdd(numm, RuleType, collect);
        }
        return replacingRuleDateVo;
    }

    /**
     * 判断考核与扣款的最大设置值
     *
     * @param request
     */
    public void publicbigSetValue(ReplacingRuleRequest request) {
        String ruleAssess = configMapper.getValueByCode("rule_assess"); // 规则考核最大设定值
        String ruleWithhold = configMapper.getValueByCode("rule_withhold"); // 规则扣款最大设定值
        if (request.getRuleRangeType() == 1
                && request.getRuleRangeThreshold() > Integer.valueOf(ruleAssess)
                && request.getRuleType() == 0) {
            throw new ApplicationException("在岗月份不可大于" + ruleAssess);
        }
        if (request.getRuleRangeThreshold() > Integer.valueOf(ruleWithhold)
                && request.getRuleType() == 1) {
            throw new ApplicationException("在岗天数不可大于" + ruleWithhold);
        }
    }

    @Override
    public ReplacingCompanyVo replacingDateCompnayDate() {
        ReplacingCompanyVo replacingCompanyVo = new ReplacingCompanyVo();
        List<ReplacingOrganizationVo> bClass = null;
        // LocalDateTime time = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth());
        String format = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        List<ReplacingOrganizationVo> replacingOrganizationVos =
                companyClassificationMapper.selCompanys(format);
    /*if (CommonUtil.ListUtils.isNotEmpty(replacingOrganizationVos)) {
      bClass =
          replacingOrganizationVos.stream()
              .filter(
                  g -> CommonUtil.StringUtils.isNotBlank(g.getLabe()) && !g.getLabe().equals("A"))
              .collect(Collectors.toList());
    }*/
        replacingCompanyVo.setBClassCompanyList(replacingOrganizationVos);

        ArrayList<ReplacingOrganizationVo> replacingCompanyLabeList = new ArrayList<>();
        Map<String, List<ReplacingOrganizationVo>> collect =
                replacingOrganizationVos.stream()
                        .collect(Collectors.groupingBy(ReplacingOrganizationVo::getLabe));
        for (Map.Entry<String, List<ReplacingOrganizationVo>> col : collect.entrySet()) {
            ReplacingOrganizationVo replacingCompanylabe = new ReplacingOrganizationVo();
            replacingCompanylabe.setLabe(col.getKey() + "类分公司");
            replacingCompanyLabeList.add(replacingCompanylabe);
        }
        replacingCompanyVo.setLabeList(replacingCompanyLabeList);
        return replacingCompanyVo;
    }

    @Override
    @Transactional
    public Integer replacingDateRuleSetUpRedact(ReplacingRuleRequest request) {
        log.info("start ReplacingService replacingDateRuleSetUpRedact request:{}", request);
        publicbigSetValue(request);
        List<ReplacingOrganizationRequest> companyList = request.getCompanyList();
        /* if (request.getOnJobMonthOriginal() != request.getRuleRangeThreshold()) {*/
        publicRuleCompany(companyList, request, 1);
        /*  }*/
        Integer update = publicRuleSetList(companyList, request, 1);
        return update;
    }

    @Override
    public Integer replacingDateRuleSetUpDelete(ReplacingRuleDeleteRequest request) {
        log.info("start ReplacingService replacingDateRuleSetUpDelete request:{}", request);
        SfaRuleSetModel ruleSetModel = new SfaRuleSetModel();
        ruleSetModel.setId(request.getRuleSetId());
        ruleSetModel.setDeleteFlag(1);
        int isdelete = sfaRuleSetMapper.updateById(ruleSetModel);
        if (isdelete > 0) {
            SfaRuleSetCompanyModel sfaRuleSetCompanyModel = new SfaRuleSetCompanyModel();
            sfaRuleSetCompanyModel.setDeleteFlag(1);
            sfaRuleSetCompanyMapper.update(
                    sfaRuleSetCompanyModel,
                    new QueryWrapper<SfaRuleSetCompanyModel>().eq("rule_set_id", request.getRuleSetId()));
        } else {
            throw new ApplicationException("数据删除失败");
        }
        return isdelete;
    }

    @Override
    public ReplacingLabelVo replacingDateLabe(String organiztaionId) {
        log.info(
                "start ReplacingService replacingDateRuleSetUpDelete organiztaionId:{}", organiztaionId);
        ReplacingLabelVo replacingLabelVo = new ReplacingLabelVo();
        // LocalDateTime time = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth());
        String format = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        CompanyClassificationPO companyClassificationPO =
                companyClassificationMapper.selectOne(
                        new QueryWrapper<CompanyClassificationPO>()
                                .eq("organization_id", organiztaionId)
                                .like("effective_date", format));
        if (null != companyClassificationPO) {
            replacingLabelVo.setLabe(companyClassificationPO.getClassification());
            replacingLabelVo.setCompanyId(companyClassificationPO.getOrganizationId());
        }
        return replacingLabelVo;
    }

    @Override
    public Page<ReplacingCompanyRulesVo> replacingDateCompanyRules(String memberKey) {
        log.info("start ReplacingService replacingDateCompanyRules memberKey:{}", memberKey);
        Page<ReplacingCompanyRulesVo> rults = new Page<>();
        String organizationId =
                ceoBusinessOrganizationPositionRelationMapper.getOrganizationIdByMemberkey(memberKey);
        List<ReplacingRuleVo> replacingRuleVos = sfaRuleSetMapper.selectRuleSetDate(organizationId);
        ArrayList<ReplacingCompanyRulesVo> replacingzCompanyRulesVos = new ArrayList<>();
        for (int i = 1; i <= replacingRuleVos.size(); i++) {
            ReplacingRuleVo replacingRuleVo = replacingRuleVos.get(i - 1);
            ReplacingCompanyRulesVo replacingCompanyRulesVo = new ReplacingCompanyRulesVo();
            String rule = "规则";
            String ruleRange =
                    replacingRuleVo.getRuleRangeType()
                            + replacingRuleVo.getRuleRangeOperationType()
                            + replacingRuleVo.getRuleRangeThreshold();
            String ruleIndicators =
                    replacingRuleVo.getRuleIndicatorsType()
                            + replacingRuleVo.getRuleIndicatorsOperationType()
                            + replacingRuleVo.getRuleIndicatorsThreshold();
            String performState = replacingRuleVo.getPerformState();

            if (replacingRuleVo.getRuleRangeType().equals("连续异常")) {
                replacingCompanyRulesVo.setRulesDate(rule + i + ": " + ruleRange + " , " + performState);
            } else {
                replacingCompanyRulesVo.setRulesDate(
                        rule + i + ": " + ruleRange + "," + ruleIndicators + "," + performState);
            }
            replacingzCompanyRulesVos.add(replacingCompanyRulesVo);
        }
        rults.setList(replacingzCompanyRulesVos);
        rults.setTotalItem(replacingzCompanyRulesVos.size());
        return rults;
    }

    @Override
    public List<String> replacingDateZbRulesNumber() {
        String zbRule = configMapper.getValueByCode("zb_rule");
        List<String> strings = Arrays.asList(zbRule.split(","));
        return strings;
    }

    @Override
    public Integer updateReplacingExamine(ReplacingExamineRequest request) {
        log.info("start ReplacingService updateReplacingExamine request:{}", request);
        SfaReplaceExamine sfaReplaceExamine = sfaReplaceExamineMapper.selectOne(new QueryWrapper<SfaReplaceExamine>().eq("the_year_month", request.getMonth()).eq("employee_info_id", request.getEmployeeInfoId()));
        SfaReplaceExamine vo = new SfaReplaceExamine();
        vo.setTheYearMonth(request.getMonth());
        vo.setEmployeeInfoId(request.getEmployeeInfoId());
        vo.setOrganizationId(request.getOrganizationId());
        vo.setExamineType(request.getExamineType());
        vo.setExamineIdea(request.getExamineIdea());
        int insert = 0;
        if (Objects.isNull(sfaReplaceExamine)) {
            vo.setCreateTime(LocalDateTime.now());
            vo.setCreatePeople(request.getEmployee());
            insert = sfaReplaceExamineMapper.insert(vo);
        } else {
            vo.setUpdateTime(LocalDateTime.now());
            vo.setUpdatePeople(request.getEmployee());
            insert = sfaReplaceExamineMapper.update(vo, new QueryWrapper<SfaReplaceExamine>().eq("the_year_month", request.getMonth()).eq("employee_info_id", request.getEmployeeInfoId()));
        }
        return insert;
    }

}
