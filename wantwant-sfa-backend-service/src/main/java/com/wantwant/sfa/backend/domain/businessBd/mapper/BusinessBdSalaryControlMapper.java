package com.wantwant.sfa.backend.domain.businessBd.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.businessBd.repository.po.BusinessBdSalaryControlPO;
import com.wantwant.sfa.backend.salary.request.BusinessBDControlSearch;
import com.wantwant.sfa.backend.salary.request.SalaryControlSearchRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDControlDetailVO;
import com.wantwant.sfa.backend.salary.vo.BusinessBDQuotaConfigVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务BD额度管控Mapper
 */
@Mapper
public interface BusinessBdSalaryControlMapper extends BaseMapper<BusinessBdSalaryControlPO> {

    /**
     * 查询业务BD额度管控
     *
     * @param page
     * @param salaryControlSearchRequest
     * @return
     */
    List<BusinessBDQuotaConfigVo> getBusinessBDQuotaControl(@Param("page") IPage<BusinessBDQuotaConfigVo> page,
                                                           @Param("req") SalaryControlSearchRequest salaryControlSearchRequest);

    /**
     * 查询业务BD管控设置
     *
     * @param page
     * @param businessBDControlSearch
     * @return
     */
    List<BusinessBDControlDetailVO> selectBusinessBDControl(@Param("page")IPage<BusinessBDControlDetailVO> page,
                                                            @Param("req") BusinessBDControlSearch businessBDControlSearch);
                                                            
    /**
     * 根据ID列表查询薪资结构，保留重复的ID记录
     * 查询结果中的每条记录对应idList中的一个ID，保持相同的顺序和数量
     * 如果某个ID在表中不存在，则结果中对应位置为null
     *
     * @param tableName 表名
     * @param idColumn ID列名
     * @param idList ID列表，可能包含重复的ID
     * @return 查询结果列表，按照idList的顺序返回
     */
    <T> List<T> selectWithDuplicateIds(@Param("tableName") String tableName, 
                                     @Param("idColumn") String idColumn, 
                                     @Param("idList") List<?> idList);
}