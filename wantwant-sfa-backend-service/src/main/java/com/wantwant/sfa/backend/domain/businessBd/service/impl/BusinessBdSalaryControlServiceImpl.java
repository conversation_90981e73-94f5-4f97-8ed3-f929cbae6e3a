package com.wantwant.sfa.backend.domain.businessBd.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.config.ConfigService;
import com.wantwant.sfa.backend.domain.businessBd.DO.*;
import com.wantwant.sfa.backend.domain.businessBd.enums.BusinessBdSalaryControlLogTypeEnum;
import com.wantwant.sfa.backend.domain.businessBd.mapper.BusinessBdPackageRateConfigMapper;
import com.wantwant.sfa.backend.domain.businessBd.mapper.BusinessBdSalaryControlMapper;
import com.wantwant.sfa.backend.domain.businessBd.mapper.BusinessBdSalaryControlLogMapper;
import com.wantwant.sfa.backend.domain.businessBd.repository.facade.IBusinessBdSalaryHistoryRepository;
import com.wantwant.sfa.backend.domain.businessBd.repository.po.BusinessBdPackageRateConfigPO;
import com.wantwant.sfa.backend.domain.businessBd.repository.po.BusinessBdSalaryControlPO;
import com.wantwant.sfa.backend.domain.businessBd.repository.po.BusinessBdSalaryControlLogPO;
import com.wantwant.sfa.backend.domain.businessBd.service.BusinessBdSalaryControlService;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.interview.enums.SalaryPositionEnum;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationViewMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SalaryMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.salary.model.OrganizationUsedSalaryModel;
import com.wantwant.sfa.backend.salary.request.BusinessBDAllocatedRequest;
import com.wantwant.sfa.backend.salary.request.BusinessBDControlSearch;
import com.wantwant.sfa.backend.salary.request.BusinessBDSalaryPackageConfig;
import com.wantwant.sfa.backend.salary.request.SalaryControlSearchRequest;
import com.wantwant.sfa.backend.salary.vo.*;
import com.wantwant.sfa.backend.service.EmployeeSalaryService;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Business BD quota control service implementation
 */
@Slf4j
@Service
public class BusinessBdSalaryControlServiceImpl implements BusinessBdSalaryControlService {

    @Resource
    private BusinessBdSalaryControlMapper businessBdSalaryControlMapper;

    @Resource
    private BusinessBdSalaryControlLogMapper businessBdSalaryControlLogMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private EmployeeSalaryService employeeSalaryService;
    @Resource
    private SalaryMapper salaryMapper;
    @Resource
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private BusinessBdPackageRateConfigMapper businessBdPackageRateConfigMapper;
    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
    @Resource
    private ConfigService configService;
    @Resource
    private IBusinessBdSalaryHistoryRepository businessBdSalaryHistoryRepository;





    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessBdSalaryControlDO saveOrUpdate(BusinessBdSalaryControlDO businessBdSalaryControlDO) {
        // 查询是否已存在记录
        BusinessBdSalaryControlPO existingPO = businessBdSalaryControlMapper.selectOne(
                new LambdaQueryWrapper<BusinessBdSalaryControlPO>()
                        .eq(BusinessBdSalaryControlPO::getOrganizationId, businessBdSalaryControlDO.getOrganizationId())
                        .eq(BusinessBdSalaryControlPO::getTheYearMonth, businessBdSalaryControlDO.getTheYearMonth())
                        .eq(BusinessBdSalaryControlPO::getDeleteFlag, 0)
        );

        BusinessBdSalaryControlPO po = new BusinessBdSalaryControlPO();
        po.init(businessBdSalaryControlDO.getProcessUserId(), businessBdSalaryControlDO.getProcessUserName());
        BeanUtils.copyProperties(businessBdSalaryControlDO, po);


        BusinessBdSalaryControlLogTypeEnum businessBdSalaryControlLogTypeEnum = BusinessBdSalaryControlLogTypeEnum.READ_MONTHLY_SALARY_PACKAGE;
        BigDecimal lastMonthBalance = businessBdSalaryControlDO.getLastMonthBalance();
        if (Objects.nonNull(lastMonthBalance)) {
            businessBdSalaryControlLogTypeEnum = BusinessBdSalaryControlLogTypeEnum.LAST_MONTH_BALANCE;
        }

        BigDecimal beforeValue = BigDecimal.ZERO;
        BigDecimal afterValue = BigDecimal.ZERO;

        switch (businessBdSalaryControlLogTypeEnum) {
            case LAST_MONTH_BALANCE:
                beforeValue = Optional.ofNullable(existingPO)
                        .map(BusinessBdSalaryControlPO::getLastMonthBalance)
                        .orElse(BigDecimal.ZERO);
                afterValue = businessBdSalaryControlDO.getLastMonthBalance();
                break;
            case READ_MONTHLY_SALARY_PACKAGE:
                beforeValue = Optional.ofNullable(existingPO)
                        .map(BusinessBdSalaryControlPO::getAvgSalaryPackage)
                        .orElse(BigDecimal.ZERO);
                afterValue = businessBdSalaryControlDO.getAvgSalaryPackage();
                break;
        }

        // 如果记录不存在，创建新记录
        if (existingPO == null) {
            businessBdSalaryControlMapper.insert(po);
        } else {
            po.update(businessBdSalaryControlDO.getProcessUserId(), businessBdSalaryControlDO.getProcessUserName());
            // 更新记录
            po.setId(existingPO.getId());
            businessBdSalaryControlMapper.updateById(po);
        }
        // 记录日志，使用原值
        createAndSaveLog(po, businessBdSalaryControlLogTypeEnum, beforeValue,afterValue);

        BusinessBdSalaryControlDO result = new BusinessBdSalaryControlDO();
        BeanUtils.copyProperties(po, result);
        return result;
    }


    @Override
    public void saveLog(BusinessBdSalaryControlLogDO businessBdSalaryControlLogDO) {
        BusinessBdSalaryControlLogPO po = new BusinessBdSalaryControlLogPO();
        BeanUtils.copyProperties(businessBdSalaryControlLogDO, po);
        po.setCreateTime(LocalDateTime.now());
        po.setUpdateTime(LocalDateTime.now());
        businessBdSalaryControlLogMapper.insert(po);
    }

    @Override
    public IPage<BusinessBDQuotaConfigVo> getBusinessBDQuotaControl(SalaryControlSearchRequest salaryControlSearchRequest) {
        log.info("Starting to retrieve business BD quota control data, query month: {}", salaryControlSearchRequest.getTheYearMonth());
        try {
            IPage<BusinessBDQuotaConfigVo> page = new Page<>(salaryControlSearchRequest.getPage(), salaryControlSearchRequest.getRows());

            List<BusinessBDQuotaConfigVo> resultList = doSearchBusinessBDQuotaControl(salaryControlSearchRequest, page);
            page.setRecords(resultList);
            log.info("Business BD quota control data retrieval completed, total records: {}", resultList.size());
            return page;
        } catch (Exception e) {
            log.error("Error retrieving business BD quota control data", e);
            throw new ApplicationException("Failed to retrieve business BD quota control data: " + e.getMessage());
        }
    }

    private List<BusinessBDQuotaConfigVo> doSearchBusinessBDQuotaControl(SalaryControlSearchRequest salaryControlSearchRequest, IPage<BusinessBDQuotaConfigVo> page) {
        // 验证并设置查询参数
        validateAndSetupSearchParams(salaryControlSearchRequest);

        // 查询基础数据
        List<BusinessBDQuotaConfigVo> resultList = Optional.ofNullable(businessBdSalaryControlMapper.getBusinessBDQuotaControl(page, salaryControlSearchRequest))
                .orElse(new ArrayList<>());

        if (CollectionUtils.isEmpty(resultList)) {
            log.info("No business BD quota control data found");
            return Collections.emptyList();
        }

        // 获取组织路径信息
        Map<String, List<String>> orgPathMap = getOrganizationPathMap(resultList);

        // 获取并设置薪资数据
        setSalaryData(resultList, salaryControlSearchRequest.getTheYearMonth(), orgPathMap);

        return resultList;
    }

    @Override
    public void exportBusinessBDQuotaControl(SalaryControlSearchRequest salaryControlSearchRequest) {
        // 构造不分页查询：size 设为很大或直接 Mapper 无分页参数重查
        List<BusinessBDQuotaConfigVo> records = doSearchBusinessBDQuotaControl(salaryControlSearchRequest, null);

        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(attrs, "System error!");
        HttpServletResponse response = attrs.getResponse();

        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-mm-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), BusinessBDQuotaConfigVo.class, records);

        try {
            String fileName = "Business_BD_Quota_Control.xlsx";
            String userAgent = attrs.getRequest().getHeader("User-Agent").toLowerCase();
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    /**
     * 验证并设置查询参数
     */
    private void validateAndSetupSearchParams(SalaryControlSearchRequest request) {
        // 设置组织类型
        String orgCode = request.getOrgCode();
        if (StringUtils.isNotBlank(orgCode)) {
            String organizationType = organizationMapper.getOrganizationType(orgCode);
            request.setOrgType(organizationType);
        }

        // 设置默认年月
        String theYearMonth = request.getTheYearMonth();
        if (StringUtils.isBlank(theYearMonth)) {
            request.setTheYearMonth(configService.getCurrentDate().toString().substring(0, 7));
        }

        // 设置业务组
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
    }

    /**
     * 获取组织路径映射
     */
    private Map<String, List<String>> getOrganizationPathMap(List<BusinessBDQuotaConfigVo> configList) {
        List<String> orgCodes = configList.stream()
                .map(BusinessBDQuotaConfigVo::getCurrentOrgCode)
                .collect(Collectors.toList());

        List<CeoBusinessOrganizationViewEntity> orgEntities = ceoBusinessOrganizationViewMapper.selectList(
                new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>()
                        .in(CeoBusinessOrganizationViewEntity::getOrganizationId, orgCodes));

        return changeToMap(orgEntities);
    }

    /**
     * 获取并设置薪资数据
     */
    private void setSalaryData(List<BusinessBDQuotaConfigVo> configList, String yearMonth, Map<String, List<String>> orgPathMap) {
        try {
            LocalDate currentDate = configService.getCurrentDate().withDayOfMonth(1);
            LocalDate searchDate = LocalDate.parse(yearMonth + "-01");

            List<String> orgCodes = configList.stream()
                    .map(BusinessBDQuotaConfigVo::getCurrentOrgCode)
                    .collect(Collectors.toList());

            if (searchDate.isBefore(currentDate)) {
                // 处理历史数据
                setHistoricalSalaryData(configList, yearMonth, orgPathMap);
            } else {
                // 处理当前数据
                setCurrentSalaryData(configList, yearMonth, orgCodes, orgPathMap);
            }
        } catch (Exception e) {
            log.error("Error setting salary data: {}", e.getMessage(), e);
            throw new ApplicationException("Failed to process salary data");
        }
    }

    /**
     * 设置历史薪资数据
     */
    private void setHistoricalSalaryData(List<BusinessBDQuotaConfigVo> configList, String yearMonth, Map<String, List<String>> orgPathMap) {
        log.info("Querying historical salary data, year-month: {}", yearMonth);

        // 查询历史薪资数据
        List<OrgSalaryDO> totalSalaryList = Optional.ofNullable(
                businessBdSalaryHistoryRepository.selectHistoryTotalSalary(yearMonth))
                .orElse(new ArrayList<>());

        // 创建Map以提高查询效率
        Map<String, OrgSalaryDO> salaryMap = totalSalaryList.stream()
                .collect(Collectors.toMap(
                        OrgSalaryDO::getOrganizationId,
                        Function.identity(),
                        (a, b) -> a
                ));

        // 设置数据
        for (BusinessBDQuotaConfigVo config : configList) {
            OrgSalaryDO orgSalary = salaryMap.get(config.getCurrentOrgCode());
            if (orgSalary != null) {
                config.setUsedQuota(orgSalary.getTotalSalary());
                config.setSurplus(config.getQuota().subtract(config.getUsedQuota()).subtract(config.getExpiredQuota()).add(config.getCumulativeBalanceAmount()));
                config.setOrgPath(orgPathMap.get(config.getCurrentOrgCode()));
            }
        }

        log.info("Historical salary data setup completed, processed {} records", configList.size());
    }

    /**
     * 设置当前薪资数据
     */
    private void setCurrentSalaryData(List<BusinessBDQuotaConfigVo> configList, String yearMonth, List<String> orgCodes, Map<String, List<String>> orgPathMap) {
        log.info("Querying current salary data, year-month: {}", yearMonth);

        // 查询处于异动中的人员数据
        List<EmpSalaryDO> transactionEmpList = Optional.ofNullable(
                employeeSalaryService.selectTransactionBD(orgCodes, yearMonth))
                .orElse(new ArrayList<>());

        // 获取异动中人员薪资
        List<OrgSalaryDO> transferringSalary = Optional.ofNullable(
                employeeSalaryService.selectTransferringSalary(transactionEmpList))
                .orElse(new ArrayList<>());
        log.info("Transferring personnel salary data count: {}", transferringSalary.size());

        // 入职中人员薪资
        List<OrgSalaryDO> onBoardProcessingSalary = Optional.ofNullable(
                employeeSalaryService.selectOnBoardProcessingSalary(orgCodes, yearMonth, null))
                .orElse(new ArrayList<>());
        log.info("Onboarding personnel salary data count: {}", onBoardProcessingSalary.size());

        // 在职人员薪资
        List<Integer> excludeEmployeeInfoIds = transactionEmpList.stream()
                .map(EmpSalaryDO::getEmployeeInfoId)
                .collect(Collectors.toList());
        List<OrganizationUsedSalaryModel> organizationUsedSalaryModels = Optional.ofNullable(
                employeeSalaryService.selectOnBoardBusinessBDSalary(orgCodes, excludeEmployeeInfoIds))
                .orElse(new ArrayList<>());
        log.info("Active personnel salary data count: {}", organizationUsedSalaryModels.size());

        // 创建Map以提高查询效率
        Map<String, BigDecimal> onBoardSalaryMap = createOnBoardSalaryMap(organizationUsedSalaryModels);
        Map<String, BigDecimal> transferSalaryMap = createSalaryMap(transferringSalary);
        Map<String, BigDecimal> onBoardProcessSalaryMap = createSalaryMap(onBoardProcessingSalary);

        // 设置数据
        for (BusinessBDQuotaConfigVo config : configList) {
            // 在职人员薪资
            BigDecimal onBoardSalary = onBoardSalaryMap.getOrDefault(config.getCurrentOrgCode(), BigDecimal.ZERO);

            // 获取目前在异动中的数据
            BigDecimal transferSalary = transferSalaryMap.getOrDefault(config.getCurrentOrgCode(), BigDecimal.ZERO);

            // 入职中人员薪资
            BigDecimal onBoardProcessSalary = onBoardProcessSalaryMap.getOrDefault(config.getCurrentOrgCode(), BigDecimal.ZERO);

            // 设置使用额度和剩余额度
            BigDecimal totalUsedQuota = onBoardSalary.add(transferSalary).add(onBoardProcessSalary);
            config.setUsedQuota(totalUsedQuota);
            config.setSurplus(config.getQuota().add(config.getCumulativeBalanceAmount()).subtract(config.getExpiredQuota()).subtract(totalUsedQuota));
            config.setOrgPath(orgPathMap.get(config.getCurrentOrgCode()));
        }

        log.info("Current salary data setup completed, processed {} records", configList.size());
    }

    /**
     * 创建在职人员薪资Map
     */
    private Map<String, BigDecimal> createOnBoardSalaryMap(List<OrganizationUsedSalaryModel> models) {
        return models.stream().collect(
                Collectors.toMap(
                        OrganizationUsedSalaryModel::getOrganizationId,
                        OrganizationUsedSalaryModel::getUsedQuota,
                        BigDecimal::add
                )
        );
    }

    /**
     * 创建薪资Map
     */
    private Map<String, BigDecimal> createSalaryMap(List<OrgSalaryDO> salaryList) {
        return salaryList.stream().collect(
                Collectors.toMap(
                        OrgSalaryDO::getOrganizationId,
                        OrgSalaryDO::getTotalSalary,
                        BigDecimal::add
                )
        );
    }

    @Override
    public IPage<BusinessBDSalaryVo> getAllocatedBusinessBDHistory(BusinessBDAllocatedRequest businessBDAllocatedRequest) {

        // 创建分页对象
        IPage<BusinessBDSalaryVo> page = new Page<>(businessBDAllocatedRequest.getPage(), businessBDAllocatedRequest.getRows());

        doSearchAllocatedBusinessBDHistory(businessBDAllocatedRequest,page);

        List<BusinessBDSalaryVo> result = doSearchAllocatedBusinessBDHistory(businessBDAllocatedRequest,page);
        page.setRecords(result);
        return page;
    }

    private List<BusinessBDSalaryVo> doSearchAllocatedBusinessBDHistory(BusinessBDAllocatedRequest businessBDAllocatedRequest, IPage<BusinessBDSalaryVo> page) {
        // 参数预处理
        prepareRequest(businessBDAllocatedRequest);

        // 根据日期决定处理方式
        List<BusinessBDSalaryVo> result;
        if(isHistoricalDate(businessBDAllocatedRequest.getTheYearMonth())) {
           return  processHistoricalData(page, businessBDAllocatedRequest);
        } else {
           return processCurrentData(page, businessBDAllocatedRequest);
        }
    }

    @Override
    public void exportAllocatedBusinessBDHistory(BusinessBDAllocatedRequest businessBDAllocatedRequest) {
        List<BusinessBDSalaryVo> result = doSearchAllocatedBusinessBDHistory(businessBDAllocatedRequest,null);

        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(attrs, "System error!");
        HttpServletResponse response = attrs.getResponse();

        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-mm-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), BusinessBDSalaryVo.class, result);

        try {
            String fileName = "Business_BD_Allocated_History.xlsx";
            String userAgent = attrs.getRequest().getHeader("User-Agent").toLowerCase();
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }
    
    /**
     * 准备请求参数，设置必要的默认值和组织类型
     * 
     * @param request 请求对象
     */
    private void prepareRequest(BusinessBDAllocatedRequest request) {
        // 设置组织类型
        String organizationId = request.getOrgCode();
        if(StringUtils.isNotBlank(organizationId)){
            request.setOrgType(organizationMapper.getOrganizationType(organizationId));
        }
        
        // 设置业务组
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        
        // 设置年月，如果为空则使用当前年月
        request.setTheYearMonth(normalizeYearMonth(request.getTheYearMonth()));
    }
    
    /**
     * 标准化年月格式，如果为空则返回当前年月
     * 
     * @param yearMonth 年月字符串 (格式: yyyy-MM)
     * @return 标准化后的年月字符串
     */
    private String normalizeYearMonth(String yearMonth) {
        if(StringUtils.isBlank(yearMonth)) {
            return LocalDate.now().toString().substring(0, 7);
        }
        return yearMonth;
    }
    
    /**
     * 判断指定年月是否是历史日期（早于当前日期）
     * 
     * @param yearMonth 年月字符串 (格式: yyyy-MM)
     * @return 如果是历史日期返回true，否则返回false
     */
    private boolean isHistoricalDate(String yearMonth) {
        try {
            LocalDate currentDate = configService.getCurrentDate().withDayOfMonth(1);
            LocalDate searchDate = parseYearMonth(yearMonth);
            return searchDate.isBefore(currentDate);
        } catch (Exception e) {
            log.error("Error comparing dates: {}", yearMonth, e);
            throw new ApplicationException("Invalid date format: " + yearMonth);
        }
    }
    
    /**
     * 解析年月字符串为LocalDate对象
     * 
     * @param yearMonth 年月字符串 (格式: yyyy-MM)
     * @return LocalDate对象，日期设置为当月1日
     */
    private LocalDate parseYearMonth(String yearMonth) {
        try {
            return LocalDate.parse(yearMonth + "-01");
        } catch (Exception e) {
            log.error("Error parsing year-month: {}", yearMonth, e);
            throw new ApplicationException("Invalid date format: " + yearMonth);
        }
    }
    
    /**
     * 处理历史数据查询
     * 
     * @param page 分页对象
     * @param request 请求参数
     * @return 薪资分配VO列表
     */
    private List<BusinessBDSalaryVo> processHistoricalData(IPage<BusinessBDSalaryVo> page, BusinessBDAllocatedRequest request) {
        List<BusinessBDSalaryVo> result = new ArrayList<>();
        
        // 查询历史数据
        List<SalaryAllocatedDO> salaryAllocatedDOS = Optional.ofNullable(
                businessBdSalaryHistoryRepository.selectHistory(page, request))
                .orElse(Collections.emptyList());
        
        if(CollectionUtils.isEmpty(salaryAllocatedDOS)) {
            return result;
        }
        
        // 转换为VO对象
        salaryAllocatedDOS.forEach(e -> {
            BusinessBDSalaryVo vo = new BusinessBDSalaryVo();
            BeanUtils.copyProperties(e, vo);
            vo.setPosition(SalaryPositionEnum.getPositionName(e.getSalaryPosition()));
            vo.setEmployeeStatus(EmployeeStatus.findNameByType(e.getEmployeeStatus()));
            result.add(vo);
        });
        
        return result;
    }
    
    /**
     * 处理当前数据查询
     * 
     * @param page 分页对象
     * @param request 请求参数
     * @return 薪资分配VO列表
     */
    private List<BusinessBDSalaryVo> processCurrentData(IPage<BusinessBDSalaryVo> page, BusinessBDAllocatedRequest request) {
        List<BusinessBDSalaryVo> result = new ArrayList<>();
        
        // 查询在职和入职中的人员
        List<BusinessSalaryDetail> businessSalaryDetails = Optional.ofNullable(
                salaryMapper.getAllocatedBusinessBDHistory(page, request))
                .orElse(Collections.emptyList());
        
        if(CollectionUtils.isEmpty(businessSalaryDetails)) {
            return result;
        }
        
        // 获取员工ID列表
        List<Integer> employeeInfoIds = businessSalaryDetails.stream()
                .map(BusinessSalaryDetail::getEmployeeInfoId)
                .collect(Collectors.toList());
        
        // 查询异动中的人员信息
        List<BusinessSalaryDetail> transfer = Optional.ofNullable(
                salaryMapper.selectBdTransferByIds(employeeInfoIds, request.getTheYearMonth()))
                .orElse(Collections.emptyList());
        
        // 更新异动信息
        if(!CollectionUtils.isEmpty(transfer)) {
            updateTransferInfo(businessSalaryDetails, transfer);
        }
        
        // 转换为VO对象
        businessSalaryDetails.forEach(e -> {
            BusinessBDSalaryVo vo = new BusinessBDSalaryVo();
            BeanUtils.copyProperties(e, vo);
            vo.setPosition(PositionEnum.getPositionName(e.getCeoType(), e.getJobsType(), e.getPosition()));
            result.add(vo);
        });
        
        return result;
    }
    
    /**
     * 更新业务薪资详情中的异动信息
     * 
     * @param businessSalaryDetails 业务薪资详情列表
     * @param transfer 异动信息列表
     */
    private void updateTransferInfo(List<BusinessSalaryDetail> businessSalaryDetails, List<BusinessSalaryDetail> transfer) {
        // 创建员工ID到异动信息的映射，提高查询效率
        Map<Integer, BusinessSalaryDetail> transferMap = transfer.stream()
                .collect(Collectors.toMap(
                        BusinessSalaryDetail::getEmployeeInfoId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        
        // 更新异动信息
        businessSalaryDetails.forEach(detail -> {
            BusinessSalaryDetail transferDetail = transferMap.get(detail.getEmployeeInfoId());
            if(transferDetail != null) {
                BeanUtils.copyProperties(transferDetail, detail);
                detail.setEmployeeStatus("异动中");
            }
        });
    }

    @Override
    public IPage<BusinessBDControlDetailVO> selectBusinessBDControl(BusinessBDControlSearch businessBDControlSearch) {
        String organizationId = businessBDControlSearch.getRegion();
        if(StringUtils.isNotBlank(organizationId)){
            String organizationType = organizationMapper.getOrganizationType(organizationId);
            if(StringUtils.isBlank(organizationType)){
                throw new ApplicationException("Organization ID error");
            }
            businessBDControlSearch.setOrganizationType(organizationType);
        }
        IPage<BusinessBDControlDetailVO> page = new Page<>(businessBDControlSearch.getPage(),businessBDControlSearch.getRows());
        List<BusinessBDControlDetailVO> list = businessBdSalaryControlMapper.selectBusinessBDControl(page,businessBDControlSearch);
        page.setRecords(list);
        return page;
    }

    @Override
    public List<SalaryPackageRateLogVO> getSalaryPackageLog(String departmentId) {

        List<BusinessBdSalaryControlLogPO> businessBdSalaryControlLogPOS = businessBdSalaryControlLogMapper.selectList(new LambdaQueryWrapper<BusinessBdSalaryControlLogPO>()
                .eq(BusinessBdSalaryControlLogPO::getOrganizationId, departmentId)
                .eq(BusinessBdSalaryControlLogPO::getType, BusinessBdSalaryControlLogTypeEnum.PACKAGE_RATE.getCode())
                .orderByDesc(BusinessBdSalaryControlLogPO::getCreateTime)
        );
        if(CollectionUtils.isEmpty(businessBdSalaryControlLogPOS)){
           return  Collections.emptyList();
        }

        return businessBdSalaryControlLogPOS.stream().map(e -> {
            SalaryPackageRateLogVO salaryPackageRateLog = new SalaryPackageRateLogVO();
            salaryPackageRateLog.setUpdateUserName(e.getUpdateUserName());
            BigDecimal afterValue = Optional.ofNullable(e.getAfterValue()).orElse(BigDecimal.ZERO);
            String ratio = afterValue.multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).setScale(0,BigDecimal.ROUND_HALF_UP).toString();
            salaryPackageRateLog.setPackageRate(ratio + "%");
            salaryPackageRateLog.setUpdateTime(LocalDateTimeUtils.formatTime(e.getUpdateTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
            return salaryPackageRateLog;
        }).collect(Collectors.toList());
    }

    @Override
    public void exportBusinessBDControl(BusinessBDControlSearch businessBDControlSearch) {
        String organizationId = businessBDControlSearch.getRegion();
        if(StringUtils.isNotBlank(organizationId)){
            businessBDControlSearch.setOrganizationType(organizationMapper.getOrganizationType(organizationId));
        }

        List<BusinessBDControlDetailVO> list = Optional.ofNullable(businessBdSalaryControlMapper.selectBusinessBDControl(null,BusinessBDControlSearch.builder().businessGroup(RequestUtils.getBusinessGroup()).build()))
                .orElse(new ArrayList<>());

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes,"System error!");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), BusinessBDControlDetailVO.class, list);

        try {
            String fileName ="Business_BD_Expense_Rate";
            if(wb instanceof HSSFWorkbook){
                fileName = fileName+".xls";
            }else{
                fileName = fileName+".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // For IE or IE-based browsers:
            if (userAgent.contains("msie") || userAgent.contains("trident") ) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // For non-IE browsers:
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    @Transactional
    public void importSalaryPackageRatio(List<SalaryPackageRatio> list, ProcessUserDO processUserDO) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }

        for (SalaryPackageRatio salaryPackageRatio : list) {
            String businessGroupName = salaryPackageRatio.getBusinessGroupName();
            SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectOne(new LambdaQueryWrapper<SfaBusinessGroupEntity>()
                    .eq(SfaBusinessGroupEntity::getBusinessGroupName, businessGroupName)
                    .eq(SfaBusinessGroupEntity::getDeleteFlag, 0)
                    .last("limit 1")
            );
            if(Objects.isNull(sfaBusinessGroupEntity)){
                throw new ApplicationException("Product group ["+businessGroupName+"] does not exist");
            }
            String organizationName = salaryPackageRatio.getOrganizationName();
            String organizationIdByName = organizationMapper.getOrganizationIdByName(organizationName, 3, sfaBusinessGroupEntity.getId());
            if(StringUtils.isBlank(organizationIdByName)){
                throw new ApplicationException("Product group ["+businessGroupName+"], organization ["+organizationName+"] does not exist");
            }

            String ratio = salaryPackageRatio.getRatio();
            if(StringUtils.isBlank(ratio)){
                throw new ApplicationException("Product group ["+businessGroupName+"], organization ["+organizationName+"] expense rate cannot be empty");
            }
            boolean number = NumberUtil.isNumber(ratio);
            if(!number){
                throw new ApplicationException("Product group ["+businessGroupName+"], organization ["+organizationName+"] expense rate is invalid");
            }


            BigDecimal afterValue = new BigDecimal(ratio).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);

            setSalaryRatio(processUserDO, organizationIdByName, afterValue);
        }
    }

    private void setSalaryRatio(ProcessUserDO processUserDO, String organizationIdByName, BigDecimal afterValue) {
        BusinessBdPackageRateConfigPO businessBdPackageRateConfigPO = businessBdPackageRateConfigMapper.selectOne(new LambdaQueryWrapper<BusinessBdPackageRateConfigPO>()
                .eq(BusinessBdPackageRateConfigPO::getOrganizationId, organizationIdByName)
                .eq(BusinessBdPackageRateConfigPO::getDeleteFlag, 0)
                .last("limit 1")
        );
        BigDecimal beforeValue = BigDecimal.ZERO;

        if(Objects.nonNull(businessBdPackageRateConfigPO)){
            beforeValue = Optional.ofNullable(businessBdPackageRateConfigPO.getSalaryPackageRate()).orElse(BigDecimal.ZERO);
            businessBdPackageRateConfigPO.update(processUserDO.getEmployeeId(), processUserDO.getEmployeeName());
            businessBdPackageRateConfigPO.setSalaryPackageRate(afterValue);
            businessBdPackageRateConfigMapper.updateById(businessBdPackageRateConfigPO);
        }else{
            businessBdPackageRateConfigPO = new BusinessBdPackageRateConfigPO();
            businessBdPackageRateConfigPO.init(processUserDO.getEmployeeId(), processUserDO.getEmployeeName());
            businessBdPackageRateConfigPO.setOrganizationId(organizationIdByName);
            businessBdPackageRateConfigPO.setSalaryPackageRate(afterValue);
            businessBdPackageRateConfigMapper.insert(businessBdPackageRateConfigPO);
        }


        // 记录操作
        BusinessBdSalaryControlPO businessBdSalaryControlPO = new BusinessBdSalaryControlPO();
        businessBdSalaryControlPO.update(processUserDO.getEmployeeId(), processUserDO.getEmployeeName());
        businessBdSalaryControlPO.setTheYearMonth(LocalDate.now().toString().substring(0,7));
        businessBdSalaryControlPO.setOrganizationId(organizationIdByName);
        createAndSaveLog(businessBdSalaryControlPO, BusinessBdSalaryControlLogTypeEnum.PACKAGE_RATE, beforeValue,afterValue);
    }

    @Override
    @Transactional
    public void setBusinessBDSalaryPackageConfig(BusinessBDSalaryPackageConfig businessBDSalaryPackageConfig, ProcessUserDO processUserDO) {
        BigDecimal salaryPackageRate = businessBDSalaryPackageConfig.getSalaryPackageRate();
        salaryPackageRate = salaryPackageRate.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
        setSalaryRatio(processUserDO, businessBDSalaryPackageConfig.getOrganizationId(), salaryPackageRate);
    }

    @Override
    public BigDecimal calculateExpiredQuota(LocalDate date, String companyCode) {
        log.info("calculate expired quota , companyCode:{} , date:{}", companyCode, date);

        // 计算基准年月（date-3个月）
        String startDate = date.minusMonths(3L).toString().substring(0, 7);
        String endDate = date.minusMonths(1L).toString().substring(0, 7);


        // 获取3个月前的总包额度（上月结余 + 平均包 - 已过期额度）
        BusinessBdSalaryControlPO controlM3 = businessBdSalaryControlMapper.selectOne(new LambdaQueryWrapper<BusinessBdSalaryControlPO>()
            .eq(BusinessBdSalaryControlPO::getTheYearMonth, startDate)
            .eq(BusinessBdSalaryControlPO::getOrganizationId, companyCode)
            .eq(BusinessBdSalaryControlPO::getDeleteFlag, 0)
            .last("limit 1")
        );

        if(Objects.isNull(controlM3)){
            log.info("No salary control configuration found three months ago");
            return BigDecimal.ZERO;
        }


        BigDecimal result1 = Optional.ofNullable(controlM3.getLastMonthBalance()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(controlM3.getAvgSalaryPackage()).orElse(BigDecimal.ZERO))
                .subtract(Optional.ofNullable(controlM3.getExpiredQuota()).orElse(BigDecimal.ZERO));
        log.info("Total package quota three months ago: {}", result1);


        // 计算date-1 ~ date-3三个月的总使用额度，结果2
        BigDecimal totalUsage = calculateUsage(companyCode, startDate,endDate);
        log.info("Total usage three months ago: {}", totalUsage);


        BigDecimal expiredQuota = BigDecimal.ZERO;
        // 返回超出部分（若不超出则返回0）
        if (totalUsage.compareTo(result1) < 0) {

            expiredQuota = result1.subtract(totalUsage);

            log.info("calculateExpiredQuota overflow: org={}, usage={}, result1={}, expiredQuota={}",
                    companyCode, totalUsage, result1, expiredQuota);
        }
        return expiredQuota;
    }

    @Override
    public BusinessBdSalaryControlDO getByOrganizationIdAndYearMonth(String theYearMonth, String organizationId) {
        BusinessBdSalaryControlPO po = businessBdSalaryControlMapper.selectOne(
                new LambdaQueryWrapper<BusinessBdSalaryControlPO>()
                        .eq(BusinessBdSalaryControlPO::getTheYearMonth, theYearMonth)
                        .eq(BusinessBdSalaryControlPO::getOrganizationId, organizationId)
                        .eq(BusinessBdSalaryControlPO::getDeleteFlag, 0)
        );

        if (po == null) {
            return BusinessBdSalaryControlDO.builder().organizationId(organizationId)
                    .theYearMonth(theYearMonth).avgSalaryPackage(BigDecimal.ZERO)
                    .lastMonthBalance(BigDecimal.ZERO).build();
        }

        BusinessBdSalaryControlDO result = new BusinessBdSalaryControlDO();
        BeanUtils.copyProperties(po, result);
        return result;
    }


    /**
     * Creates and saves a log entry
     *
     * @param newPO       New record
     * @param type        Log type
     * @param beforeValue Value before modification
     * @param afterValue  Value after modification
     */
    private void createAndSaveLog(BusinessBdSalaryControlPO newPO, BusinessBdSalaryControlLogTypeEnum type,
                                  BigDecimal beforeValue, BigDecimal afterValue) {
        BusinessBdSalaryControlLogDO logDO = new BusinessBdSalaryControlLogDO();
        logDO.setType(type.getCode());
        logDO.setTheYearMonth(newPO.getTheYearMonth());
        logDO.setOrganizationId(newPO.getOrganizationId());
        logDO.setBeforeValue(beforeValue);
        logDO.setAfterValue(afterValue);
        logDO.setCreateUserId(newPO.getUpdateUserId());
        logDO.setCreateUserName(newPO.getUpdateUserName());
        logDO.setUpdateUserId(newPO.getUpdateUserId());
        logDO.setUpdateUserName(newPO.getUpdateUserName());
        saveLog(logDO);
    }

    /**
     * 计算某一组织在指定年月的历史使用额度之和
     */
    private BigDecimal calculateUsage(String companyCode, String startYearMonth, String endYearMonth) {
        return Optional.ofNullable(
                        businessBdSalaryHistoryRepository.selectByOrgIdAndDateRange(companyCode, startYearMonth, endYearMonth)
                )
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(s -> Optional.ofNullable(s.getEmployeeBaseSalary()).orElse(BigDecimal.ZERO)
                        .add(Optional.ofNullable(s.getEmployeeAllowance()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(s.getSocialSecurityBase()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(s.getEmployeeBonus()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(s.getTravelExpenses()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(s.getFullRiskFee()).orElse(BigDecimal.ZERO)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Converts organization view entity list to organization path mapping
     * 
     * @param orgViewEntities Organization view entity list
     * @return Mapping of organization ID to path list
     */
    private Map<String, List<String>> changeToMap(List<CeoBusinessOrganizationViewEntity> orgViewEntities) {
        Map<String, List<String>> orgPathMap = new HashMap<>();
        
        if (CollectionUtils.isEmpty(orgViewEntities)) {
            return orgPathMap;
        }
        
        for (CeoBusinessOrganizationViewEntity entity : orgViewEntities) {
            List<String> path = new ArrayList<>();
            path.add(entity.getOrgId3());
            path.add(entity.getVirtualAreaId());
            path.add(entity.getProvinceId());
            path.add(entity.getOrgId2());
            orgPathMap.put(entity.getOrganizationId(), path);
        }
        
        return orgPathMap;
    }

} 