package com.wantwant.sfa.backend.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特批类型(1.普通特批 2.特案特批)
 */
@Getter
@AllArgsConstructor
public enum SpecialApprovalTypeEnum {
    NORMAL(1, "普通特批"),
    SPECIAL_CASE(2, "特案特批");

    private final Integer code;
    private final String desc;

    public static String getDesc(Integer code) {
        for (SpecialApprovalTypeEnum value : SpecialApprovalTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
