package com.wantwant.sfa.backend.domain.businessBd.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 业务BD额度管控持久化对象
 * 对应数据库表 sfa_business_bd_salary_control
 */
@Data
@TableName("sfa_business_bd_salary_control")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessBdSalaryControlPO extends CommonEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 年月，格式：yyyy-MM
     */
    private String theYearMonth;
    
    /**
     * 组织ID
     */
    private String organizationId;
    
    /**
     * 上月结余
     */
    private BigDecimal lastMonthBalance;
    
    /**
     * 当季度每月用人费用包
     */
    private BigDecimal avgSalaryPackage;

    /**
     * 过期额度
     */
    private BigDecimal expiredQuota;

} 