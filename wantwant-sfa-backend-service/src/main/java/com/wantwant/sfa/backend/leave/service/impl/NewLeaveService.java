package com.wantwant.sfa.backend.leave.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.arch.model.AccountModel;
import com.wantwant.sfa.backend.arch.request.SAccountRequest;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.common.OrganizationPositionRelationEnums;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.leave.entity.SfaLeave;
import com.wantwant.sfa.backend.leave.entity.SfaLeaveQuotaEntity;
import com.wantwant.sfa.backend.leave.entity.SfaLeaveQuotaRecordEntity;
import com.wantwant.sfa.backend.leave.entity.SfaLeaveRecord;
import com.wantwant.sfa.backend.leave.request.LeaveAuditPopupRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveAuditRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveCommitInfoRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveDetailRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveListRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveRevocationRequest;
import com.wantwant.sfa.backend.leave.service.INewLeaveService;
import com.wantwant.sfa.backend.leave.vo.LeaveAuditPopupVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveCheckVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveListVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveRecordVo;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.NotifyMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SettingsMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.arch.AccountMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveQuotaMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveQuotaRecordMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveRecordMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.SensitiveInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NewLeaveService extends ServiceImpl<SfaLeaveMapper, SfaLeave> implements INewLeaveService {

    public static final int ATTENDANCE_START_DAY = 26;//考勤周期
    public static final int ATTENDANCE_END_DAY = 25;//考勤周期
    LocalTime ClockInTime1 = LocalTime.of(8, 30);
    LocalTime ClockInTime2 = LocalTime.of(10, 0);

    public static final String CLOCK_IN_TIME_WLMQ = "clock_in_time_wlmq";

    @Autowired
    private NewLeaveRecordService newLeaveRecordService;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private NotifyService notifyService;
    @Resource
    private SettingServiceImpl settingServiceImpl;

    @Resource
    private SfaLeaveMapper sfaLeaveMapper;
    @Resource
    private SfaLeaveRecordMapper sfaLeaveRecordMapper;
    @Resource
    private SfaLeaveQuotaMapper sfaLeaveQuotaMapper;
    @Resource
    private SfaLeaveQuotaRecordMapper sfaLeaveQuotaRecordMapper;
    @Resource
    private SettingsMapper settingsMapper;
    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Resource
    private NotifyMapper notifyMapper;
    @Resource
    private OrganizationMapper organizationMapper;


    @Override
    public LeaveAuditPopupVo leaveAuditPopup(LeaveAuditPopupRequest request) {

        LeaveAuditPopupVo vo = new LeaveAuditPopupVo();

        int countdownDays = 3;
        // 获取配置的请假审批提醒倒数天数
        String leaveAuditCountdownDays = settingServiceImpl.getValue(DictCodeConstants.LEAVE_AUDIT_COUNTDOWN_DAYS);
        if (StringUtils.isNotBlank(leaveAuditCountdownDays)) {
            countdownDays = Integer.parseInt(leaveAuditCountdownDays);
        }

        LocalDate nowDate = LocalDate.now();
        LocalDate endDate = nowDate.withDayOfMonth(ATTENDANCE_END_DAY);
        LocalDate startDate = endDate.minusDays(countdownDays);
        vo.setDays(ChronoUnit.DAYS.between(nowDate, endDate));
        if (nowDate.isBefore(startDate) || nowDate.isAfter(endDate)) {
            vo.setIsPopup(Boolean.FALSE);
            return vo;
        }
        NewLeaveListRequest leaveListRequest = new NewLeaveListRequest();
        leaveListRequest.setPerson(request.getPerson());
        leaveListRequest.setType(1);
        IPage<NewLeaveListVo> leaveList = this.leaveList(leaveListRequest);
        if (Objects.isNull(leaveList) || leaveList.getTotal() == 0) {
            vo.setIsPopup(Boolean.FALSE);
            return vo;
        }
        vo.setIsPopup(Boolean.TRUE);
        vo.setQuantity(leaveList.getTotal());
        return vo;
    }

    /*
     * 校验请假按钮
     */
    @Override
    public NewLeaveCheckVo leaveCheck(String person) {
        log.info("leaveCheck request:{}", person);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        NewLeaveCheckVo newLeaveCheckVo = sfaPositionRelationMapper.selectEmpInfo(person, loginInfo.getBusinessGroup(), loginInfo.getPositionTypeId());
        if (Objects.isNull(newLeaveCheckVo)) {
            return new NewLeaveCheckVo();
        } else {
            List<SfaLeaveQuotaEntity> quotaList = sfaLeaveQuotaMapper.selectList(new LambdaQueryWrapper<SfaLeaveQuotaEntity>()
                    .eq(SfaLeaveQuotaEntity::getQuotaStatus, 1)
                    .eq(SfaLeaveQuotaEntity::getEmployeeInfoId, newLeaveCheckVo.getEmployeeInfoId())
            );
            if (!CollectionUtils.isEmpty(quotaList)) {
                newLeaveCheckVo.setQuotaSurplusHours(quotaList.stream()
                        .filter(Objects::nonNull)
                        .mapToInt(quota -> Optional.ofNullable(quota.getQuotaHours()).orElse(0) - Optional.ofNullable(quota.getQuotaAlreadyHours()).orElse(0))
                        .sum());
            }
            newLeaveCheckVo.setBShowBtn(true);
            newLeaveCheckVo.setClockInTime(getClockInTime(newLeaveCheckVo.getCompanyCode()));
            newLeaveCheckVo.setClockInMidTime(newLeaveCheckVo.getClockInTime().plusHours(5));
            newLeaveCheckVo.setClockOutMidTime(newLeaveCheckVo.getClockInTime().plusHours(4));
            newLeaveCheckVo.setClockOutTime(newLeaveCheckVo.getClockInTime().plusHours(9));
            newLeaveCheckVo.setCheckTime(LocalTime.parse(configMapper.getValueByCode("leave_check_time"), DateTimeFormatter.ofPattern("HH:mm:ss")));
        }
        return newLeaveCheckVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void leaveCommit(NewLeaveCommitInfoRequest request) {
        log.info("leaveCommit request:{}", request);

        SfaEmployeeInfoModel sfaEmployeeInfoEntity = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getId, request.getEmployeeInfoId()));
        if (Objects.isNull(sfaEmployeeInfoEntity)) {
            throw new ApplicationException("操作人信息查询失败");
        }
        if (!request.getPerson().equals(sfaEmployeeInfoEntity.getEmployeeId())) {
            throw new ApplicationException("只能自己请假");
        }
        if (DictCodeConstants.CLASSCD_LEAVE_TYPE_ITEMVALUE_1.equals(request.getLeaveType())) {
            if (StringUtils.isBlank(request.getWantLeaveNum())) {
                throw new ApplicationException("爱旺旺请假单号不能为空");
            }
            if (CollectionUtils.isEmpty(request.getWantImages())) {
                throw new ApplicationException("爱旺旺请假截图不能为空");
            }
        }

//        LocalTime clockInTime = getClockInTime(sfaEmployeeInfoEntity.getCompanyCode());

        // 当前时间
        LocalDateTime nowDateTime = LocalDateTime.now();
        LocalDate nowDate = nowDateTime.toLocalDate();
        LocalTime nowTime = nowDateTime.toLocalTime();

        // 请假开始时间
        LocalDateTime leaveStartTime = request.getLeaveStartTime();
        LocalDate leaveStartDate = leaveStartTime.toLocalDate();

        // 请假结束时间
        LocalDateTime leaveEndTime = request.getLeaveEndTime();
        LocalDate leaveEndDate = leaveEndTime.toLocalDate();

        //以开始日期为模板，计算考勤周期(请假不能跨考勤周期)
        //考勤周期开始日期
        LocalDate attendanceStartDate;
        //考勤周期结束日期
        LocalDate attendanceEndDate;
        if (leaveStartTime.getDayOfMonth() >= ATTENDANCE_START_DAY) {
            attendanceStartDate = leaveStartDate.withDayOfMonth(ATTENDANCE_START_DAY);
            attendanceEndDate = leaveStartDate.plusMonths(1).withDayOfMonth(ATTENDANCE_END_DAY);
        } else {
            attendanceStartDate = leaveStartDate.minusMonths(1).withDayOfMonth(ATTENDANCE_START_DAY);
            attendanceEndDate = leaveStartDate.withDayOfMonth(ATTENDANCE_END_DAY);
        }

        if (!leaveStartTime.isBefore(leaveEndTime)) {
            throw new ApplicationException("请假开始时间必须小于结束时间");
        }
        if (leaveStartDate.isBefore(nowDate)) {
            throw new ApplicationException("不可事后补假");
        }
//        if (leaveStartDate.isEqual(nowDate) && nowTime.isAfter(clockInTime)) {
//            throw new ApplicationException("当天请假只能在上班打卡时间之前");
//        }
        if (leaveStartDate.isBefore(attendanceStartDate) || leaveStartDate.isAfter(attendanceEndDate)) {
            throw new ApplicationException("请假时间需在考勤周期内（上月26日-本月25日）");
        }
        if (leaveEndDate.isBefore(attendanceStartDate) || leaveEndDate.isAfter(attendanceEndDate)) {
            throw new ApplicationException("请假时间需在考勤周期内（上月26日-本月25日）");
        }
        List<SfaLeave> existsList = sfaLeaveMapper.getExists(attendanceStartDate, attendanceEndDate, sfaEmployeeInfoEntity.getId());
        existsList.forEach(sfaLeave -> {
            LocalDate leaveStartDateExists = sfaLeave.getLeaveStartTime().toLocalDate();
            LocalDate leaveEndDateExists = sfaLeave.getLeaveEndTime().toLocalDate();
            if (!(leaveEndDate.isBefore(leaveStartDateExists) || leaveStartDate.isAfter(leaveEndDateExists))) {
                if (Objects.equals(sfaLeave.getLeaveType(), DictCodeConstants.CLASSCD_LEAVE_TYPE_ITEMVALUE_1) && Objects.equals(request.getLeaveType(), DictCodeConstants.CLASSCD_LEAVE_TYPE_ITEMVALUE_1)) {
                    if (leaveEndDate.isEqual(leaveStartDateExists)) {
                        if (!leaveEndTime.isBefore(sfaLeave.getLeaveStartTime())) {
                            throw new ApplicationException("请假时间有重叠");
                        }
                    } else if (leaveStartDate.isEqual(leaveEndDateExists)) {
                        if (!leaveStartTime.isAfter(sfaLeave.getLeaveEndTime())) {
                            throw new ApplicationException("请假时间有重叠");
                        }
                    } else {
                        throw new ApplicationException("请假日期有重叠");
                    }
                } else {
                    throw new ApplicationException("请假日期有重叠");
                }
            }
        });
//        if (Objects.nonNull(sfaLeaveMapper.checkExists(leaveStartTime, sfaEmployeeInfoEntity.getId()))) {
//            throw new ApplicationException("请勿重复申请");
//        }
        List<SfaLeaveQuotaEntity> quotaList = new ArrayList<>();
        if (Objects.equals(request.getLeaveType(), DictCodeConstants.CLASSCD_LEAVE_TYPE_ITEMVALUE_1)) {
            Integer quotaSurplusHours = 0;
            quotaList = sfaLeaveQuotaMapper.selectList(new LambdaQueryWrapper<SfaLeaveQuotaEntity>()
                    .eq(SfaLeaveQuotaEntity::getQuotaStatus, 1)
                    .eq(SfaLeaveQuotaEntity::getEmployeeInfoId, request.getEmployeeInfoId())
            );
            if (!CollectionUtils.isEmpty(quotaList)) {
                quotaSurplusHours = quotaList.stream()
                        .filter(Objects::nonNull)
                        .mapToInt(quota -> Optional.ofNullable(quota.getQuotaHours()).orElse(0) - Optional.ofNullable(quota.getQuotaAlreadyHours()).orElse(0))
                        .sum();
            }
            if (request.getLeaveHours() > quotaSurplusHours) {
                throw new ApplicationException("年假剩余额度不足！");
            }
        }

        //当前申请人在考勤周期内累计申请的小时数
        SfaLeave lastSfaLeave = sfaLeaveMapper.getLastestApprovalRecord(attendanceStartDate, attendanceEndDate, sfaEmployeeInfoEntity.getId());
        int monthAlreadyLeaveHours = 0;//当前周期内已请假小时数
        if (Objects.nonNull(lastSfaLeave)) {
            monthAlreadyLeaveHours = lastSfaLeave.getMonthAlreadyLeaveHours();
        }
        SfaLeave sfaLeave = new SfaLeave();
        sfaLeave.setLeaveType(request.getLeaveType());
        sfaLeave.setApplyEmployeeInfoId(request.getEmployeeInfoId());
        sfaLeave.setBusinessGroup(RequestUtils.getLoginInfo().getBusinessGroup());
        sfaLeave.setSubmitTime(nowDateTime);
        sfaLeave.setAttendanceStartDate(attendanceStartDate);
        sfaLeave.setAttendanceEndDate(attendanceEndDate);
        sfaLeave.setLeaveStartTime(leaveStartTime);
        sfaLeave.setLeaveEndTime(leaveEndTime);
        sfaLeave.setLeaveHours(request.getLeaveHours());
//        sfaLeave.setLeaveHours(8 * Math.toIntExact(LocalDateTimeUtils.betweenTwoTime(request.getLeaveStartTime(), request.getLeaveEndTime(), ChronoUnit.DAYS)));
        if (DictCodeConstants.CLASSCD_LEAVE_TYPE_ITEMVALUE_1.equals(request.getLeaveType())) {
            sfaLeave.setWantLeaveNum(request.getWantLeaveNum());
            sfaLeave.setWantImage(Optional.ofNullable(request.getWantImages()).map(list -> String.join(",", list)).orElse(null));
        }
        sfaLeave.setLeaveReason(request.getLeaveReason());
        sfaLeave.setAppendix(request.getAppendix());
        sfaLeave.setAppendixName(request.getAppendixName());
        sfaLeave.setImage(request.getImage());
        sfaLeave.setImageName(request.getImageName());
        sfaLeave.setMonthAlreadyLeaveHours(monthAlreadyLeaveHours);
        // 审核人员工表id（为空：总部营运岗审核）
        List<SfaEmployeeInfoModel> parentList = sfaEmployeeInfoMapper.queryParentList(sfaEmployeeInfoEntity.getPositionId());
        if (!CollectionUtils.isEmpty(parentList)) {
            sfaLeave.setAuditEmployeeInfoId(parentList.get(0).getId());
        }
        sfaLeave.setLeaveStatus(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_0));
        sfaLeave.init(sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
        sfaLeaveMapper.insert(sfaLeave);
        sfaLeave.setBusinessNum(LocalDateTimeUtils.formatTime(nowDateTime, "yyyyMMddHHmmss") + String.format("%05d", sfaLeave.getId()));
        sfaLeaveMapper.updateById(sfaLeave);

        SfaLeaveRecord sfaLeaveRecord = new SfaLeaveRecord();
        sfaLeaveRecord.setLeaveId(sfaLeave.getId());
        sfaLeaveRecord.setOperatorEmployeeId(sfaEmployeeInfoEntity.getEmployeeId());
        sfaLeaveRecord.setOperatorType(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_9));
        sfaLeaveRecord.setOperatorTime(nowDateTime);
        sfaLeaveRecord.init(sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
        sfaLeaveRecordMapper.insert(sfaLeaveRecord);

        SfaLeaveRecord leaveRecord = new SfaLeaveRecord();
        leaveRecord.setLeaveId(sfaLeave.getId());
        if (!CollectionUtils.isEmpty(parentList)) {
            leaveRecord.setOperatorEmployeeId(parentList.get(0).getEmployeeId());
        }
        leaveRecord.setOperatorType(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_0));
        leaveRecord.setOperatorTime(nowDateTime);
        leaveRecord.init(sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
        sfaLeaveRecordMapper.insert(leaveRecord);

        if (!CollectionUtils.isEmpty(quotaList)) {
            Map<Integer, SfaLeaveQuotaEntity> quotaMap = quotaList.stream()
                    .filter(e -> e.getQuotaType() != null)
                    .collect(Collectors.toMap(
                            SfaLeaveQuotaEntity::getQuotaType,
                            v -> v,
                            (existingValue, newValue) -> existingValue // 保留第一个出现的值
                    ));
            List<SfaLeaveQuotaRecordEntity> quotaRecordEntityList = new ArrayList<>();
            Integer leaveHours = request.getLeaveHours();
            leaveHours = reduceQuota(quotaMap, leaveHours, quotaRecordEntityList, sfaLeave.getId(), DictCodeConstants.CLASSCD_LEAVE_QUOTA_TYPE_ITEMVALUE_3, sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
            leaveHours = reduceQuota(quotaMap, leaveHours, quotaRecordEntityList, sfaLeave.getId(), DictCodeConstants.CLASSCD_LEAVE_QUOTA_TYPE_ITEMVALUE_4, sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
            leaveHours = reduceQuota(quotaMap, leaveHours, quotaRecordEntityList, sfaLeave.getId(), DictCodeConstants.CLASSCD_LEAVE_QUOTA_TYPE_ITEMVALUE_1, sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
            leaveHours = reduceQuota(quotaMap, leaveHours, quotaRecordEntityList, sfaLeave.getId(), DictCodeConstants.CLASSCD_LEAVE_QUOTA_TYPE_ITEMVALUE_2, sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
        }
    }

    private Integer reduceQuota(Map<Integer, SfaLeaveQuotaEntity> quotaMap, Integer leaveHours, List<SfaLeaveQuotaRecordEntity> quotaRecordEntityList, Long leaveId, Integer quotaType, String employeeId, String employeeName, LocalDateTime time) {
        if (leaveHours > 0) {
            if (quotaMap.containsKey(quotaType)) {
                SfaLeaveQuotaEntity quota = quotaMap.get(quotaType);
                if (Objects.nonNull(quota)) {
                    int surplusHours = Optional.ofNullable(quota.getQuotaHours()).orElse(0) - Optional.ofNullable(quota.getQuotaAlreadyHours()).orElse(0);
                    if (surplusHours > 0) {
                        SfaLeaveQuotaRecordEntity quotaRecordEntity = new SfaLeaveQuotaRecordEntity();
                        quotaRecordEntity.setLeaveId(leaveId);
                        quotaRecordEntity.setQuotaId(quota.getId());
                        if (surplusHours < leaveHours) {
                            quotaRecordEntity.setLeaveQuotaHours(surplusHours);
                            quota.setQuotaAlreadyHours(Optional.ofNullable(quota.getQuotaAlreadyHours()).orElse(0) + surplusHours);
                            leaveHours = leaveHours - surplusHours;
                        } else {
                            quotaRecordEntity.setLeaveQuotaHours(leaveHours);
                            quota.setQuotaAlreadyHours(Optional.ofNullable(quota.getQuotaAlreadyHours()).orElse(0) + leaveHours);
                            leaveHours = 0;
                        }
                        quota.update(employeeId, employeeName, time);
                        sfaLeaveQuotaMapper.updateById(quota);
                        quotaRecordEntity.init(employeeId, employeeName, time);
                        sfaLeaveQuotaRecordMapper.insert(quotaRecordEntity);
                        quotaRecordEntityList.add(quotaRecordEntity);
                    }
                }
            }
        }
        return leaveHours;
    }

    /*
     * 获取请假申请列表
     *
     */
    @Override
    public IPage<NewLeaveListVo> leaveList(NewLeaveListRequest request) {
        log.info("leaveList request:{}", request);
        CeoBusinessOrganizationPositionRelation relation = getCeoBusinessOrganizationPositionRelation(request.getPerson());
        //判断账号权限
        boolean role30 = checkRole30(request.getPerson());
        boolean role32 = checkRole32(request.getPerson());
        int auditType = role30 ? role32 ? 3 : 1 : role32 ? 2 : 0;
        SfaEmployeeInfoModel sfaEmployeeInfoEntity = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getEmployeeId, request.getPerson()));
        if (Objects.nonNull(sfaEmployeeInfoEntity)) {
            request.setEmployeeInfoId(sfaEmployeeInfoEntity.getId());
        } else {
            request.setEmployeeInfoId(null);
        }
        setDate(request);

        LoginModel loginInfo = RequestUtils.getLoginInfo();
        if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType()) && CommonUtil.StringUtils.isBlank(request.getOrganizationId())) {
            List<String> employeeOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
            if (CollectionUtils.isEmpty(employeeOrganizationIds)) {
                throw new ApplicationException("操作人组织信息获取失败");
            }
            request.setOrganizationIds(employeeOrganizationIds);
        }

        Page<NewLeaveListVo> page = new Page<>(request.getPage(), request.getRows());
        List<NewLeaveListVo> list = sfaLeaveMapper.queryLeaveList(page, request, auditType, relation.getBusinessGroup());
        if (!CollectionUtils.isEmpty(list)) {
            // 当前时间
            LocalDateTime nowDateTime = LocalDateTime.now();
            LocalDate nowDate = nowDateTime.toLocalDate();
            LocalTime nowTime = nowDateTime.toLocalTime();

            list.forEach(e -> {
                if (request.getSensitiveFlg()) {
                    e.setMobile(SensitiveInfoUtils.desensitizeMobile(e.getMobile()));
                }
                e.setPositionName(OrganizationPositionRelationEnums.getPositionName(e.getPostTypeId()));
                setBShowBtn(request.getEmployeeInfoId(), e, auditType, null, null, nowDate, nowTime);
            });
        }

        page.setRecords(list);
        return page;
    }

    private static void setDate(NewLeaveListRequest request) {
        // 计算日期
        Integer submitDateType = request.getSubmitDateType();
        if (Objects.nonNull(submitDateType)) {
            // 1.今天 2.昨天 3.本周 4.上周 5.本月 6.上月s
            LocalDate now = LocalDate.now();
            switch (submitDateType) {
                case 2:
                    request.setCommitStartDate(now.minusDays(1L).toString());
                    request.setCommitEndDate(now.minusDays(1L).toString());
                    break;
                case 3:
                    request.setCommitStartDate(now.with(DayOfWeek.MONDAY).toString());
                    request.setCommitEndDate(now.with(DayOfWeek.SUNDAY).toString());
                    break;
                case 4:
                    request.setCommitStartDate(now.minusWeeks(1L).with(DayOfWeek.MONDAY).toString());
                    request.setCommitEndDate(now.minusWeeks(1L).with(DayOfWeek.SUNDAY).toString());
                    break;
                case 5:
                    request.setCommitStartDate(now.with(TemporalAdjusters.firstDayOfMonth()).toString());
                    request.setCommitEndDate(now.with(TemporalAdjusters.lastDayOfMonth()).toString());
                    break;
                case 6:
                    request.setCommitStartDate(now.minusMonths(1L).with(TemporalAdjusters.firstDayOfMonth()).toString());
                    request.setCommitEndDate(now.minusMonths(1L).with(TemporalAdjusters.lastDayOfMonth()).toString());
                    break;
                default:
                    request.setCommitStartDate(now.toString());
                    request.setCommitEndDate(now.toString());
                    break;
            }
        }
    }

    @Override
    public NewLeaveListVo leaveDetail(NewLeaveDetailRequest request) {
        log.info("leaveDetail request:{}", request);

        CeoBusinessOrganizationPositionRelation relation = getCeoBusinessOrganizationPositionRelation(request.getPerson());
        //判断账号权限
        boolean role30 = checkRole30(request.getPerson());
        boolean role32 = checkRole32(request.getPerson());
        int auditType = role30 ? role32 ? 3 : 1 : role32 ? 2 : 0;
        SfaEmployeeInfoModel sfaEmployeeInfoEntity = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getEmployeeId, request.getPerson()));
        if (Objects.nonNull(sfaEmployeeInfoEntity)) {
            request.setEmployeeInfoId(sfaEmployeeInfoEntity.getId());
        } else {
            request.setEmployeeInfoId(null);
        }
//        SfaEmployeeInfoModel sfaEmployeeInfoEntity = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getEmployeeId, request.getPerson()).eq(SfaEmployeeInfoModel::getPositionId, relation.getPositionId()));
//        if (leaveAudit == 0) {
//            if (Objects.nonNull(sfaEmployeeInfoEntity)) {
//                request.setEmployeeInfoId(sfaEmployeeInfoEntity.getId());
//            }
//        }
        NewLeaveListVo detail = sfaLeaveMapper.queryLeaveDetail(request);
        if (Objects.nonNull(detail)) {
            if (request.getSensitiveFlg()) {
                detail.setMobile(SensitiveInfoUtils.desensitizeMobile(detail.getMobile()));
            }
            if (StringUtils.isNotBlank(detail.getWantImage())) {
                detail.setWantImages(Arrays.asList(detail.getWantImage().split(",")));
            }
            detail.setPositionName(OrganizationPositionRelationEnums.getPositionName(detail.getPostTypeId()));
            setBShowBtn(request.getEmployeeInfoId(), detail, auditType, null, null, null, null);
        }
        return detail;
    }

    private void setBShowBtn(Integer employeeInfoId, NewLeaveListVo detail, int auditType, SfaEmployeeInfoModel sfaEmployeeInfoEntity, String ClockInTimeWLMQ, LocalDate nowDate, LocalTime nowTime) {
        if (Objects.isNull(nowDate) || Objects.isNull(nowTime)) {
            // 当前时间
            LocalDateTime nowDateTime = LocalDateTime.now();
            nowDate = nowDateTime.toLocalDate();
            nowTime = nowDateTime.toLocalTime();
        }
        if (detail.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_0))) {
            if (Objects.nonNull(detail.getAuditEmployeeInfoId())) {
                if (detail.getAuditEmployeeInfoId().equals(employeeInfoId)) {
                    detail.setBShowBtn1(true);
                    detail.setBShowBtn2(true);
                }
            } else if (auditType == 1 || auditType == 3) {
                detail.setBShowBtn1(true);
                detail.setBShowBtn2(true);
            }
            if (detail.getApplyEmployeeInfoId().equals(employeeInfoId)) {
                detail.setBShowBtn3(true);
            }
        } else if (detail.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_4))) {
            if (auditType == 2 || auditType == 3) {
                detail.setBShowBtn1(true);
                detail.setBShowBtn2(true);
            }
            if (detail.getApplyEmployeeInfoId().equals(employeeInfoId)) {
                detail.setBShowBtn3(true);
            }
        } else if (detail.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_1))) {
            if (detail.getApplyEmployeeInfoId().equals(employeeInfoId)) {
//                LocalTime clockInTime = getClockInTime(sfaEmployeeInfoEntity.getCompanyCode(), ClockInTimeWLMQ);
                // 请假开始时间
                LocalDateTime leaveStartTime = detail.getLeaveStartTime();
                LocalDate leaveStartDate = leaveStartTime.toLocalDate();
                // 请假日期假期开始前的一天23:59前可以撤回
                if (leaveStartDate.isAfter(nowDate)) {
                    detail.setBShowBtn3(true);
                }
//                if (leaveStartDate.isEqual(nowDate) && nowTime.isBefore(clockInTime)) {
//                    detail.setBShowBtn3(true);
//                }
            }
        }
    }

    private CeoBusinessOrganizationPositionRelation getCeoBusinessOrganizationPositionRelation(String person) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation relation = checkCustomerService.getPersonInfo(person, loginInfo);
        if (Objects.isNull(relation)) {
            throw new ApplicationException("操作人岗位查询失败");
        }
        return relation;
    }

    @Override
    public List<NewLeaveRecordVo> leaveRecordList(String businessNum) {
        log.info("leaveRecordList request:{}", businessNum);
        SfaLeave sfaLeave = sfaLeaveMapper.selectOne(new LambdaQueryWrapper<SfaLeave>().eq(SfaLeave::getBusinessNum, businessNum).eq(SfaLeave::getDeleteFlag, 0));
        if (Objects.isNull(sfaLeave)) {
            throw new ApplicationException("申请单号不存在");
        }
        //审核流程
        return sfaLeaveRecordMapper.queryLeaveRecordList(sfaLeave.getId());
    }

    /*
     * 1、判断审核人是否有审核权限
     * 2、当前审核状态是否是待审核
     * 3、更新当前状态
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void leaveAudit(NewLeaveAuditRequest request) {
        log.info("leaveAudit request:{}", request);

        CeoBusinessOrganizationPositionRelation relation = getCeoBusinessOrganizationPositionRelation(request.getPerson());

        if (Objects.isNull(request.getBusinessNum()) && CollectionUtils.isEmpty(request.getBusinessNumList())) {
            throw new ApplicationException("表单编号不能为空");
        }
        Integer operatorType = request.getOperatorType() == 1 ? Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_1) : Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_2);

        if (StringUtils.isNotBlank(request.getBusinessNum())) {
            request.setBusinessNumList(Collections.singletonList(request.getBusinessNum()));
        }

        List<SfaLeave> sfaLeaveList = sfaLeaveMapper.queryAuditLeaveList(request.getBusinessNumList());
        if (CollectionUtils.isEmpty(sfaLeaveList)) {
            throw new ApplicationException("表单单号不存在");
        }
        List<Integer> pendingTypeList = Arrays.asList(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_0), Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_4));
        List<SfaLeaveRecord> sfaLeaveRecordList = sfaLeaveRecordMapper.selectList(new LambdaQueryWrapper<SfaLeaveRecord>()
                .in(SfaLeaveRecord::getLeaveId, sfaLeaveList.stream().map(SfaLeave::getId).collect(Collectors.toList()))
                .in(SfaLeaveRecord::getOperatorType, pendingTypeList)
                .eq(SfaLeaveRecord::getDeleteFlag, 0));
        if (CollectionUtils.isEmpty(sfaLeaveRecordList) || sfaLeaveRecordList.size() != sfaLeaveList.size()) {
            throw new ApplicationException("待审核记录获取失败");
        }

        //判断账号权限
        boolean role30 = checkRole30(request.getPerson());
        boolean role32 = checkRole32(request.getPerson());
        LocalDateTime nowLocalDateTime = LocalDateTime.now();

        sfaLeaveList.forEach(sfaLeave -> {
            if (!sfaLeave.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_0)) && !sfaLeave.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_4))) {
                throw new ApplicationException("当前请假的已审批，请勿重新提交");
            }
            if (sfaLeave.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_0))) {
                if (Objects.nonNull(sfaLeave.getAuditEmployeeInfoId()) && !sfaLeave.getAuditEmployeeId().equals(request.getPerson())) {
                    throw new ApplicationException("审核人不正确");
                } else if (Objects.isNull(sfaLeave.getAuditEmployeeInfoId()) && !role30) {
                    throw new ApplicationException("审核人不正确");
                }
            } else if (sfaLeave.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_4))) {
                if (!role32) {
                    throw new ApplicationException("审核人不正确");
                }
            }

//            if (Objects.isNull(sfaLeave.getAuditEmployeeInfoId())) {
//                sfaLeave.setAuditEmployeeInfoId(request.getEmployeeInfoId());
//            }
            int leaveStatus = 0;

            if (request.getOperatorType() == 1) {
                if (sfaLeave.getLeaveType() == 0) {
                    leaveStatus = 1;
                    sfaLeaveMapper.updateAttendanceMonthAlreadyLeaveHours(sfaLeave.getAttendanceStartDate(), sfaLeave.getAttendanceEndDate(), sfaLeave.getApplyEmployeeInfoId(), sfaLeave.getLeaveHours(), 1);
                } else {
                    if (sfaLeave.getLeaveStatus() == 0) {
                        leaveStatus = 4;

                        SfaLeaveRecord leaveRecord = new SfaLeaveRecord();
                        leaveRecord.setLeaveId(sfaLeave.getId());
                        leaveRecord.setOperatorType(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_4));
                        leaveRecord.setOperatorTime(nowLocalDateTime);
                        leaveRecord.init(request.getPerson(), relation.getEmployeeName(), nowLocalDateTime);
                        sfaLeaveRecordMapper.insert(leaveRecord);
                    } else {
                        leaveStatus = 1;
                        sfaLeaveMapper.updateAttendanceMonthAlreadyLeaveHours(sfaLeave.getAttendanceStartDate(), sfaLeave.getAttendanceEndDate(), sfaLeave.getApplyEmployeeInfoId(), sfaLeave.getLeaveHours(), 1);
                    }
                }
            } else if (request.getOperatorType() == 2) {
                leaveStatus = 2;
                NotifyPO po = new NotifyPO();
                String formatContent = MessageFormat.format("【{0}({1})】的{2}申请【审核驳回】，请查看！", sfaLeave.getApplyEmployeeName(), sfaLeave.getApplyEmployeeId(), sfaLeave.getLeaveTypeName());
                po.setTitle(formatContent);
                po.setType(NotifyTypeEnum.LEAVE.getType());
                po.setContent(formatContent);
                po.setCode("/leaveApprovalDetail?businessNum=" + sfaLeave.getBusinessNum());
                po.setEmployeeId(sfaLeave.getApplyEmployeeId());
                po.setCreateTime(nowLocalDateTime);
                po.setCreateBy("-1");
                po.setUpdateTime(nowLocalDateTime);
                po.setUpdateBy("-1");
                notifyMapper.insert(po);
                if (sfaLeave.getLeaveType() == 1) {
                    rollbackQuota(sfaLeave.getId(), request.getPerson(), relation.getEmployeeName(), nowLocalDateTime);
                }
            }

            SfaLeave sfaLeaveUpdate = new SfaLeave();
            sfaLeaveUpdate.setId(sfaLeave.getId());
            sfaLeaveUpdate.setLeaveStatus(leaveStatus);
            sfaLeaveUpdate.setAuditEmployeeInfoId(sfaLeave.getAuditEmployeeInfoId());
            sfaLeaveUpdate.update(request.getPerson(), relation.getEmployeeName(), nowLocalDateTime);
            sfaLeaveMapper.updateLeaveById(sfaLeaveUpdate);
        });
        sfaLeaveRecordList.forEach(sfaLeaveRecord -> {
            sfaLeaveRecord.setOperatorEmployeeId(request.getPerson());
            sfaLeaveRecord.setOperatorType(operatorType);
            sfaLeaveRecord.setOperatorTime(nowLocalDateTime);
            sfaLeaveRecord.setOperatorReason(request.getReason());
            sfaLeaveRecord.update(request.getPerson(), relation.getEmployeeName(), nowLocalDateTime);
        });

        newLeaveRecordService.saveOrUpdateBatch(sfaLeaveRecordList);
    }

    private void rollbackQuota(Long leaveId, String person, String employeeName, LocalDateTime nowLocalDateTime) {
        List<SfaLeaveQuotaRecordEntity> sfaLeaveQuotaRecordList = sfaLeaveQuotaRecordMapper.selectList(new LambdaQueryWrapper<SfaLeaveQuotaRecordEntity>()
                .eq(SfaLeaveQuotaRecordEntity::getLeaveId, leaveId)
                .eq(SfaLeaveQuotaRecordEntity::getDeleteFlag, 0));
        if (!CollectionUtils.isEmpty(sfaLeaveQuotaRecordList)) {
            Map<Long, Integer> quotaRecordMap = sfaLeaveQuotaRecordList.stream()
                    .filter(e -> Objects.nonNull(e.getQuotaId()))
                    .collect(Collectors.toMap(
                            SfaLeaveQuotaRecordEntity::getQuotaId,
                            SfaLeaveQuotaRecordEntity::getLeaveQuotaHours,
                            (existingValue, newValue) -> existingValue // 保留第一个出现的值
                    ));
            List<SfaLeaveQuotaEntity> quotaList = sfaLeaveQuotaMapper.selectList(new LambdaQueryWrapper<SfaLeaveQuotaEntity>()
                    .in(SfaLeaveQuotaEntity::getId, sfaLeaveQuotaRecordList.stream().map(SfaLeaveQuotaRecordEntity::getQuotaId).filter(Objects::nonNull).collect(Collectors.toList()))
            );
            if (!CollectionUtils.isEmpty(quotaList)) {
                quotaList.forEach(quota -> {
                    quota.setQuotaAlreadyHours(Optional.ofNullable(quota.getQuotaAlreadyHours()).orElse(0) - quotaRecordMap.getOrDefault(quota.getId(), 0));
                    quota.update(person, employeeName, nowLocalDateTime);
                    sfaLeaveQuotaMapper.updateById(quota);
                });
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void leaveRevocation(NewLeaveRevocationRequest request) {
        log.info("leaveRevocation request: {}", request);

        SfaEmployeeInfoModel sfaEmployeeInfoEntity = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getId, request.getEmployeeInfoId()));
        if (Objects.isNull(sfaEmployeeInfoEntity)) {
            throw new ApplicationException("操作人信息查询失败");
        }

        SfaLeave sfaLeave = sfaLeaveMapper.selectOne(new LambdaQueryWrapper<SfaLeave>().eq(SfaLeave::getBusinessNum, request.getBusinessNum()));
        if (Objects.isNull(sfaLeave)) {
            throw new ApplicationException("表单单号不存在");
        }
        if (!sfaLeave.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_0)) && !sfaLeave.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_1)) && !sfaLeave.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_4))) {
            throw new ApplicationException("当前请假的状态不可撤回");
        }
        if (!sfaLeave.getApplyEmployeeInfoId().equals(request.getEmployeeInfoId())) {
            throw new ApplicationException("只能撤回自己的请假单");
        }
        // 当前时间
        LocalDateTime nowDateTime = LocalDateTime.now();
        LocalDate nowDate = nowDateTime.toLocalDate();
        if (sfaLeave.getLeaveStatus().equals(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_1))) {
            // 请假开始时间
            LocalDateTime leaveStartTime = sfaLeave.getLeaveStartTime();
            LocalDate leaveStartDate = leaveStartTime.toLocalDate();
            if (!leaveStartDate.isAfter(nowDate)) {
                throw new ApplicationException("当天日期≥请假开始日期，不可以撤回请假");
            }
            sfaLeaveMapper.updateAttendanceMonthAlreadyLeaveHours(sfaLeave.getAttendanceStartDate(), sfaLeave.getAttendanceEndDate(), sfaLeave.getApplyEmployeeInfoId(), sfaLeave.getLeaveHours(), 2);
            sfaLeave.setMonthAlreadyLeaveHours(sfaLeave.getMonthAlreadyLeaveHours() - sfaLeave.getLeaveHours());
        }
        sfaLeave.setLeaveStatus(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_3));
        sfaLeave.update(sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
        sfaLeaveMapper.updateById(sfaLeave);

        SfaLeaveRecord sfaLeaveRecord = new SfaLeaveRecord();
        sfaLeaveRecord.setLeaveId(sfaLeave.getId());
        sfaLeaveRecord.setOperatorEmployeeId(sfaEmployeeInfoEntity.getEmployeeId());
        sfaLeaveRecord.setOperatorType(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_3));
        sfaLeaveRecord.setOperatorReason(request.getReason());
        sfaLeaveRecord.setOperatorTime(nowDateTime);
        sfaLeaveRecord.init(sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
        sfaLeaveRecordMapper.insert(sfaLeaveRecord);

        if (sfaLeave.getLeaveType() == 1) {
            rollbackQuota(sfaLeave.getId(), sfaEmployeeInfoEntity.getEmployeeId(), sfaEmployeeInfoEntity.getEmployeeName(), nowDateTime);
        }
    }

    private LocalTime getClockInTime(String companyCode) {
        return getClockInTime(companyCode, null);
    }

    private LocalTime getClockInTime(String companyCode, String clockInTimeWLMQ) {

        //默认标准打卡时间
        LocalTime clockInTime = ClockInTime1;
        if (Objects.nonNull(companyCode)) {
            if (Objects.isNull(clockInTimeWLMQ)) {
                clockInTimeWLMQ = configMapper.getValueByCode(CLOCK_IN_TIME_WLMQ);
                if (Objects.isNull(clockInTimeWLMQ)) {
                    throw new ApplicationException("请配置非标准打卡时间的分公司");
                }
            }
            if (clockInTimeWLMQ.contains(companyCode)) {
                clockInTime = ClockInTime2;
            }
        }
        return clockInTime;

    }

    private boolean checkRole30(String person) {
        List<String> account30List = getAccountList(30);
        return account30List.contains(person);
    }

    private boolean checkRole32(String person) {
        List<String> account32List = getAccountList(32);
        return account32List.contains(person);
    }

    private List<String> getAccountList(Integer roleId) {
        SAccountRequest accountRequest = new SAccountRequest();
        accountRequest.setRows(1000);
        accountRequest.setRoleId(roleId);
        List<AccountModel> accountList = accountMapper.selectList(accountRequest, RequestUtils.getChannel(), RequestUtils.getBusinessGroup());
        return accountList.stream().map(AccountModel::getEmployeeId).collect(Collectors.toList());
    }

}
