package com.wantwant.sfa.backend.interview.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.interview.command.Impl.*;
import com.wantwant.sfa.backend.interview.enums.ProcessResult;
import com.wantwant.sfa.backend.interview.enums.ProcessType;
import com.wantwant.sfa.backend.interview.model.DuplicateModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessConfigModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.interview.process.InterviewProcessDispatchService;
import com.wantwant.sfa.backend.interview.request.InterviewCancelRequest;
import com.wantwant.sfa.backend.interview.request.InterviewOperateRequest;
import com.wantwant.sfa.backend.interview.service.InterviewProbationService;
import com.wantwant.sfa.backend.interview.service.InterviewProcessService;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessConfigMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.market.model.OrganizationModel;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.core.Local;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.wantwant.sfa.backend.interview.service.InterviewService.ZW_HR_EMPLOYEE_ID_CODE;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/24/上午10:32
 */
@Service
@Slf4j
public class InterviewProcessServiceImpl implements InterviewProcessService {

    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private SfaInterviewProcessConfigMapper sfaInterviewProcessConfigMapper;
    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private CeoManagerInterviewOperationCommandImpl ceoManagerInterviewOperationCommand;
    @Autowired
    private CacheInterviewOperationCommandImpl cacheInterviewOperationCommand;
    @Autowired
    private InterviewProcessDispatchService interviewProcessDispatchService;
    @Autowired
    private FailedInterviewOperationCommandImpl failedInterviewOperationCommand;
    @Autowired
    private CloseInterviewOperationCommandImpl closeInterviewOperationCommand;
    @Autowired
    private ProvinceManagerInterviewCommandImpl provinceManagerInterviewCommand;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Resource
    private InterviewProbationService interviewProbationService;


    @Override
    public void interviewSubmit(InterviewOperateRequest request) {
        log.info("【面试提交】request:{}",request);
        Integer ceoType = request.getCeoType();
        Integer position = request.getPosition();

        Integer processResult = request.getProcessResult();
        // 获取面试记录
        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordMapper.selectOne(new QueryWrapper<SfaInterviewProcessRecordModel>()
                .eq("id", request.getInterviewProcessRecordId()));


        if (Objects.isNull(sfaInterviewProcessRecordModel) ||
                (StringUtils.isNotBlank(sfaInterviewProcessRecordModel.getProcessUserId())) && !sfaInterviewProcessRecordModel.getProcessUserId().equals(request.getProcessUserId())) {
            throw new ApplicationException("未找到面试记录");
        }

        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(request.getApplicationId());
        String idCardNum = applyMemberPo.getIdCardNum();
        if(StringUtils.isNotBlank(idCardNum) && request.getProcessResult() == 1){
            // 检查是否用重复信息
            List<DuplicateModel> duplicateModels = sfaEmployeeInfoMapper.checkDuplicate(request.getApplicationId(), applyMemberPo.getUserName(), applyMemberPo.getIdCardNum());
            if(!CollectionUtils.isEmpty(duplicateModels)){
                Optional<DuplicateModel> first = duplicateModels.stream().filter(f -> Objects.nonNull(f.getEmployeeStatus()) && f.getEmployeeStatus() <= 2).findFirst();
                if(first.isPresent()){
                    throw new ApplicationException("当前实名认证的身份已在职或试岗中");
                }
            }
        }

        // 待处理
        if(processResult == ProcessResult.PROCESSING.getResultCode()){
            cacheInterviewOperationCommand.execute(request);
            return;
        }
        // 面试未通过
        else if(processResult == ProcessResult.FAILED.getResultCode()){
            failedInterviewOperationCommand.execute(request);
            return;
        }


        // 放弃入职
        else if(processResult == ProcessResult.CLOSE.getResultCode()){
            closeInterviewOperationCommand.execute(request);
            return;
        }

        // 造旺总监
        else if((position == 2 || position == 6) && processResult == ProcessResult.PASS.getResultCode()){
            ceoManagerInterviewOperationCommand.execute(request);
            return;
        }


        // 战区/大区总监
        else if(position == 3 || position == 5){
            interviewProcessDispatchService.managerDispatch(request);
            return;
        }


        // 区域经理人
        else if(position == 4 && processResult == ProcessResult.PASS.getResultCode()){
            interviewProcessDispatchService.cityManagerDispatch(request);
            return;
        }

        // 业务BD
        else if((position == 7 || position == 8) && processResult == ProcessResult.PASS.getResultCode()){
            interviewProcessDispatchService.businessBDDispatch(request);
        }

        // 业务合伙人
        else if(position == 1 && (ceoType == CEO_TYPE || ceoType == CONTACT_TYPE || ceoType == CEO_PART_TYPE) && processResult == ProcessResult.PASS.getResultCode()){
            interviewProcessDispatchService.ceoDispatch(request);
            return;
        }
        // 企业合伙人
        else{
            interviewProcessDispatchService.businessCeoDispatch(request);
        }
    }

    @Override
    public SfaInterviewProcessRecordModel findNextProcessRecord(ProcessType nextProcessTypeByCode,InterviewOperateRequest request, SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel, SfaInterviewProcessModel sfaInterviewProcessModel) {

        log.info("【造旺合伙人面试】检查配置信息,areaCode：{},processType:{}",request.getAreaCode(),nextProcessTypeByCode.getProcessCode());

        // 运营审核直接返回
        if(nextProcessTypeByCode.getProcessCode() == ProcessType.ZB_VERIFY.getProcessCode() || nextProcessTypeByCode.getProcessCode() == ProcessType.PROBATION.getProcessCode()){
            return sfaInterviewProcessRecordModel;
        }


        // 检查当前流程是否以配置
        SfaInterviewProcessConfigModel sfaInterviewProcessConfigModel =
                sfaInterviewProcessConfigMapper.selectOne(
                        new QueryWrapper<SfaInterviewProcessConfigModel>()
                                .eq("company_code", request.getCompanyCode())
                                .eq("process_type", nextProcessTypeByCode.getProcessCode())
                                .eq("interview_type", 1)
                                .eq("status", 1));
        // 当前流程已配置直接返回
        if (Objects.nonNull(sfaInterviewProcessConfigModel)) {
            return sfaInterviewProcessRecordModel;
        }

        if (nextProcessTypeByCode.getProcessCode() == ProcessType.APPLY_ONBOARD_VAILD.getProcessCode()) {
            sfaInterviewProcessModel.setApplyOnboardTime(new Date());
            sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
        }

        if (nextProcessTypeByCode.getProcessCode() == ProcessType.REINTERVIEW.getProcessCode()) {
            sfaInterviewProcessModel.setInterviewTime(new Date());
            // v6.4.0新增需求，增加市场走访日期
            LocalDateTime now = LocalDateTime.now();
            sfaInterviewProcessModel.setMarketVisitStartTime(now);
            sfaInterviewProcessModel.setMarketVisitExpectTime(now);
            sfaInterviewProcessModel.setMarketVisitEndTime(now);

            sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
        }


        // 将当前节点设置为通过
        sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.PASS.getResultCode());
        sfaInterviewProcessRecordModel.setProcessDate(new Date());
        sfaInterviewProcessRecordModel.setCeoType(request.getCeoType());
        sfaInterviewProcessRecordModel.setBranchCode(sfaInterviewProcessRecordModel.getBranchCode());
        sfaInterviewProcessRecordModel.setBranchName(sfaInterviewProcessRecordModel.getBranchName());
        sfaInterviewProcessRecordModel.setAreaCode(sfaInterviewProcessRecordModel.getAreaCode());
        sfaInterviewProcessRecordModel.setAreaName(sfaInterviewProcessRecordModel.getAreaName());
        sfaInterviewProcessRecordModel.setCompanyName(sfaInterviewProcessRecordModel.getCompanyName());
        sfaInterviewProcessRecordModel.setCompanyCode(sfaInterviewProcessRecordModel.getCompanyCode());
        sfaInterviewProcessRecordModel.setSmallMarketCodes(sfaInterviewProcessRecordModel.getSmallMarketCodes());
        List<String> productionGroup = request.getProductionGroup();
        if(!CollectionUtils.isEmpty(productionGroup)){
            sfaInterviewProcessRecordModel.setProductionGroup(String.join(",", productionGroup));
        }
        List<String> smallMarketCodes = request.getSmallMarketCodes();
        if(!CollectionUtils.isEmpty(smallMarketCodes)){
            sfaInterviewProcessRecordModel.setSmallMarketCodes(String.join(",", smallMarketCodes));
        }

        sfaInterviewProcessRecordModel.setRecruitmentId(request.getRecruitmentId());
        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);


        // 没有配置则自动审核
        SfaInterviewProcessRecordModel nextRecord = new SfaInterviewProcessRecordModel();
        nextRecord.setProcessDate(new Date());
        nextRecord.setCeoType(request.getCeoType());
        nextRecord.setInterviewProcessId(sfaInterviewProcessRecordModel.getInterviewProcessId());
        nextRecord.setProcessType(nextProcessTypeByCode.getProcessCode());
        nextRecord.setProcessResult(ProcessResult.PASS.getResultCode());
        nextRecord.setProcessUserName("系统自动审核");
        nextRecord.setCreateTime(new Date());
        nextRecord.setProcessDate(new Date());
        if(!CollectionUtils.isEmpty(productionGroup)){
            nextRecord.setProductionGroup(String.join(",", productionGroup));
        }
        if(!CollectionUtils.isEmpty(smallMarketCodes)){
            nextRecord.setSmallMarketCodes(String.join(",", smallMarketCodes));
        }


        nextRecord.setPrevProcessId(sfaInterviewProcessRecordModel.getId());
        sfaInterviewProcessRecordMapper.insert(nextRecord);


        return nextRecord;
    }

    @Override
    @Transactional
    public void saveApplyInfo(InterviewOperateRequest request, ApplyMemberPo applyMemberPo,String areaName, String companyName) {
        BeanUtils.copyProperties(request,applyMemberPo);

        applyMemberPo.setWwPosition(request.getWwPosition());
        applyMemberPo.setUpdatedTime(LocalDateTime.now());
        applyMemberPo.setHoldCardUrl(request.getHoldCardUrl());
        applyMemberPo.setJobsType(request.getJobsType());
        applyMemberPo.setIdCardFrontUrl(request.getIdCardFrontUrl());
        applyMemberPo.setIdCardBackUrl(request.getIdCardBackUrl());
        applyMemberPo.setEmployId(request.getEmployeeId());
        applyMemberPo.setCompany(companyName);
        applyMemberPo.setPicUrl(request.getPicUrl());
        applyMemberPo.setBirthDate(request.getBirthDate());
        applyMemberPo.setArea(areaName);
        applyMemberPo.setWorkMode(request.getWorkMode());

        Integer ceoType = request.getCeoType();
        // 全职合伙人
        if(ceoType == 1){
            applyMemberPo.setCeoType(1);
            applyMemberPo.setJobsType(1);
        }
        else if(ceoType == 2){
            /** 企业合伙人保存信息 ⬇⬇ */
            applyMemberPo.setCeoType(request.getCeoType());
            applyMemberPo.setJobsType(2);
            applyMemberPo.setTeamCount(request.getTeamCount());
            applyMemberPo.setMotorCount(request.getMotorCount());
            applyMemberPo.setStreet(request.getStreet());
            applyMemberPo.setWarehouse(request.getWarehouse());
            applyMemberPo.setBankStatements(request.getBankStatements());
            applyMemberPo.setTerminalCount(request.getTerminalCount());
            applyMemberPo.setInvoicingSys(request.getInvoicingSys());
            applyMemberPo.setBusinessLicensePhoto(request.getBusinessLicensePhoto());
            applyMemberPo.setPartnerCompanyName(request.getPartnerCompanyName());
            /** 企业合伙人保存信息 ⬆⬆ */
        }
        // 承揽合伙人
        else if(ceoType == 3){
            applyMemberPo.setCeoType(3);
            applyMemberPo.setJobsType(2);
        }
        else if(ceoType == 4){
            applyMemberPo.setCeoType(1);
            applyMemberPo.setJobsType(2);
        }
        else if(ceoType == 7 || ceoType == 8 || ceoType == 9){
            PositionEnum positionEnum = PositionEnum.BUSINESS_BD;
            if(ceoType == 8){
                positionEnum = PositionEnum.BUSINESS_BD_PART_TIME;
            }else if(ceoType == 9){
                positionEnum = PositionEnum.BUSINESS_BD_CONTRACT;
            }

            applyMemberPo.setJobsType(positionEnum.getJobsType());
            applyMemberPo.setPosition(positionEnum.getPosition());
            applyMemberPo.setCeoType(positionEnum.getCeoType());
        }else{
            applyMemberPo.setPosition(request.getPosition());
        }







        applyMemberPo.setGraduationDate(request.getGraduationDate());
        applyMemberPo.setGraduateSchool(request.getGraduateSchool());
        applyMemberPo.setSpecializing(request.getSpecializing());
        applyMemberPo.setCurrentStatus(request.getCurrentStatus());

        List<String> productionGroup = request.getProductionGroup();
        if(!CollectionUtils.isEmpty(productionGroup)){
            applyMemberPo.setProductionGroupCode(String.join(",",productionGroup));
        }

        List<Integer> marketChannel = request.getMarketChannel();
        if(!CollectionUtils.isEmpty(marketChannel)){
            List<String> collect = marketChannel.stream().map(String::valueOf).collect(Collectors.toList());
            applyMemberPo.setMarketChannel(String.join(",",collect));
        }

        applyMemberMapper.updateById(applyMemberPo);
    }

    @Override
    public SfaInterviewProcessRecordModel commonRecordBuilder(SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel, ProcessType nextProcessTypeByCode, InterviewOperateRequest request) {
        SfaInterviewProcessRecordModel model = new SfaInterviewProcessRecordModel();

        model.setBranchCode(request.getBranchCode());
        String branchName = organizationMapper.getOrganizationName(request.getBranchCode());
        model.setBranchName(branchName);

        model.setCompanyCode(request.getCompanyCode());
        String companyName = organizationMapper.getOrganizationName(request.getCompanyCode());
        model.setCompanyName(companyName);

        model.setAreaCode(request.getAreaCode());
        String areaName =  organizationMapper.getOrganizationName(request.getAreaCode());
        model.setAreaName(areaName);

        List<String> regionCodes = request.getRegionCodes();
        if (!CollectionUtils.isEmpty(regionCodes)) {}

        if (!CollectionUtils.isEmpty(request.getRegionCodes())) {
            model.setRegionMarketCodes(String.join(",", request.getRegionCodes()));
        }
        model.setSalaryStructureId(request.getSalaryStructureId());
        model.setCreateTime(new Date());
        model.setRecruitmentId(request.getRecruitmentId());
        model.setInterviewProcessId(sfaInterviewProcessRecordModel.getInterviewProcessId());
        model.setPrevProcessId(sfaInterviewProcessRecordModel.getId());
        model.setProcessType(nextProcessTypeByCode.getProcessCode());
        return model;
    }

    @Override
    public CeoBusinessOrganizationPositionRelation choosePerson(String organizationId) {
        if (StringUtils.isBlank(organizationId)) {
            throw new ApplicationException("获取审核人组织信息失败");
        }

        if (!organizationId.contains("ZB")) {
            String organizationType = organizationMapper.getOrganizationType(organizationId);
            if ("area".equals(organizationType)) {
                return ceoBusinessOrganizationPositionRelationMapper.selectOne(
                        new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                .eq("organization_id", organizationId)
                                .eq("channel", RequestUtils.getChannel()));
            }

            String parentId = organizationMapper.getOrganizationParentId(organizationId);

            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = null;
            if (StringUtils.isNotBlank(parentId)) {
                // 大区经理信息
                ceoBusinessOrganizationPositionRelation =
                        ceoBusinessOrganizationPositionRelationMapper.selectOne(
                                new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                        .eq("organization_id", parentId)
                                        .eq("channel", RequestUtils.getChannel()));
            }

            if (Objects.nonNull(ceoBusinessOrganizationPositionRelation)
                    && StringUtils.isNotBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())) {
                return ceoBusinessOrganizationPositionRelation;
            }
        }

        String employeeId = configMapper.getValueByCode(ZW_HR_EMPLOYEE_ID_CODE);

        if (StringUtils.isBlank(employeeId)) {
            throw new ApplicationException("获取审核人信息失败");
        }

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation =
                ceoBusinessOrganizationPositionRelationMapper.selectOne(
                        new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                .eq("employee_id", employeeId)
                                .eq("channel", RequestUtils.getChannel()));

        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
            throw new ApplicationException("获取审核人信息失败");
        }

        return ceoBusinessOrganizationPositionRelation;
    }

    @Override
    @Transactional
    public SfaInterviewProcessRecordModel operatorAutoProcess(SfaInterviewProcessModel sfaInterviewProcessModel, SfaInterviewProcessRecordModel newProcessRecord) {
        SfaInterviewProcessRecordModel hrProcessRecord = new SfaInterviewProcessRecordModel();
        BeanUtils.copyProperties(newProcessRecord,hrProcessRecord,
                "processUserId","processUserName","organizationId","processDate","prevProcessId","nextProcessId",
                "processType","processResult","createTime","comment"
        );
        hrProcessRecord.setCreateTime(new Date());
        hrProcessRecord.setProcessType(ProcessType.getNextProcessTypeByCode(newProcessRecord.getProcessType()).getProcessCode());
        hrProcessRecord.setProcessResult(ProcessResult.PROCESSING.getResultCode());
        hrProcessRecord.setPrevProcessId(newProcessRecord.getId());
        sfaInterviewProcessRecordMapper.insert(hrProcessRecord);

        newProcessRecord.setProcessUserId("ROOT");
        newProcessRecord.setProcessUserName("系统自动");
        newProcessRecord.setProcessDate(new Date());
        newProcessRecord.setProcessResult(ProcessResult.PASS.getResultCode());
        newProcessRecord.setNextProcessId(hrProcessRecord.getId());
        sfaInterviewProcessRecordMapper.updateById(newProcessRecord);
        return hrProcessRecord;
    }

    @Override
    @Transactional
    public void cancel(InterviewCancelRequest interviewCancelRequest) {
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectOne(new LambdaQueryWrapper<ApplyMemberPo>().eq(ApplyMemberPo::getUserMobile, interviewCancelRequest.getMobile())
                .orderByDesc(ApplyMemberPo::getId).last("limit 1"));
        if(Objects.isNull(applyMemberPo)){
            throw new ApplicationException("报名信息获取失败");
        }


        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new LambdaQueryWrapper<SfaInterviewProcessModel>().eq(SfaInterviewProcessModel::getApplicationId, applyMemberPo.getId()).last("limit 1"));
        if(Objects.isNull(sfaInterviewProcessModel)){
            throw new ApplicationException("面试流程获取失败");
        }

        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordMapper.selectById(sfaInterviewProcessModel.getInterviewRecordId());
        if(Objects.isNull(sfaInterviewProcessRecordModel)){
            throw new ApplicationException("面试记录获取失败");
        }

        Integer processType = sfaInterviewProcessModel.getProcessType();
        Integer processResult = sfaInterviewProcessModel.getProcessResult();

        if(processResult == 1 || processResult == 4 || processResult == 5){
            throw new ApplicationException("无法处理当前流程");
        }


        if(processType > 3){
            String processName = "入职申请";
            if(processType == 6){
                processName = "入职办理";
            }
            if(processType > 6){
                processName = "离职";
            }
            throw new ApplicationException("流程进行到"+ processName +"步骤，取消报名暂未成功");
        }




        PositionEnum positionEnum = PositionEnum.getEnum(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
        if(positionEnum.getId() == PositionEnum.CITY_MANAGER.getId() && processType == 3 && processResult == 3){
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getApplicationId, applyMemberPo.getId()).last("limit 1"));
            if(Objects.isNull(sfaEmployeeInfoModel)){
                throw new ApplicationException("员工信息获取失败");
            }
            // 关闭试岗账号
            interviewProbationService.failedProcess(sfaInterviewProcessModel,sfaInterviewProcessRecordModel,sfaEmployeeInfoModel,5,interviewCancelRequest.getReason());
        }else{
            sfaInterviewProcessRecordModel.setProcessResult(5);
            sfaInterviewProcessRecordModel.setProcessDate(new Date());
            sfaInterviewProcessRecordModel.setComment(interviewCancelRequest.getReason());
            sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);
            sfaInterviewProcessModel.setProcessResult(sfaInterviewProcessRecordModel.getProcessResult());
            sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
        }


    }

}
