package com.wantwant.sfa.backend.domain.businessBd.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.businessBd.DO.BusinessBdSalaryControlDO;
import com.wantwant.sfa.backend.domain.businessBd.DO.BusinessBdSalaryControlLogDO;
import com.wantwant.sfa.backend.domain.businessBd.DO.SalaryPackageRatio;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.salary.request.BusinessBDAllocatedRequest;
import com.wantwant.sfa.backend.salary.request.BusinessBDControlSearch;
import com.wantwant.sfa.backend.salary.request.BusinessBDSalaryPackageConfig;
import com.wantwant.sfa.backend.salary.request.SalaryControlSearchRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDControlDetailVO;
import com.wantwant.sfa.backend.salary.vo.BusinessBDSalaryVo;
import com.wantwant.sfa.backend.salary.vo.BusinessBDQuotaConfigVo;
import com.wantwant.sfa.backend.salary.vo.SalaryPackageRateLogVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 业务BD额度管控服务接口
 */
public interface BusinessBdSalaryControlService {

    /**
     * 新增或修改业务BD额度管控
     *
     * @param businessBdSalaryControlDO 业务BD额度管控DO
     * @return 业务BD额度管控DO
     */
    BusinessBdSalaryControlDO saveOrUpdate(BusinessBdSalaryControlDO businessBdSalaryControlDO);

    /**
     * 根据组织ID和年月获取业务BD额度管控
     *
     * @param organizationId 组织ID
     * @param theYearMonth 年月
     * @return 业务BD额度管控DO
     */
    BusinessBdSalaryControlDO getByOrganizationIdAndYearMonth(String organizationId, String theYearMonth);

    /**
     * 记录业务BD额度管控日志
     *
     * @param businessBdSalaryControlLogDO 业务BD额度管控日志DO
     */
    void saveLog(BusinessBdSalaryControlLogDO businessBdSalaryControlLogDO);

    /**
     * 获取业务BD额度管控
     *
     * @param salaryControlSearchRequest
     * @return
     */
    IPage<BusinessBDQuotaConfigVo> getBusinessBDQuotaControl(SalaryControlSearchRequest salaryControlSearchRequest);

    /**
     * 导出业务BD管控额度（不分页）
     */
    void exportBusinessBDQuotaControl(SalaryControlSearchRequest salaryControlSearchRequest);

    /**
     * 业务BD已用额度
     *
     * @param businessBDAllocatedRequest
     * @return
     */
    IPage<BusinessBDSalaryVo> getAllocatedBusinessBDHistory(BusinessBDAllocatedRequest businessBDAllocatedRequest);

    /**
     * 导出业务BD已用额度（不分页）
     */
    void exportAllocatedBusinessBDHistory(BusinessBDAllocatedRequest businessBDAllocatedRequest);

    /**
     * 业务BD额度管控查询
     *
     * @param businessBDControlSearch
     * @return
     */
    IPage<BusinessBDControlDetailVO> selectBusinessBDControl(BusinessBDControlSearch businessBDControlSearch);


    /**
     * 获取修改费用率记录
     *
     * @return
     */
    List<SalaryPackageRateLogVO> getSalaryPackageLog(String departmentId);

    /**
     * 导出费用包
     */
    void exportBusinessBDControl(BusinessBDControlSearch businessBDControlSearch);

    /**
     * 导入费用包
     *
     * @param list
     * @param processUserDO
     */
    void importSalaryPackageRatio(List<SalaryPackageRatio> list, ProcessUserDO processUserDO);

    /**
     * 设置费用率
     *
     * @param businessBDSalaryPackageConfig
     * @param processUserDO
     */
    void setBusinessBDSalaryPackageConfig(BusinessBDSalaryPackageConfig businessBDSalaryPackageConfig, ProcessUserDO processUserDO);

    /**
     * 计算过期费用
     *
     * @param date
     * @param companyCode
     * @return
     */
    BigDecimal calculateExpiredQuota(LocalDate date, String companyCode);

}