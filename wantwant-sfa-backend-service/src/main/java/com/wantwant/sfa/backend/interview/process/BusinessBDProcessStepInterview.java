package com.wantwant.sfa.backend.interview.process;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.application.businessBd.BusinessBdOrgQuotaAppService;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.AuditService;
import com.wantwant.sfa.backend.common.BusinessGroupEnum;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.businessBd.DO.BusinessBdEmployeeQuotaOperationValue;
import com.wantwant.sfa.backend.domain.businessBd.enums.QuotaOperationTypeEnum;
import com.wantwant.sfa.backend.domain.emp.DO.EstablishSearchDO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdConfigPO;
import com.wantwant.sfa.backend.domain.emp.service.IBusinessBDRuleService;
import com.wantwant.sfa.backend.interview.dto.BusinessBDRetainDTO;
import com.wantwant.sfa.backend.interview.dto.JobPositionChangeDto;
import com.wantwant.sfa.backend.interview.dto.JoiningCompanyConfigDto;
import com.wantwant.sfa.backend.interview.dto.SalaryPaymentTypeModel;
import com.wantwant.sfa.backend.interview.entity.BusinessBdControlEntity;
import com.wantwant.sfa.backend.interview.enums.ProcessResult;
import com.wantwant.sfa.backend.interview.enums.ProcessType;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.interview.request.InterviewOperateRequest;
import com.wantwant.sfa.backend.interview.service.IBusinessBDService;
import com.wantwant.sfa.backend.interview.service.InterviewProcessService;
import com.wantwant.sfa.backend.interview.service.InterviewService;
import com.wantwant.sfa.backend.interview.vo.SalaryStructureVo;
import com.wantwant.sfa.backend.mapper.ApplyMemberMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.businessBD.BusinessBdControlMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.mapper.organizationGoal.OrganizationGoalMapper;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.model.organizationGoal.OrganizationGoalPO;
import com.wantwant.sfa.backend.rabbitMQ.QueueConstant;
import com.wantwant.sfa.backend.rabbitMQ.RabbitMQSender;
import com.wantwant.sfa.backend.salary.service.ISalaryMiddlewareService;
import com.wantwant.sfa.backend.service.ApplyMemberService;
import com.wantwant.sfa.backend.service.EmployeeSalaryService;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.service.MemberExperienceService;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.ROOTConnectorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/11/07/下午6:10
 */
@Component
@Slf4j
public class BusinessBDProcessStepInterview implements BiConsumer<InterviewOperateRequest, SfaInterviewProcessRecordModel> {

    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private MemberExperienceService experienceService;
    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Autowired
    private InterviewProcessService interviewProcessService;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private AuditService auditService;
    @Autowired
    private ApplyMemberService applyMemberService;
    @Autowired
    private OpenAccountProcess openAccountProcess;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private InterviewService interviewService;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private IBusinessBDService businessBDService;
    @Autowired
    private EmployeeSalaryService salaryService;
    @Autowired
    private ROOTConnectorUtil rootConnectorUtil;
    @Resource
    private IBusinessBDRuleService businessBDRuleService;
    @Resource
    private ICheckCustomerService checkCustomerService;
    @Resource
    private BusinessBdOrgQuotaAppService businessBdOrgQuotaAppService;
    @Resource
    private ISalaryMiddlewareService salaryMiddlewareService;

    @Override
    @Transactional
    public void accept(InterviewOperateRequest request, SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel) {

        log.info("【business db submit】request:{}",request);
        // 获取报名信息
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(request.getApplicationId());
        if (Objects.isNull(applyMemberPo)) {
            throw new ApplicationException("未找到申请记录");
        }
        // 检查是否有操作过
        Integer processResult = sfaInterviewProcessRecordModel.getProcessResult();
        if (ProcessResult.PASS.getResultCode() == processResult
                || ProcessResult.FAILED.getResultCode() == processResult
                || ProcessResult.CLOSE.getResultCode() == processResult) {
            throw new ApplicationException("无法处理该数据");
        }

        // 面试主表信息获取
        Integer interviewProcessId = sfaInterviewProcessRecordModel.getInterviewProcessId();

        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectById(interviewProcessId);
        if (Objects.isNull(sfaInterviewProcessModel)) {
            throw new ApplicationException("未找到面试记录");
        }

        // 保存当前修改记录，保存关联对象，支付方式
        saveRecord(sfaInterviewProcessRecordModel,request);

        // 修改applyMember的ceoType
        PositionEnum positionEnum = PositionEnum.BUSINESS_BD;
        // 7.全职业务BD 8.兼职业务BD 9.承揽业务BD 10.客户主任
        Integer position = request.getCeoType();
        if(position == 8){
            positionEnum = PositionEnum.BUSINESS_BD_PART_TIME;
        }else if(position == 9){
            positionEnum = PositionEnum.BUSINESS_BD_CONTRACT;
        }else if(position == 10){
            positionEnum = PositionEnum.CUSTOMER_DIRECTOR;
        }

        applyMemberPo.setJobsType(positionEnum.getJobsType());
        applyMemberPo.setPosition(positionEnum.getPosition());
        applyMemberPo.setCeoType(positionEnum.getCeoType());

        // 更具组织选择编织检查模式
        BusinessBdConfigPO businessBdConfigPO = businessBDRuleService.getConfig(applyMemberPo.getBranchOrganizationId());
        if(Objects.isNull(businessBdConfigPO)){
            throw new ApplicationException("当前组织未配置规则");
        }

        Integer checkType = businessBdConfigPO.getCheckType();
        if(BusinessGroupEnum.O.getBusinessGroupId() != RequestUtils.getBusinessGroup() &&
                BusinessGroupEnum.P.getBusinessGroupId() != RequestUtils.getBusinessGroup() ) {
            List<Integer> contextEmpIdList = request.getContextEmpIdList();
            if(CollectionUtils.isEmpty(contextEmpIdList)){
                throw new ApplicationException("请选择服务对象");
            }
            Integer serverEmployeeInfoId = contextEmpIdList.stream().findFirst().get();

            String theYearMonth = LocalDate.now().toString().substring(0,7);

            BigDecimal salary = BigDecimal.ZERO;
            Integer salaryStructureId = request.getSalaryStructureId();
            if(Objects.nonNull(salaryStructureId)){
                salary = salaryService.selectTotalSalaryById(salaryStructureId);
            }

            EstablishSearchDO establishSearch = EstablishSearchDO.builder().serverEmployeeInfoId(serverEmployeeInfoId)
                    .applyId(request.getApplicationId())
                    .positionEnum(positionEnum)
                    .theYearMonth(theYearMonth)
                    .businessGroup(applyMemberPo.getBusinessGroup())
                    .changeServer(false)
                    .newSalary(salary)
                    .departmentId(applyMemberPo.getBranchOrganizationId())
                    .checkType(checkType).build();
            // 检查编制数是否已满
            businessBDRuleService.checkOvershootEstablished(establishSearch);

            if(Objects.nonNull(salaryStructureId)){
                salaryMiddlewareService.cancel(request.getApplicationId());
                // 保存薪资中间信息
                salaryMiddlewareService.addSalaryMiddleware(request.getApplicationId(),1,null,salaryStructureId);
            }

        }


        // 保存修改的申请信息
        interviewProcessService.saveApplyInfo(request, applyMemberPo, sfaInterviewProcessRecordModel.getAreaName(),sfaInterviewProcessRecordModel.getCompanyName());



        // 保存工作经历
        experienceService.batchSave(request.getExperiences(), request.getApplicationId());

        // 创建下级节点
        Integer processType = sfaInterviewProcessRecordModel.getProcessType();
        ProcessType nextProcessType = processType == ProcessType.INTERVIEW.getProcessCode()? ProcessType.REINTERVIEW : ProcessType.DO_ONBOARD;
        SfaInterviewProcessRecordModel nextProcess = createNextProcess(request,sfaInterviewProcessRecordModel,nextProcessType);

        // 绑定流程
        bindProcess(nextProcess,sfaInterviewProcessRecordModel,sfaInterviewProcessModel);

        if(StringUtils.isNotBlank(request.getRecommendOnboardTime())){
            sfaInterviewProcessModel.setRecommendOnboardTime(LocalDate.parse(request.getRecommendOnboardTime()).atStartOfDay());
        }


        if(processType == ProcessType.INTERVIEW.getProcessCode()){
            sfaInterviewProcessModel.setInterviewTime(new Date());
            sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
        }


        // 如果是2面,承揽或兼职BD需要开通账号
        if(processType == ProcessType.REINTERVIEW.getProcessCode()){
            if(positionEnum.getId() == PositionEnum.BUSINESS_BD_PART_TIME.getId() || positionEnum.getId() == PositionEnum.BUSINESS_BD_CONTRACT.getId()){

                JoiningCompanyConfigDto joiningCompanyConfigDto = sfaEmployeeInfoMapper.selectJoiningCompany(applyMemberPo.getCompanyOrganizationId(), applyMemberPo.getPosition());

                // 设置入职公司
                String joiningCompanyName = "";
                String contractCompany = "";
                // 开通账号
                if(Objects.nonNull(joiningCompanyConfigDto)){
                    request.setContractCompany(joiningCompanyConfigDto.getContractCompany());
                    request.setJoiningCompany(joiningCompanyConfigDto.getDeptName());
                    joiningCompanyName = joiningCompanyConfigDto.getDeptName();
                    contractCompany = joiningCompanyConfigDto.getContractCompany();
                }
                openAccountProcess.synEmployee(request, applyMemberPo);

                // 设置二面时间
                sfaInterviewProcessModel.setReinterviewTime(new Date());
                sfaInterviewProcessModel.setOnboardTime(new Date());

                // 如果是承揽或兼职BD，6-0的节点设置为完成
                nextProcess.setProcessDate(new Date());
                nextProcess.setProcessResult(ProcessResult.PASS.getResultCode());
                nextProcess.setProcessUserId("ROOT");
                nextProcess.setProcessUserName("系统自动");
                sfaInterviewProcessRecordMapper.updateById(nextProcess);

                // 获取employeeInfo信息
                SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getApplicationId, applyMemberPo.getId()));
                sfaEmployeeInfoModel.setWorkMode(applyMemberPo.getWorkMode());
                sfaEmployeeInfoModel.setEmployeeStatus(2);
                sfaEmployeeInfoModel.setOnboardTime(LocalDate.now().atStartOfDay());


//                sfaEmployeeInfoModel.setJoiningCompany(joiningCompanyName);
                sfaEmployeeInfoModel.setContractCompany(contractCompany);
                sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);

                // 绑定合伙人
                BusinessBDRetainDTO businessBDRetainDTO = new BusinessBDRetainDTO();
                businessBDRetainDTO.setProcessUserId(request.getEmployeeId());
                businessBDRetainDTO.setOrganizationId(applyMemberPo.getBranchOrganizationId());
                businessBDRetainDTO.setEmployeeInfoId(sfaEmployeeInfoModel.getId());
                businessBDRetainDTO.setRetainEmpIds(request.getContextEmpIdList());
                businessBDService.retainServer(businessBDRetainDTO);

                // 绑定薪资
                int paymentType = 1;
                if(Objects.nonNull(request.getPaymentType())){
                    paymentType = request.getPaymentType();
                }
                salaryService.addSalaryByEmpId(sfaEmployeeInfoModel,sfaEmployeeInfoModel.getId(),request.getSalaryStructureId(), request.getProcessUserId(), LocalDate.now(),paymentType);


                if(position == 9){
                    // 业务BD调用旺铺薪资发放类型接口
                    SalaryPaymentTypeModel salaryPaymentTypeModel = new SalaryPaymentTypeModel();
                    salaryPaymentTypeModel.setMemberKey(sfaEmployeeInfoModel.getMemberKey());
                    salaryPaymentTypeModel.setEnableTime(LocalDate.now().toString());
                    if(paymentType == 1){
                        salaryPaymentTypeModel.setSalaryType(1);
                    }else{
                        salaryPaymentTypeModel.setSalaryType(0);
                    }
                    rootConnectorUtil.salaryPaymentType(salaryPaymentTypeModel);

                }

                // 设置入职公司
//                rootConnectorUtil.updateSigningCompany(sfaEmployeeInfoModel.getMemberKey(),joiningCompanyName);



                // 推送调整额度
                CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getProcessUserId(), RequestUtils.getLoginInfo());
                if(2 == checkType){
                    BusinessBdEmployeeQuotaOperationValue businessBdEmployeeQuotaOperationValue = BusinessBdEmployeeQuotaOperationValue.builder().employeeInfoId(sfaEmployeeInfoModel.getId())
                            .curPositionId(PositionEnum.getPositionId(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition()))
                            .type(QuotaOperationTypeEnum.ENTRY.getCode())
                            .organizationId(applyMemberPo.getBranchOrganizationId())
                            .userId(personInfo.getEmployeeId())
                            .userName(personInfo.getEmployeeName())
                            .remark("")
                            .overStaffHandle(false).build();
                    businessBdOrgQuotaAppService.adjustQuotaByEmployeeOperation(businessBdEmployeeQuotaOperationValue);
                }


                // 发送入职短信
                interviewService.sendMessage(
                        sfaEmployeeInfoModel.getCompanyName(),
                        positionEnum.getPositionName(),
                        sfaEmployeeInfoModel.getEmployeeName(),
                        sfaEmployeeInfoModel.getMobile(),
                        ProcessType.DO_ONBOARD.getProcessCode());
            }else{
                // 设置入职公司
                JoiningCompanyConfigDto joiningCompanyConfigDto = sfaEmployeeInfoMapper.selectJoiningCompany(applyMemberPo.getCompanyOrganizationId(), applyMemberPo.getPosition());
                if(Objects.nonNull(joiningCompanyConfigDto)){
                    String deptName = joiningCompanyConfigDto.getDeptName();
                    // 全职BD当前节点设置为6-3
                    if(StringUtils.isNotBlank(deptName)){
                        applyMemberPo.setJoiningCompany(deptName);
                        nextProcess.setProcessResult(ProcessResult.PROCESSING.getResultCode());
                        sfaInterviewProcessRecordMapper.updateById(nextProcess);
                    }
                }
            }

            sfaInterviewProcessModel.setProcessResult(nextProcess.getProcessResult());
            sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
        }


        // 同步信息到EHR
        applyMemberService.syn(
                applyMemberPo,
                request.getBranchCode(),
                sfaInterviewProcessModel.getProcessType(),
                sfaInterviewProcessModel.getProcessResult(),
                StringUtils.EMPTY,
                applyMemberPo.getHighestEducation(),
                applyMemberPo.getIdCardNum(),
                applyMemberPo.getBirthDate(),
                sfaInterviewProcessModel.getRecommendOnboardTime(),
                null);
    }



    private void bindProcess(SfaInterviewProcessRecordModel nextProcess, SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel, SfaInterviewProcessModel sfaInterviewProcessModel) {
        sfaInterviewProcessModel.setInterviewRecordId(nextProcess.getId());
        sfaInterviewProcessModel.setProcessType(nextProcess.getProcessType());
        sfaInterviewProcessModel.setProcessResult(nextProcess.getProcessResult());
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);

        sfaInterviewProcessRecordModel.setProcessResult(1);
        sfaInterviewProcessRecordModel.setProcessDate(new Date());
        sfaInterviewProcessRecordModel.setNextProcessId(nextProcess.getId());
        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);

        nextProcess.setPrevProcessId(sfaInterviewProcessRecordModel.getId());
        sfaInterviewProcessRecordMapper.updateById(nextProcess);
    }

    private SfaInterviewProcessRecordModel createNextProcess(InterviewOperateRequest request,SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel,ProcessType nextProcessTypeByCode) {


        SfaInterviewProcessRecordModel model =interviewProcessService.commonRecordBuilder(sfaInterviewProcessRecordModel, nextProcessTypeByCode, request);
        model.setContextEmp(sfaInterviewProcessRecordModel.getContextEmp());
        model.setAttitudeIntention(request.getAttitudeIntentionScore());
        model.setExperienceAbility(request.getExperienceAbilityScore());
        model.setCustomerResources(request.getCustomerResourcesScore());
        model.setPaymentType(request.getPaymentType());
        if(nextProcessTypeByCode.getProcessCode() == ProcessType.REINTERVIEW.getProcessCode()){
            // 获取审核人信息
            SelectAuditDto selectAuditDto = new SelectAuditDto();
            selectAuditDto.setBusinessGroup(RequestUtils.getBusinessGroup());
            selectAuditDto.setChannel(RequestUtils.getChannel());
            selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
            selectAuditDto.setCurrentOrganizationId(request.getCompanyCode());

            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = auditService.chooseAuditPerson(selectAuditDto);
            model.setOrganizationId(ceoBusinessOrganizationPositionRelation.getOrganizationId());
            model.setProcessUserId(ceoBusinessOrganizationPositionRelation.getEmployeeId());
            model.setProcessUserName(ceoBusinessOrganizationPositionRelation.getEmployeeName());
        }else{
            String zbOrganizationIdByBusinessGroup = organizationMapper.getZbOrganizationIdByBusinessGroup(RequestUtils.getBusinessGroup());
            model.setOrganizationId(zbOrganizationIdByBusinessGroup);
        }



        model.setProcessResult(ProcessResult.NOT_PROCESS.getResultCode());


        model.setCeoType(request.getCeoType());


        sfaInterviewProcessRecordMapper.insert(model);
        return model;
    }

    private void saveRecord(SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel, InterviewOperateRequest request) {
        sfaInterviewProcessRecordModel.setPaymentType(request.getPaymentType());
        if(Objects.nonNull(request.getContextEmpIdList())) {
            String contextEmp = String.join(",", request.getContextEmpIdList().stream().map(String::valueOf).collect(Collectors.toList()));
            sfaInterviewProcessRecordModel.setContextEmp(contextEmp);
        }
        sfaInterviewProcessRecordModel.setAttitudeIntention(request.getAttitudeIntentionScore());
        sfaInterviewProcessRecordModel.setExperienceAbility(request.getExperienceAbilityScore());
        sfaInterviewProcessRecordModel.setCustomerResources(request.getCustomerResourcesScore());
        sfaInterviewProcessRecordModel.setPaymentType(request.getPaymentType());
        sfaInterviewProcessRecordModel.setProcessResult(1);
        sfaInterviewProcessRecordModel.setComment(request.getComment());
        sfaInterviewProcessRecordModel.setProcessDate(new Date());
        List<String> sku = request.getSku();
        if(!CollectionUtils.isEmpty(sku)){
            sfaInterviewProcessRecordModel.setSku(String.join(",",sku));
        }

        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);
    }
}
