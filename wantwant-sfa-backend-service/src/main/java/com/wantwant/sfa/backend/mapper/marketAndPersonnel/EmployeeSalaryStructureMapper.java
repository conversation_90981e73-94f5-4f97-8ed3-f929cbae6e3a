package com.wantwant.sfa.backend.mapper.marketAndPersonnel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wantwant.sfa.backend.domain.businessBd.DO.EmpSalaryDO;
import com.wantwant.sfa.backend.domain.businessBd.DO.OrgSalaryDO;
import com.wantwant.sfa.backend.domain.emp.DO.SalaryDO;
import com.wantwant.sfa.backend.marketAndPersonnel.request.StructureQueryRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.StructureVO;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryStructurePO;
import com.wantwant.sfa.backend.salary.model.OrganizationUsedSalaryModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 合伙人薪资结构 Mapper
 *
 * @since 2022-04-20
 */
@Mapper
public interface EmployeeSalaryStructureMapper extends BaseMapper<EmployeeSalaryStructurePO> {

    List<StructureVO> listStructure(@Param("params") StructureQueryRequest request);

    EmployeeSalaryStructurePO selectIncomplete(@Param("organizationId") String organizationId,@Param("startDate") String startDate);


    StructureVO selectStructureByDate(@Param("organizationId") String companyCode, @Param("date") LocalDate date, @Param("position") Integer position, @Param("grade") String grade);

    SalaryDO selectSalaryInfo(@Param("employeeInfoId") Integer employeeInfoId);

    List<OrganizationUsedSalaryModel> selectOnBoardBusinessBDSalary(@Param("deptCodes") List<String> deptCodes,@Param("excludeEmployeeInfoIds")List<Integer> excludeEmployeeInfoIds);

    List<OrganizationUsedSalaryModel> selectPendingBusinessBDSalary(@Param("deptCodes") List<String> deptCodes,@Param("applyId") Integer applyId);

    /**
     * 获取异动中的BD
     *
     * @param deptCodes
     * @companyCodes
     */
    List<EmpSalaryDO> selectTransactionBD(@Param("companyCodes")List<String> companyCodes,@Param("theYearMonth")String theYearMonth);

    /**
     * 查询入职过程中的业务BD薪资合计
     *
     * @param companyCodes
     * @param theYearMonth
     * @return
     */
    List<OrgSalaryDO> selectOnBoardProcessingSalary(@Param("companyCodes") List<String> companyCodes, @Param("theYearMonth") String theYearMonth,@Param("excludeApplyId")Integer excludeApplyId);

    /**
     * 获取业务BD异动中人员薪资合计
     *
     * @param transactionEmpList
     * @return
     */
    List<OrgSalaryDO> selectTransferringSalary(@Param("transactionEmpList") List<EmpSalaryDO> transactionEmpList);

}
