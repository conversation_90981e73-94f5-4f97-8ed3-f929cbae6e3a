package com.wantwant.sfa.backend.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
@Getter
public class ResourceConfig {

    @Value("${resource.list.resource.name.pc:PC}")
    private String resourceListPcResourceName;

    @Value("${resource.list.resource.name.app:APP}")
    private String resourceListAppResourceName;
}
