package com.wantwant.sfa.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.businessBd.DO.BusinessSalaryDetail;
import com.wantwant.sfa.backend.model.ZWSalaryModel;
import com.wantwant.sfa.backend.salary.request.BusinessBDAllocatedRequest;
import com.wantwant.sfa.backend.salary.request.SalarySchemeRequest;
import com.wantwant.sfa.backend.salary.request.SearchAllocatedHistoryRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDSalaryVo;
import com.wantwant.sfa.backend.salary.vo.SalaryAllocatedVo;
import com.wantwant.sfa.backend.salary.vo.SalarySchemeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SalaryMapper extends BaseMapper<ZWSalaryModel> {


    List<SalarySchemeVo> getSalaryScheme(SalarySchemeRequest salarySchemeRequest);


    int getSalarySchemeCount(SalarySchemeRequest salarySchemeRequest);


    List<SalaryAllocatedVo> getAllocatedHistory(SearchAllocatedHistoryRequest searchAllocatedHistoryRequest);

    List<SalaryAllocatedVo> getGroupCompanyAndDepartmentAndProvinceAllocatedHistory(@Param("businessGroup") Integer businessGroup);


    List<BusinessSalaryDetail> getAllocatedBusinessBDHistory(@Param("page") IPage<BusinessBDSalaryVo> page, @Param("req") BusinessBDAllocatedRequest businessBDAllocatedRequest);

    List<BusinessSalaryDetail> selectBdTransferByIds(List<Integer> employeeInfoIds, String theYearMonth);
}
