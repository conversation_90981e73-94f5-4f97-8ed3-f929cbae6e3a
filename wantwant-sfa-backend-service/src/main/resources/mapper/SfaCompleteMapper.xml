<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wantwant.sfa.backend.mapper.complete.SfaCompleteMapper">

    <sql id="where_list">
        and sc.delete_flag = 0

        <if test="params.completeNum != null and params.completeNum != '' ">
            and scr.complete_num like CONCAT('%',#{params.completeNum},'%')
        </if>
        <if test="params.completeDate != null">
            and scr.complete_date = #{params.completeDate}
        </if>
        <if test="params.completeStartDate != null">
            and scr.complete_date &gt;= #{params.completeStartDate}
        </if>
        <if test="params.completeEndDate != null">
            and scr.complete_date &lt;= #{params.completeEndDate}
        </if>
        <if test="params.employeeInfo != null and params.employeeInfo != '' ">
            and (sei.employee_id like CONCAT('%',#{params.employeeInfo},'%') or sei.employee_name like CONCAT('%',#{params.employeeInfo},'%') or sei.mobile like CONCAT('%',#{params.employeeInfo},'%'))
        </if>
        <if test="params.positionType != null">
            <choose>
                <when test="params.positionType == 361">
                    and spr.position_type_id = 3 and spr.type = 6 and spr.post_type = 1
                </when>
                <when test="params.positionType == 362">
                    and spr.position_type_id = 3 and spr.type = 6 and spr.post_type = 2
                </when>
                <when test="params.positionType == 372">
                    and spr.position_type_id = 3 and spr.type = 7 and spr.post_type = 2
                </when>
                <otherwise>
                    and spr.position_type_id = #{params.positionType}
                </otherwise>
            </choose>

        </if>
        <if test="params.employeeInfoId != null">
            and sc.employee_info_id = #{params.employeeInfoId}
        </if>
        <if test="params.employeeStatus != null">
            and sc.employee_status = #{params.employeeStatus}
        </if>
        <if test="params.completeStatus != null">
            and sc.complete_status = #{params.completeStatus}
        </if>
        <if test="params.auditStatus != null">
            and sc.audit_status = #{params.auditStatus}
        </if>
        <if test="params.completeTimeoutTime != null">
            and scr.complete_end_time &lt;= #{params.completeTimeoutTime}
        </if>
        <if test="params.timeout != null">
            and sc.timeout = #{params.timeout}
        </if>
        <if test="params.auditStatusList != null and params.auditStatusList.size>0">
            and sc.audit_status in
            <foreach collection="params.auditStatusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="params.completeNumList != null and params.completeNumList.size>0">
            and sc.complete_num in
            <foreach collection="params.completeNumList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>


    </sql>

    <select id="queryCompleteList" resultType="com.wantwant.sfa.backend.complete.vo.CompleteListVo">
        SELECT
            scr.id as ruleId,
            scr.complete_num,
            scr.complete_date,
            CONVERT_TZ(scr.complete_start_time, @@global.time_zone, #{params.timezone}) as complete_start_time,
            CONVERT_TZ(scr.complete_end_time, @@global.time_zone, #{params.timezone}) as complete_end_time,

            sc.id,
            sc.employee_info_id,
            sc.employee_status,
            sc.complete_status,
            CONVERT_TZ(sc.complete_time, @@global.time_zone, #{params.timezone}) as complete_time,
            sc.longitude,
            sc.latitude,
            sc.image,
            sc.image_name,
            sc.audit_employee_id,
            sc.audit_status,
            sc.reason,
            sc.face_similar_score,
            sc.sign_up_pic_url,

            spr.business_group,
            spr.organization_code as organizationId,
            spr.organization_code,
<!--            cbo.organization_name,-->
            spr.area_name,
            spr.varea_name,
            spr.province_name,
            spr.company_name,
            spr.department_name,
            spr.position_type_id,
            case
            when spr.position_type_id = 7 then '总部'
            when spr.position_type_id = 1 then '战区督导'
            when spr.position_type_id = 12 then '大区总监'
            when spr.position_type_id = 11 then '省区总监'
            when spr.position_type_id = 2 then '区域总监'
            when spr.position_type_id = 10 then '区域经理'
            when spr.position_type_id = 3 and spr.type = 6 and spr.post_type = 1 then '全职业务BD'
            when spr.position_type_id = 3 and spr.type = 6 and spr.post_type = 2 then '兼职业务BD'
            when spr.position_type_id = 3 and spr.type = 7 and spr.post_type = 2 then '承揽业务BD'
            else '合伙人' end as positionName,
<!--            cbpt.position_name,-->

            sei.employee_name,
            sei.employee_id,
            CONVERT_TZ(sei.onboard_time, @@global.time_zone, #{params.timezone}) as onboard_time,
            sam.pic_url as avatar,
            spr.part_time

        FROM sfa_complete sc
        INNER JOIN sfa_complete_rule scr ON scr.id = sc.rule_id
        INNER JOIN sfa_position_relation spr on spr.employee_info_id = sc.employee_info_id and spr.delete_flag = 0 and spr.status = 1
                                                <if test="params.businessGroup != null">
                                                    and spr.business_group = #{params.businessGroup}
                                                </if>
                                                <if test="params.partTime != null">
                                                    and spr.part_time = #{params.partTime}
                                                </if>
        INNER JOIN sfa_employee_info sei ON sei.id = sc.employee_info_id
        LEFT JOIN sfa_apply_member sam on sam.id = sei.application_id

        <if test=" params.organizationId != null and params.organizationId != '' ">
        INNER JOIN ceo_business_organization_tree cbot on cbot.organization_id = spr.organization_code and cbot.organization_parent_id = #{params.organizationId}
        </if>
        <if test="params.organizationIds != null and params.organizationIds.size()>0">
        INNER JOIN ceo_business_organization_tree cbot1 on cbot1.organization_id = spr.organization_code
            <foreach collection="params.organizationIds" item="item" open="and cbot1.organization_parent_id in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <where>
            <include refid="where_list"/>
        </where>ORDER BY
        <choose>
            <when test="null != params.sortName and params.sortName =='completeEndTime'">scr.complete_end_time</when>
            <otherwise>
                sc.complete_time
            </otherwise>
        </choose>
        <choose>
            <when test="null!= params.sortType and params.sortType!=''">${params.sortType}</when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <update id="updateLeaveNotComplete">
        UPDATE sfa_complete sc
        INNER JOIN sfa_complete_rule scr ON scr.id = sc.rule_id and scr.complete_date = #{params.completeDate}
        INNER JOIN sfa_leave sl ON sl.apply_employee_info_id = sc.employee_info_id and sl.leave_status = 1 AND sl.delete_flag = 0
        AND date_format(sl.leave_start_time,'%Y-%m-%d')&lt;= #{params.completeDate}
        AND date_format(sl.leave_end_time,'%Y-%m-%d')&gt;= #{params.completeDate}
        SET sc.complete_status = 2 ,sc.timeout = 0 ,sc.employee_status = 1 ,sc.update_time = now()
        <where>
            sc.delete_flag = 0
            and sc.complete_status = 0
        </where>
    </update>

    <sql id="mapCompleteAllInfo">
        scr.complete_num,
        scr.complete_date,
        CONVERT_TZ(scr.complete_start_time, @@global.time_zone, #{params.timezone}) as completeStartTime,
        CONVERT_TZ(scr.complete_end_time, @@global.time_zone, #{params.timezone}) as completeEndTime,

        sc.complete_status,
        CONVERT_TZ(sc.complete_time, @@global.time_zone, #{params.timezone}) as completeTime,
        CONCAT(sc.province,sc.city,sc.district,IFNULL(sc.address, '')) as address,
        sc.longitude,
        sc.latitude,
        sc.image,
        sc.image_name,
        sc.audit_status,
        sc.reason,
        sei.employee_name
    </sql>
    <select id="selectCompleteListForMap" resultType="com.wantwant.sfa.backend.complete.vo.CompleteListVo">
        SELECT
            <include refid="mapCompleteAllInfo"/>
        FROM sfa_complete sc
        INNER JOIN sfa_complete_rule scr ON scr.id = sc.rule_id
        INNER JOIN sfa_employee_info sei ON sei.id = sc.employee_info_id
        <where>
            sc.delete_flag = 0
            and scr.complete_start_time &gt;= CONVERT_TZ(#{params.attendanceStartDateTime},#{params.timezone},@@global.time_zone)
            and scr.complete_start_time &lt;= CONVERT_TZ(#{params.attendanceEndDateTime},#{params.timezone},@@global.time_zone)
            and sc.employee_info_id = #{params.employeeInfoId}
        </where>
        group by sc.id
    </select>

    <select id="selectCompleteListForMapSame" resultType="com.wantwant.sfa.backend.complete.vo.CompleteListVo">
        SELECT
            <include refid="mapCompleteAllInfo"/>
        FROM sfa_complete sc
        INNER JOIN sfa_complete_rule scr ON scr.id = sc.rule_id
        INNER JOIN sfa_employee_info sei ON sei.id = sc.employee_info_id
        <where>
            sc.delete_flag = 0
            and scr.complete_start_time &gt;= CONVERT_TZ(#{params.attendanceStartDateTime},#{params.timezone},@@global.time_zone)
            and scr.complete_start_time &lt;= CONVERT_TZ(#{params.attendanceEndDateTime},#{params.timezone},@@global.time_zone)
            <foreach collection="params.sameEmployeeInfoIdList" open=" and sc.employee_info_id in (" close=")" separator="," item="item">
                #{item}
            </foreach>
        </where>
        group by sc.id
    </select>

    <select id="selectCompleteListForDaily" resultType="com.wantwant.sfa.backend.complete.vo.CompleteListVo">
        SELECT

        scr.complete_num,
        scr.complete_date,

        sc.image,
        sc.image_name,
        sc.audit_status,
        sc.reason,
        sc.employee_info_id,

        sei.employee_name

        FROM sfa_complete sc
        INNER JOIN sfa_complete_rule scr ON scr.id = sc.rule_id
        INNER JOIN sfa_employee_info sei ON sei.id = sc.employee_info_id

        <where>
            sc.delete_flag = 0
            and scr.complete_date = #{params.theDate}
            <if test="null != params.employeeInfoIdList and params.employeeInfoIdList.size()>0 ">
                <foreach collection="params.employeeInfoIdList" open=" and sc.employee_info_id in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryCompleteTodayLatest" resultType="com.wantwant.sfa.backend.complete.vo.CompleteTodayLatestListVo">
        SELECT
        scr.complete_num,
        scr.complete_date,
        scr.complete_start_time,
        scr.complete_end_time,

        sc.id,
        sc.complete_status,
        sc.complete_time,
        CONCAT_WS('', sc.province ,sc.city, sc.district,sc.address) as fullAddress,
        sc.image,
        sc.image_name,
        sc.audit_status,
        sc.reason,
        sc.biz_audit_status,
        sc.biz_reason

        FROM sfa_complete sc
        INNER JOIN sfa_complete_rule scr ON scr.id = sc.rule_id
        <where>
            sc.delete_flag = 0
            and scr.complete_date = #{params.completeDate}
            and sc.employee_info_id = #{params.employeeInfoId}
            <if test="params.type != null and params.type == 1">
                and scr.complete_start_time &lt;= now()
                and scr.complete_end_time &gt;= now()
                and sc.timeout = 0
                and sc.complete_status = 0
            </if>
        </where>
        order by scr.complete_start_time
    </select>

    <select id="queryCompleteDetail" resultType="com.wantwant.sfa.backend.complete.vo.CompleteDetailVo">
        SELECT
        scr.complete_num,
        scr.complete_date,
        scr.complete_start_time,
        scr.complete_end_time,

        sc.id,
        sc.complete_status,
        sc.audit_status,

        sei.employee_id,
        sei.employee_name

        FROM sfa_complete sc
        INNER JOIN sfa_complete_rule scr ON scr.id = sc.rule_id
        INNER JOIN sfa_employee_info sei ON sei.id = sc.employee_info_id
        <where>
            sc.delete_flag = 0
            and sc.id = #{params.id}
            and scr.complete_num = #{params.completeNum}
            and sc.employee_info_id = #{params.employeeInfoId}
        </where>
    </select>

    <select id="queryCompleteDetailById" resultType="com.wantwant.sfa.backend.complete.vo.CompleteDetailVo">
        SELECT
        scr.complete_num,
        scr.complete_date,
        scr.complete_start_time,
        scr.complete_end_time,

        sc.id,
        sc.complete_status,
        sc.audit_status,

        sei.employee_id,
        sei.employee_name

        FROM sfa_complete sc
        INNER JOIN sfa_complete_rule scr ON scr.id = sc.rule_id
        INNER JOIN sfa_employee_info sei ON sei.id = sc.employee_info_id
        <where>
            sc.delete_flag = 0
            and sc.id = #{id}
        </where>
    </select>

    <select id="queryLatestCompleteList" resultType="com.wantwant.sfa.backend.complete.vo.CompleteListVo">
        SELECT
        sc.employee_info_id,
        sc.complete_time,
        sc.image
        FROM sfa_complete sc
        inner join(
            SELECT
            sc.employee_info_id,
            max(sc.complete_time) AS completeTime
            FROM sfa_complete sc
            <where>
                sc.delete_flag = 0
                and sc.image is not null
                and sc.complete_time is not null
                <if test="employeeInfoIdList != null and employeeInfoIdList.size()>0">
                    <foreach collection="employeeInfoIdList" item="item" open="and sc.employee_info_id in (" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            </where>
            GROUP BY sc.employee_info_id
        ) sc_max
        on sc.employee_info_id = sc_max.employee_info_id
        and sc.complete_time = sc_max.completeTime
    </select>

    <select id="queryLatestComplete" resultType="com.wantwant.sfa.backend.complete.vo.CompleteListVo">
        SELECT
        sc.employee_info_id,
        sc.complete_time,
        sc.image
        FROM sfa_complete sc
        <where>
            sc.delete_flag = 0
            and sc.image is not null
            and sc.complete_time is not null
            and sc.employee_info_id = #{employeeInfoId}
        </where>
        order by sc.complete_time desc
        limit 1
    </select>


</mapper>