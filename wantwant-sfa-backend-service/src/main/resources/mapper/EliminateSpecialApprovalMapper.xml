<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wantwant.sfa.backend.mapper.interview.EliminateSpecialApprovalMapper">

    <select id="selectSpecialApproval" resultType="java.lang.Integer">
        select
            COUNT(id)
        from
            sfa_eliminate_special_approval
        where
            employee_info_id = #{employeeInfoId}
            and delete_flag = 0
    </select>


    <select id="checkSpecialApprovalDuringEvaluationPeriod" resultType="Integer">
        select count(*) from sfa_eliminate_special_approval
        <where>
            and employee_info_id = #{employeeInfoId}
            and delete_flag = 0
            <foreach collection="currentQuarterMonths" item="item" open="and assess_month in (" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectSpecialApprovalList"
            resultType="com.wantwant.sfa.backend.replacing.vo.ReplacingWarningSpecialApprovalVo">
        SELECT
            sam.id as applyId,
            sesa.type,
            sesa.assess_month
        FROM sfa_eliminate_special_approval sesa
        inner join sfa_employee_info sei on sei.id = sesa.employee_info_id
        inner join sfa_apply_member sam on sam.id = sei.application_id
        WHERE
            sesa.delete_flag = 0
            and employee_info_id = #{employeeInfoId}
        ORDER BY
            sesa.assess_month DESC,
            sesa.create_time DESC;
    </select>
</mapper>