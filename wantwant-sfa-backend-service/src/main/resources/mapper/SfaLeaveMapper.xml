<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wantwant.sfa.backend.mapper.leave.SfaLeaveMapper">

    <sql id="where_list_verify">
        and sl.delete_flag = 0
        and sl.business_group = #{businessGroup}
        <if test="params.commitStartDate != null and params.commitStartDate != '' ">
            <bind name="commitStartDate" value="params.commitStartDate + ' 00:00:00'"/>
            and sl.submit_time >= #{commitStartDate}
        </if>
        <if test="params.commitEndDate != null and params.commitEndDate != '' ">
            <bind name="commitEndDate" value="params.commitEndDate + ' 23:59:59'"/>
            and sl.submit_time &lt;= #{commitEndDate}
        </if>
        <if test="params.employeeInfo != null and params.employeeInfo != '' ">
            and (sei.employee_name like CONCAT('%',#{params.employeeInfo},'%') or sei.mobile like CONCAT('%',#{params.employeeInfo},'%'))
        </if>
        <if test="params.businessNum != null and params.businessNum != '' ">
            and sl.business_num = #{params.businessNum}
        </if>
        <if test="params.leaveType != null">
            and sl.leave_type = #{params.leaveType}
        </if>

        <choose>
            <when test="params.type != null  and params.type == 1">
                <if test="auditType != null and auditType == 0">
                    and sl.leave_status = 0
                    and sl.audit_employee_info_id = #{params.employeeInfoId}
                </if>
                <if test="auditType != null and auditType == 1">
                    and sl.leave_status = 0
                    and sl.audit_employee_info_id is null
                </if>
                <if test="auditType != null and auditType == 2">
                    and sl.leave_type = 1
                    and sl.leave_status = 4
                </if>
                <if test="auditType != null and auditType == 3">
                    and ((sl.leave_status = 0 and sl.audit_employee_info_id is null)
                    or (sl.leave_type = 1 and sl.leave_status = 4))
                </if>
            </when>
            <when test="params.type != null  and params.type == 2">
                <if test="params.leaveStatus != null">
                    and sl.leave_status = #{params.leaveStatus}
                </if>
                <if test="auditType != null and auditType == 0">
                    and sl.leave_status != 0
                    and sl.audit_employee_info_id = #{params.employeeInfoId}
                </if>
                <if test="auditType != null and auditType == 1">
                    and sl.leave_status != 0
                    and sl.audit_employee_info_id is null
                </if>
                <if test="auditType != null and auditType == 2">
                    and sl.leave_type = 1
                    and sl.leave_status not in (0,4)
                </if>
                <if test="auditType != null and auditType == 3">
                    and ((sl.leave_status != 0 and sl.audit_employee_info_id is null)
                    or (sl.leave_type = 1 and sl.leave_status not in (0,4)))
                </if>
            </when>
            <when test="params.type != null  and params.type == 3">
                <if test="params.leaveStatus != null">
                    and sl.leave_status = #{params.leaveStatus}
                </if>
                and sl.apply_employee_info_id = #{params.employeeInfoId}
            </when>
            <otherwise>
                <if test="params.leaveStatus != null">
                    and sl.leave_status = #{params.leaveStatus}
                </if>
            </otherwise>
        </choose>
    </sql>

    <select id="queryLeaveList" resultType="com.wantwant.sfa.backend.leave.vo.NewLeaveListVo">
        SELECT
            sam.pic_url as avatar,
            sl.business_num ,
            sl.submit_time,
            spr.organization_code as organizationId,
            case
                when spr.position_type_id = 1 then spr.area_name
                when spr.position_type_id = 12 then spr.varea_name
                when spr.position_type_id = 11 then spr.province_name
                when spr.position_type_id = 2 then spr.company_name
                when spr.position_type_id = 10 then spr.department_name
                when spr.position_type_id = 3 then spr.department_name
                else ''
            end as organizationName,
            spr.area_name,
            spr.varea_name as vareaOrganizationName,
            spr.province_name as provinceOrganizationName,
            spr.company_code,
            spr.company_name,
            spr.department_name,
            IF(spr.position_type_id = 3, concat(spr.position_type_id,spr.type, spr.post_type), spr.position_type_id) as postTypeId,
            sei.employee_name,
            sei.employee_id,
            sei.mobile,
            sl.leave_type,
            ltcd.item_content as leave_type_name,
            sl.leave_start_time,
            sl.leave_end_time,
            sl.leave_hours,
            round(sl.leave_hours/8,1) as leave_days,
            sl.month_already_leave_hours,
            round(sl.month_already_leave_hours/8,1) as month_already_leave_days,
            sl.want_leave_num,
            sl.want_image,
            sl.leave_reason,
            sl.leave_status,
            lscd.item_content as leave_status_name,
            sl.appendix,
            sl.appendix_name,
            sl.image,
            sl.image_name,
            sl.apply_employee_info_id,
            sl.audit_employee_info_id
        FROM sfa_leave sl
        INNER JOIN sfa_employee_info sei ON sei.id = sl.apply_employee_info_id
        inner join sfa_position_relation spr on spr.employee_info_id = sei.id and spr.delete_flag = 0 and spr.status = 1 and spr.part_time = 0
<!--        INNER JOIN ceo_business_organization_position_relation cbopr on cbopr.position_id = sei.position_id and sei.employee_id = cbopr.employee_id-->
<!--        INNER JOIN ceo_business_position_type cbpt on cbpt.id = cbopr.position_type_id-->
<!--        INNER JOIN ceo_business_organization cbo on cbo.organization_id = cbopr.organization_id-->
        <if test=" params.organizationId != null and params.organizationId != '' ">
        INNER JOIN ceo_business_organization_tree cbot on cbot.organization_id = spr.organization_code and cbot.organization_parent_id = #{params.organizationId}
        </if>
        <if test="params.organizationIds != null ">
        INNER JOIN ceo_business_organization_tree cbot1 on cbot1.organization_id = spr.organization_code
            <foreach collection="params.organizationIds" item="item" open="and cbot1.organization_parent_id in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        INNER JOIN sfa_apply_member sam on sam.id = sei.application_id
        LEFT JOIN sfa_dict_code lscd on lscd.class_cd = 'leave_status' and lscd.item_value = sl.leave_status
        LEFT JOIN sfa_dict_code ltcd on ltcd.class_cd = 'leave_type' and ltcd.item_value = sl.leave_type
        <where>
            <include refid="where_list_verify"/>
        </where>
        ORDER BY
        <choose>
            <when test="params.sortType == 1 ">sl.leave_start_time</when>
            <when test="params.sortType == 2 ">sl.leave_end_time</when>
            <when test="params.sortType == 3 ">sl.leave_hours</when>
            <when test="params.sortType == 4 ">sl.month_already_leave_hours</when>
            <otherwise>
                sl.submit_time
            </otherwise>
        </choose>
        <choose>
            <when test="params.sortOrder == 1 ">asc</when>
            <when test="params.sortOrder == 2 ">desc</when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="queryLeaveDetail" resultType="com.wantwant.sfa.backend.leave.vo.NewLeaveListVo">
        SELECT
        sam.pic_url as avatar,
        sl.business_num ,
        sl.submit_time,
        spr.organization_code as organizationId,
        case
            when spr.position_type_id = 1 then spr.area_name
            when spr.position_type_id = 12 then spr.varea_name
            when spr.position_type_id = 11 then spr.province_name
            when spr.position_type_id = 2 then spr.company_name
            when spr.position_type_id = 10 then spr.department_name
            when spr.position_type_id = 3 then spr.department_name
            else ''
        end as organizationName,
        spr.area_name,
        spr.varea_name as vareaOrganizationName,
        spr.province_name as provinceOrganizationName,
        spr.company_code,
        spr.company_name,
        spr.department_name,
        IF(spr.position_type_id = 3, concat(spr.position_type_id,spr.type, spr.post_type), spr.position_type_id) as postTypeId,
        sei.employee_name,
        sei.employee_id,
        sei.mobile,
        sl.leave_type,
        ltcd.item_content as leave_type_name,
        sl.leave_start_time,
        sl.leave_end_time,
        sl.leave_hours,
        round(sl.leave_hours/8,1) as leave_days,
        sl.month_already_leave_hours,
        round(sl.month_already_leave_hours/8,1) as month_already_leave_days,
        sl.want_leave_num,
        sl.want_image,
        sl.leave_reason,
        sl.leave_status,
        lscd.item_content as leave_status_name,
        sl.appendix,
        sl.appendix_name,
        sl.image,
        sl.image_name,
        sl.apply_employee_info_id,
        sl.audit_employee_info_id
        FROM sfa_leave sl
        INNER JOIN sfa_employee_info sei ON sei.id = sl.apply_employee_info_id
        inner join sfa_position_relation spr on spr.employee_info_id = sei.id and spr.delete_flag = 0 and spr.status = 1 and spr.part_time = 0
<!--        INNER JOIN ceo_business_organization_position_relation cbopr on cbopr.position_id = sei.position_id and sei.employee_id = cbopr.employee_id-->
<!--        INNER JOIN ceo_business_position_type cbpt on cbpt.id = cbopr.position_type_id-->
<!--        INNER JOIN ceo_business_organization cbo on cbo.organization_id = cbopr.organization_id-->
        INNER JOIN sfa_apply_member sam on sam.id = sei.application_id
        LEFT JOIN sfa_dict_code lscd on lscd.class_cd = 'leave_status' and lscd.item_value = sl.leave_status
        LEFT JOIN sfa_dict_code ltcd on ltcd.class_cd = 'leave_type' and ltcd.item_value = sl.leave_type
        <where>
            sl.delete_flag = 0
            and sl.business_num = #{params.businessNum}
        </where>
    </select>

    <select id="queryAuditLeaveList" resultType="com.wantwant.sfa.backend.leave.entity.SfaLeave">
        SELECT
        sl.id ,
        sl.business_num ,
        sl.attendance_start_date,
        sl.attendance_end_date,
        sl.leave_type,
        ltcd.item_content as leave_type_name,
        sl.leave_start_time,
        sl.leave_end_time,
        sl.leave_hours,
        sl.month_already_leave_hours,
        sl.leave_reason,
        sl.leave_status,
        sl.appendix,
        sl.image,
        sl.apply_employee_info_id,
        sl.audit_employee_info_id,
        sei1.employee_id as apply_employee_id,
        sei1.employee_name as apply_employee_name,
        sei2.employee_id as audit_employee_id
        FROM sfa_leave sl
        LEFT JOIN sfa_employee_info sei1 ON sei1.id = sl.apply_employee_info_id
        LEFT JOIN sfa_employee_info sei2 ON sei2.id = sl.audit_employee_info_id
        LEFT JOIN sfa_dict_code ltcd on ltcd.class_cd = 'leave_type' and ltcd.item_value = sl.leave_type
        <where>
            sl.delete_flag = 0
            <if test="null != businessNumList and businessNumList.size()>0 ">
                <foreach collection="businessNumList" open=" and sl.business_num in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateAttendanceMonthAlreadyLeaveHours">
        UPDATE sfa_leave
        <set>
            <if test="type != null and type == 1">month_already_leave_hours = month_already_leave_hours + #{leaveHours}</if>
            <if test="type != null and type == 2">month_already_leave_hours = month_already_leave_hours - #{leaveHours}</if>
        </set>
        WHERE
        attendance_start_date = #{attendanceStartDate} and attendance_end_date = #{attendanceEndDate} and apply_employee_info_id = #{applyEmployeeInfoId}
    </update>

    <update id="updateLeaveById">
        UPDATE sfa_leave
        <set>
            leave_status = #{leaveStatus}
            ,audit_employee_info_id = #{auditEmployeeInfoId}
            ,update_user_id = #{updateUserId}
            ,update_user_name = #{updateUserName}
            ,update_time = #{updateTime}
        </set>
        WHERE
            id = #{id}
    </update>

    <select id="queryLeaveAuditList" resultType="com.wantwant.sfa.backend.leave.entity.SfaLeave">
        SELECT
        sl.id ,
        sl.leave_type ,
        sl.leave_status ,
        sl.leave_start_time ,
        sl.attendance_start_date,
        sl.attendance_end_date,
        sl.leave_hours,
        sl.apply_employee_info_id,
        sl.audit_employee_info_id
        FROM sfa_leave sl
        <where>
            sl.delete_flag = 0
            and sl.leave_status = 0
        </where>

    </select>

</mapper>