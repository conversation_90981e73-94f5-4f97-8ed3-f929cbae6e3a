<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper

        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"

        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
   mapper标签:配置各类声明
   namespace：名称空间，由于映射文件有多个，为了防止crud语句的唯一标识被重复，可以设置空间名称。
 -->
<mapper namespace="com.wantwant.sfa.backend.mapper.SalaryMapper">

   <select id="getSalaryScheme" resultType="com.wantwant.sfa.backend.salary.vo.SalarySchemeVo">
        SELECT
          v.organization_name3 as areaName,
          v.virtual_area_name as vareaName,
          case sess.position when 201 then '区域总监' when 601 then '省区总监' when 401 then '区域经理' end as positionName,
          v.province_name as provinceName,
          v.organization_name2 as companyName,
          sess.grade as `level`,
          sess.base_salary as baseSalary,
          sess.allowance as allowance,
          sess.travel_expenses,
          IFNULL(sess.bonus,0) as bonus,
          IFNULL(sess.full_risk_fee, 0) as fullRiskFee,
          sess.social_security_base as socialSecurityBase,
          IFNULL(sess.base_salary,0)  + IFNULL(sess.allowance,0)
         + IFNULL(sess.travel_expenses,0)  +  IFNULL(sess.bonus,0)
         + IFNULL(sess.full_risk_fee, 0)
         + IFNULL(sess.social_security_base,0)
            as total
        FROM
          sfa_employee_salary_structure sess
          INNER JOIN ceo_business_organization_view v ON v.organization_id = sess.organization_id
          and v.business_group = #{businessGroup} and sess.is_delete = 0

      <where>
         and sess.position in (201,401,601)
         and (sess.end_date >= now() or sess.end_date is null)
         <if test="areaOrganizationIds != null and areaOrganizationIds.size() > 0">
            <foreach collection="areaOrganizationIds" item="item" open="and v.organization_id3 in (" close=")" separator=",">
               #{item}
            </foreach>
         </if>


         <if test="vareaOrganizationIds != null and vareaOrganizationIds.size() > 0">
            <foreach collection="vareaOrganizationIds" item="item" open="and virtual_area_id in (" close=")" separator=",">
               #{item}
            </foreach>
         </if>

         <if test="provinceOrganizationIds != null and provinceOrganizationIds.size() > 0">
            <foreach collection="provinceOrganizationIds" item="item" open="and v.province_id in (" close=")" separator=",">
               #{item}
            </foreach>
         </if>

         <if test="companyOrganizationIds != null and companyOrganizationIds.size() > 0">
            <foreach collection="companyOrganizationIds" item="item" open="and v.organization_id2 in (" close=")" separator=",">
               #{item}
            </foreach>
         </if>


         <if test="position != null">
            <choose>
               <when test="position == 1">
                  and sess.position = 601
               </when>
               <when test="position == 2">
                  and sess.position = 201
               </when>
               <when test="position == 3">
                  and sess.position = 401
               </when>
            </choose>
         </if>

      </where>
      order by v.organization_name3, v.virtual_area_name, v.province_name,v.organization_name2, positionName,total
      limit #{offset},#{limit}
   </select>



   <select id="getSalarySchemeCount" resultType="int">
      SELECT
        count(1)
      FROM
      sfa_employee_salary_structure sess
      INNER JOIN ceo_business_organization_view v ON v.organization_id = sess.organization_id
      and v.business_group = #{businessGroup} and sess.is_delete = 0
      <where>
         and sess.position in (201,601,401)
         and (sess.end_date >= now() or sess.end_date is null)
         <if test="areaOrganizationIds != null and areaOrganizationIds.size() > 0">
            <foreach collection="areaOrganizationIds" item="item" open="and v.organization_id3 in (" close=")" separator=",">
               #{item}
            </foreach>
         </if>


         <if test="vareaOrganizationIds != null and vareaOrganizationIds.size() > 0">
            <foreach collection="vareaOrganizationIds" item="item" open="and virtual_area_id in (" close=")" separator=",">
               #{item}
            </foreach>
         </if>

         <if test="provinceOrganizationIds != null and provinceOrganizationIds.size() > 0">
            <foreach collection="provinceOrganizationIds" item="item" open="and v.province_id in (" close=")" separator=",">
               #{item}
            </foreach>
         </if>

         <if test="companyOrganizationIds != null and companyOrganizationIds.size() > 0">
            <foreach collection="companyOrganizationIds" item="item" open="and v.organization_id2 in (" close=")" separator=",">
               #{item}
            </foreach>
         </if>

         <if test="position != null">
            <choose>
               <when test="position == 1">
                  and sess.position = 601
               </when>
               <when test="position == 2">
                  and sess.position = 201
               </when>
               <when test="position == 3">
                  and sess.position = 401
               </when>
            </choose>
         </if>
      </where>
   </select>


   <select id="getAllocatedHistory" resultType="com.wantwant.sfa.backend.salary.vo.SalaryAllocatedVo">
      select employeeInfoId,areaName,vareaName,provinceName,companyName,departmentName,employeeName,`position`,
      employeeStatus,salaryLevel,baseSalary,allowance,social_security_base,bonus,travelExpenses,fullRiskFee,
      startDate from (
      <!-- 在职试岗人员薪资 -->
      select
      sei.id as employeeInfoId,
      sei.area_name as areaName,
      sei.varea_organization_name as vareaName,
      sei.province_organization_name as provinceName,
      sei.company_name as companyName,
      sei.department_name as departmentName,
      sei.employee_name as employeeName,
      case cbopr.position_type_id when 1 then '战区督导' when 12 then '大区总监' when 11 then '省区总监'
      when 2 then '区域总监' when 10 then '区域经理' end as `position`,
      case
      sei.employee_status when 1 then '试岗'
      else '在职'
      end as `employeeStatus`,
      ses.salary_level as salaryLevel,
      ses.employee_base_salary as baseSalary,
      ses.employee_allowance as allowance,
      ses.social_security_base ,
      ifnull(ses.employee_bonus,0)  as bonus,
      ifnull(ses.travel_expenses,0) as travelExpenses,
      ifnull(ses.full_risk_fee,0) as fullRiskFee,
      ses.start_date as startDate
      from
      sfa_employee_info sei
      inner join sfa_employee_salary ses on
      ses.employee_info_id = sei.id
      and ses.is_delete = 0
      and ses.end_date is null
      inner join ceo_business_organization_position_relation cbopr on cbopr.position_id = sei.position_id
      and cbopr.position_type_id in (2,11,10,12)
      left join sfa_salary_middleware ssm on
      ssm.apply_id = sei.application_id and ssm.delete_flag = 0 and ssm.status = 1
      where
      ssm.id is null
      and sei.employee_status in (1, 2)
      and cbopr.position_type_id != 3
      <choose>
         <when test="orgType == 'area'">
         and sei.area_code = #{organizationId}
         </when>
             <when test="orgType == 'varea'">
                and sei.varea_organization_id = #{organizationId}
             </when>
             <when test="orgType == 'province'">
                and sei.province_organization_id = #{organizationId}
             </when>
             <when test="orgType == 'company'">
                and sei.company_code = #{organizationId}
             </when>
             <when test="orgType == 'department'">
                and sei.department_code = #{organizationId}
             </when>
          </choose>
          <if test="positionTypeId != null">
             and cbopr.position_type_id = #{positionTypeId}
          </if>
        union all
      <!-- 入职中的 -->
      select
         null as employeeInfoId,
         sam.area as areaName,
         sam.varea_organization_name as vareaName,
         sam.province_organization_name as provinceName,
         sam.company  as companyName,
         sam.branch_organization_name as departmentName,
         sam.user_name as employeeName,
         case sam.`position` when 3 then '战区督导' when 5 then '大区总监' when 6 then '省区总监'
         when 2 then '区域总监' when 4 then '区域经理' end as `position`,
         '入职中' as employeeStatus,
         sess.grade as salaryLevel,
         sess.base_salary ,
         sess.allowance ,
         sess.social_security_base ,
         sess.bonus ,
         sess.travel_expenses as travelExpenses,
         sess.full_risk_fee,
         now()
      from
        sfa_salary_middleware ssm
      inner join sfa_apply_member sam on
      sam.id = ssm.apply_id and sam.position in (2,4,5,6)
      inner join sfa_interview_process sip on
      sip.application_id = sam.id
      inner join sfa_interview_process_record sipr on
      sipr.id = sip.interview_record_id
      inner join sfa_employee_salary_structure sess on
      sess.id = sipr.salary_structure_id
      where
      ((sam.position = 4 and  sip.process_type  in (5,6))
      or
      (sam.position in (6,5,2) and  sip.process_type > 1 and  sip.process_type &lt;= 6))
      and sip.process_result in (0,1,3)
      and ssm.delete_flag = 0 and ssm.status = 1
      and sam.position != 1
      <choose>
         <when test="orgType == 'area'">
            and sam.area_organization_id = #{organizationId}
         </when>
         <when test="orgType == 'varea'">
            and sam.varea_organization_id = #{organizationId}
         </when>
         <when test="orgType == 'province'">
            and sam.province_organization_id = #{organizationId}
         </when>
         <when test="orgType == 'company'">
            and sam.company_organization_id = #{organizationId}
         </when>
         <when test="orgType == 'department'">
            and sam.branch_organization_id = #{organizationId}
         </when>
      </choose>
      <if test="positionTypeId != null">
         <choose>
            <when test="positionTypeId == 1">
               and sam.position = 3
            </when>
            <when test="positionTypeId == 12">
               and sam.position = 5
            </when>
            <when test="positionTypeId == 11">
               and sam.position = 6
            </when>
            <when test="positionTypeId == 2">
               and sam.position = 2
            </when>
            <when test="positionTypeId == 10">
               and sam.position = 4
            </when>
         </choose>
      </if>
      union all
      -- 异动中的
      select
      sei.id as employeeInfoId,
      sam.area as areaName,
      sam.varea_organization_name as vareaName,
      sam.province_organization_name as provinceName,
      sam.company  as companyName,
      sam.branch_organization_name as departmentName,
      sam.user_name as employeeName,
      case sam.`position` when 3 then '战区督导' when 5 then '大区总监' when 6 then '省区总监'
      when 2 then '区域总监' when 4 then '区域经理' end as `position`,
      case when sei.id is not null then '异动中' else '入职中' end  as employeeStatus,
      sess.grade as salaryLevel,
      sess.base_salary ,
      sess.allowance ,
      sess.social_security_base ,
      sess.bonus ,
      sess.travel_expenses as travelExpenses,
      sess.full_risk_fee,
      now()
      from
      sfa_salary_middleware ssm
      inner join sfa_apply_member sam on
      sam.id = ssm.apply_id  and sam.position in (2,4,5,6)
      inner join sfa_employee_info sei on sei.application_id  = sam.id
      inner join sfa_interview_process sip on
      sip.application_id = sam.id
      inner join sfa_interview_process_record sipr on
      sipr.id = sip.interview_record_id
      INNER JOIN sfa_employee_salary_structure sess ON
      sess.id = ssm.salary_structure_id
      AND sess.is_delete = 0
      where
      sip.process_type = 6  and ssm.delete_flag = 0 and ssm.status = 1 and sam.position != 1
      and sip.process_result = 1
      <choose>
         <when test="orgType == 'area'">
            and sam.area_organization_id = #{organizationId}
         </when>
         <when test="orgType == 'varea'">
            and sam.varea_organization_id = #{organizationId}
         </when>
         <when test="orgType == 'province'">
            and sam.province_organization_id = #{organizationId}
         </when>
         <when test="orgType == 'company'">
            and sam.company_organization_id = #{organizationId}
         </when>
         <when test="orgType == 'department'">
            and sam.branch_organization_id = #{organizationId}
         </when>
      </choose>
      <if test="positionTypeId  != null">
         <choose>
            <when test="positionTypeId == 1">
               and sam.position = 3
            </when>
            <when test="positionTypeId == 12">
               and sam.position = 5
            </when>
            <when test="positionTypeId == 11">
               and sam.position = 6
            </when>
            <when test="positionTypeId == 2">
               and sam.position = 2
            </when>
            <when test="positionTypeId == 10">
               and sam.position = 4
            </when>
         </choose>
      </if>

      ) temp
      order by temp.areaName,temp.vareaName,temp.provinceName,temp.companyName,temp.departmentName
   </select>



   <select id="getGroupCompanyAndDepartmentAndProvinceAllocatedHistory" resultType="com.wantwant.sfa.backend.salary.vo.SalaryAllocatedVo">
      select areaName,vareaName,provinceName,companyName,departmentName,employeeName,`position`,positionTypeId,
      employeeStatus,salaryLevel,baseSalary,allowance,social_security_base,bonus,total from (
      <!-- 在职试岗人员薪资 -->
      select
      sei.area_name as areaName,
      sei.varea_organization_name as vareaName,
      sei.province_organization_name as provinceName,
      sei.company_name as companyName,
      sei.department_name as departmentName,
      sei.employee_name as employeeName,
      cbopr.position_type_id as positionTypeId,
      case cbopr.position_type_id
          when 1 then '战区督导'
          when 12 then '大区总监'
          when 11 then '省区总监'
          when 2 then '区域总监'
          when 10 then '区域经理'
      end as `position`,
      case
      sei.employee_status when 1 then '试岗'
      else '在职'
      end as `employeeStatus`,
      ses.salary_level as salaryLevel,
      ses.employee_base_salary as baseSalary,
      ses.employee_allowance as allowance,
      ses.social_security_base ,
      ifnull(ses.employee_bonus,0) + ifnull(ses.travel_expenses,0) as bonus,
      IFNULL(ses.employee_base_salary, 0)  + IFNULL(ses.travel_expenses, 0)  + IFNULL(ses.employee_allowance, 0) + IFNULL(ses.social_security_base, 0) +
      IFNULL(ses.employee_bonus, 0) as total
      from
      sfa_employee_info sei
      inner join sfa_employee_salary ses on
      ses.employee_info_id = sei.id
      and ses.is_delete = 0
      and ses.end_date is null
      inner join ceo_business_organization_position_relation cbopr on cbopr.position_id = sei.position_id
      and cbopr.position_type_id in (1,2,10,11,12)
      left join sfa_salary_middleware ssm on
      ssm.apply_id = sei.application_id and ssm.delete_flag = 0 and ssm.status = 1
      where
      ssm.id is null
      and sei.employee_status in (1, 2)
      and cbopr.position_type_id != 3
      and cbopr.business_group = #{businessGroup}
      union all
      <!-- 入职中的 -->
      select
      sam.area as areaName,
      sam.varea_organization_name as vareaName,
      sam.province_organization_name as provinceName,
      sam.company  as companyName,
      sam.branch_organization_name as departmentName,
      sam.user_name as employeeName,
      case sam.`position`
         when 3 then 1
         when 5 then 12
         when 6 then 11
         when 2 then 2
         when 4 then 10
      end as positionTypeId,
      case sam.`position`
         when 3 then '战区督导'
         when 5 then '大区总监'
         when 6 then '省区总监'
         when 2 then '区域总监'
         when 4 then '区域经理'
      end as `position`,
      '入职中' as employeeStatus,
      sess.grade as salaryLevel,
      sess.base_salary ,
      sess.allowance ,
      sess.social_security_base ,
      IFNULL(sess.bonus,0) + IFNULL(sess.travel_expenses,0) as bonus,
      IFNULL(sess.base_salary , 0) + IFNULL(sess.allowance , 0)  + IFNULL(sess.travel_expenses, 0)  + IFNULL(sess.social_security_base, 0) + IFNULL(sess.bonus, 0) as total
      from
      sfa_salary_middleware ssm
      inner join sfa_apply_member sam on
      sam.id = ssm.apply_id and sam.position in (2,3,4,5,6)
      inner join sfa_interview_process sip on
      sip.application_id = sam.id
      inner join sfa_interview_process_record sipr on
      sipr.id = sip.interview_record_id
      inner join sfa_employee_salary_structure sess on
      sess.id = sipr.salary_structure_id
      where
      ((sam.position = 4 and  sip.process_type  in (5,6))
      or
      (sam.position in (6,2) and  sip.process_type > 1 and  sip.process_type &lt;= 6))

      and sip.process_result in (0,3)
      and ssm.delete_flag = 0 and ssm.status = 1
      and sam.position != 1
      and sam.business_group = #{businessGroup}

      union all
      -- 异动中的
      select
      sam.area as areaName,
      sam.varea_organization_name as vareaName,
      sam.province_organization_name as provinceName,
      sam.company  as companyName,
      sam.branch_organization_name as departmentName,
      sam.user_name as employeeName,
      case sam.`position`
         when 3 then 1
         when 5 then 12
         when 6 then 11
         when 2 then 2
         when 4 then 10
      end as positionTypeId,
      case sam.`position`
         when 3 then '战区督导'
         when 5 then '大区总监'
         when 6 then '省区总监'
         when 2 then '区域总监'
         when 4 then '区域经理'
      end as `position`,
      case when sei.id is not null then '异动中' else '入职中' end  as employeeStatus,
      sess.grade as salaryLevel,
      sess.base_salary ,
      sess.allowance ,
      sess.social_security_base ,
      IFNULL(sess.bonus,0) + IFNULL(sess.travel_expenses,0) as bonus,
      IFNULL(sess.base_salary , 0)  + IFNULL(sess.travel_expenses, 0)  + IFNULL(sess.allowance , 0) + IFNULL(sess.social_security_base, 0) + IFNULL(sess.bonus, 0) as total
      from
      sfa_salary_middleware ssm
      inner join sfa_apply_member sam on
      sam.id = ssm.apply_id  and sam.position in (2,3,4,5,6)
      inner join sfa_employee_info sei on sei.application_id  = sam.id
      inner join sfa_interview_process sip on
      sip.application_id = sam.id
      inner join sfa_interview_process_record sipr on
      sipr.id = sip.interview_record_id
      INNER JOIN sfa_employee_salary_structure sess ON
      sess.id = ssm.salary_structure_id
      AND sess.is_delete = 0
      where
      sip.process_type = 6  and ssm.delete_flag = 0 and ssm.status = 1 and sam.position != 1
      and sip.process_result = 1
      and sam.business_group = #{businessGroup}
      ) temp
      order by temp.areaName,temp.vareaName,temp.provinceName,temp.companyName,temp.departmentName
   </select>

   <select id="getAllocatedBusinessBDHistory" resultType="com.wantwant.sfa.backend.domain.businessBd.DO.BusinessSalaryDetail">
      select employeeInfoId,ceo_type,position,jobs_type,areaName,vareaName,provinceName,companyName,departmentName,employeeName,`position`,
      employeeStatus,salaryLevel,baseSalary,allowance,socialSecurityBase,bonus,travelExpenses,fullRiskFee,total from (
      <!-- 在职的 -->
      SELECT
      sei.id as employeeInfoId,
      sam.ceo_type,
      sam.jobs_type,
      sam.position,
      sei.area_name as areaName,
      sei.varea_organization_name as vareaName,
      sei.province_organization_name as provinceName,
      sei.company_name as companyName,
      sei.department_name as departmentName,
      sei.employee_name as employeeName,
      case
      sei.employee_status when 1 then '试岗'
      else '在职' end as `employeeStatus`,
      sess.grade as salaryLevel,
      IFNULL(ses.employee_base_salary,0) as baseSalary,
      IFNULL(ses.employee_allowance,0) as allowance,
      IFNULL(ses.social_security_base,0) as socialSecurityBase,
      IFNULL(ses.employee_bonus,0) as bonus,
      IFNULL(ses.travel_expenses,0) as travelExpenses,
      IFNULL(ses.full_risk_fee,0) as fullRiskFee,
      IFNULL(ses.employee_base_salary,0) + IFNULL(ses.employee_allowance,0) + IFNULL(ses.social_security_base,0)
      + IFNULL(ses.employee_bonus,0) + IFNULL(ses.travel_expenses,0) + IFNULL(ses.full_risk_fee,0) as total
      FROM
      sfa_employee_info sei
      INNER JOIN sfa_apply_member sam ON sam.id = sei.application_id
      INNER JOIN sfa_employee_salary ses ON ses.employee_info_id = sei.id
      AND ses.end_date IS NULL
      AND ses.is_delete = 0
      INNER JOIN sfa_employee_salary_structure sess on sess.id = ses.structure_id
      <where>
         AND sei.employee_status in (1,2)
         AND sam.business_group = #{req.businessGroup}
         <choose>
            <when test="req.positionTypeId != null and req.positionTypeId == 1">
               and sam.ceo_type =6 and sam.jobs_type = 1
            </when>
            <when test="req.positionTypeId != null and req.positionTypeId == 2">
               and sam.ceo_type =7
            </when>
            <otherwise>
               and ((sam.ceo_type =6 and sam.jobs_type = 1) or (sam.ceo_type = 7))
            </otherwise>
         </choose>
         <if test="req.orgType != null and req.orgType != '' ">
            <choose>
               <when test="req.orgType == 'area'">
                  and sam.area_organization_id = #{req.orgCode}
               </when>
               <when test="req.orgType == 'varea'">
                  and sam.varea_organization_id = #{req.orgCode}
               </when>
               <when test="req.orgType == 'province'">
                  and sam.province_organization_id = #{req.orgCode}
               </when>
               <when test="req.orgType == 'company'">
                  and sam.company_organization_id = #{req.orgCode}
               </when>
               <when test="req.orgType == 'department'">
                  and sam.branch_organization_id = #{req.orgCode}
               </when>
            </choose>
         </if>
      </where>

      <!-- 入职中的 -->
      union all

      SELECT
      null as employeeInfoId,
      sam.ceo_type,
      sam.jobs_type,
      sam.position,
      sam.area as areaName,
      sam.varea_organization_name as vareaName,
      sam.province_organization_name as provinceName,
      sam.company as companyName,
      sam.branch_organization_name as departmentName,
      sam.user_name as employeeName,
      '入职中' `employeeStatus`,
      sess.grade as `salaryLevel`,
      IFNULL(sess.base_salary,0) as baseSalary,
      IFNULL(sess.allowance,0) as allowance,
      IFNULL(sess.social_security_base,0) as socialSecurityBase,
      IFNULL(sess.bonus,0) as bonus,
      IFNULL(sess.travel_expenses,0) as travelExpenses,
      IFNULL(sess.full_risk_fee,0) as fullRiskFee,
      IFNULL(sess.base_salary, 0) + IFNULL(sess.allowance, 0) + IFNULL(sess.social_security_base, 0)
      + IFNULL(sess.bonus, 0) + IFNULL(sess.travel_expenses, 0) + IFNULL(sess.full_risk_fee, 0) AS total
      FROM
      sfa_salary_middleware ssm
      INNER JOIN sfa_apply_member sam ON sam.id = ssm.apply_id
      INNER JOIN sfa_interview_process sip ON sip.application_id = sam.id
      INNER JOIN sfa_interview_process_record sipr ON sipr.id = sip.interview_record_id
      INNER JOIN sfa_employee_salary_structure sess ON sess.id = sipr.salary_structure_id
      LEFT JOIN sfa_job_position_task sjpt on sjpt.apply_id = ssm.apply_id
      and sjpt.status != 1
      <where>
         and ssm.delete_flag = 0 and ssm.status = 1
         AND ((sip.process_type &lt;= 6 and sip.process_result in (0,3))
         or (sip.process_type = 6 and sip.process_result = 1 and sjpt.task_id is not null)
         )
         AND (sip.recommend_onboard_time like concat(#{req.theYearMonth},'%') or sip.recommend_onboard_time is null)
         AND sam.business_group = #{req.businessGroup}
         <choose>
            <when test="req.positionTypeId != null and req.positionTypeId == 1">
               and sam.ceo_type =6 and sam.jobs_type = 1
            </when>
            <when test="req.positionTypeId != null and req.positionTypeId == 2">
               and sam.ceo_type =7
            </when>
            <otherwise>
               and ((sam.ceo_type =6 and sam.jobs_type = 1) or (sam.ceo_type = 7))
            </otherwise>
         </choose>
         <if test="req.orgType != null and req.orgType != '' ">
            <choose>
               <when test="req.orgType == 'area'">
                  and sam.area_organization_id = #{req.orgCode}
               </when>
               <when test="req.orgType == 'varea'">
                  and sam.varea_organization_id = #{req.orgCode}
               </when>
               <when test="req.orgType == 'province'">
                  and sam.province_organization_id = #{req.orgCode}
               </when>
               <when test="req.orgType == 'company'">
                  and sam.company_organization_id = #{req.orgCode}
               </when>
               <when test="req.orgType == 'department'">
                  and sam.branch_organization_id = #{req.orgCode}
               </when>
            </choose>
         </if>
      </where>
      ) temp
      order by temp.areaName, temp.vareaName, temp.provinceName, temp.companyName, temp.departmentName
   </select>

   <select id="selectBdTransferByIds" resultType="com.wantwant.sfa.backend.domain.businessBd.DO.BusinessSalaryDetail">
      SELECT
         sei.id as employeeInfoId,
         sei.area_name as areaName,
         sei.varea_organization_name as vareaName,
         sei.province_organization_name as provinceName,
         sei.company_name as companyName,
         sei.department_name as departmentName,
         sei.employee_name as employeeName,
         ceoType.transaction_value as ceoType,
         jobsType.transaction_value as jobsType,
         position.transaction_value as position,
         sess.grade AS `salaryLevel`,
         IFNULL(sess.base_salary,0) as baseSalary,
         IFNULL(sess.allowance,0) as allowance,
         IFNULL(sess.social_security_base,0) AS socialSecurityBase,
         IFNULL(sess.bonus,0) as bonus,
         IFNULL(sess.travel_expenses,0) AS travelExpenses,
         IFNULL(sess.full_risk_fee,0) AS fullRiskFee,
         IFNULL(sess.base_salary, 0) + IFNULL(sess.allowance, 0) + IFNULL(sess.social_security_base, 0) + IFNULL(sess.bonus, 0) + IFNULL(sess.travel_expenses, 0) + IFNULL(sess.full_risk_fee, 0) AS total
      FROM
         sfa_transaction_apply a
         INNER JOIN sfa_employee_info sei ON sei.id = a.employee_info_id
         INNER JOIN sfa_transaction_process p ON p.transaction_apply_id = a.id
         AND p.delete_flag = 0
         AND a.delete_flag = 0
         INNER JOIN sfa_transaction_action ceoType ON ceoType.transaction_id = a.id
         AND ceoType.change_column = 'ceoType' and ceoType.delete_flag = 0
         INNER JOIN sfa_transaction_action jobsType ON jobsType.transaction_id = a.id
         AND jobsType.change_column = 'jobsType' and jobsType.delete_flag = 0
         INNER JOIN sfa_transaction_action position ON position.transaction_id = a.id
         AND position.change_column = 'position' and position.delete_flag = 0
         INNER JOIN sfa_transaction_action salary ON salary.transaction_id = a.id
         AND salary.change_column = 'salary_struct_id'  and salary.delete_flag = 0
         LEFT JOIN sfa_employee_salary_structure sess ON sess.id = salary.transaction_value
      <where>
         AND a.transaction_type = 8
         AND a.revert_status = 0
         AND p.process_result IN (1, 0)
         AND a.advice_execute_date LIKE concat(#{theYearMonth}, '%')
         AND a.execute_date IS NULL
         <if test="employeeInfoIds != null and employeeInfoIds.size() >0">
            <foreach collection="employeeInfoIds" separator="," item="item" open=" and sei.id in (" close=")">
               #{item}
            </foreach>
         </if>
      </where>

   </select>
</mapper>