<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryMapper">

    <select id="selectPageBySql" resultType="com.wantwant.sfa.backend.marketAndPersonnel.vo.SalaryVO">
        SELECT
        s.id,v.organization_name3 as area,v.organization_name2 as company,v.department_name as departmentName,
        s.organization_id,c.classification,s.employee_name,s.salary_describe,
        s.employee_base_salary,s.employee_bonus as bonus,
        s.start_date,s.end_date,s.updated_time,r1.employee_name as updatedBy
        from sfa_employee_salary s
        LEFT JOIN sfa_company_classification c on c.organization_id = s.organization_id and c.is_delete = 0
        and DATE_FORMAT(c.effective_date,'%Y-%m') = #{params.yyyyMM}
        INNER JOIN ceo_business_organization_view v on v.organization_id = s.organization_id
        LEFT JOIN ceo_business_organization_position_relation r1 on r1.employee_id = s.updated_by and r1.channel = 3
        where s.is_delete = 0 and s.position = 1
        and DATE_FORMAT(s.start_date,'%Y-%m') &lt;= #{params.yyyyMM}
        and DATE_FORMAT(IFNULL(s.end_date,'2099-12-01'),'%Y-%m') >= #{params.yyyyMM}
        <if test="params.organizationId != null and params.organizationId != ''">
            and (v.organization_id3 = #{params.organizationId}
            or v.organization_id2 = #{params.organizationId}
            or v.department_id = #{params.organizationId})
        </if>
        <if test="params.areaOrganizationId != null and params.areaOrganizationId != ''">
            and v.organization_id3 = #{params.areaOrganizationId}
        </if>
        <if test="params.companyOrganizationId != null and params.companyOrganizationId != ''">
            and v.organization_id2 = #{params.companyOrganizationId}
        </if>
    </select>
    <select id="selectIncompleteSalary" resultType="com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO">
        select s.*
        from sfa_employee_salary s
        where s.employee_info_id = #{employeeInfoId} and s.is_delete = 0
        and DATE_FORMAT(s.start_date,'%Y%m') &lt;= #{startDate} and s.end_date is null
        ORDER BY id DESC LIMIT 1
    </select>

    <select id="selectSalaryByEmpId" resultType="com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO">
        select s.*
        from sfa_employee_salary s
        where s.employee_info_id = #{employeeInfoId} and s.is_delete = 0 and s.end_date is null
        ORDER BY id DESC LIMIT 1
    </select>

    <select id="selectSalaryList" resultType="com.wantwant.sfa.backend.interview.vo.SalaryStructureVo">
        SELECT
            id,
            `grade` as level,
            base_salary,
            allowance,
            ifnull(social_security_base,0) + ifnull(bonus,0) + ifnull(full_risk_fee,0) + ifnull(travel_expenses,0) as socialSecurityBase,
            ifnull(base_salary,0) + ifnull(social_security_base,0) +  ifnull(allowance,0) + ifnull(travel_expenses,0) + ifnull(bonus,0) + ifnull(full_risk_fee,0) as total
        FROM
            sfa_employee_salary_structure
        where organization_id = #{organizationId}
        and `position` = #{position}

<!--        <choose>-->
<!--            <when test="position == 601">-->
<!--                and grade in ('A','A1')-->
<!--            </when>-->
<!--            <if test="position == 301">-->
<!--                and (grade like '_1-%')-->
<!--            </if>-->
<!--            <when test="position == 201">-->
<!--                and grade in ('A','A1')-->
<!--            </when>-->
<!--        </choose>-->


        and start_date &lt;= date_format(now(),'%Y-%m-%d')
        and (end_date &gt;=  date_format(now(),'%Y-%m-%d') or end_date is null)
        and is_delete = 0
    </select>

    <select id="selectSalaryByStartDate" resultType="java.lang.Integer">
        select id
        from sfa_employee_salary
        where start_date = #{startDate} and employee_info_id = #{employeeInfoId} and is_delete = 0
    </select>

    <update id="updateInvalidateSalaryById">
        update sfa_employee_salary
        set is_delete = 1
        where id = #{id}
    </update>

    <select id="selectBusinessPositionTypeName" resultType="String">
        select distinct position_name from ceo_business_position_type
    </select>


    <select id="selectBusinessBDSalary" resultType="com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO">
        SELECT ses.*
        FROM sfa_business_bd_config sbc
        INNER JOIN ceo_business_organization_tree cbot
        ON cbot.organization_id = sbc.organization_id
        AND cbot.level = 1
        INNER JOIN (
        SELECT s1.*
        FROM sfa_employee_salary s1
        INNER JOIN (
        SELECT employee_info_id, MAX(
                IFNULL(employee_base_salary, 0) +
                IFNULL(employee_bonus, 0) +
                IFNULL(employee_allowance, 0) +
                IFNULL(social_security_base, 0) +
                IFNULL(travel_expenses, 0) +
                IFNULL(full_risk_fee, 0)
                ) AS maxTotalSalary
        FROM sfa_employee_salary
        WHERE salary_describe IN ('10-全职业务BD', '11-承揽业务BD')
        AND (end_date IS NULL OR end_date like concat('%',#{theYearMonth},'%'))
        AND is_delete = 0
        GROUP BY employee_info_id
        ) s2 ON s1.employee_info_id = s2.employee_info_id
        AND (
                IFNULL(s1.employee_base_salary, 0) +
                IFNULL(s1.employee_bonus, 0) +
                IFNULL(s1.employee_allowance, 0) +
                IFNULL(s1.social_security_base, 0) +
                IFNULL(s1.travel_expenses, 0) +
                IFNULL(s1.full_risk_fee, 0)
                ) = s2.maxTotalSalary
        WHERE s1.salary_describe IN ('10-全职业务BD', '11-承揽业务BD')
        AND s1.start_date &gt;= #{startDate}
        AND (s1.end_date IS NULL OR s1.end_date  &lt; #{endDate})
        AND s1.is_delete = 0
        ) ses ON ses.organization_id = cbot.organization_parent_id
        WHERE sbc.department_id = #{departmentId};
    </select>

    <select id="selectBusinessBdSnapshot" resultType="com.wantwant.sfa.backend.domain.businessBd.repository.po.BusinessBdSalaryHistoryPO">
        SELECT
            #{theYearMonth} as theYearMonth,
            sei.department_code as department_id,
            sei.company_code as companyId,
            sei.id as employeeInfoId,
            sess.position as salaryPosition,
            sess.grade as salaryLevel,
            s1.employee_base_salary,
            s1.employee_bonus,
            s1.employee_allowance,
            s1.social_security_base,
            s1.travel_expenses,
            s1.full_risk_fee,
            s1.start_date,
            s1.end_date
        FROM
            sfa_employee_salary s1
            INNER JOIN (
            SELECT
                employee_info_id,
                MAX(
                        IFNULL(employee_base_salary, 0) + IFNULL(employee_bonus, 0) + IFNULL(employee_allowance, 0) + IFNULL(social_security_base, 0) + IFNULL(travel_expenses, 0) + IFNULL(full_risk_fee, 0)) AS maxTotalSalary
            FROM
                sfa_employee_salary
            WHERE
                salary_describe IN ('10-全职业务BD', '11-承揽业务BD')
              AND (end_date IS NULL OR end_date LIKE concat('%', #{theYearMonth}, '%'))
              AND start_date &lt;  DATE_FORMAT(DATE_ADD(concat( #{theYearMonth},'-01'), INTERVAL 1 MONTH), '%Y-%m')
              AND is_delete = 0
            GROUP BY
                employee_info_id) s2 ON s1.employee_info_id = s2.employee_info_id
            AND (
                    IFNULL(s1.employee_base_salary, 0) + IFNULL(s1.employee_bonus, 0) + IFNULL(s1.employee_allowance, 0) + IFNULL(s1.social_security_base, 0) + IFNULL(s1.travel_expenses, 0) + IFNULL(s1.full_risk_fee, 0)) = s2.maxTotalSalary
            INNER JOIN sfa_employee_salary_structure sess on sess.id = s1.structure_id
            INNER JOIN sfa_employee_info sei on sei.id = s1.employee_info_id
            order by s1.start_date desc
    </select>

    <select id="selectMinimumLevel" resultType="String">
        select
            minimum_level
        from
            sfa_bd_salary_grade_limit
        <where>
            and company_code = #{companyCode}
            and salary_position = #{salaryPositionByCondition}
            and delete_flag = 0
        </where>
        limit 1
    </select>
</mapper>
