<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper

        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"

        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
   mapper标签:配置各类声明
   namespace：名称空间，由于映射文件有多个，为了防止crud语句的唯一标识被重复，可以设置空间名称。
 -->
<mapper namespace="com.wantwant.sfa.backend.mapper.arch.ResourceMapper">

    <select id="selectListByRoleId" resultType="com.wantwant.sfa.backend.arch.entity.ResourceEntity">
        select
        distinct resource.id,
        COALESCE(i18n.title,resource.title) as title,
        resource.path,
        resource.name,
        resource.component,
        resource.hidden,
        resource.parent_id,
        resource.icon,
        resource.`cache`,
        resource.redirect,
        resource.always_show,
        resource.app_support,
        resource.update_prompt,
        max(srr.concern) as concern
        from sfa_resources resource
        inner join sfa_role_resources srr on srr.resource_id = resource.id
        and srr.delete_flag = 0 and resource.delete_flag = 0
        LEFT JOIN sfa_resources_i18n i18n ON resource.id = i18n.resource_id
        AND i18n.`ww_language` = #{language}
        AND i18n.delete_flag = 0
        <where>
            <foreach collection="roleIds" open=" and srr.role_id in (" separator="," close=")" item="item">
                #{item}
            </foreach>
        </where>
        group by resource.id
        order by parent_id,resource.order_num

    </select>


    <select id="selectFrequency" resultType="com.wantwant.sfa.backend.arch.dto.MenuFrequencyDTO">
        select route_after_name as title,100 as viewRate,count(1) as monthFrequency from sfa_track_menu_info where employee_id = #{person}
                                                                                                               and create_time >= date_add(curdate(), interval - day(curdate()) + 1 day)
                                                                                                               and route_after_name is not null
        group by route_after_name
    </select>


    <select id="selectByLanguage" resultType="com.wantwant.sfa.backend.arch.entity.ResourceEntity">
        SELECT
            r.id,
            COALESCE(i18n.title, r.title) as title,
            r.path,
            r.name,
            r.component,
            r.hidden,
            r.type,
            r.parent_id,
            r.order_num,
            r.icon,
            r.cache,
            r.redirect,
            r.terminal,
            r.always_show,
            r.context_menu,
            r.description,
            r.update_prompt,
            r.app_support,
            r.delete_flag,
            r.create_time,
            r.create_user_name,
            r.create_user_id,
            r.update_time,
            r.update_user_name,
            r.update_user_id
        FROM sfa_resources r
                 LEFT JOIN sfa_resources_i18n i18n ON r.id = i18n.resource_id
            AND i18n.`ww_language` = #{language}
            AND i18n.delete_flag = 0
        WHERE r.delete_flag = 0
            <if test="terminal != null">
                and r.terminal = #{terminal}
            </if>
    </select>

    <select id="selectResourcesByIds" resultType="com.wantwant.sfa.backend.arch.entity.ResourceEntity">
        SELECT
        r.id,
        COALESCE(i18n.title, r.title) as title,
        r.path,
        r.name,
        r.component,
        r.hidden,
        r.type,
        r.parent_id,
        r.order_num,
        r.icon,
        r.cache,
        r.redirect,
        r.terminal,
        r.always_show,
        r.context_menu,
        r.description,
        r.update_prompt,
        r.app_support,
        r.delete_flag,
        r.create_time,
        r.create_user_name,
        r.create_user_id,
        r.update_time,
        r.update_user_name,
        r.update_user_id,
        r.title as originalTitle
        FROM sfa_resources r
        LEFT JOIN sfa_resources_i18n i18n ON r.id = i18n.resource_id
        AND i18n.`ww_language` = #{language}
        AND i18n.delete_flag = 0
        WHERE r.delete_flag = 0
        AND r.id IN
        <foreach collection="menuIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>
</mapper>