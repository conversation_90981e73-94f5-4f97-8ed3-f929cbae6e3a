<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper

        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"

        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
   mapper标签:配置各类声明
   namespace：名称空间，由于映射文件有多个，为了防止crud语句的唯一标识被重复，可以设置空间名称。
 -->
<mapper namespace="com.wantwant.sfa.backend.domain.businessBd.mapper.BusinessBdSalaryControlMapper">

    <select id="getBusinessBDQuotaControl" resultType="com.wantwant.sfa.backend.salary.vo.BusinessBDQuotaConfigVo">
        SELECT
            cbov.organization_name3 as areaName,
            cbov.virtual_area_name as vareaName,
            cbov.province_name as provinceName,
            cbov.organization_name2 as companyName,
            ifnull(nsbdsc.last_month_balance,0) as balanceAmountCm,
            ifnull(sbdsc.last_month_balance,0) as cumulativeBalanceAmount,
            ifnull(sbdsc.avg_salary_package,0) as quota,
            ifnull(sbdsc.expired_quota,0) as expiredQuota,
            ifnull(sbdsc.organization_id,0) as currentOrgCode,
            ifnull(cbov.organization_type,0) as currentOrgType
        FROM
            sfa_business_bd_salary_control sbdsc
            INNER JOIN ceo_business_organization_view cbov ON cbov.organization_id = sbdsc.organization_id
            LEFT JOIN sfa_business_bd_salary_control nsbdsc on nsbdsc.the_year_month =
            DATE_FORMAT(DATE_ADD(STR_TO_DATE(CONCAT(sbdsc.the_year_month, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m')
            and sbdsc.organization_id = nsbdsc.organization_id
            and nsbdsc.delete_flag = 0
        <where>
            and sbdsc.the_year_month = #{req.theYearMonth}
            and cbov.business_group = #{req.businessGroup}
            and sbdsc.delete_flag = 0
            <if test="req.orgType != null and req.orgType != '' ">
                <choose>
                    <when test="req.orgType == 'area'">
                        and cbov.organization_id3 = #{req.orgCode}
                    </when>
                    <when test="req.orgType == 'varea'">
                        and cbov.virtual_area_id = #{req.orgCode}
                    </when>
                    <when test="req.orgType == 'province'">
                        and cbov.province_id = #{req.orgCode}
                    </when>
                    <when test="req.orgType == 'company'">
                        and cbov.organization_id2 = #{req.orgCode}
                    </when>
                    <when test="req.orgType == 'department'">
                        and cbov.department_id = #{req.orgCode}
                    </when>
                </choose>
            </if>
        </where>

    </select>

    <select id="selectBusinessBDControl" resultType="com.wantwant.sfa.backend.salary.vo.BusinessBDControlDetailVO">
        SELECT
        CONCAT_WS('/',cbov.organization_name3,cbov.virtual_area_name,cbov.province_name,cbov.organization_name2,cbov.department_name) as organizationName,
        sbbprc.organization_id,
        round(ifnull(sbbprc.salary_package_rate,0) * 100,0) as salaryPackageRate,
        sbbsc.avg_salary_package as avgSalaryPackage,
        sbbprc.update_user_name,
        DATE_FORMAT( sbbprc.update_time, '%Y-%m-%d %H:%i:%s') updateTime
        FROM
        sfa_business_bd_package_rate_config sbbprc
        LEFT JOIN sfa_business_bd_salary_control sbbsc ON sbbsc.organization_id = sbbprc.organization_id
        AND sbbsc.the_year_month = DATE_FORMAT(now(), '%Y-%m')
        INNER JOIN ceo_business_organization_view cbov on cbov.organization_id = sbbprc.organization_id
        <where>
            cbov.business_group = #{req.businessGroup}
            <if test="req.organizationType != null and req.organizationType != '' ">
                <choose>
                    <when test="req.organizationType == 'area'">
                        and cbov.organization_id3 = #{req.region}
                    </when>
                    <when test="req.organizationType == 'varea'">
                        and cbov.virtual_area_id = #{req.region}
                    </when>
                    <when test="req.organizationType == 'province'">
                        and cbov.province_id = #{req.region}
                    </when>
                    <when test="req.organizationType == 'company'">
                        and cbov.organization_id2 = #{req.region}
                    </when>
                    <when test="req.organizationType == 'department'">
                        and sbbsc.organization_id = #{req.region}
                    </when>
                </choose>
            </if>
        </where>
    </select>
    
    <!-- 查询表记录，保留重复的ID记录 -->
    <select id="selectWithDuplicateIds" resultType="java.util.Map">
        SELECT t.* FROM ${tableName} t
        JOIN (
            <foreach collection="idList" item="id" index="index" separator="UNION ALL">
                SELECT #{id} as id, #{index}+1 as position
            </foreach>
        ) vals ON t.${idColumn} = vals.id
        ORDER BY vals.position
    </select>
</mapper>