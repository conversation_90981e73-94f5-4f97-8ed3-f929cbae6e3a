<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper

        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"

        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
   mapper标签:配置各类声明
   namespace：名称空间，由于映射文件有多个，为了防止crud语句的唯一标识被重复，可以设置空间名称。
 -->
<mapper namespace="com.wantwant.sfa.backend.domain.emp.mapper.BusinessBDConfigMapper">

    <select id="selectOrgCodeByType" resultType="String">
        select distinct v.organization_id2 from sfa_business_bd_config c
        inner join ceo_business_organization_view v on v.organization_id = c.department_id
        <where>
            and c.delete_flag = 0 and c.check_type = #{type}
        </where>
    </select>
</mapper>