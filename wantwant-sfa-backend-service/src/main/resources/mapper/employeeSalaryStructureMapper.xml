<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryStructureMapper">

    <select id="listStructure" resultType="com.wantwant.sfa.backend.marketAndPersonnel.vo.StructureVO">
        SELECT
        s.id,v.organization_name3 as area,v.organization_name2 as company,v.department_name as departmentName,
        s.organization_id,c.classification,s.base_salary,s.bonus,
        s.start_date,s.end_date,s.updated_time,r1.employee_name as updatedBy
        from sfa_employee_salary_structure s
        LEFT JOIN sfa_company_classification c on c.organization_id = s.organization_id and c.is_delete = 0
        and DATE_FORMAT(c.effective_date,'%Y-%m') = #{params.yyyyMM}
        INNER JOIN ceo_business_organization_view v on v.organization_id = s.organization_id
        LEFT JOIN ceo_business_organization_position_relation r1 on r1.employee_id = s.updated_by and r1.channel = 3
        where s.is_delete = 0
        and DATE_FORMAT(s.start_date,'%Y-%m') &lt;= #{params.yyyyMM}
        and DATE_FORMAT(IFNULL(s.end_date,'2099-12-01'),'%Y-%m') >= #{params.yyyyMM}
        <if test="params.orgainizationId != null and params.orgainizationId != ''">
            and (v.organization_id3 = #{params.orgainizationId}
            or v.organization_id2 = #{params.orgainizationId}
            or v.department_id = #{params.orgainizationId})
        </if>
        <if test="params.areaOrganizationId != null and params.areaOrganizationId != ''">
            and v.organization_id3 = #{params.areaOrganizationId}
        </if>
        <if test="params.companyOrganizationId != null and params.companyOrganizationId != ''">
            and v.organization_id2 = #{params.companyOrganizationId}
        </if>
    </select>

    <select id="selectIncomplete" resultType="com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryStructurePO">
        select
        s.*
        from sfa_employee_salary_structure s
        where s.organization_id = #{organizationId} and s.is_delete = 0
        and DATE_FORMAT(s.start_date,'%Y%m') &lt;= #{startDate} and s.end_date is null
        ORDER BY id DESC LIMIT 1
    </select>

    <select id="selectStructureByDate" resultType="com.wantwant.sfa.backend.marketAndPersonnel.vo.StructureVO">
        select
        s.*
        from sfa_employee_salary_structure s
        where s.organization_id = #{organizationId} and s.is_delete = 0
        and s.start_date &lt;= #{date}
        and IFNULL(s.end_date,'2099-12-31') >= #{date}
        <if test="null != position and position != '' ">
            and s.position = #{position}
        </if>
        <if test="null != grade and grade != '' ">
            and s.grade = #{grade}
        </if>
        ORDER BY id DESC LIMIT 1
    </select>

    <select id="selectSalaryInfo" resultType="com.wantwant.sfa.backend.domain.emp.DO.SalaryDO">
        select
            ses.structure_id as id,
            ses.start_date ,

            ses.salary_level as `level`,
            ses.employee_base_salary  as baseSalary ,
            ses.employee_allowance  as allowance,
            ses.social_security_base ,
            ifnull(ses.employee_base_salary ,0) + ifnull(ses.employee_allowance,0) + ifnull(ses.social_security_base,0) total
        from
	    sfa_employee_salary ses
	    <where>
            and employee_info_id = #{employeeInfoId}
            and ses.is_delete  = 0 and ses.end_date  is null
        </where>

        limit 1
    </select>

    <select id="selectOnBoardBusinessBDSalary" resultType="com.wantwant.sfa.backend.salary.model.OrganizationUsedSalaryModel">
        SELECT
            sei.company_code as organizationId,
            sum(ifnull(ses.full_risk_fee, 0) + IFNULL(ses.travel_expenses, 0) + IFNULL(ses.employee_bonus, 0)
                    + IFNULL(ses.employee_allowance, 0) + IFNULL(ses.employee_base_salary, 0) + IFNULL(ses.social_security_base,0)) as usedQuota
        FROM
            sfa_employee_info sei
            INNER JOIN sfa_employee_salary ses ON ses.employee_info_id = sei.id
        <where>
            AND ses.is_delete = 0
            AND ses.end_date IS NULL
            AND sei.type IN (6, 7)
            AND sei.employee_status = 2
            <foreach collection="deptCodes" item="item" open=" and sei.company_code in (" close=")" separator=",">
                #{item}
            </foreach>
            <if test="excludeEmployeeInfoIds != null and excludeEmployeeInfoIds.size() > 0">
                <foreach collection="excludeEmployeeInfoIds" item="item" open=" and sei.id not in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by sei.company_code
    </select>

    <select id="selectPendingBusinessBDSalary" resultType="com.wantwant.sfa.backend.salary.model.OrganizationUsedSalaryModel">
        SELECT
            sam.branch_organization_id as organizationId,
            sum(IFNULL(sess.base_salary, 0) + IFNULL(sess.allowance, 0) + IFNULL(sess.bonus, 0)
                    + IFNULL(sess.full_risk_fee, 0) + IFNULL(sess.social_security_base, 0) + IFNULL(sess.travel_expenses, 0)) as usedQuota
        FROM
            sfa_salary_middleware ssm
            INNER JOIN sfa_apply_member sam ON sam.id = ssm.apply_id
            INNER JOIN sfa_employee_salary_structure sess ON sess.id = ssm.salary_structure_id
        WHERE
            ssm.`status` = 1
          AND ssm.delete_flag = 0
          AND sam.position = 7
          <foreach collection="deptCodes" item="item" open=" and sam.branch_organization_id in (" close=")" separator=",">
            #{item}
          </foreach>
          <if test="applyId != null">
              and sam.id != #{applyId}
          </if>
        group by sam.branch_organization_id
    </select>

    <select id="selectTransactionBD" resultType="com.wantwant.sfa.backend.domain.businessBd.DO.EmpSalaryDO">
        select a.employee_info_id,sta.transaction_value as salaryStructureId,sei.application_id as applyId,
               sei.company_code from sfa_transaction_apply a
        inner join sfa_transaction_process p on p.transaction_apply_id = a.id
        and p.delete_flag= 0
        inner join sfa_employee_info sei on sei.id = a.employee_info_id
        and sei.type in (6,7) and sei.employee_status in (1,2)
        left join sfa_transaction_action sta on sta.transaction_id = a.id
        and sta.change_column = 'salary_struct_id' and sta.delete_flag = 0

        <where>
            and a.transaction_type = 8 and p.process_result in (1,0)
            and a.execute_date is null and advice_execute_date like concat(#{theYearMonth},'%')
            and a.revert_status = 0
            <if test="companyCodes != null and companyCodes.size() > 0">
                <foreach collection="companyCodes" item="item" separator="," open=" and sei.company_code in (" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectOnBoardProcessingSalary" resultType="com.wantwant.sfa.backend.domain.businessBd.DO.OrgSalaryDO">
        select
            a.company_organization_id as organizationId,
            sum(
            IFNULL(sess.allowance,0) +
            IFNULL(sess.base_salary,0) +
            IFNULL(sess.bonus,0) +
            IFNULL(sess.social_security_base,0)+
            IFNULL(sess.travel_expenses,0) +
            IFNULL(sess.full_risk_fee,0)
            ) as totalSalary
        from sfa_apply_member a
        inner join sfa_interview_process p on p.application_id = a.id
        inner join sfa_salary_middleware m on m.apply_id = a.id
        and m.status = 1 and m.delete_flag = 0
        and m.type = 1
        inner join sfa_employee_salary_structure sess on sess.id = m.salary_structure_id
        <where>
            and p.process_result in (0,1,3) and a.ceo_type in (6,7)
            and (p.recommend_onboard_time is null or p.recommend_onboard_time like concat(#{theYearMonth},'%'))
            <if test="companyCodes != null and companyCodes.size() > 0">
                <foreach collection="companyCodes" item="item" separator="," open=" and a.company_organization_id in (" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="excludeApplyId != null">
                and a.id != #{excludeApplyId}
            </if>
        </where>
        group by a.company_organization_id
    </select>


    <select id="selectTransferringSalary" resultType="com.wantwant.sfa.backend.domain.businessBd.DO.OrgSalaryDO">
        SELECT  sum(
        IFNULL(sess.allowance,0) +
        IFNULL(sess.base_salary,0) +
        IFNULL(sess.bonus,0) +
        IFNULL(sess.social_security_base,0)+
        IFNULL(sess.travel_expenses,0) +
        IFNULL(sess.full_risk_fee,0)
        ) as totalSalary,
        vals.companyCode as organizationId
        FROM sfa_employee_salary_structure sess
        JOIN (

            <foreach collection="transactionEmpList" item="item"  separator="union all">
                select #{item.salaryStructureId} as structureId,#{item.companyCode} as companyCode
            </foreach>

        ) vals
        ON sess.id = vals.structureId
        group by vals.companyCode
    </select>
</mapper>
