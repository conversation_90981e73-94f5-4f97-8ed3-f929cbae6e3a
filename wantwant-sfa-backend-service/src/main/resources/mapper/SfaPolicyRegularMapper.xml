<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper

        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"

        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
   mapper标签:配置各类声明
   namespace：名称空间，由于映射文件有多个，为了防止crud语句的唯一标识被重复，可以设置空间名称。
 -->
<mapper namespace="com.wantwant.sfa.backend.mapper.policy.SfaPolicyRegularMapper">

    <select id="selectCeoMemberByRegular" resultType="Long">
        SELECT
            sei.member_key
        FROM
            sfa_position_relation spr
            INNER JOIN sfa_employee_info sei ON sei.id = spr.employee_info_id
            AND spr.`status` = 1
            AND spr.delete_flag = 0
            INNER JOIN sfa_apply_member a on a.id = sei.application_id
        <where>
            <foreach collection="regular" item="regular" open="AND (" close=")" separator="or">
                (
                <choose>
                    <when test="regular.businessGroup != null">
                        spr.business_group = #{regular.businessGroup}
                    </when>
                    <otherwise>
                        <choose>
                            <when test="regular.organizationType == 'area'">
                                 spr.area_code = #{regular.organizationCode}
                            </when>
                            <when test="regular.organizationType == 'varea'">
                                 spr.varea_code = #{regular.organizationCode}
                            </when>
                            <when test="regular.organizationType == 'province'">
                                 spr.province_code = #{regular.organizationCode}
                            </when>
                            <when test="regular.organizationType == 'company'">
                                 spr.company_code = #{regular.organizationCode}
                            </when>
                            <when test="regular.organizationType == 'department'">
                                 spr.department_code = #{regular.organizationCode}
                            </when>
                        </choose>
                    </otherwise>
                </choose>

                <if test="regular.businessCeo and regular.contractCeo">
                    and a.position = 1  and a.ceo_type in (2,3)
                </if>
                <if test="regular.businessCeo and !regular.contractCeo">
                    and a.position = 1  and a.ceo_type in (2)
                </if>
                <if test="!regular.businessCeo and regular.contractCeo">
                    and a.position = 1  and a.ceo_type in (3)
                </if>
                )
            </foreach>
        </where>
    </select>


    <insert id="saveLog">
        INSERT INTO `hotkidceo_production`.`sfa_policy_ceo_send_record`( `policy_id`, `member_key`, `send_time`, `delete_flag`)
        VALUES
        <foreach collection="memberKeyList" item="item" index="index" separator=",">
        (#{policyId},#{item},now(),0)
        </foreach>
    </insert>

    <select id="selectSendPolicyId" resultType="Long">
        SELECT
            distinct sp.policy_id
        FROM
            sfa_policy_regular spr
            INNER JOIN sfa_policy sp ON sp.policy_id = spr.policy_id
            AND sp.delete_flag = 0
            AND spr.delete_flag = 0
            LEFT JOIN sfa_policy_ceo_send_record spcsc on spcsc.policy_id = sp.policy_id
            and spcsc.member_key = #{memberKey} and spcsc.delete_flag = 0
        where sp.the_year_month = DATE_FORMAT(now(),'%Y-%m')
        and (
            spr.range_business_id = #{position.businessGroup}
            or
            spr.range_organization_id = #{position.areaCode}
            or
            spr.range_organization_id = #{position.vareaCode}
            or
            spr.range_organization_id = #{position.provinceCode}
            or
            spr.range_organization_id = #{position.companyCode}
            or
            spr.range_organization_id = #{position.departmentCode}
        )
        and spcsc.log_id is null

    </select>
</mapper>