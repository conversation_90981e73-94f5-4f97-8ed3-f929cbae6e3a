<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper

        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"

        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
   mapper标签:配置各类声明
   namespace：名称空间，由于映射文件有多个，为了防止crud语句的唯一标识被重复，可以设置空间名称。
 -->
<mapper namespace="com.wantwant.sfa.backend.mapper.salary.SalaryBigTableMapper">

    <select id="selectLastMonthSalary" resultType="com.wantwant.sfa.backend.salary.model.EmployeeSalaryModel">
        select
           hsei.id as employeeInfoId,
           IFNULL(mpaec.account_fee,0) as salary
        from
            management_post_actual_employment_cost mpaec
        inner join ods.hp_sfa_employee_info hsei on
            hsei.id = mpaec.employee_info_id
        <!-- 排除战区 -->
        inner join ods.hp_sfa_apply_member hsam on hsam.id = hsei.id
        and hsam.`position`  != 3
        <where>
            and mpaec.the_year_month  = #{theYearMonth}
            <choose>
                <when test="orgType == 'area'">
                    and hsei.area_code  = #{orgCode}
                </when>
                <when test="orgType == 'varea'">
                    and hsei.varea_organization_id  = #{orgCode}
                </when>
                <when test="orgType == 'province'">
                    and hsei.position_id  = #{orgCode}
                </when>
                <when test="orgType == 'company'">
                    and hsei.company_code  = #{orgCode}
                </when>
            </choose>
            <if test="excludeEmployeeInfoIds != null and excludeEmployeeInfoIds.size() >0">
                <foreach collection="excludeEmployeeInfoIds" item="item" open=" and mpaec.employee_info_id not in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>


    <select id="selectLastMonthSalaryById" resultType="java.math.BigDecimal">
        select
            IFNULL(mpaec.account_fee,0) as salary
        from
            management_post_actual_employment_cost mpaec
        inner join ods.hp_sfa_employee_info hsei on
        hsei.id = mpaec.employee_info_id
        <!-- 排除战区 -->
        inner join ods.hp_sfa_apply_member hsam on hsam.id = hsei.id
        and hsam.`position`  != 3
        <where>
            and mpaec.the_year_month  = #{theYearMonth}
            and mpaec.employee_info_id = #{employeeInfoId}
        </where>
        limit 1
    </select>


    <select id="selectRealSalary" resultType="com.wantwant.sfa.backend.salary.vo.SalaryAllocatedVo">
        select
            hsei.id as employeeInfoId,
            mpaec.actual_attendance_salary,
            IFNULL( mpaec.enterprise_social_security,0) + IFNULL( mpaec.enterprise_accumulation_fund ,0) as socialSecurityBase,
            IFNULL( mpaec.total_achievement_bonus ,0) + IFNULL(mpaec.taxes_and_surcharges,0) as bonus,
            mpaec.travel_expenses ,
            mpaec.full_risk_service_fee as fullRiskFee
        from
            management_post_actual_employment_cost mpaec
            inner join ods.hp_sfa_employee_info hsei
            on hsei.member_key  = mpaec.memberkey
        <where>
            and the_year_month  = #{lastMonth}
            <foreach collection="empIds" item="item" open=" and hsei.id in (" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectSalaryPackage" resultType="com.wantwant.sfa.backend.salary.model.SalaryPackageModel">
        SELECT
            case position_type_id
                when 1 then area_id
                when 12 then varea_id
                when 11 then province_id
                when 2 then company_id
                end as organizationId,
            balance_amount_cm,
            cumulative_balance_amount
        FROM
            ads_employee_cost_package
        <where>
            and the_quater = #{theYearMonth}
            <foreach collection="salaryPackageSearchModelList" item="item" open=" and (" separator="or" close=")">
                (
                    CASE #{item.positionTypeId}
                    WHEN 1 THEN area_id
                    WHEN 12 THEN varea_id
                    WHEN 11 THEN province_id
                    WHEN 2 THEN company_id
                    ELSE NULL
                    END = #{item.organizationId}
                    AND position_type_id = #{item.positionTypeId}
                )
            </foreach>
        </where>
    </select>

    <select id="selectMaxYearMonth" resultType="String">
        select MAX(the_quater) from ads_employee_cost_package where the_quater  not like '%Q%'
    </select>

    <select id="selectSalaryPackageDetail" resultType="com.wantwant.sfa.backend.salary.vo.SalaryPackageDetailVO">
        SELECT
            case position_type_id
            when 1 then area_name
            when 12 then varea_name
            when 11 then province_name
            when 2 then company_name
            end as organizationName,
            the_quater as theYearMonth,
            balance_amount_cm,
            cumulative_balance_amount
        FROM
            ads_employee_cost_package
        <where>
            <if test="req.theYearMonth != null and req.theYearMonth != '' ">
                and the_quater = #{theYearMonth}
            </if>

            <if test="req.organizationId != null and req.organiationId != '' ">
                CASE #{item.positionTypeId}
                WHEN 1 THEN area_id
                WHEN 12 THEN varea_id
                WHEN 11 THEN province_id
                WHEN 2 THEN company_id
                ELSE NULL
                END = #{req.organizationId}
                AND position_type_id = #{req.positionTypeId}
            </if>

            and the_quater  not like '%Q%'
        </where>
        order by the_quater desc
    </select>

    <select id="selectBusinessBDPackage" resultType="com.wantwant.sfa.backend.salary.model.BusinessBDPackageModel">
        SELECT
            company_id as companyCode,
            sum(avg_salary_package) avgSalaryPackage
        FROM
            ads_BD_cost_package
        <where>
            and the_year_month  = #{theYearMonth}
        </where>
        group by company_id
    </select>

    <select id="selectActualSalary" resultType="com.wantwant.sfa.backend.salary.model.ActualSalaryModel">
        SELECT
            employee_info_id,
            real_post_allowance,
            actual_attendance_salary,
            enterprise_social_security,
            enterprise_accumulation_fund,
            total_achievement_bonus,
            travel_expenses,
            full_risk_service_fee,
            taxes_and_surcharges,
            account_fee
        FROM
            management_post_actual_employment_cost
        <where>
            and the_year_month = #{theYearMonth}
        </where>
    </select>
</mapper>