<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wantwant.sfa.backend.domain.businessBd.mapper.BusinessBdSalaryHistoryMapper">

    <select id="selectHistoryTotalSalary" resultType="com.wantwant.sfa.backend.domain.businessBd.DO.OrgSalaryDO">
        select company_id as organizationId,
               ifnull(sum(IFNULL(employee_base_salary,0)+
                   IFNULL(employee_bonus,0) +
                   IFNULL(employee_allowance,0) +
                   IFNULL(social_security_base,0) +
                   IFNULL(travel_expenses,0) +
                   IFNULL(full_risk_fee,0)
               ),0) as totalSalary
        from sfa_business_bd_salary_history
        <where>
            and the_year_month = #{theYearMonth}
            and delete_flag = 0
        </where>
        group by company_id
    </select>


    <select id="selectHistory" resultType="com.wantwant.sfa.backend.domain.businessBd.DO.SalaryAllocatedDO">
       SELECT
            sbdsh.employee_info_id,
            cbov.organization_name3 as areaName,
            cbov.virtual_area_name as vareaName,
            cbov.province_name,
            cbov.organization_name2 as companyName,
            cbov.department_name,
            sbdsh.salary_position,
            sei.employee_status,
            sei.employee_name,
            sbdsh.salary_level,
            IFNULL(sbdsh.employee_bonus,0) as bonus,
            IFNULL(sbdsh.employee_base_salary,0) as baseSalary,
            IFNULL(sbdsh.employee_allowance,0) as allowance,
            IFNULL(sbdsh.social_security_base,0) as socialSecurityBase,
            IFNULL(sbdsh.travel_expenses,0) as travelExpenses,
            IFNULL(sbdsh.full_risk_fee,0) as fullRiskFee,
            IFNULL(sbdsh.full_risk_fee,0) + IFNULL(sbdsh.employee_bonus,0)  + IFNULL(sbdsh.employee_base_salary,0) +
            IFNULL(sbdsh.employee_allowance,0) + IFNULL(sbdsh.social_security_base,0) + IFNULL(sbdsh.travel_expenses,0) as total

        FROM
            sfa_business_bd_salary_history sbdsh
            INNER JOIN ceo_business_organization_view cbov ON cbov.organization_id = sbdsh.department_id
            INNER JOIN sfa_employee_info sei on sei.id = sbdsh.employee_info_id
        <where>
            and cbov.business_group = #{req.businessGroup}
            and sbdsh.the_year_month = #{req.theYearMonth}
            and sbdsh.delete_flag = 0
            <if test="req.orgType != null and req.orgType != '' ">
                <choose>
                    <when test="req.orgType == 'area'">
                        AND  sei.area_code = #{req.orgCode}
                    </when>
                    <when test="req.orgType == 'varea'">
                        AND  sei.varea_organization_id = #{req.orgCode}
                    </when>
                    <when test="req.orgType == 'province'">
                        AND  sei.province_organization_id = #{req.orgCode}
                    </when>
                    <when test="req.orgType == 'company'">
                        AND  sei.company_code = #{req.orgCode}
                    </when>
                    <when test="req.orgType == 'department'">
                        AND  sei.department_code = #{req.orgCode}
                    </when>
                </choose>
            </if>

            <choose>
                <when test="req.positionTypeId != null and req.positionTypeId == 1">
                    and sbdsh.salary_position = 501
                </when>
                <when test="req.positionTypeId != null and req.positionTypeId == 2">
                    and sbdsh.salary_position = 502
                </when>
            </choose>
        </where>

    </select>
</mapper>
