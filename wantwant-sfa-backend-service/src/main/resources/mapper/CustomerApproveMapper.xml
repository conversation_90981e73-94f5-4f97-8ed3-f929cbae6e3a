<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wantwant.sfa.backend.mapper.CustomerApproveMapper">

    <resultMap id="customerApproveMap" type="com.wantwant.sfa.backend.customer.vo.CustomerApproveVO">
        <id column="customerId" property="customerId"/>
        <result column="customerType" property="customerType"/>
        <result column="contact" property="contact"/>
        <result column="contactPhone" property="contactPhone"/>
        <result column="customerName" property="customerName"/>
        <result column="contactAddress" property="contactAddress"/>
        <result column="businessLicenseId" property="businessLicenseId"/>
        <result column="businessLicenseImg" property="businessLicenseImg"/>
        <result column="terminalNumber" property="terminalNumber"/>
        <result column="businessNumber" property="businessNumber"/>
        <result column="vehicleNumber" property="vehicleNumber"/>
        <result column="monthAccountMoney" property="monthAccountMoney"/>
        <result column="commitTime" property="commitTime"/>
        <result column="invoicingSystem" property="invoicingSystem"/>
        <result column="customerStatus" property="customerStatus"/>
        <result column="updator" property="updator"/>
        <result column="updatorName" property="updatorName"/>
        <result column="departmentEmployeeId" property="departmentEmployeeId"/>
        <result column="companyEmployeeId" property="companyEmployeeId"/>
        <result column="area" property="area"/>
        <result column="company" property="company"/>
        <result column="position" property="position"/>
        <result column="employeeName" property="employeeName"/>
        <result column="mobile" property="mobile"/>
        <result column="processStep" property="processStep"/>
        <result column="results" property="results"/>
        <result column="companyCode" property="companyCode"/>
        <result column="storeType" property="storeType"/>
        <result column="headStoreId" property="headStoreId"/>
        <result column="businessNature" property="businessNature"/>
        <result column="businessPosition" property="businessPosition"/>
        <result column="businessName" property="businessName"/>
        <result column="businessContact" property="businessContact"/>
        <result column="businessConcatPhone" property="businessConcatPhone"/>
        <result column="businessConcatAddress" property="businessConcatAddress"/>
        <result column="businessPhoto" property="businessPhoto"/>
        <result column="businessCooperationAgreement" property="businessCooperationAgreement"/>
        <result column="shopType" property="shopType"/>
        <result column="departMentName" property="departMentName"/>
        <result column="openType" property="openType"/>
        <result column="storeSize" property="storeSize"/>
        <result column="storeSize" property="storeSize"/>
        <result column="storeImg" property="storeImg"/>
        <result column="varea" property="varea"/>
        <result column="province" property="province"/>
        <result column="organizationCode" property="organizationCode"/>
        <result column="creator" property="creator"/>
        <result column="businessGroupId" property="businessGroupId"/>
        <result column="operatingBrand" property="operatingBrand"/>
        <result column="storeInsideImg" property="storeInsideImg"/>
        <result column="channelType" property="channelType"></result>
        <result column="channelTypeStr" property="channelTypeStr"></result>
        <result column="salesRoomType" property="salesRoomType"></result>
        <result column="salesRoomTypeStr" property="salesRoomTypeStr"></result>
        <result column="chainSystemFlag" property="chainSystemFlag"></result>
        <result column="systemRegionName" property="systemRegionName"></result>
        <result column="salesRoomType" property="salesRoomType"></result>
        <result column="belongDistributor" property="belongDistributor"></result>
        <result column="type" property="type"></result>
        <result column="postType" property="postType"></result>
        <collection property="scopeList" ofType="com.wantwant.sfa.backend.customer.vo.CustomerRegionVO" columnPrefix="s_">
            <result column="marketName" property="marketName"/>
            <result column="population" property="population"/>
        </collection>
    </resultMap>

    <sql id="column_list">
        ci.id,ci.customer_id as customerId,
        ci.customer_type as customerType,
        ci.contact as contact,
        ci.contact_phone as contactPhone,
        ci.customer_name as customerName,

        ci.channel_type as channelType,
        ci.salesroom_type as salesRoomType,

        CONCAT_WS('/',ci.contact_address,ci.detail_address) as contactAddress,
        ci.business_license_id as businessLicenseId,
        ci.business_license_img as businessLicenseImg,
        ci.terminal_number as terminalNumber,ci.business_number as businessNumber,
        ci.vehicle_number as vehicleNumber,
        ci.month_account_money as monthAccountMoney,
        ci.commit_time as commitTime,
        ci.invoicing_system as invoicingSystem,
        ci.customer_status as customerStatus,
        sciv.process_step as processStep,
        sciv.result as results,ci.updator,
        v.organization_name3 as area,
        v.organization_name2 as company,
        v.organization_id2 as companyCode,
        r.position_type_id as position,
        ei.employee_name as employeeName,
        ei.mobile as mobile,
        sr.market_name as s_marketName,
        sr.population as s_population,
        if(sr.market_name is not null and sr.market_name != '',CONCAT_WS('',sr.market_name,'(',ifnull(sr.population,'-'),')'),'') as s_scope,
        ci.store_type as storeType ,
        ci.head_store_id as headStoreId,
        ci.business_nature as businessNature,
        ci.business_district_position as businessPosition,
        ci.business_district_name as businessName,
        ci.business_district_contact as businessContact,
        ci.business_district_mobile as businessConcatPhone,
        CONCAT_WS('/',ci.business_district_contact_address,ci.business_district_detail_address) AS businessConcatAddress ,
        ci.business_district_image as businessPhoto,
        ci.business_district_contract_image as businessCooperationAgreement,
        ci.shop_type as shopType,
        v.department_name as departMentName,
        r1.employee_name as updatorName,
        ci.store_size AS storeSize,
        ci.open_type AS openType,ci.store_img AS storeImg,
        ci.operating_brand AS operatingBrand,
        ci.store_inside_img AS storeInsideImg
    </sql>

    <sql id="column_list1">
        ci.id,ci.customer_id as customerId,ci.customer_type as customerType,ci.contact as contact,
        ci.contact_phone as contactPhone,ci.customer_name as customerName,CONCAT_WS('/',ci.contact_address,ci.detail_address) as contactAddress,
        ci.business_license_id as businessLicenseId,ci.business_license_img as businessLicenseImg,ci.terminal_number as terminalNumber,ci.business_number as businessNumber,
        ci.vehicle_number as vehicleNumber,ci.month_account_money as monthAccountMoney,ci.commit_time as commitTime,
        ci.invoicing_system as invoicingSystem,ci.customer_status as customerStatus,sciv.process_step as processStep,sciv.result as results,ci.updator,
        v.organization_name3 as area,v.organization_name2 as company,v.organization_id2 as companyCode,r.position_type_id as position,
        ei.employee_name as employeeName,ei.mobile as mobile,ci.store_type as storeType ,ci.head_store_id as headStoreId,ci.shop_type as shopType,
        v.department_name as departMentName
    </sql>

    <sql id="where_list">
        and ci.delete_flag = 0 and ci.open_type in (0,2) and ci.customer_status in (1,2,3,4)
        <if test="params.commitTimeStart != null and params.commitTimeStart != '' ">
            <bind name="commitTimeStart" value="params.commitTimeStart + ' 00:00:00'"/>
            and ci.commit_time >= #{commitTimeStart}
        </if>
        <if test="params.commitTimeEnd != null and params.commitTimeEnd != '' ">
            <bind name="commitTimeEnd" value="params.commitTimeEnd + ' 23:59:59'"/>
            and ci.commit_time &lt;= #{commitTimeEnd}
        </if>
        <if test="params.businessLicenseId != null and params.businessLicenseId != '' ">
            and ci.business_license_id = #{params.businessLicenseId}
        </if>
        <if test="params.customerKey != null and params.customerKey != '' ">
            and (ci.customer_id like CONCAT('%',#{params.customerKey},'%') or ci.contact like CONCAT('%',#{params.customerKey},'%')
            or ci.contact_phone like CONCAT('%',#{params.customerKey},'%') or ci.customer_name like CONCAT('%',#{params.customerKey},'%') )
        </if>
        <if test="params.employeeKey != null and params.employeeKey != '' ">
            and (ei.employee_name like CONCAT('%',#{params.employeeKey},'%') or ei.mobile like CONCAT('%',#{params.employeeKey},'%'))
        </if>
        <if test="params.customerType != null ">
            and ci.customer_type = #{params.customerType}
        </if>
        <if test="params.shopType != null ">
            and ci.shop_type = #{params.shopType}
        </if>
        <if test="params.channelType != null">
            and ci.channel_type = #{params.channelType}
        </if>
        <if test="params.salesRoomType != null">
            and ci.salesroom_type = #{params.salesRoomType}
        </if>
        <if test="params.invoicingSystem != null">
            and ci.invoicing_system = #{params.invoicingSystem}
        </if>
        <if test="params.openType != null">
            and ci.open_type = #{params.openType}
        </if>
        <choose>
            <when test="params.customerStatus != null  and params.customerStatus == 1">
                and ci.customer_status = 1
            </when>
            <when test="params.customerStatus != null  and params.customerStatus == 2">
                and ci.customer_status = 2
            </when>
            <when test="params.customerStatus != null  and params.customerStatus == 3">
                and ci.customer_status = 3
            </when>
            <when test="params.customerStatus != null  and params.customerStatus == 4">
                and ci.customer_status = 4
            </when>
        </choose>

        <if test="params.position != null ">
            and r.position_type_id = #{params.position}
        </if>
        <if test="params.areaOrganizationId != null and params.areaOrganizationId != '' ">
            and pr.area_code = #{params.areaOrganizationId}
        </if>
        <if test="params.companyOrganizationId != null and params.companyOrganizationId != '' ">
            and pr.company_code = #{params.companyOrganizationId}
        </if>
        <if test="params.departmentId != null and params.departmentId != '' ">
            and pr.department_code = #{params.departmentId}
        </if>
        <if test="params.vareaId != null and params.vareaId != '' ">
            and pr.varea_code = #{params.vareaId}
        </if>
        <if test="params.provinceId != null and params.provinceId != '' ">
            and pr.province_code = #{params.provinceId}
        </if>
        <if test="params.headStoreId != null and params.headStoreId != '' ">
            and ci.head_store_id = #{params.headStoreId}
        </if>
    </sql>


    <select id="pageCustomerApproveIds" resultMap="customerApproveMap">
        SELECT
        <include refid="column_list1"/>,r1.employee_name as updatorName,ci.store_size AS storeSize,ci.store_img AS storeImg,ci.open_type as openType, ci.creator AS creator
        from customer_info ci
        INNER JOIN sfa_customer sc on sc.memberKey = ci.creator and sc.channel = 3
        INNER JOIN ceo_business_organization_position_relation r on r.position_id = sc.position_id
        INNER JOIN sfa_employee_info ei on ei.mobile = sc.mobile_number
        INNER JOIN ceo_business_organization_view v ON v.organization_id = r.organization_id and v.organization_id3 not in ('ZB','TEST6','TS')
        <if test=" params.organizationId != null and params.organizationId != ''
        and params.organizationId != 'ZB_Z' and params.organizationId != 'ZB_S' and params.organizationId != 'ZB' ">
            INNER JOIN ceo_business_organization_tree cbot on cbot.organization_id = r.organization_id and cbot.organization_parent_id = #{params.organizationId}
        </if>
        LEFT JOIN customer_distribution_info di on di.customer_id = ci.customer_id and di.delete_flag = 0
        LEFT JOIN ceo_business_organization_position_relation r1 on r1.employee_id = ci.updator and r.channel = r1.channel
        LEFT JOIN sfa_customer_info_verify sciv ON ci.customer_id = sciv.customer_id and sciv.delete_flag = 0
        <where>
            ci.customer_id in
            <foreach item="id" collection="ids" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </where>
        ORDER BY
        <choose>
            <when test="params.sortField == 1 "> terminalNumber</when>
            <when test="params.sortField == 2 "> businessNumber</when>
            <when test="params.sortField == 3 "> vehicleNumber</when>
            <when test="params.sortField == 4 "> monthAccountMoney</when>
            <when test="params.sortField == 5 "> commitTime</when>
            <otherwise>
                commitTime
            </otherwise>
        </choose>
        <choose>
            <when test="params.sortOrder == 1 "> asc</when>
            <when test="params.sortOrder == 2 "> desc</when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="listCustomerApprove" resultMap="customerApproveMap">
        SELECT
        <include refid="column_list1"/>,r1.employee_name as updatorName
        from customer_info ci
        INNER JOIN sfa_customer sc on sc.memberKey = ci.creator
        INNER JOIN ceo_business_organization_position_relation r on r.position_id = sc.position_id
        INNER JOIN sfa_employee_info ei on ei.position_id = r.position_id
        INNER JOIN ceo_business_organization_view v ON v.organization_id = r.organization_id and v.organization_id3 not in ('ZB','TEST6','TS')
        <if test=" params.organizationId != null and params.organizationId != ''
        and params.organizationId != 'ZB_Z' and params.organizationId != 'ZB_S' and params.organizationId != 'ZB' ">
            INNER JOIN ceo_business_organization_tree cbot on cbot.organization_id = r.organization_id and cbot.organization_parent_id = #{params.organizationId}
        </if>
        LEFT JOIN customer_distribution_info di on di.customer_id = ci.customer_id and di.delete_flag = 0
        LEFT JOIN ceo_business_organization_position_relation r1 on r1.employee_id = ci.updator and r.channel = r1.channel
        LEFT JOIN sfa_customer_info_verify sciv ON ci.customer_id = sciv.customer_id and sciv.delete_flag = 0
        <where>
            <include refid="where_list"/>
        </where>
        ORDER BY
        <choose>
            <when test="params.sortField == 1 "> terminalNumber</when>
            <when test="params.sortField == 2 "> businessNumber</when>
            <when test="params.sortField == 3 "> vehicleNumber</when>
            <when test="params.sortField == 4 "> monthAccountMoney</when>
            <when test="params.sortField == 5 "> commitTime</when>
            <otherwise>
                commitTime
            </otherwise>
        </choose>
        <choose>
            <when test="params.sortOrder == 1 "> asc</when>
            <when test="params.sortOrder == 2 "> desc</when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="customerModifyList" resultMap="customerApproveMap">
        SELECT
        ci.id,ci.customer_id as customerId,ci.customer_type as customerType,ci.contact as contact,
        ci.contact_phone as contactPhone,ci.customer_name as customerName,CONCAT_WS('/',ci.contact_address,ci.detail_address) as contactAddress,
        ci.business_license_id as businessLicenseId,ci.business_license_img as businessLicenseImg,ci.terminal_number as terminalNumber,ci.business_number as businessNumber,
        ci.vehicle_number as vehicleNumber,ci.month_account_money as monthAccountMoney,ci.commit_time as commitTime,
        ci.invoicing_system as invoicingSystem,ci.customer_status as customerStatus,sciv.process_step as processStep,sciv.result as results,ci.updator,
        v.organization_name3 as area,v.organization_name2 as company,v.organization_id2 as companyCode,r.position_type_id as position,
        ei.employee_name as employeeName,ei.mobile as mobile,
        r1.employee_name as updatorName
        from customer_info ci
        INNER JOIN sfa_customer sc on sc.memberKey = ci.creator
        INNER JOIN ceo_business_organization_position_relation r on r.position_id = sc.position_id
        INNER JOIN sfa_employee_info ei on ei.position_id = r.position_id
        INNER JOIN ceo_business_organization_view v ON v.organization_id = r.organization_id and v.organization_id3 not in ('ZB','TEST6','TS')
        <if test=" params.organizationId != null and params.organizationId != ''
        and params.organizationId != 'ZB_Z' and params.organizationId != 'ZB_S' and params.organizationId != 'ZB' ">
            INNER JOIN ceo_business_organization_tree cbot on cbot.organization_id = r.organization_id and cbot.organization_parent_id = #{params.organizationId}
        </if>
        LEFT JOIN ceo_business_organization_position_relation r1 on r1.employee_id = ci.updator and r.channel = r1.channel
        LEFT JOIN sfa_customer_info_verify sciv ON ci.customer_id = sciv.customer_id and sciv.delete_flag = 0
        <where>
            <include refid="where_list"/>
        </where>
    </select>

    <select id="getCustomerApproveById" resultMap="customerApproveMap">
        select
        <include refid="column_list"/>
        from customer_info ci
        INNER JOIN sfa_customer sc on sc.memberKey = ci.creator
        INNER JOIN ceo_business_organization_position_relation r on r.position_id = sc.position_id
        INNER JOIN sfa_employee_info ei on ei.position_id = r.position_id
        INNER JOIN ceo_business_organization_view v ON v.organization_id = r.organization_id and v.organization_id3 not in ('ZB','TEST6','TS')
        LEFT JOIN customer_distribution_info di on di.customer_id = ci.customer_id and di.delete_flag = 0
        <if test=" storeType == null or storeType == 0 or storeType == 1">
            LEFT JOIN sfa_small_market_region_relation ssmrr on ssmrr.small_market_id = di.area_code and ssmrr.delete_flag = 0
            LEFT JOIN sfa_region sr on sr.market_code = ssmrr.village_code and sr.status = 1
        </if>
        <if test=" storeType != null and  storeType == 2">
            LEFT JOIN sfa_region sr on sr.market_code = di.area_code and sr.status = 1
        </if>
        LEFT JOIN ceo_business_organization_position_relation r1 on r1.employee_id = ci.updator and r.channel = r1.channel
        LEFT JOIN sfa_customer_info_verify sciv ON ci.customer_id = sciv.customer_id and sciv.delete_flag = 0
        where ci.customer_id = #{customerId}
    </select>
    
    <select id="getCustomerStoreType" resultType="Integer">
        select store_type
        from customer_info
        where customer_id = #{customerId}
    </select>

    <select id="listWarehouse" resultType="com.wantwant.sfa.backend.customer.vo.CustomerWarehouseVO">
        select cw.customer_id as customerId,cw.warehouse_address as warehouseAddress,round(cw.warehouse_area,0) as warehouseArea
        from customer_warehouse cw
        where cw.customer_id = #{customerId} and cw.delete_flag = 0 and cw.warehouse_address is not null and cw.warehouse_area is not null
    </select>
    <select id="pageCustomerApprove1" resultType="java.lang.String">
        SELECT
        distinct ci.customer_id
        FROM
        customer_info ci
        INNER JOIN sfa_employee_info ei ON ei.member_key = ci.creator
        INNER JOIN sfa_position_relation pr ON ei.id = pr.employee_info_id
        INNER JOIN ceo_business_organization_tree cbot on cbot.organization_id = pr.organization_code
        INNER JOIN ceo_business_organization_position_relation r on r.organization_id = cbot.organization_id
        LEFT JOIN sfa_customer_info_verify sciv ON ci.customer_id = sciv.customer_id AND sciv.delete_flag = 0
        <where>
            <include refid="where_list"/>
            AND pr.business_group = #{params.businessGroupId}
            AND pr.part_time = '0'
            AND pr.delete_flag = '0'
            AND pr.status = '1'
            <if test="params.businessGroup != null and params.businessGroup != ''">
            AND ci.audit_product_group_id = #{params.businessGroup}
            </if>
            <if test="params.deOrganizationIds != null and params.deOrganizationIds.size() > 0">
                AND cbot.organization_parent_id IN
                <foreach collection="params.deOrganizationIds" item="organizationId" index="index" separator="," open="(" close=")" >
                    #{organizationId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="pageCustomerApproveIds1" resultMap="customerApproveMap">
        SELECT
        ci.id,
        ci.customer_id AS customerId,
        ci.customer_type AS customerType,
        ci.contact AS contact,
        ci.contact_phone AS contactPhone,
        ci.customer_name AS customerName,
        CONCAT_WS( '/', ci.contact_address, ci.detail_address ) AS contactAddress,
        ci.business_license_id AS businessLicenseId,
        ci.business_license_img AS businessLicenseImg,
        ci.terminal_number AS terminalNumber,
        ci.business_number AS businessNumber,
        ci.vehicle_number AS vehicleNumber,
        ci.month_account_money AS monthAccountMoney,
        ci.commit_time AS commitTime,
        ci.invoicing_system AS invoicingSystem,
        ci.customer_status AS customerStatus,
        sciv.process_step AS processStep,
        sciv.result AS results,
        ci.updator,
        ei.employee_name AS employeeName,
        ei.mobile AS mobile,
        ci.store_type AS storeType,
        ci.head_store_id AS headStoreId,
        ci.shop_type AS shopType,
        cbov.organization_name3  as area,
        cbov.virtual_area_name as varea,
        cbov.province_name  as province,
        cbov.organization_name2 as company,
        cbov.department_name  as departmentName,
        cbo.organization_id  AS organizationCode,
        ci.store_size AS storeSize,
        ci.store_img AS storeImg,
        ci.open_type AS openType,
        pr.position_type_id AS position,
        r1.employee_name AS updatorName,
        r1.employee_id AS departmentEmployeeId,
        r2.employee_id AS companyEmployeeId,
        ci.creator AS creator,
        bg.id AS businessGroupId,
        ci.channel_type as channelType,
        ci.salesroom_type as salesRoomType,
        ci.chain_system_flag as chainSystemFlag,
        cisr.system_region_name as systemRegionName,
        cdiei.employee_name as belongDistributor,
        ei.type as type,
        ei.post_type as postType
        FROM
        customer_info ci
        INNER JOIN sfa_employee_info ei ON ei.member_key = ci.creator
        INNER JOIN sfa_business_group bg ON bg.business_group_code = ci.audit_product_group_id
        INNER JOIN ceo_business_organization cbo on cbo.organization_name  = ci.audit_organization
        and cbo.business_group  = bg.id
        and cbo.organization_type  != 'branch'
        left join ceo_business_organization_view cbov on cbov.organization_id  = cbo.organization_id
        left JOIN sfa_position_relation pr ON ei.position_id = pr.position_id
        and pr.status = 1 and pr.delete_flag = 0
        left join ceo_interlock_system_region cisr on cisr.system_region_code = ci.system_region_code and cisr.delete_flag = 0
        left join customer_distributor_info cdi on ci.customer_id = cdi.customer_id and cdi.delete_flag = 0
        left join sfa_employee_info cdiei on cdi.distributor_member_key = cdiei.member_key
        LEFT JOIN customer_distribution_info di ON di.customer_id = ci.customer_id
        AND di.delete_flag = 0
        LEFT JOIN sfa_customer_info_verify sciv ON ci.customer_id = sciv.customer_id
        AND sciv.delete_flag = 0
        LEFT JOIN ceo_business_organization_position_relation r1 ON r1.organization_id = cbo.organization_id and r1.position_type_id = 10 and r1.employee_id is not null and r1.employee_id != ''
        LEFT JOIN ceo_business_organization_position_relation r2 ON r2.organization_id = r1.organization_parent_id and r1.position_type_id = 2 and r2.employee_id is not null and r2.employee_id != ''

        <where>
            ci.customer_id in
            <foreach item="id" collection="ids" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </where>
        ORDER BY
        <choose>
            <when test="params.sortField == 1 "> terminalNumber</when>
            <when test="params.sortField == 2 "> businessNumber</when>
            <when test="params.sortField == 3 "> vehicleNumber</when>
            <when test="params.sortField == 4 "> monthAccountMoney</when>
            <when test="params.sortField == 5 "> commitTime</when>
            <otherwise>
                commitTime
            </otherwise>
        </choose>
        <choose>
            <when test="params.sortOrder == 1 "> asc</when>
            <when test="params.sortOrder == 2 "> desc</when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="queryBusinessGroupCode" resultType="java.lang.String">
        SELECT
            bg.business_group_code
        FROM
            sfa_business_group bg
        WHERE bg.id = #{id}
    </select>
    <select id="querySmallMarketByCustomerId" resultType="java.lang.String">
        SELECT
            area_code
        FROM
            customer_distribution_info  ci
        WHERE
            ci.customer_id = #{customerId}
          AND ci.delete_flag = 0
    </select>
    
    <!-- 统计可审核的客户数量（BShowBtn为true的数量） -->
    <select id="countAuditableCustomers" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM customer_info ci
        INNER JOIN sfa_employee_info ei ON ei.member_key = ci.creator
        INNER JOIN sfa_business_group bg ON bg.business_group_code = ci.audit_product_group_id
        INNER JOIN ceo_business_organization cbo on cbo.organization_name = ci.audit_organization
            and cbo.business_group = bg.id
            and cbo.organization_type != 'branch'
        LEFT JOIN ceo_business_organization_view cbov on cbov.organization_id = cbo.organization_id
        LEFT JOIN sfa_position_relation pr ON ei.position_id = pr.position_id
            and pr.status = 1 and pr.delete_flag = 0
        LEFT JOIN ceo_interlock_system_region cisr on cisr.system_region_code = ci.system_region_code and cisr.delete_flag = 0
        LEFT JOIN customer_distributor_info cdi on ci.customer_id = cdi.customer_id and cdi.delete_flag = 0
        LEFT JOIN sfa_employee_info cdiei on cdi.distributor_member_key = cdiei.member_key
        LEFT JOIN customer_distribution_info di ON di.customer_id = ci.customer_id AND di.delete_flag = 0
        LEFT JOIN sfa_customer_info_verify sciv ON ci.customer_id = sciv.customer_id AND sciv.delete_flag = 0
        LEFT JOIN ceo_business_organization_position_relation r1 ON r1.organization_id = cbo.organization_id 
            and r1.position_type_id = 10 and r1.employee_id is not null and r1.employee_id != ''
        LEFT JOIN ceo_business_organization_position_relation r2 ON r2.organization_id = r1.organization_parent_id 
            and r2.position_type_id = 2 and r2.employee_id is not null and r2.employee_id != ''
        <where>
            <include refid="where_list"/>
            AND pr.business_group = #{params.businessGroupId}
            AND pr.part_time = '0'
            AND pr.delete_flag = '0'
            AND pr.status = '1'
            <if test="params.businessGroup != null and params.businessGroup != ''">
                AND ci.audit_product_group_id = #{params.businessGroup}
            </if>
            <if test="params.deOrganizationIds != null and params.deOrganizationIds.size() > 0">
                AND cbo.organization_id IN
                <foreach collection="params.deOrganizationIds" item="organizationId" index="index" separator="," open="(" close=")" >
                    #{organizationId}
                </foreach>
            </if>
            <!-- BShowBtn判断逻辑：只统计待审核状态的客户 -->
            AND ci.customer_status = 1
            AND (
                <!-- 第一次审批的情况 -->
                ((sciv.process_step IS NULL OR sciv.result!=0) AND (
                    <!-- 城市经理审批 -->
                    (r1.employee_id IS NOT NULL AND (sciv.result IS NULL OR sciv.result!=0) AND #{currentPositionType} = 10 AND #{currentPerson} = r1.employee_id)
                    OR
                    <!-- 分公司总监审批 -->
                    ((r1.employee_id IS NULL OR sciv.result=0) AND r2.employee_id IS NOT NULL AND #{currentPositionType} = 2 AND #{currentPerson} = r2.employee_id)
                    OR
                    <!-- 其他人员审批（非总部） -->
                    ((r1.employee_id IS NULL OR sciv.result=0) AND r2.employee_id IS NULL AND #{currentPositionType} != 7)
                ))
                OR
                <!-- 第二次审批的情况 -->
                (sciv.process_step IS NOT NULL AND sciv.result=0 AND (
                    <!-- 分公司总监审批 -->
                    (r2.employee_id IS NOT NULL AND #{currentPositionType} = 2 AND #{currentPerson} = r2.employee_id)
                    OR
                    <!-- 其他人员审批（非总部且非区域经理） -->
                    (r2.employee_id IS NULL AND #{currentPositionType} != 7 AND #{currentPositionType} != 10)
                ))
            )
        </where>
    </select>
</mapper>