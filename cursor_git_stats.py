#!/usr/bin/env python3
"""
基于Git提交记录的Cursor代码修改统计分析器
通过分析Git提交记录来统计代码修改行数和变更情况
时间周期：上周一到今天
"""

import os
import subprocess
import re
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import argparse


@dataclass
class GitCommit:
    """Git提交记录"""
    hash: str
    author: str
    date: datetime
    message: str
    files_changed: int
    insertions: int
    deletions: int
    files: List[str]
    is_cursor_generated: bool = False
    cursor_lines: int = 0


@dataclass
class DailyStats:
    """每日统计"""
    date: str
    commits: int
    files_changed: int
    lines_added: int
    lines_removed: int
    net_lines: int
    cursor_commits: int = 0
    cursor_lines: int = 0


class CursorGitStatsAnalyzer:
    """基于Git的Cursor统计分析器"""

    def __init__(self, repo_path: str = ".", author_filter: Optional[str] = None):
        """
        初始化分析器

        Args:
            repo_path: Git仓库路径
            author_filter: 作者过滤器，只统计指定作者的提交
        """
        self.repo_path = repo_path
        self.author_filter = author_filter
        self.commits: List[GitCommit] = []
        
    def _get_time_range(self) -> Tuple[datetime, datetime]:
        """获取时间范围：上周一到今天"""
        today = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 计算上周一
        days_since_monday = today.weekday()  # 0=Monday, 6=Sunday
        days_to_last_monday = days_since_monday + 7
        last_monday = today - timedelta(days=days_to_last_monday)
        last_monday = last_monday.replace(hour=0, minute=0, second=0, microsecond=0)
        
        return last_monday, today
    
    def _run_git_command(self, command: List[str]) -> str:
        """执行Git命令"""
        try:
            result = subprocess.run(
                command,
                cwd=self.repo_path,
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"Git命令执行失败: {' '.join(command)}")
            print(f"错误: {e.stderr}")
            return ""
    
    def load_git_commits(self) -> None:
        """加载Git提交记录"""
        start_time, end_time = self._get_time_range()

        # 格式化时间为Git可识别的格式
        since = start_time.strftime('%Y-%m-%d')
        until = end_time.strftime('%Y-%m-%d')

        if self.author_filter:
            print(f"分析时间范围: {since} 到 {until} (作者: {self.author_filter})")
        else:
            print(f"分析时间范围: {since} 到 {until}")

        # 获取提交记录
        git_log_cmd = [
            'git', 'log',
            f'--since={since}',
            f'--until={until}',
            '--pretty=format:%H|%an|%ad|%s',
            '--date=iso',
            '--numstat'
        ]

        # 添加作者过滤
        if self.author_filter:
            git_log_cmd.extend(['--author', self.author_filter])

        output = self._run_git_command(git_log_cmd)
        if not output:
            if self.author_filter:
                print(f"没有找到作者 '{self.author_filter}' 的Git提交记录")
            else:
                print("没有找到Git提交记录")
            return

        self._parse_git_log(output)
    
    def _parse_git_log(self, git_log_output: str) -> None:
        """解析Git日志输出"""
        lines = git_log_output.split('\n')
        current_commit = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查是否是提交信息行
            if '|' in line and len(line.split('|')) >= 4:
                # 保存之前的提交
                if current_commit:
                    self.commits.append(current_commit)
                
                # 解析新提交
                parts = line.split('|', 3)
                hash_val = parts[0]
                author = parts[1]
                date_str = parts[2]
                message = parts[3]
                
                # 解析日期
                try:
                    date = datetime.fromisoformat(date_str.replace(' +', '+'))
                except ValueError:
                    try:
                        date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S %z')
                    except ValueError:
                        date = datetime.now()
                
                # 检测是否是Cursor生成的代码
                is_cursor_generated = self._is_cursor_generated_commit(message)

                current_commit = GitCommit(
                    hash=hash_val,
                    author=author,
                    date=date,
                    message=message,
                    files_changed=0,
                    insertions=0,
                    deletions=0,
                    files=[],
                    is_cursor_generated=is_cursor_generated,
                    cursor_lines=0
                )
            
            # 检查是否是文件统计行
            elif current_commit and '\t' in line:
                parts = line.split('\t')
                if len(parts) >= 3:
                    try:
                        insertions = int(parts[0]) if parts[0] != '-' else 0
                        deletions = int(parts[1]) if parts[1] != '-' else 0
                        filename = parts[2]
                        
                        current_commit.insertions += insertions
                        current_commit.deletions += deletions
                        current_commit.files_changed += 1
                        current_commit.files.append(filename)

                        # 如果是Cursor生成的提交，累计代码行数
                        if current_commit.is_cursor_generated:
                            current_commit.cursor_lines += insertions
                    except ValueError:
                        continue
        
        # 添加最后一个提交
        if current_commit:
            self.commits.append(current_commit)

    def _is_cursor_generated_commit(self, commit_message: str) -> bool:
        """检测提交是否是Cursor生成的代码"""
        cursor_indicators = [
            'cursor', 'ai generated', 'auto generated', 'generated by ai',
            'ai助手', 'ai辅助', 'cursor辅助', 'cursor生成',
            'copilot', 'ai completion', 'auto completion',
            '自动生成', '智能生成', 'ai代码', 'cursor代码',
            'fix by cursor', 'cursor fix', 'cursor suggestion',
            'cursor建议', 'cursor修复', 'ai建议', 'ai修复',
            'refactor by cursor', 'cursor重构', 'ai重构',
            'optimize by cursor', 'cursor优化', 'ai优化'
        ]

        message_lower = commit_message.lower()
        return any(indicator in message_lower for indicator in cursor_indicators)
    
    def get_author_stats(self) -> Dict[str, Dict]:
        """获取按作者分组的统计"""
        author_stats = {}

        for commit in self.commits:
            author = commit.author
            if author not in author_stats:
                author_stats[author] = {
                    'commits': 0,
                    'files_changed': 0,
                    'lines_added': 0,
                    'lines_removed': 0,
                    'net_lines': 0,
                    'cursor_commits': 0,
                    'cursor_lines': 0,
                    'cursor_percentage': 0.0
                }

            stats = author_stats[author]
            stats['commits'] += 1
            stats['files_changed'] += commit.files_changed
            stats['lines_added'] += commit.insertions
            stats['lines_removed'] += commit.deletions
            stats['net_lines'] += (commit.insertions - commit.deletions)

            # Cursor相关统计
            if commit.is_cursor_generated:
                stats['cursor_commits'] += 1
                stats['cursor_lines'] += commit.cursor_lines

        # 计算Cursor代码占比
        for author, stats in author_stats.items():
            if stats['lines_added'] > 0:
                stats['cursor_percentage'] = (stats['cursor_lines'] / stats['lines_added']) * 100

        return author_stats
    
    def get_daily_stats(self) -> List[DailyStats]:
        """获取每日统计"""
        daily_stats = {}

        for commit in self.commits:
            date_key = commit.date.strftime('%Y-%m-%d')

            if date_key not in daily_stats:
                daily_stats[date_key] = DailyStats(
                    date=date_key,
                    commits=0,
                    files_changed=0,
                    lines_added=0,
                    lines_removed=0,
                    net_lines=0,
                    cursor_commits=0,
                    cursor_lines=0
                )

            stats = daily_stats[date_key]
            stats.commits += 1
            stats.files_changed += commit.files_changed
            stats.lines_added += commit.insertions
            stats.lines_removed += commit.deletions
            stats.net_lines += (commit.insertions - commit.deletions)

            # Cursor相关统计
            if commit.is_cursor_generated:
                stats.cursor_commits += 1
                stats.cursor_lines += commit.cursor_lines

        return sorted(daily_stats.values(), key=lambda x: x.date)
    
    def get_file_type_stats(self) -> Dict[str, Dict]:
        """获取按文件类型分组的统计"""
        file_type_stats = {}
        
        for commit in self.commits:
            for file_path in commit.files:
                # 获取文件扩展名
                ext = os.path.splitext(file_path)[1].lower()
                if not ext:
                    ext = 'no_extension'
                
                if ext not in file_type_stats:
                    file_type_stats[ext] = {
                        'files': 0,
                        'commits': 0
                    }
                
                file_type_stats[ext]['files'] += 1
                # 避免重复计算同一提交中的多个文件
                
        # 计算每种文件类型的提交数
        for commit in self.commits:
            file_types_in_commit = set()
            for file_path in commit.files:
                ext = os.path.splitext(file_path)[1].lower()
                if not ext:
                    ext = 'no_extension'
                file_types_in_commit.add(ext)
            
            for ext in file_types_in_commit:
                if ext in file_type_stats:
                    file_type_stats[ext]['commits'] += 1
        
        return file_type_stats
    
    def generate_report(self) -> str:
        """生成统计报告"""
        if not self.commits:
            if self.author_filter:
                return f"没有找到作者 '{self.author_filter}' 的Git提交记录"
            else:
                return "没有找到符合条件的Git提交记录"

        start_time, end_time = self._get_time_range()

        # 总体统计
        total_commits = len(self.commits)
        total_files = sum(c.files_changed for c in self.commits)
        total_insertions = sum(c.insertions for c in self.commits)
        total_deletions = sum(c.deletions for c in self.commits)
        net_lines = total_insertions - total_deletions

        # Cursor相关统计
        cursor_commits = sum(1 for c in self.commits if c.is_cursor_generated)
        cursor_lines = sum(c.cursor_lines for c in self.commits)
        cursor_commit_percentage = (cursor_commits / total_commits * 100) if total_commits > 0 else 0
        cursor_line_percentage = (cursor_lines / total_insertions * 100) if total_insertions > 0 else 0

        # 获取各种统计
        author_stats = self.get_author_stats()
        daily_stats = self.get_daily_stats()
        file_type_stats = self.get_file_type_stats()

        # 报告标题
        title = "Git代码修改统计报告 (基于Cursor开发活动)"
        if self.author_filter:
            title += f" - 作者: {self.author_filter}"

        report = f"""
{title}
{'=' * len(title)}

统计时间范围: {start_time.strftime('%Y-%m-%d')} 到 {end_time.strftime('%Y-%m-%d')}
仓库路径: {os.path.abspath(self.repo_path)}
{'作者过滤: ' + self.author_filter if self.author_filter else ''}

总体统计:
--------
总提交数: {total_commits}
总文件变更数: {total_files}
总添加行数: {total_insertions}
总删除行数: {total_deletions}
净增行数: {net_lines:+d}
平均每次提交行数: {(total_insertions + total_deletions) / total_commits:.1f} (如果有提交)

Cursor代码统计:
--------------
Cursor生成提交数: {cursor_commits} ({cursor_commit_percentage:.1f}%)
Cursor生成代码行数: {cursor_lines} ({cursor_line_percentage:.1f}%)
手动编写代码行数: {total_insertions - cursor_lines} ({100 - cursor_line_percentage:.1f}%)

按作者统计:
----------
"""
        
        for author, stats in sorted(author_stats.items(), key=lambda x: x[1]['commits'], reverse=True):
            report += f"""
{author}:
  提交数: {stats['commits']}
  文件变更: {stats['files_changed']}
  添加行数: {stats['lines_added']}
  删除行数: {stats['lines_removed']}
  净增行数: {stats['net_lines']:+d}
  Cursor提交数: {stats['cursor_commits']} ({stats['cursor_commits']/stats['commits']*100:.1f}%)
  Cursor代码行数: {stats['cursor_lines']} ({stats['cursor_percentage']:.1f}%)
  手动代码行数: {stats['lines_added'] - stats['cursor_lines']} ({100 - stats['cursor_percentage']:.1f}%)
"""
        
        report += "\n每日统计:\n--------\n"
        for day_stat in daily_stats:
            cursor_info = f", Cursor: {day_stat.cursor_commits}次提交/{day_stat.cursor_lines}行" if day_stat.cursor_commits > 0 else ""
            report += f"{day_stat.date}: {day_stat.commits}次提交, {day_stat.files_changed}个文件, {day_stat.net_lines:+d}行净变更{cursor_info}\n"
        
        report += "\n文件类型统计:\n-----------\n"
        for ext, stats in sorted(file_type_stats.items(), key=lambda x: x[1]['files'], reverse=True):
            if stats['files'] > 0:
                report += f"{ext or '无扩展名'}: {stats['files']}个文件, {stats['commits']}次提交\n"
        
        # 最近的提交
        report += "\n最近的提交:\n----------\n"
        recent_commits = sorted(self.commits, key=lambda x: x.date, reverse=True)[:10]
        for commit in recent_commits:
            report += f"{commit.date.strftime('%Y-%m-%d %H:%M')} - {commit.author}\n"
            report += f"  {commit.message[:80]}{'...' if len(commit.message) > 80 else ''}\n"
            report += f"  {commit.files_changed}个文件, +{commit.insertions}/-{commit.deletions}行\n\n"
        
        return report
    
    def run_analysis(self) -> str:
        """运行完整分析"""
        if self.author_filter:
            print(f"开始分析Git提交记录 (作者: {self.author_filter})...")
        else:
            print("开始分析Git提交记录...")

        # 检查是否是Git仓库
        if not os.path.exists(os.path.join(self.repo_path, '.git')):
            return f"错误: {self.repo_path} 不是一个Git仓库"

        # 加载提交记录
        self.load_git_commits()

        # 生成报告
        return self.generate_report()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基于Git的Cursor代码修改统计分析器')
    parser.add_argument('--repo-path', type=str, default='.', help='Git仓库路径 (默认: 当前目录)')
    parser.add_argument('--output', type=str, help='输出文件路径')
    parser.add_argument('--author', type=str, help='只统计指定作者的提交 (例如: zhengxu)')

    args = parser.parse_args()

    try:
        analyzer = CursorGitStatsAnalyzer(args.repo_path, args.author)
        report = analyzer.run_analysis()

        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"报告已保存到: {args.output}")
        else:
            print(report)

    except Exception as e:
        print(f"分析过程中出错: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
