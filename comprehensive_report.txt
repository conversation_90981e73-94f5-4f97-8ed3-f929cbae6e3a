
Cursor使用情况综合统计报告 - 用户: zhengxu
==============================

生成时间: 2025-08-18 17:06:59
统计周期: 上周一到今天

📊 综合统计摘要:
===============

Git提交统计:
-----------
• 总提交数: 38
• 添加代码行数: 1849
• 净增代码行数: 418

Cursor生成统计:
--------------
• 包含代码的对话数: 31
• 生成代码行数: 117
• 平均每对话: 3.8行

🎯 效率分析:
===========
• Cursor代码采纳率: 100.0%
• AI辅助效率: 3.1行/提交
• 开发活跃度: 5.4次提交/天

💡 分析建议:
===========
• ✅ 很高的代码采纳率，AI辅助效果显著
• 📝 可以更多地使用AI辅助来提高开发效率
• 🚀 开发活跃度很高，保持良好的开发节奏


📋 详细报告:
===========


Git代码修改统计报告 (基于Cursor开发活动) - 作者: zhengxu
========================================

统计时间范围: 2025-08-11 到 2025-08-18
仓库路径: /Users/<USER>/project/sfa-backend
作者过滤: zhengxu

总体统计:
--------
总提交数: 38
总文件变更数: 145
总添加行数: 1849
总删除行数: 1431
净增行数: +418
平均每次提交行数: 86.3 (如果有提交)

Cursor代码统计:
--------------
Cursor生成提交数: 0 (0.0%)
Cursor生成代码行数: 0 (0.0%)
手动编写代码行数: 1849 (100.0%)

按作者统计:
----------

zhengxu:
  提交数: 38
  文件变更: 145
  添加行数: 1849
  删除行数: 1431
  净增行数: +418
  Cursor提交数: 0 (0.0%)
  Cursor代码行数: 0 (0.0%)
  手动代码行数: 1849 (100.0%)

每日统计:
--------
2025-08-12: 7次提交, 53个文件, +346行净变更
2025-08-13: 7次提交, 24个文件, +173行净变更
2025-08-14: 18次提交, 34个文件, +180行净变更
2025-08-15: 6次提交, 34个文件, -281行净变更

文件类型统计:
-----------
.java: 119个文件, 21次提交
.xml: 25个文件, 13次提交
.yaml: 1个文件, 1次提交

最近的提交:
----------
2025-08-15 11:47 - zhengxu
  修改maven包
  1个文件, +1/-1行

2025-08-15 11:20 - zhengxu
  Merge branch 'hotfix_2025_08_11' into release_V13.5.0
  0个文件, +0/-0行

2025-08-15 11:15 - zhengxu
  消息发放修改
  3个文件, +40/-0行

2025-08-15 10:33 - zhengxu
  Merge branch 'feature/V13.5.0_part_agent' into release_V13.5.0
  0个文件, +0/-0行

2025-08-15 10:32 - zhengxu
  代办修改
  5个文件, +23/-7行

2025-08-15 07:46 - zhengxu
  revert
  25个文件, +264/-601行

2025-08-14 19:38 - zhengxu
  Merge branch 'feature/V13.5.0_salary' into release_V13.5.0
  0个文件, +0/-0行

2025-08-14 19:37 - zhengxu
  bug fix
  1个文件, +1/-1行

2025-08-14 19:21 - zhengxu
  Merge branch 'feature/V13.5.0_salary' into release_V13.5.0
  0个文件, +0/-0行

2025-08-14 19:21 - zhengxu
  入职流程修改
  9个文件, +86/-85行



============================================================


Cursor聊天记录代码生成分析 - 用户: zhengxu
==============================

分析时间: 2025-08-18 17:06:59
搜索范围: 上周一到今天 (2025-08-11 到 2025-08-18)

统计结果:
--------
包含代码的对话数: 31
总代码块数: 117
总代码行数: 117
平均每对话行数: 3.8

代码块详情:
----------
1. inline: 1行
2. inline: 1行
3. inline: 1行
4. inline: 1行
5. inline: 1行
6. inline: 1行
7. inline: 1行
8. inline: 1行
9. inline: 1行
10. inline: 1行
11. inline: 1行
12. inline: 1行
13. inline: 1行
14. inline: 1行
15. inline: 1行
16. inline: 1行
17. inline: 1行
18. inline: 1行
19. inline: 1行
20. inline: 1行
21. inline: 1行
22. inline: 1行
23. inline: 1行
24. inline: 1行
25. inline: 1行
26. inline: 1行
27. inline: 1行
28. inline: 1行
29. inline: 1行
30. inline: 1行
31. inline: 1行
32. inline: 1行
33. inline: 1行
34. inline: 1行
35. inline: 1行
36. inline: 1行
37. inline: 1行
38. inline: 1行
39. inline: 1行
40. inline: 1行
41. inline: 1行
42. inline: 1行
43. inline: 1行
44. inline: 1行
45. inline: 1行
46. inline: 1行
47. inline: 1行
48. inline: 1行
49. inline: 1行
50. inline: 1行
51. inline: 1行
52. inline: 1行
53. inline: 1行
54. inline: 1行
55. inline: 1行
56. inline: 1行
57. inline: 1行
58. inline: 1行
59. inline: 1行
60. inline: 1行
61. inline: 1行
62. inline: 1行
63. inline: 1行
64. inline: 1行
65. inline: 1行
66. inline: 1行
67. inline: 1行
68. inline: 1行
69. inline: 1行
70. inline: 1行
71. inline: 1行
72. inline: 1行
73. inline: 1行
74. inline: 1行
75. inline: 1行
76. inline: 1行
77. inline: 1行
78. inline: 1行
79. inline: 1行
80. inline: 1行
81. inline: 1行
82. inline: 1行
83. inline: 1行
84. inline: 1行
85. inline: 1行
86. inline: 1行
87. inline: 1行
88. inline: 1行
89. inline: 1行
90. inline: 1行
91. inline: 1行
92. inline: 1行
93. inline: 1行
94. inline: 1行
95. inline: 1行
96. inline: 1行
97. inline: 1行
98. inline: 1行
99. inline: 1行
100. inline: 1行
101. inline: 1行
102. inline: 1行
103. inline: 1行
104. inline: 1行
105. inline: 1行
106. inline: 1行
107. inline: 1行
108. inline: 1行
109. inline: 1行
110. inline: 1行
111. inline: 1行
112. inline: 1行
113. inline: 1行
114. inline: 1行
115. inline: 1行
116. inline: 1行
117. inline: 1行

分析说明:
--------
- 此分析基于Cursor本地聊天记录文件
- 统计了所有```包围的代码块和单行代码
- 过滤了注释行，只统计有效代码行数
- 如果未发现代码，可能是：
  1. Cursor聊天记录存储在其他位置
  2. 聊天记录已被清理
  3. 使用的是在线版本的Cursor

建议:
----
- 结合Git提交记录分析获得更完整的统计
- 定期备份重要的代码生成记录
- 考虑使用Git提交信息标记Cursor生成的代码

