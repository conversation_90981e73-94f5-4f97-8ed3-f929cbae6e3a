#!/usr/bin/env python3
"""
Cursor统计工具运行脚本
提供简单的交互式界面来运行不同的统计工具
"""

import os
import sys
from datetime import datetime, timedelta


def get_time_range_display():
    """显示当前的时间范围"""
    today = datetime.now()
    days_since_monday = today.weekday()
    days_to_last_monday = days_since_monday + 7
    last_monday = today - timedelta(days=days_to_last_monday)
    
    return last_monday.strftime('%Y-%m-%d'), today.strftime('%Y-%m-%d')


def run_git_stats(repo_path=None, output_file=None):
    """运行Git统计工具"""
    try:
        from cursor_git_stats import CursorGitStatsAnalyzer
        
        if not repo_path:
            repo_path = input("请输入Git仓库路径 (回车使用当前目录): ").strip()
            if not repo_path:
                repo_path = "."
        
        print(f"\n正在分析Git仓库: {os.path.abspath(repo_path)}")
        
        analyzer = CursorGitStatsAnalyzer(repo_path)
        report = analyzer.run_analysis()
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"\n报告已保存到: {output_file}")
        else:
            print("\n" + "="*50)
            print(report)
            
        return True
        
    except ImportError:
        print("错误: 找不到 cursor_git_stats.py 文件")
        return False
    except Exception as e:
        print(f"运行Git统计时出错: {e}")
        return False


def run_cursor_data_stats(data_path=None, output_file=None):
    """运行Cursor数据统计工具"""
    try:
        from cursor_stats_analyzer import CursorStatsAnalyzer
        
        print("\n正在分析Cursor数据...")
        
        analyzer = CursorStatsAnalyzer(data_path)
        report = analyzer.run_analysis()
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"\n报告已保存到: {output_file}")
        else:
            print("\n" + "="*50)
            print(report)
            
        return True
        
    except ImportError:
        print("错误: 找不到 cursor_stats_analyzer.py 文件")
        return False
    except Exception as e:
        print(f"运行Cursor数据统计时出错: {e}")
        return False


def main():
    """主函数"""
    print("Cursor代码修改统计工具")
    print("=" * 30)
    
    start_date, end_date = get_time_range_display()
    print(f"统计时间范围: {start_date} 到 {end_date}")
    print()
    
    while True:
        print("请选择统计方式:")
        print("1. Git提交记录统计 (推荐)")
        print("2. Cursor数据分析统计")
        print("3. 同时运行两种统计")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            # Git统计
            repo_path = input("\n请输入Git仓库路径 (回车使用当前目录): ").strip()
            if not repo_path:
                repo_path = "."
            
            save_to_file = input("是否保存报告到文件? (y/n): ").strip().lower()
            output_file = None
            if save_to_file == 'y':
                output_file = f"git_stats_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            run_git_stats(repo_path, output_file)
            
        elif choice == "2":
            # Cursor数据统计
            data_path = input("\n请输入Cursor数据路径 (回车自动检测): ").strip()
            if not data_path:
                data_path = None
            
            save_to_file = input("是否保存报告到文件? (y/n): ").strip().lower()
            output_file = None
            if save_to_file == 'y':
                output_file = f"cursor_stats_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            run_cursor_data_stats(data_path, output_file)
            
        elif choice == "3":
            # 同时运行两种统计
            print("\n=== 运行Git统计 ===")
            repo_path = input("请输入Git仓库路径 (回车使用当前目录): ").strip()
            if not repo_path:
                repo_path = "."
            
            git_output = f"git_stats_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            cursor_output = f"cursor_stats_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            print(f"Git报告将保存到: {git_output}")
            print(f"Cursor报告将保存到: {cursor_output}")
            
            success1 = run_git_stats(repo_path, git_output)
            
            print("\n=== 运行Cursor数据统计 ===")
            success2 = run_cursor_data_stats(None, cursor_output)
            
            if success1 or success2:
                print(f"\n统计完成! 报告文件:")
                if success1:
                    print(f"- Git统计: {git_output}")
                if success2:
                    print(f"- Cursor统计: {cursor_output}")
            
        elif choice == "4":
            print("退出程序")
            break
            
        else:
            print("无效选择，请重新输入")
            continue
        
        print("\n" + "-" * 50)
        continue_choice = input("是否继续使用? (y/n): ").strip().lower()
        if continue_choice != 'y':
            break
    
    print("感谢使用!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
