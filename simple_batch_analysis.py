#!/usr/bin/env python3
"""
简化版批量分析脚本
直接调用命令行工具进行批量分析
"""

import os
import subprocess
from datetime import datetime
import argparse


def run_analysis_for_user(username: str, repo_path: str = ".") -> dict:
    """为单个用户运行分析"""
    print(f"\n📊 正在分析用户: {username}")
    print("-" * 40)
    
    result = {
        'username': username,
        'git_commits': 0,
        'git_lines_added': 0,
        'git_net_lines': 0,
        'cursor_conversations': 0,
        'cursor_lines': 0,
        'git_success': False,
        'cursor_success': False
    }
    
    # 运行Git分析
    try:
        print("📈 运行Git分析...")
        git_cmd = ['python3', 'cursor_git_stats.py', '--author', username, '--repo-path', repo_path]
        git_result = subprocess.run(git_cmd, capture_output=True, text=True, cwd=repo_path)
        
        if git_result.returncode == 0:
            # 解析Git输出
            for line in git_result.stdout.split('\n'):
                if '总提交数:' in line:
                    result['git_commits'] = int(line.split(':')[1].strip())
                elif '总添加行数:' in line:
                    result['git_lines_added'] = int(line.split(':')[1].strip())
                elif '净增行数:' in line:
                    result['git_net_lines'] = int(line.split(':')[1].strip().replace('+', ''))
            result['git_success'] = True
            print(f"   ✅ Git分析成功: {result['git_commits']}次提交, {result['git_lines_added']}行代码")
        else:
            print(f"   ❌ Git分析失败: {git_result.stderr}")
            
    except Exception as e:
        print(f"   ❌ Git分析异常: {e}")
    
    # 运行Cursor聊天分析
    try:
        print("💬 运行Cursor聊天分析...")
        cursor_cmd = ['python3', 'cursor_chat_analyzer.py', '--author', username]
        cursor_result = subprocess.run(cursor_cmd, capture_output=True, text=True, cwd=repo_path)
        
        if cursor_result.returncode == 0:
            # 解析Cursor输出
            for line in cursor_result.stdout.split('\n'):
                if '包含代码的对话:' in line:
                    result['cursor_conversations'] = int(line.split(':')[1].strip())
                elif '总代码行数:' in line:
                    result['cursor_lines'] = int(line.split(':')[1].strip())
            result['cursor_success'] = True
            print(f"   ✅ Cursor分析成功: {result['cursor_conversations']}次对话, {result['cursor_lines']}行代码")
        else:
            print(f"   ❌ Cursor分析失败: {cursor_result.stderr}")
            
    except Exception as e:
        print(f"   ❌ Cursor分析异常: {e}")
    
    # 计算采纳率
    if result['cursor_lines'] > 0:
        result['adoption_rate'] = min(100, (result['git_lines_added'] / result['cursor_lines']) * 100)
    else:
        result['adoption_rate'] = 0
    
    # 计算效率分数
    if result['git_commits'] > 0:
        result['efficiency_score'] = result['cursor_lines'] / result['git_commits']
    else:
        result['efficiency_score'] = 0
    
    print(f"📊 {username} 分析完成:")
    print(f"   Git: {result['git_commits']}次提交, {result['git_lines_added']}行")
    print(f"   Cursor: {result['cursor_conversations']}次对话, {result['cursor_lines']}行")
    print(f"   采纳率: {result['adoption_rate']:.1f}%")
    
    return result


def generate_team_report(results: list) -> str:
    """生成团队对比报告"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 过滤成功的结果
    valid_results = [r for r in results if r['git_success'] or r['cursor_success']]
    
    if not valid_results:
        return "没有成功分析的用户数据"
    
    # 排序
    valid_results.sort(key=lambda x: x['git_commits'], reverse=True)
    
    report = f"""
团队Cursor使用情况对比报告
========================

生成时间: {timestamp}
分析用户数: {len(valid_results)}
统计周期: 上周一到今天

📊 团队排行榜:
=============
"""
    
    for i, result in enumerate(valid_results, 1):
        status = "✅" if result['git_success'] and result['cursor_success'] else "⚠️"
        report += f"""
{i}. {result['username']} {status}
   Git提交: {result['git_commits']}次
   添加代码: {result['git_lines_added']}行
   净增代码: {result['git_net_lines']:+d}行
   Cursor对话: {result['cursor_conversations']}次
   Cursor生成: {result['cursor_lines']}行
   采纳率: {result['adoption_rate']:.1f}%
   效率分数: {result['efficiency_score']:.1f}行/提交
"""
    
    # 团队统计
    total_commits = sum(r['git_commits'] for r in valid_results)
    total_git_lines = sum(r['git_lines_added'] for r in valid_results)
    total_cursor_lines = sum(r['cursor_lines'] for r in valid_results)
    total_conversations = sum(r['cursor_conversations'] for r in valid_results)
    
    avg_adoption_rate = sum(r['adoption_rate'] for r in valid_results) / len(valid_results) if valid_results else 0
    
    report += f"""

📈 团队总体统计:
===============
总Git提交数: {total_commits}
总添加代码行数: {total_git_lines}
总Cursor对话数: {total_conversations}
总Cursor生成行数: {total_cursor_lines}
团队平均采纳率: {avg_adoption_rate:.1f}%
团队AI使用率: {(total_cursor_lines / total_git_lines * 100) if total_git_lines > 0 else 0:.1f}%
"""
    
    # 最佳表现
    if valid_results:
        most_commits = max(valid_results, key=lambda x: x['git_commits'])
        most_cursor = max(valid_results, key=lambda x: x['cursor_lines'])
        best_adoption = max(valid_results, key=lambda x: x['adoption_rate'])
        
        report += f"""

🏆 最佳表现:
===========
最多提交: {most_commits['username']} ({most_commits['git_commits']}次)
最多AI使用: {most_cursor['username']} ({most_cursor['cursor_lines']}行)
最高采纳率: {best_adoption['username']} ({best_adoption['adoption_rate']:.1f}%)
"""
    
    return report


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简化版批量分析工具')
    parser.add_argument('--users', type=str, nargs='+', help='用户名列表')
    parser.add_argument('--users-file', type=str, help='用户名文件')
    parser.add_argument('--repo-path', type=str, default='.', help='Git仓库路径')
    parser.add_argument('--output', type=str, help='输出文件')
    
    args = parser.parse_args()
    
    # 获取用户列表
    users = []
    if args.users:
        users.extend(args.users)
    
    if args.users_file and os.path.exists(args.users_file):
        with open(args.users_file, 'r', encoding='utf-8') as f:
            users.extend([line.strip() for line in f if line.strip()])
    
    if not users:
        print("❌ 请提供用户列表")
        print("使用方法:")
        print("  python3 simple_batch_analysis.py --users zhengxu zhanghaijun")
        print("  python3 simple_batch_analysis.py --users-file users.txt")
        return 1
    
    print(f"🚀 团队Cursor使用情况批量分析")
    print(f"📁 仓库路径: {os.path.abspath(args.repo_path)}")
    print(f"👥 分析用户: {', '.join(users)}")
    print("=" * 50)
    
    # 批量分析
    results = []
    for user in users:
        result = run_analysis_for_user(user, args.repo_path)
        results.append(result)
    
    # 生成报告
    print(f"\n📊 生成团队对比报告...")
    report = generate_team_report(results)
    
    # 输出报告
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"✅ 报告已保存到: {args.output}")
    else:
        print("\n" + "="*60)
        print(report)
    
    # 显示快速摘要
    successful_users = [r for r in results if r['git_success'] or r['cursor_success']]
    total_commits = sum(r['git_commits'] for r in successful_users)
    total_cursor_lines = sum(r['cursor_lines'] for r in successful_users)
    
    print(f"\n🎯 快速摘要:")
    print(f"   👥 成功分析用户: {len(successful_users)}/{len(users)}")
    print(f"   📊 团队总提交: {total_commits}次")
    print(f"   💬 团队Cursor生成: {total_cursor_lines}行")
    
    return 0


if __name__ == "__main__":
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        exit(1)
