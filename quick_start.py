#!/usr/bin/env python3
"""
Cursor统计工具快速启动脚本
一键运行统计分析
"""

import os
import sys
from datetime import datetime


def main():
    """快速启动主函数"""
    print("🚀 Cursor代码修改统计工具 - 快速启动")
    print("=" * 50)
    
    # 显示当前时间和统计范围
    now = datetime.now()
    print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查是否在Git仓库中
    if os.path.exists('.git'):
        print("✓ 检测到Git仓库")
        
        # 直接运行Git统计
        print("\n正在运行Git统计分析...")
        try:
            from cursor_git_stats import CursorGitStatsAnalyzer
            
            analyzer = CursorGitStatsAnalyzer('.')
            report = analyzer.run_analysis()
            
            # 生成报告文件名
            timestamp = now.strftime('%Y%m%d_%H%M%S')
            output_file = f"cursor_git_stats_{timestamp}.txt"
            
            # 保存报告
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"✅ 统计完成! 报告已保存到: {output_file}")
            
            # 显示简要统计
            lines = report.split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['总提交数:', '总添加行数:', '总删除行数:', '净增行数:']):
                    print(f"📊 {line.strip()}")
            
            print(f"\n📄 完整报告请查看: {output_file}")
            
        except ImportError:
            print("❌ 找不到cursor_git_stats.py文件")
            print("请确保所有脚本文件都在当前目录下")
        except Exception as e:
            print(f"❌ 运行统计时出错: {e}")
    
    else:
        print("⚠️  当前目录不是Git仓库")
        print("\n请选择以下操作:")
        print("1. 切换到Git仓库目录后重新运行")
        print("2. 使用完整版工具: python run_cursor_stats.py")
        print("3. 手动指定仓库路径: python cursor_git_stats.py --repo-path /path/to/repo")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("请尝试运行: python test_cursor_stats.py 来检查环境")
